<?php

use \Essential_Addons_Elementor\Classes\Helper;

/**
 * Template Name: Preset 1
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
} // Exit if accessed directly

$product = wc_get_product( get_the_ID() );

if ( ! $product ) {
    error_log( '$product not found in ' . __FILE__ );
    return;
}

// Improvement
$sale_badge_align = isset( $settings['eael_product_sale_badge_alignment'] ) ? $settings['eael_product_sale_badge_alignment'] : '';
$sale_badge_preset = !empty($settings['eael_product_sale_badge_preset']) ?
    $settings['eael_product_sale_badge_preset'] : 'sale-preset-3';
$sale_text = !empty($settings['eael_product_carousel_sale_text']) ? $settings['eael_product_carousel_sale_text'] : 'Sale!';
$stockout_text = !empty($settings['eael_product_carousel_stockout_text']) ? $settings['eael_product_carousel_stockout_text'] : 'Stock Out';

// should print vars
$should_print_rating = isset( $settings['eael_product_slider_rating'] ) && 'yes' === $settings['eael_product_slider_rating'];
$should_print_quick_view = isset( $settings['eael_product_slider_quick_view'] ) && 'yes' === $settings['eael_product_slider_quick_view'];
$should_print_image_clickable = isset( $settings['eael_product_slider_image_clickable'] ) && 'yes' === $settings['eael_product_slider_image_clickable'];
$should_print_price = isset( $settings['eael_product_slider_price'] ) && 'yes' === $settings['eael_product_slider_price'];
$should_print_excerpt = isset( $settings['eael_product_slider_excerpt'] ) && ('yes' === $settings['eael_product_slider_excerpt'] && has_excerpt());
$widget_id = isset($settings['eael_widget_id']) ? $settings['eael_widget_id'] : null;
$quick_view_setting = [
        'widget_id' => $widget_id,
        'product_id' => $product->get_id(),
        'page_id' => $settings['eael_page_id'],
];

$reverse = ($settings['eael_product_slider_column_reverse'] == 'yes') ? 'eael-reverse-column' : '';

if ( true === wc_get_loop_product_visibility( $product->get_id() ) || $product->is_visible() ) {
    ?>
    <div <?php post_class( ['product', 'swiper-slide'] ); ?>>
        <div class="eael-product-slider <?php echo esc_attr( $reverse );?>">
            <div class="product-details-wrap">
                <div class="product-details">
			        <?php
                    $product_details_html = '';

			        if ($settings['eael_show_post_terms'] === 'yes') {
				        $product_details_html .= Helper::get_product_categories_list($settings['eael_post_terms']);
			        }

			        if ( $settings['eael_product_slider_show_title'] ) {
				        $product_details_html .= '<div class="eael-product-title">';
				        $product_details_html .= '<' . $settings['eael_product_slider_title_tag'] . '>';
				        if ( empty( $settings['eael_product_slider_title_length'] ) ) {
					        $product_details_html .= $product->get_title();
				        } else {
					        $product_details_html .= implode( " ", array_slice( explode( " ", $product->get_title() ), 0, $settings['eael_product_slider_title_length'] ) );
				        }
				        $product_details_html .= '</' . $settings['eael_product_slider_title_tag'] . '>';
				        $product_details_html .= '</div>';
			        }
			       
                    if ( $should_print_rating ) {
                        $avg_rating = $product->get_average_rating();
                        if( $avg_rating > 0 ){
                            $product_details_html .= wc_get_rating_html( $avg_rating, $product->get_rating_count());
                        } else {
                            $product_details_html .= Helper::eael_rating_markup( $avg_rating, $product->get_rating_count() );
                        }
			        }
			        if ( $should_print_excerpt ) {
				        $product_details_html .= '<div class="eael-product-excerpt">';
				        $product_details_html .= '<p>' . wp_trim_words(strip_shortcodes(get_the_excerpt()), $settings['eael_product_slider_excerpt_length'],
                        $settings['eael_product_slider_excerpt_expanison_indicator']) . '</p>';
				        $product_details_html .= '</div>';
			        }
			         
                    if($should_print_price ){
				        $product_details_html .= '<div class="eael-product-price">'. $product->get_price_html() .'</div>';
			        }

                    if( $product_details_html ){
                        echo wp_kses( $product_details_html, Helper::eael_allowed_tags() );
                    }
                    ?>
                </div>

                <ul class="icons-wrap box-style">
                    <li class="add-to-cart"><?php woocommerce_template_loop_add_to_cart(); ?></li>
		            <?php if( $should_print_quick_view ){?>
                        <li class="eael-product-quick-view">
                            <a id="eael_quick_view_<?php echo esc_attr( uniqid() ); ?>" data-quickview-setting="<?php echo esc_attr( htmlspecialchars(wp_json_encode($quick_view_setting),ENT_QUOTES) ); ?>"
                               class="open-popup-link">
                                <i class="fas fa-eye"></i>
                            </a>
                        </li>
		            <?php } ?>
                    <li class="view-details" title="Details"><?php echo '<a aria-label="view_details_' . $product->get_id() . '" href="' . esc_url( $product->get_permalink() ) . '"><i class="fas fa-link"></i></a>'; ?></li>
                </ul>
            </div>
            <div class="product-image-wrap">
                <div class="image-wrap">
                    <?php
                    echo ( ! $product->managing_stock() && ! $product->is_in_stock() ? '<span class="eael-onsale outofstock ' . esc_attr( $sale_badge_preset ) . ' ' . esc_attr( $sale_badge_align ) . '">'. esc_html( $stockout_text ) .'</span>' : ($product->is_on_sale() ? '<span class="eael-onsale ' . esc_attr( $sale_badge_preset ) . ' ' . esc_attr( $sale_badge_align ) . '">' . esc_html( $sale_text ) . '</span>' : '') );
                    if( $should_print_image_clickable ) {
	                    echo '<a href="' . esc_url( $product->get_permalink() ) . '" class="woocommerce-LoopProduct-link woocommerce-loop-product__link">';
                    }
                    echo wp_kses( $product->get_image( $settings['eael_product_slider_image_size_size'], ['loading' => 'eager']), Helper::eael_allowed_tags() );

                    if( $should_print_image_clickable ) {
	                    echo '</a>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <?php
}
