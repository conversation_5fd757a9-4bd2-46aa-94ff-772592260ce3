<?php

namespace Essential_Addons_Elementor\Pro\Classes;

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

use Exception;
use WP_Error;

/**
 * @property string $namespace
 */
#[\AllowDynamicProperties ]
class FigmaImageHandler
{

    /**
     * Register AJAX actions
     */
    public function register()
    {
        add_action( 'wp_ajax_eael_upload_figma_images', [ $this, 'eael_handle_figma_images_upload' ] );
    }

    /**
     * Handle multiple Figma image uploads via AJAX
     */
    public function eael_handle_figma_images_upload()
    {
        // Verify nonce
        if ( ! isset( $_POST[ 'nonce' ] ) || ! wp_verify_nonce( $_POST[ 'nonce' ], 'essential-addons-elementor' ) ) {
            wp_send_json_error( 'Invalid nonce', 403 );
            return;
        }

        // Check user capabilities
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( 'Insufficient permissions', 403 );
            return;
        }

        // Get and validate figma URLs
        $figma_urls = isset( $_POST[ 'figma_urls' ] ) ? json_decode( wp_unslash( $_POST[ 'figma_urls' ] ) ) : [  ];

        if ( empty( $figma_urls ) || ! is_array( $figma_urls ) ) {
            wp_send_json_error( 'No valid image URLs provided', 400 );
            return;
        }

        $uploaded_images = [  ];

        foreach ( $figma_urls as $figma_url ) {
            $uploaded_image = $this->eael_process_single_figma_image( $figma_url );
            if ( ! is_wp_error( $uploaded_image ) ) {
                $uploaded_images[ $figma_url ] = $uploaded_image;
            }
        }

        wp_send_json_success( [
            'images' => $uploaded_images
         ] );
    }

    /**
     * Process a single Figma image
     *
     * @param string $figma_url The Figma image URL
     * @return array|WP_Error
     */
    public function eael_process_single_figma_image( $figma_url )
    {
        if ( empty( $figma_url ) ) {
            return new WP_Error( 'missing_url', 'No image URL provided' );
        }

        // Download image from Figma
        $response = wp_remote_get( $figma_url );
        if ( is_wp_error( $response ) ) {
            return new WP_Error( 'download_failed', 'Failed to download image' );
        }

        $image_data   = wp_remote_retrieve_body( $response );
        $content_type = wp_remote_retrieve_header( $response, 'content-type' );

        // Determine file extension
        $extension = '';
        if ( $content_type === 'image/png' ) {
            $extension = 'png';
        } elseif ( $content_type === 'image/jpeg' || $content_type === 'image/jpg' ) {
            $extension = 'jpg';
        } elseif ( $content_type === 'image/webp' ) {
            $extension = 'webp';
        } else {
            return new WP_Error( 'unsupported_type', 'Unsupported image type' );
        }

        // Save image to WordPress
        $upload_dir = wp_upload_dir();
        $file_name  = 'figma_image_' . time() . '_' . wp_generate_password( 6, false ) . '.' . $extension;
        $file_path  = $upload_dir[ 'path' ] . '/' . $file_name;

        if ( ! file_put_contents( $file_path, $image_data ) ) {
            return new WP_Error( 'save_failed', 'Failed to save image' );
        }

        // Insert into media library
        $attachment_id = wp_insert_attachment( [
            'post_mime_type' => $content_type,
            'post_title'     => sanitize_file_name( $file_name ),
            'post_content'   => '',
            'post_status'    => 'inherit'
         ], $file_path );

        if ( is_wp_error( $attachment_id ) ) {
            return new WP_Error( 'upload_failed', 'Failed to upload image to media library' );
        }

        // Generate attachment metadata
        require_once ABSPATH . 'wp-admin/includes/image.php';
        $attach_data = wp_generate_attachment_metadata( $attachment_id, $file_path );
        wp_update_attachment_metadata( $attachment_id, $attach_data );

        return [
            'id'  => $attachment_id,
            'url' => wp_get_attachment_url( $attachment_id )
         ];
    }
}