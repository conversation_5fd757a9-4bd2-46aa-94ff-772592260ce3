!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("three"),require("@photo-sphere-viewer/core")):"function"==typeof define&&define.amd?define(["exports","three","@photo-sphere-viewer/core"],t):t(((e="undefined"!=typeof globalThis?globalThis:e||self).PhotoSphereViewer=e.PhotoSphereViewer||{},e.PhotoSphereViewer.MarkersPlugin={}),e.THREE,e.PhotoSphereViewer)}(this,function(e,t,i){"use strict";var s=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,a=(e,t)=>{for(var i in t)s(e,i,{get:t[i],enumerable:!0})},l={};a(l,{MarkersPlugin:()=>ex,events:()=>c});var h=i,c={};a(c,{EnterMarkerEvent:()=>k,GotoMarkerDoneEvent:()=>m,HideMarkersEvent:()=>P,LeaveMarkerEvent:()=>y,MarkerVisibilityEvent:()=>v,MarkersPluginEvent:()=>d,RenderMarkersListEvent:()=>V,SelectMarkerEvent:()=>E,SelectMarkerListEvent:()=>b,SetMarkersEvent:()=>L,ShowMarkersEvent:()=>z,UnselectMarkerEvent:()=>S});var p=i,d=class extends p.TypedEvent{},g=class e extends d{constructor(t,i){super(e.type),this.marker=t,this.visible=i}};g.type="marker-visibility";var v=g,u=class e extends d{constructor(t){super(e.type),this.marker=t}};u.type="goto-marker-done";var m=u,f=class e extends d{constructor(t){super(e.type),this.marker=t}};f.type="leave-marker";var y=f,w=class e extends d{constructor(t){super(e.type),this.marker=t}};w.type="enter-marker";var k=w,$=class e extends d{constructor(t,i,s){super(e.type),this.marker=t,this.doubleClick=i,this.rightClick=s}};$.type="select-marker";var E=$,_=class e extends d{constructor(t){super(e.type),this.marker=t}};_.type="select-marker-list";var b=_,M=class e extends d{constructor(t){super(e.type),this.marker=t}};M.type="unselect-marker";var S=M,x=class e extends d{constructor(){super(e.type)}};x.type="hide-markers";var P=x,C=class e extends d{constructor(t){super(e.type),this.markers=t}};C.type="set-markers";var L=C,T=class e extends d{constructor(){super(e.type)}};T.type="show-markers";var z=T,A=class e extends d{constructor(t){super(e.type),this.markers=t}};A.type="render-markers-list";var V=A,D=i,N=class extends D.AbstractButton{constructor(e){super(e,{className:"psv-markers-button",icon:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity=".8" stroke="#fff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="M24 26a6 6 0 1 0 0-12 6 6 0 0 0 0 12Z"/><path d="M24 4A16 16 0 0 0 8 20c0 3.784.804 6.26 3 9l13 15 13-15c2.196-2.74 3-5.216 3-9A16 16 0 0 0 24 4Z"/></g></svg>\n',hoverScale:!0,collapsable:!0,tabbable:!0}),this.plugin=this.viewer.getPlugin("markers"),this.plugin&&(this.plugin.addEventListener(z.type,this),this.plugin.addEventListener(P.type,this),this.toggleActive(!0))}destroy(){this.plugin&&(this.plugin.removeEventListener(z.type,this),this.plugin.removeEventListener(P.type,this)),super.destroy()}isSupported(){return!!this.plugin}handleEvent(e){e instanceof z?this.toggleActive(!0):e instanceof P&&this.toggleActive(!1)}onClick(){this.plugin.toggleAllMarkers()}};N.id="markers";var H=i,O='<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity=".8" stroke="#fff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="M24.001 4a16 16 0 0 0-16 16c0 3.784.804 6.26 3 9l13 15 13-15c2.196-2.74 3-5.216 3-9a16 16 0 0 0-16-16ZM19.68 14.4h8.48M20.211 22.03h7.58M20.211 28.99h7.58"/></g></svg>\n',j="http://www.w3.org/2000/svg",R="psvMarker",I=i.utils.dasherize(R),U="marker",B="markersList",F={amount:2,duration:100,easing:"linear"},W=(e,t)=>`
<div class="psv-panel-menu psv-panel-menu--stripped">
 <h1 class="psv-panel-menu-title">${O} ${t}</h1>
 <ul class="psv-panel-menu-list">
   ${e.map(e=>`
   <li data-${I}="${e.id}" class="psv-panel-menu-item" tabindex="0">
     ${"image"===e.type?`<span class="psv-panel-menu-item-icon"><img src="${e.definition}"/></span>`:""}
     <span class="psv-panel-menu-item-label">${e.getListContent()}</span>
   </li>
   `).join("")}
 </ul>
</div>
`,q=class extends H.AbstractButton{constructor(e){super(e,{className:" psv-markers-list-button",icon:O,hoverScale:!0,collapsable:!0,tabbable:!0}),this.plugin=this.viewer.getPlugin("markers"),this.plugin&&(this.viewer.addEventListener(H.events.ShowPanelEvent.type,this),this.viewer.addEventListener(H.events.HidePanelEvent.type,this))}destroy(){this.viewer.removeEventListener(H.events.ShowPanelEvent.type,this),this.viewer.removeEventListener(H.events.HidePanelEvent.type,this),super.destroy()}isSupported(){return!!this.plugin}handleEvent(e){e instanceof H.events.ShowPanelEvent?this.toggleActive(e.panelId===B):e instanceof H.events.HidePanelEvent&&this.toggleActive(!1)}onClick(){this.plugin.toggleMarkersList()}};q.id="markersList";var G,K=i,Y=i,X=t,Z=i,Q=((G=Q||{}).image="image",G.html="html",G.element="element",G.imageLayer="imageLayer",G.videoLayer="videoLayer",G.polygon="polygon",G.polygonPixels="polygonPixels",G.polyline="polyline",G.polylinePixels="polylinePixels",G.square="square",G.rect="rect",G.circle="circle",G.ellipse="ellipse",G.path="path",G);function J(e,t=!1){let i=[];if(Object.keys(Q).forEach(t=>{e[t]&&i.push(t)}),0!==i.length||t){if(i.length>1)throw new Z.PSVError(`multiple marker content, either ${Object.keys(Q).join(", ")}`)}else throw new Z.PSVError(`missing marker content, either ${Object.keys(Q).join(", ")}`);return i[0]}var ee=i,et=i,ei=class{constructor(e,t,i){if(this.viewer=e,this.plugin=t,this.state={dynamicSize:!1,anchor:null,visible:!1,staticTooltip:!1,position:null,position2D:null,positions3D:null,size:null},!i.id)throw new et.PSVError("missing marker id");this.type=J(i),this.createElement(),this.update(i)}get id(){return this.config.id}get data(){return this.config.data}get domElement(){return null}get threeElement(){return null}get video(){return null}destroy(){this.hideTooltip()}is3d(){return!1}isNormal(){return!1}isPoly(){return!1}isSvg(){return!1}update(e){let t=J(e,!0);if(void 0!==t&&t!==this.type)throw new et.PSVError("cannot change marker type");et.utils.isExtendedPosition(e)&&(et.utils.logWarn('Use the "position" property to configure the position of a marker'),e.position=this.viewer.dataHelper.cleanPosition(e)),"width"in e&&"height"in e&&(et.utils.logWarn('Use the "size" property to configure the size of a marker'),e.size={width:e.width,height:e.height}),this.config=et.utils.deepmerge(this.config,e),"string"==typeof this.config.tooltip&&(this.config.tooltip={content:this.config.tooltip}),this.config.tooltip&&!this.config.tooltip.trigger&&(this.config.tooltip.trigger="hover"),et.utils.isNil(this.config.visible)&&(this.config.visible=!0),et.utils.isNil(this.config.zIndex)&&(this.config.zIndex=1),et.utils.isNil(this.config.opacity)&&(this.config.opacity=1),this.state.anchor=et.utils.parsePoint(this.config.anchor)}getListContent(){return this.config.listContent?this.config.listContent:this.config.tooltip?.content?this.config.tooltip.content:this.config.html?this.config.html:(0,this.id)}showTooltip(e,t){if(this.state.visible&&this.config.tooltip?.content&&this.state.position2D){let i={...this.config.tooltip,style:{pointerEvents:this.state.staticTooltip?"auto":"none"},data:this,top:0,left:0};if(this.isPoly()||this.is3d()){if(e||t){let s=et.utils.getPosition(this.viewer.container);i.top=t-s.y,i.left=e-s.x,i.box={width:20,height:20}}else i.top=this.state.position2D.y,i.left=this.state.position2D.x}else{let r=this.viewer.dataHelper.vector3ToViewerCoords(this.state.positions3D[0]),o=this.state.size.width,n=this.state.size.height;this.config.hoverScale&&!this.state.staticTooltip&&(o*=this.config.hoverScale.amount,n*=this.config.hoverScale.amount),i.top=r.y-n*this.state.anchor.y+n/2,i.left=r.x-o*this.state.anchor.x+o/2,i.box={width:o,height:n}}this.tooltip?this.tooltip.update(this.config.tooltip.content,i):this.tooltip=this.viewer.createTooltip(i)}}hideTooltip(){this.tooltip&&(this.tooltip.hide(),this.tooltip=null)}},es=class extends ei{get domElement(){return this.element}constructor(e,t,i){super(e,t,i)}destroy(){delete this.element[R],super.destroy()}update(e){super.update(e);let t=this.domElement;t.id=`psv-marker-${this.config.id}`,t.setAttribute("class","psv-marker"),this.state.visible&&t.classList.add("psv-marker--visible"),this.config.tooltip&&t.classList.add("psv-marker--has-tooltip"),this.config.content&&t.classList.add("psv-marker--has-content"),this.config.className&&ee.utils.addClasses(t,this.config.className),t.style.opacity=`${this.config.opacity}`,t.style.zIndex=`${30+this.config.zIndex}`,this.config.style&&Object.assign(t.style,this.config.style)}},er=class extends es{constructor(e,t,i){super(e,t,i)}createElement(){this.element[R]=this,this.domElement.addEventListener("transitionend",()=>{this.domElement.style.transition=""})}render({viewerPosition:e,zoomLevel:t,hoveringMarker:i}){this.__updateSize();let s=this.viewer.dataHelper.vector3ToViewerCoords(this.state.positions3D[0]);s.x-=this.state.size.width*this.state.anchor.x,s.y-=this.state.size.height*this.state.anchor.y;let r=this.state.positions3D[0].dot(this.viewer.state.direction)>0&&s.x+this.state.size.width>=0&&s.x-this.state.size.width<=this.viewer.state.size.width&&s.y+this.state.size.height>=0&&s.y-this.state.size.height<=this.viewer.state.size.height;return r?(this.domElement.style.translate=`${s.x}px ${s.y}px 0px`,this.applyScale({zoomLevel:t,viewerPosition:e,mouseover:this===i}),"element"===this.type&&this.config.element.updateMarker?.({marker:this,position:s,viewerPosition:e,zoomLevel:t,viewerSize:this.viewer.state.size}),s):null}update(e){super.update(e);let t=this.domElement;t.classList.add("psv-marker--normal"),this.config.scale&&Array.isArray(this.config.scale)&&(this.config.scale={zoom:this.config.scale}),"boolean"==typeof this.config.hoverScale?this.config.hoverScale=this.config.hoverScale?this.plugin.config.defaultHoverScale||F:null:"number"==typeof this.config.hoverScale?this.config.hoverScale={amount:this.config.hoverScale}:this.config.hoverScale||(this.config.hoverScale=this.plugin.config.defaultHoverScale),this.config.hoverScale&&(this.config.hoverScale={...F,...this.plugin.config.defaultHoverScale,...this.config.hoverScale})}__updateSize(){if(!this.state.dynamicSize)return;let e=this.domElement,t=!this.state.size;if(t&&e.classList.add("psv-marker--transparent"),this.isSvg()){let i=e.firstElementChild.getBoundingClientRect();this.state.size={width:i.width,height:i.height}}else this.isNormal()&&(this.state.size={width:e.offsetWidth,height:e.offsetHeight});t&&e.classList.remove("psv-marker--transparent"),this.isSvg()&&(e.style.width=this.state.size.width+"px",e.style.height=this.state.size.height+"px"),"element"!==this.type&&(this.state.dynamicSize=!1)}applyScale({zoomLevel:e,viewerPosition:t,mouseover:i}){null!==i&&this.config.hoverScale&&(this.domElement.style.transition=`scale ${this.config.hoverScale.duration}ms ${this.config.hoverScale.easing}`);let s=1;if("function"==typeof this.config.scale)s=this.config.scale(e,t);else if(this.config.scale){if(Array.isArray(this.config.scale.zoom)){let[r,o]=this.config.scale.zoom;s*=r+(o-r)*Y.CONSTANTS.EASINGS.inQuad(e/100)}if(Array.isArray(this.config.scale.yaw)){let[n,a]=this.config.scale.yaw,l=X.MathUtils.degToRad(this.viewer.state.hFov)/2,h=Math.abs(Y.utils.getShortestArc(this.state.position.yaw,t.yaw));s*=a+(n-a)*Y.CONSTANTS.EASINGS.outQuad(Math.max(0,(l-h)/l))}}i&&this.config.hoverScale&&(s*=this.config.hoverScale.amount),this.domElement.style.scale=`${s}`}},eo=i,en=class extends er{constructor(e,t,i){super(e,t,i)}isNormal(){return!0}createElement(){this.element=document.createElement("div"),super.createElement()}update(e){super.update(e);let t=this.domElement;if(!eo.utils.isExtendedPosition(this.config.position))throw new eo.PSVError("missing marker position");if(this.config.image&&!this.config.size)throw new eo.PSVError("missing marker size");switch(this.config.size?(this.state.dynamicSize=!1,this.state.size=this.config.size,t.style.width=this.config.size.width+"px",t.style.height=this.config.size.height+"px"):this.state.dynamicSize=!0,this.type){case"image":this.definition=this.config.image,t.style.backgroundImage=`url("${this.config.image}")`;break;case"html":this.definition=this.config.html,t.innerHTML=this.config.html;break;case"element":this.definition!==this.config.element&&(this.definition=this.config.element,t.childNodes.forEach(e=>e.remove()),t.appendChild(this.config.element),this.config.element.style.display="block")}t.style.transformOrigin=`${100*this.state.anchor.x}% ${100*this.state.anchor.y}%`,this.state.position=this.viewer.dataHelper.cleanPosition(this.config.position),this.state.positions3D=[this.viewer.dataHelper.sphericalCoordsToVector3(this.state.position)]}},ea=i,el=t,eh=t,ec=class extends eh.ShaderMaterial{constructor(e){super({transparent:!0,depthTest:!1,uniforms:{map:{value:e?.map},repeat:{value:new eh.Vector2(1,1)},offset:{value:new eh.Vector2(0,0)},alpha:{value:e?.alpha??1},keying:{value:!1},color:{value:new eh.Color(65280)},similarity:{value:.2},smoothness:{value:.2},spill:{value:.1}},vertexShader:"varying vec2 vUv;\nuniform vec2 repeat;\nuniform vec2 offset;\n\nvoid main() {\n    vUv = uv * repeat + offset;\n    gl_Position = projectionMatrix *  modelViewMatrix * vec4( position, 1.0 );\n}\n",fragmentShader:"// https://www.8thwall.com/playground/chromakey-threejs\n\nuniform sampler2D map;\nuniform float alpha;\nuniform bool keying;\nuniform vec3 color;\nuniform float similarity;\nuniform float smoothness;\nuniform float spill;\n\nvarying vec2 vUv;\n\nvec2 RGBtoUV(vec3 rgb) {\n    return vec2(\n        rgb.r * -0.169 + rgb.g * -0.331 + rgb.b *  0.5    + 0.5,\n        rgb.r *  0.5   + rgb.g * -0.419 + rgb.b * -0.081  + 0.5\n    );\n}\n\nvoid main(void) {\n    gl_FragColor = texture2D(map, vUv);\n\n    if (keying) {\n        float chromaDist = distance(RGBtoUV(gl_FragColor.rgb), RGBtoUV(color));\n\n        float baseMask = chromaDist - similarity;\n        float fullMask = pow(clamp(baseMask / smoothness, 0., 1.), 1.5);\n        gl_FragColor.a *= fullMask * alpha;\n\n        float spillVal = pow(clamp(baseMask / spill, 0., 1.), 1.5);\n        float desat = clamp(gl_FragColor.r * 0.2126 + gl_FragColor.g * 0.7152 + gl_FragColor.b * 0.0722, 0., 1.);\n        gl_FragColor.rgb = mix(vec3(desat, desat, desat), gl_FragColor.rgb, spillVal);\n    } else {\n        gl_FragColor.a *= alpha;\n    }\n}\n"}),this.chromaKey=e?.chromaKey}get map(){return this.uniforms.map.value}set map(e){this.uniforms.map.value=e}set alpha(e){this.uniforms.alpha.value=e}get offset(){return this.uniforms.offset.value}get repeat(){return this.uniforms.repeat.value}set chromaKey(e){this.uniforms.keying.value=e?.enabled===!0,e?.enabled&&("object"==typeof e.color&&"r"in e.color?this.uniforms.color.value.set(e.color.r/255,e.color.g/255,e.color.b/255):this.uniforms.color.value.set(e.color??65280),this.uniforms.similarity.value=e.similarity??.2,this.uniforms.smoothness.value=e.smoothness??.2)}},ep=i,ed=t;function eg(e,t,i){let[s,r]=e,[o,n]=t,a=ep.utils.greatArcDistance(e,t),l=Math.sin((1-i)*a)/Math.sin(a),h=Math.sin(i*a)/Math.sin(a),c=l*Math.cos(r)*Math.cos(s)+h*Math.cos(n)*Math.cos(o),p=l*Math.cos(r)*Math.sin(s)+h*Math.cos(n)*Math.sin(o);return[Math.atan2(p,c),Math.atan2(l*Math.sin(r)+h*Math.sin(n),Math.sqrt(c*c+p*p))]}function ev(e){let t=[e[0]],i=0;for(let s=1;s<e.length;s++){let r=e[s-1][0]-e[s][0];r>Math.PI?i+=1:r<-Math.PI&&(i-=1),t.push([e[s][0]+2*i*Math.PI,e[s][1]])}return t}function eu(e){let t=ev(e),i=t.reduce((e,t)=>[e[0]+t[0],e[1]+t[1]]);return[ep.utils.parseAngle(i[0]/e.length),i[1]/e.length]}var em=new ed.Vector3,ef=new ed.Vector3,ey=new ed.Vector3,ew=new ed.Vector3,ek=new ed.Vector3,e$=new ed.Vector3,eE=class extends ei{get threeElement(){return this.element}get video(){return"videoLayer"===this.type?this.threeElement.material.map.image:null}constructor(e,t,i){super(e,t,i)}is3d(){return!0}createElement(){let e=new ec({alpha:0}),t=new el.PlaneGeometry(1,1),i=new el.Mesh(t,e);i.userData={[R]:this};let s=new el.Group().add(i);Object.defineProperty(s,"visible",{enumerable:!0,get:function(){return this.children[0].userData[R].state.visible},set:function(e){this.children[0].userData[R].state.visible=e}}),this.element=i,"videoLayer"===this.type&&this.viewer.needsContinuousUpdate(!0)}destroy(){delete this.threeElement.userData[R],"videoLayer"===this.type&&(this.video.pause(),this.viewer.needsContinuousUpdate(!1)),super.destroy()}render(){return this.viewer.renderer.isObjectVisible(this.threeElement)?this.viewer.dataHelper.sphericalCoordsToViewerCoords(this.state.position):null}update(e){super.update(e);let t=this.threeElement,i=t.parent,s=t.material;if(this.state.dynamicSize=!1,ea.utils.isExtendedPosition(this.config.position)){if(!this.config.size)throw new ea.PSVError("missing marker size");switch(this.state.position=this.viewer.dataHelper.cleanPosition(this.config.position),this.state.size=this.config.size,t.position.set(.5-this.state.anchor.x,this.state.anchor.y-.5,0),this.viewer.dataHelper.sphericalCoordsToVector3(this.state.position,i.position),i.lookAt(0,i.position.y,0),this.config.orientation){case"horizontal":i.rotateX(this.state.position.pitch<0?-Math.PI/2:Math.PI/2);break;case"vertical-left":i.rotateY(-(.4*Math.PI));break;case"vertical-right":i.rotateY(.4*Math.PI)}i.scale.set(this.config.size.width/100,this.config.size.height/100,1);let r=t.geometry.getAttribute("position");this.state.positions3D=[0,1,3,2].map(e=>{let i=new el.Vector3;return i.fromBufferAttribute(r,e),t.localToWorld(i)})}else{if(this.config.position?.length!==4)throw new ea.PSVError("missing marker position");let o=this.config.position.map(e=>this.viewer.dataHelper.cleanPosition(e)),n=o.map(e=>this.viewer.dataHelper.sphericalCoordsToVector3(e)),a=eu(o.map(({yaw:e,pitch:t})=>[e,t]));this.state.position={yaw:a[0],pitch:a[1]},this.state.positions3D=n;let l=t.geometry.getAttribute("position");[n[0],n[1],n[3],n[2]].forEach((e,t)=>{l.setX(t,e.x),l.setY(t,e.y),l.setZ(t,e.z)}),l.needsUpdate=!0,this.__setTextureWrap(s)}switch(this.type){case"videoLayer":if(this.definition!==this.config.videoLayer){s.map?.dispose();let h=function e({src:t,withCredentials:i,muted:s,autoplay:r}){let o=document.createElement("video");return o.crossOrigin=i?"use-credentials":"anonymous",o.loop=!0,o.playsInline=!0,o.autoplay=r,o.muted=s,o.preload="metadata",o.src=t,o}({src:this.config.videoLayer,withCredentials:this.viewer.config.withCredentials,muted:!0,autoplay:!0}),c=new el.VideoTexture(h);s.map=c,s.alpha=0,h.addEventListener("loadedmetadata",()=>{s.alpha=this.config.opacity,ea.utils.isExtendedPosition(this.config.position)||(t.material.userData[R]={width:h.videoWidth,height:h.videoHeight},this.__setTextureWrap(s))},{once:!0}),h.play(),this.definition=this.config.videoLayer}break;case"imageLayer":if(this.definition!==this.config.imageLayer){s.map?.dispose();let p=new el.Texture;s.map=p,s.alpha=0,this.viewer.textureLoader.loadImage(this.config.imageLayer).then(e=>{ea.utils.isExtendedPosition(this.config.position)||(t.material.userData[R]={width:e.width,height:e.height},this.__setTextureWrap(s)),p.image=e,p.anisotropy=4,p.needsUpdate=!0,s.alpha=this.config.opacity,this.viewer.needsUpdate()}),this.definition=this.config.imageLayer}}s.chromaKey=this.config.chromaKey,t.renderOrder=1e3+this.config.zIndex,t.geometry.boundingBox=null}__setTextureWrap(e){let t=e.userData[R];if(!t||!t.height||!t.width){e.repeat.set(1,1),e.offset.set(0,0);return}let i=this.config.position.map(e=>this.viewer.dataHelper.cleanPosition(e)),s=ea.utils.greatArcDistance([i[0].yaw,i[0].pitch],[i[1].yaw,i[1].pitch]),r=ea.utils.greatArcDistance([i[3].yaw,i[3].pitch],[i[2].yaw,i[2].pitch]),o=ea.utils.greatArcDistance([i[1].yaw,i[1].pitch],[i[2].yaw,i[2].pitch]),n=ea.utils.greatArcDistance([i[0].yaw,i[0].pitch],[i[3].yaw,i[3].pitch]),a=(s+r)/(o+n),l=t.width/t.height,h=0,c=0;a<l?h=l-a:c=1/l-1/a,e.repeat.set(1-h,1-c),e.offset.set(h/2,c/2)}},e_=i,eb=class extends es{constructor(e,t,i){super(e,t,i)}createElement(){this.element=document.createElementNS(j,this.isPolygon?"polygon":"polyline"),this.element[R]=this}isPoly(){return!0}get isPixels(){return"polygonPixels"===this.type||"polylinePixels"===this.type}get isPolygon(){return"polygon"===this.type||"polygonPixels"===this.type}get isPolyline(){return"polyline"===this.type||"polylinePixels"===this.type}render(){let e=this.__getPolyPositions(),t=e.length>(this.isPolygon?2:1);if(!t)return null;{let i=this.viewer.dataHelper.sphericalCoordsToViewerCoords(this.state.position),s=e.map(e=>e.x-i.x+","+(e.y-i.y)).join(" ");return this.domElement.setAttributeNS(null,"points",s),this.domElement.setAttributeNS(null,"transform",`translate(${i.x} ${i.y})`),i}}update(e){super.update(e);let t=this.domElement;t.classList.add("psv-marker--poly"),this.state.dynamicSize=!0,this.config.svgStyle?(Object.entries(this.config.svgStyle).forEach(([e,i])=>{t.setAttributeNS(null,e_.utils.dasherize(e),i)}),this.isPolyline&&!this.config.svgStyle.fill&&t.setAttributeNS(null,"fill","none")):this.isPolygon?t.setAttributeNS(null,"fill","rgba(0,0,0,0.5)"):this.isPolyline&&(t.setAttributeNS(null,"fill","none"),t.setAttributeNS(null,"stroke","rgb(0,0,0)"));let i=this.config[this.type];if(!Array.isArray(i[0]))for(let s=0;s<i.length;s++)i.splice(s,2,[i[s],i[s+1]]);this.isPixels?this.definition=i.map(e=>{let t=this.viewer.dataHelper.textureCoordsToSphericalCoords({textureX:e[0],textureY:e[1]});return[t.yaw,t.pitch]}):this.definition=i.map(e=>[e_.utils.parseAngle(e[0]),e_.utils.parseAngle(e[1],!0)]);let r=this.isPolygon?eu(this.definition):function e(t){let i=ev(t),s=0,r=[];for(let o=0;o<i.length-1;o++){let n=ep.utils.greatArcDistance(i[o],i[o+1])*ep.CONSTANTS.SPHERE_RADIUS;r.push(n),s+=n}let a=0;for(let l=0;l<i.length-1;l++){if(a+r[l]>s/2){let h=(s/2-a)/r[l];return eg(i[l],i[l+1],h)}a+=r[l]}return i[Math.round(i.length/2)]}(this.definition);this.state.position={yaw:r[0],pitch:r[1]},this.state.positions3D=this.definition.map(e=>this.viewer.dataHelper.sphericalCoordsToVector3({yaw:e[0],pitch:e[1]}))}__getPolyPositions(){let e=this.state.positions3D.length,t=this.state.positions3D.map(e=>({vector:e,visible:e.dot(this.viewer.state.direction)>0})),i=[];return t.forEach((s,r)=>{if(!s.visible){let o=[0===r?t[e-1]:t[r-1],r===e-1?t[0]:t[r+1]];o.forEach(e=>{e.visible&&i.push({visible:e.vector,invisible:s.vector,index:r})})}}),i.reverse().forEach(e=>{t.splice(e.index,0,{vector:function e(t,i,s){em.copy(s).normalize(),ef.crossVectors(t,i).normalize(),ey.crossVectors(ef,t).normalize(),ew.copy(t).multiplyScalar(-em.dot(ey)),ek.copy(ey).multiplyScalar(em.dot(t));let r=new ed.Vector3().addVectors(ew,ek).normalize();return e$.crossVectors(r,em),r.applyAxisAngle(e$,.01).multiplyScalar(ep.CONSTANTS.SPHERE_RADIUS)}(e.visible,e.invisible,this.viewer.state.direction),visible:!0})}),t.filter(e=>e.visible).map(e=>this.viewer.dataHelper.vector3ToViewerCoords(e.vector))}},eM=i,e1=class extends er{constructor(e,t,i){super(e,t,i)}isSvg(){return!0}createElement(){let e="square"===this.type?"rect":this.type,t=document.createElementNS(j,e);this.element=document.createElementNS(j,"svg"),this.element.appendChild(t),super.createElement()}update(e){super.update(e);let t=this.domElement.firstElementChild;if(!eM.utils.isExtendedPosition(this.config.position))throw new eM.PSVError("missing marker position");switch(this.state.dynamicSize=!0,this.type){case"square":this.definition={x:0,y:0,width:this.config.square,height:this.config.square};break;case"rect":Array.isArray(this.config.rect)?this.definition={x:0,y:0,width:this.config.rect[0],height:this.config.rect[1]}:this.definition={x:0,y:0,width:this.config.rect.width,height:this.config.rect.height};break;case"circle":this.definition={cx:this.config.circle,cy:this.config.circle,r:this.config.circle};break;case"ellipse":Array.isArray(this.config.ellipse)?this.definition={cx:this.config.ellipse[0],cy:this.config.ellipse[1],rx:this.config.ellipse[0],ry:this.config.ellipse[1]}:this.definition={cx:this.config.ellipse.rx,cy:this.config.ellipse.ry,rx:this.config.ellipse.rx,ry:this.config.ellipse.ry};break;case"path":this.definition={d:this.config.path}}Object.entries(this.definition).forEach(([e,i])=>{t.setAttributeNS(null,e,i)}),this.config.svgStyle?Object.entries(this.config.svgStyle).forEach(([e,i])=>{t.setAttributeNS(null,eM.utils.dasherize(e),i)}):t.setAttributeNS(null,"fill","rgba(0,0,0,0.5)"),this.domElement.style.transformOrigin=`${100*this.state.anchor.x}% ${100*this.state.anchor.y}%`,this.state.position=this.viewer.dataHelper.cleanPosition(this.config.position),this.state.positions3D=[this.viewer.dataHelper.sphericalCoordsToVector3(this.state.position)]}},eS=K.utils.getConfigParser({clickEventOnMarker:!1,gotoMarkerSpeed:"8rpm",markers:null,defaultHoverScale:null},{defaultHoverScale:e=>e?(!0===e&&(e=F),"number"==typeof e&&(e={amount:e}),{...F,...e}):null}),ex=class extends K.AbstractConfigurablePlugin{constructor(e,t){super(e,t),this.markers={},this.state={visible:!0,showAllTooltips:!1,currentMarker:null,hoveringMarker:null,needsReRender:!1},this.container=document.createElement("div"),this.container.className="psv-markers",this.viewer.container.appendChild(this.container),this.svgContainer=document.createElementNS(j,"svg"),this.svgContainer.setAttribute("class","psv-markers-svg-container"),this.container.appendChild(this.svgContainer),this.container.addEventListener("mouseenter",this,!0),this.container.addEventListener("mouseleave",this,!0),this.container.addEventListener("mousemove",this,!0),this.container.addEventListener("contextmenu",this)}init(){super.init(),K.utils.checkStylesheet(this.viewer.container,"markers-plugin"),this.viewer.addEventListener(K.events.ClickEvent.type,this),this.viewer.addEventListener(K.events.DoubleClickEvent.type,this),this.viewer.addEventListener(K.events.RenderEvent.type,this),this.viewer.addEventListener(K.events.ConfigChangedEvent.type,this),this.viewer.addEventListener(K.events.ObjectEnterEvent.type,this),this.viewer.addEventListener(K.events.ObjectHoverEvent.type,this),this.viewer.addEventListener(K.events.ObjectLeaveEvent.type,this),this.viewer.addEventListener(K.events.ReadyEvent.type,this,{once:!0})}destroy(){this.clearMarkers(!1),this.viewer.unobserveObjects(R),this.viewer.removeEventListener(K.events.ClickEvent.type,this),this.viewer.removeEventListener(K.events.DoubleClickEvent.type,this),this.viewer.removeEventListener(K.events.RenderEvent.type,this),this.viewer.removeEventListener(K.events.ObjectEnterEvent.type,this),this.viewer.removeEventListener(K.events.ObjectHoverEvent.type,this),this.viewer.removeEventListener(K.events.ObjectLeaveEvent.type,this),this.viewer.removeEventListener(K.events.ReadyEvent.type,this),this.viewer.container.removeChild(this.container),super.destroy()}handleEvent(e){switch(e.type){case K.events.ReadyEvent.type:this.config.markers&&(this.setMarkers(this.config.markers),delete this.config.markers);break;case K.events.RenderEvent.type:this.renderMarkers();break;case K.events.ClickEvent.type:this.__onClick(e,!1);break;case K.events.DoubleClickEvent.type:this.__onClick(e,!0);break;case K.events.ObjectEnterEvent.type:case K.events.ObjectLeaveEvent.type:case K.events.ObjectHoverEvent.type:if(e.userDataKey===R){let t=e.originalEvent,i=e.object.userData[R];switch(e.type){case K.events.ObjectEnterEvent.type:i.config.style?.cursor?this.viewer.setCursor(i.config.style.cursor):(i.config.tooltip||i.config.content)&&this.viewer.setCursor("pointer"),this.__onEnterMarker(t,i);break;case K.events.ObjectLeaveEvent.type:this.viewer.setCursor(null),this.__onLeaveMarker(i);break;case K.events.ObjectHoverEvent.type:this.__onHoverMarker(t,i)}}break;case"mouseenter":this.__onEnterMarker(e,this.__getTargetMarker(e.target));break;case"mouseleave":this.__onLeaveMarker(this.__getTargetMarker(e.target));break;case"mousemove":this.__onHoverMarker(e,this.__getTargetMarker(e.target,!0));break;case"contextmenu":e.preventDefault()}}toggleAllMarkers(){this.state.visible?this.hideAllMarkers():this.showAllMarkers()}showAllMarkers(){this.state.visible=!0,this.renderMarkers(),this.dispatchEvent(new z)}hideAllMarkers(){this.state.visible=!1,this.renderMarkers(),this.dispatchEvent(new P)}toggleAllTooltips(){this.state.showAllTooltips?this.hideAllTooltips():this.showAllTooltips()}showAllTooltips(){this.state.showAllTooltips=!0,Object.values(this.markers).forEach(e=>{e.state.staticTooltip=!0,e.showTooltip()})}hideAllTooltips(){this.state.showAllTooltips=!1,Object.values(this.markers).forEach(e=>{e.state.staticTooltip=!1,e.hideTooltip()})}getNbMarkers(){return Object.keys(this.markers).length}getMarkers(){return Object.values(this.markers)}addMarker(e,t=!0){if(this.markers[e.id])throw new K.PSVError(`marker "${e.id}" already exists`);let i=new(function e(t){let i=J(t,!1);switch(i){case"image":case"html":case"element":return en;case"imageLayer":case"videoLayer":return eE;case"polygon":case"polyline":case"polygonPixels":case"polylinePixels":return eb;case"square":case"rect":case"circle":case"ellipse":case"path":return e1;default:throw new K.PSVError("invalid marker type")}}(e))(this.viewer,this,e);i.isPoly()?this.svgContainer.appendChild(i.domElement):i.is3d()?this.viewer.renderer.addObject(i.threeElement.parent):this.container.appendChild(i.domElement),this.markers[i.id]=i,this.state.showAllTooltips&&(i.state.staticTooltip=!0),t&&this.__afterChangerMarkers()}getMarker(e){let t="object"==typeof e?e.id:e;if(!this.markers[t])throw new K.PSVError(`cannot find marker "${t}"`);return this.markers[t]}getCurrentMarker(){return this.state.currentMarker}updateMarker(e,t=!0){let i=this.getMarker(e.id);i.update(e),t&&(this.__afterChangerMarkers(),(i===this.state.hoveringMarker&&i.config.tooltip?.trigger==="hover"||i.state.staticTooltip)&&i.showTooltip())}removeMarker(e,t=!0){let i=this.getMarker(e);i.isPoly()?this.svgContainer.removeChild(i.domElement):i.is3d()?this.viewer.renderer.removeObject(i.threeElement.parent):this.container.removeChild(i.domElement),this.state.hoveringMarker===i&&(this.state.hoveringMarker=null),this.state.currentMarker===i&&(this.state.currentMarker=null),i.destroy(),delete this.markers[i.id],t&&this.__afterChangerMarkers()}removeMarkers(e,t=!0){e.forEach(e=>this.removeMarker(e,!1)),t&&this.__afterChangerMarkers()}setMarkers(e,t=!0){this.clearMarkers(!1),e?.forEach(e=>{this.addMarker(e,!1)}),t&&this.__afterChangerMarkers()}clearMarkers(e=!0){Object.keys(this.markers).forEach(e=>{this.removeMarker(e,!1)}),e&&this.__afterChangerMarkers()}gotoMarker(e,t=this.config.gotoMarkerSpeed){let i=this.getMarker(e);return t?this.viewer.animate({...i.state.position,zoom:i.config.zoomLvl,speed:t}).then(()=>{this.dispatchEvent(new m(i))}):(this.viewer.rotate(i.state.position),K.utils.isNil(i.config.zoomLvl)||this.viewer.zoom(i.config.zoomLvl),this.dispatchEvent(new m(i)),Promise.resolve())}hideMarker(e){this.toggleMarker(e,!1)}showMarker(e){this.toggleMarker(e,!0)}showMarkerTooltip(e){let t=this.getMarker(e);t.state.staticTooltip=!0,t.showTooltip()}hideMarkerTooltip(e){let t=this.getMarker(e);t.state.staticTooltip=!1,t.hideTooltip()}toggleMarker(e,t){let i=this.getMarker(e);i.config.visible=K.utils.isNil(t)?!i.config.visible:t,this.renderMarkers()}showMarkerPanel(e){let t=this.getMarker(e);t.config.content?this.viewer.panel.show({id:U,content:t.config.content}):this.hideMarkerPanel()}hideMarkerPanel(){this.viewer.panel.hide(U)}toggleMarkersList(){this.viewer.panel.isVisible(B)?this.hideMarkersList():this.showMarkersList()}showMarkersList(){let e=[];this.state.visible&&Object.values(this.markers).forEach(t=>{t.config.visible&&!t.config.hideList&&e.push(t)});let t=new V(e);this.dispatchEvent(t),e=t.markers,this.viewer.panel.show({id:B,content:W(e,this.viewer.config.lang[N.id]),noMargin:!0,clickHandler:e=>{let t=K.utils.getClosest(e,"li"),i=t?t.dataset[R]:void 0;if(i){let s=this.getMarker(i);this.dispatchEvent(new b(s)),this.gotoMarker(s.id),this.hideMarkersList()}}})}hideMarkersList(){this.viewer.panel.hide(B)}renderMarkers(){if(this.state.needsReRender){this.state.needsReRender=!1;return}let e=this.viewer.getZoomLevel(),t=this.viewer.getPosition(),i=this.state.hoveringMarker;Object.values(this.markers).forEach(s=>{let r=this.state.visible&&s.config.visible,o=!1,n=null;r&&(r=!!(n=s.render({viewerPosition:t,zoomLevel:e,hoveringMarker:i}))),o=s.state.visible!==r,s.state.visible=r,s.state.position2D=n,s.is3d()||K.utils.toggleClass(s.domElement,"psv-marker--visible",r),r?s.state.staticTooltip?s.showTooltip():s!==this.state.hoveringMarker&&s.hideTooltip():s.hideTooltip(),o&&(this.dispatchEvent(new v(s,r)),s.is3d()&&(this.state.needsReRender=!0))}),this.state.needsReRender&&this.viewer.needsUpdate()}__getTargetMarker(e,t=!1){if(e instanceof Node){let i=t?K.utils.getClosest(e,".psv-marker"):e;return i?i[R]:void 0}return Array.isArray(e)?e.map(e=>e.userData[R]).filter(e=>!!e).sort((e,t)=>t.config.zIndex-e.config.zIndex)[0]:null}__onEnterMarker(e,t){t&&(this.state.hoveringMarker=t,this.dispatchEvent(new k(t)),t instanceof er&&t.applyScale({zoomLevel:this.viewer.getZoomLevel(),viewerPosition:this.viewer.getPosition(),mouseover:!0}),t.state.staticTooltip||t.config.tooltip?.trigger!=="hover"||t.showTooltip(e.clientX,e.clientY))}__onLeaveMarker(e){e&&(this.dispatchEvent(new y(e)),e instanceof er&&e.applyScale({zoomLevel:this.viewer.getZoomLevel(),viewerPosition:this.viewer.getPosition(),mouseover:!1}),this.state.hoveringMarker=null,e.state.staticTooltip||e.config.tooltip?.trigger!=="hover"?e.state.staticTooltip&&e.showTooltip():e.hideTooltip())}__onHoverMarker(e,t){t&&(t.isPoly()||t.is3d())&&t.config.tooltip?.trigger==="hover"&&t.showTooltip(e.clientX,e.clientY)}__onClick(e,t){let i=this.__getTargetMarker(e.data.objects),s=this.__getTargetMarker(e.data.target,!0),r=s||i;this.state.currentMarker&&this.state.currentMarker!==r&&(this.dispatchEvent(new S(this.state.currentMarker)),this.viewer.panel.hide(U),this.state.showAllTooltips||this.state.currentMarker.config.tooltip?.trigger!=="click"||this.hideMarkerTooltip(this.state.currentMarker.id),this.state.currentMarker=null),r&&(this.state.currentMarker=r,this.dispatchEvent(new E(r,t,e.data.rightclick)),this.config.clickEventOnMarker?e.data.marker=r:e.stopImmediatePropagation(),this.markers[r.id]&&(r.config.tooltip?.trigger==="click"?r.tooltip?this.hideMarkerTooltip(r.id):this.showMarkerTooltip(r.id):this.showMarkerPanel(r.id)))}__afterChangerMarkers(){this.__refreshUi(),this.__checkObjectsObserver(),this.viewer.needsUpdate(),this.dispatchEvent(new L(this.getMarkers()))}__refreshUi(){let e=Object.values(this.markers).filter(e=>!e.config.hideList).length;0===e?(this.viewer.panel.isVisible(B)||this.viewer.panel.isVisible(U))&&this.viewer.panel.hide():this.viewer.panel.isVisible(B)?this.showMarkersList():this.viewer.panel.isVisible(U)&&(this.state.currentMarker?this.showMarkerPanel(this.state.currentMarker.id):this.viewer.panel.hide()),this.viewer.navbar.getButton(N.id,!1)?.toggle(e>0),this.viewer.navbar.getButton(q.id,!1)?.toggle(e>0)}__checkObjectsObserver(){let e=Object.values(this.markers).some(e=>e.is3d());e?this.viewer.observeObjects(R):this.viewer.unobserveObjects(R)}};ex.id="markers",ex.VERSION="5.6.0",ex.configParser=eS,ex.readonlyOptions=["markers"],h.DEFAULTS.lang[N.id]="Markers",h.DEFAULTS.lang[q.id]="Markers list",(0,h.registerButton)(N,"caption:left"),(0,h.registerButton)(q,"caption:left"),((e,t,i,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of o(t))n.call(e,l)||void 0===l||s(e,l,{get:()=>t[l],enumerable:!(a=r(t,l))||a.enumerable});return e})(s(e,"__esModule",{value:!0}),l)});