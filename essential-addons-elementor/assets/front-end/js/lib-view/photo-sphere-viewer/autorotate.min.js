!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("three"),require("@photo-sphere-viewer/core")):"function"==typeof define&&define.amd?define(["exports","three","@photo-sphere-viewer/core"],e):e(((t="undefined"!=typeof globalThis?globalThis:t||self).PhotoSphereViewer=t.PhotoSphereViewer||{},t.PhotoSphereViewer.AutorotatePlugin={}),t.THREE,t.PhotoSphereViewer)}(this,function(t,e,i){"use strict";var s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,a=(t,e)=>{for(var i in e)s(t,i,{get:e[i],enumerable:!0})},l={};a(l,{AutorotatePlugin:()=>y,events:()=>_});var h=i,p=i,_={};a(_,{AutorotateEvent:()=>$});var u=i,d=class t extends u.TypedEvent{constructor(e){super(t.type),this.autorotateEnabled=e}};d.type="autorotate";var $=d,c=class extends p.AbstractButton{constructor(t){super(t,{className:"psv-autorotate-button",hoverScale:!0,collapsable:!0,tabbable:!0,icon:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity=".8" stroke="#fff"><path d="M19.92 17.04v13.92ZM32.1 24l-12.18 6.96Zm0 0-12.18-6.96Z" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"/><path d="M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Z" stroke-width="2.5"/></g></svg>\n',iconActive:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.96 17.04v13.92m9.943-13.92v13.92" stroke="#fff" stroke-width="2.2" stroke-linecap="round"/><path d="M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Z" stroke="#fff" stroke-width="2.5"/></svg>\n'}),this.plugin=this.viewer.getPlugin("autorotate"),this.plugin?.addEventListener($.type,this)}destroy(){this.plugin?.removeEventListener($.type,this),super.destroy()}isSupported(){return!!this.plugin}handleEvent(t){t instanceof $&&this.toggleActive(t.autorotateEnabled)}onClick(){this.plugin.isEnabled()&&(this.plugin.config.autostartOnIdle=!1),this.plugin.toggle()}};c.id="autorotate";var v=i,g=e,m=v.utils.getConfigParser({autostartDelay:2e3,autostartOnIdle:!0,autorotateSpeed:v.utils.parseSpeed("2rpm"),autorotatePitch:null,autorotateZoomLvl:null,keypoints:null,startFromClosest:!0},{autostartOnIdle:(t,{rawConfig:e})=>t&&v.utils.isNil(e.autostartDelay)?(v.utils.logWarn("autostartOnIdle requires a non null autostartDelay"),!1):t,autorotateSpeed:t=>v.utils.parseSpeed(t),autorotatePitch:t=>v.utils.isNil(t)?null:v.utils.parseAngle(t,!0),autorotateZoomLvl:t=>v.utils.isNil(t)?null:g.MathUtils.clamp(t,0,100)});function f(t){return[t.yaw,t.pitch]}var y=class extends v.AbstractConfigurablePlugin{constructor(t,e){super(t,e),this.state={initialStart:!0,enabled:!1,idx:-1,curve:[],startStep:null,endStep:null,startTime:null,stepDuration:null,remainingPause:null,lastTime:null,tooltip:null},this.state.initialStart=!v.utils.isNil(this.config.autostartDelay)}init(){super.init(),this.video=this.viewer.getPlugin("video"),this.markers=this.viewer.getPlugin("markers"),this.config.keypoints&&(this.setKeypoints(this.config.keypoints),delete this.config.keypoints),this.viewer.addEventListener(v.events.StopAllEvent.type,this),this.viewer.addEventListener(v.events.BeforeRenderEvent.type,this),this.video||this.viewer.addEventListener(v.events.KeypressEvent.type,this)}destroy(){this.viewer.removeEventListener(v.events.StopAllEvent.type,this),this.viewer.removeEventListener(v.events.BeforeRenderEvent.type,this),this.viewer.removeEventListener(v.events.KeypressEvent.type,this),delete this.video,delete this.markers,delete this.keypoints,super.destroy()}handleEvent(t){switch(t.type){case v.events.StopAllEvent.type:this.stop();break;case v.events.BeforeRenderEvent.type:this.__beforeRender(t.timestamp);break;case v.events.KeypressEvent.type:t.key===v.CONSTANTS.KEY_CODES.Space&&this.viewer.state.keyboardEnabled&&(this.toggle(),t.preventDefault())}}setKeypoints(t){if(t){if(t.length<2)throw new v.PSVError("At least two points are required");this.keypoints=t.map((t,e)=>{let i={position:null,markerId:null,pause:0,tooltip:null},s;if("string"==typeof t?i.markerId=t:v.utils.isExtendedPosition(t)?s=t:(i.markerId=t.markerId,i.pause=t.pause,s=t.position,t.tooltip&&"object"==typeof t.tooltip?i.tooltip=t.tooltip:"string"==typeof t.tooltip&&(i.tooltip={content:t.tooltip})),i.markerId){if(!this.markers)throw new v.PSVError(`Keypoint #${e} references a marker but the markers plugin is not loaded`);let o=this.markers.getMarker(i.markerId);i.position=f(o.state.position)}else if(s)i.position=f(this.viewer.dataHelper.cleanPosition(s));else throw new v.PSVError(`Keypoint #${e} is missing marker or position`);return i})}else this.keypoints=null;this.isEnabled()&&(this.stop(),this.start())}isEnabled(){return this.state.enabled}start(){!this.isEnabled()&&(this.viewer.stopAll(),this.keypoints?this.config.startFromClosest&&this.__shiftKeypoints():this.__animate(),this.state.initialStart=!1,this.state.enabled=!0,this.dispatchEvent(new $(!0)))}stop(){this.isEnabled()&&(this.__hideTooltip(),this.__reset(),this.viewer.stopAnimation(),this.viewer.dynamics.position.stop(),this.viewer.dynamics.zoom.stop(),this.state.enabled=!1,this.dispatchEvent(new $(!1)))}toggle(){this.isEnabled()?this.stop():this.start()}reverse(){this.isEnabled()&&!this.keypoints&&(this.config.autorotateSpeed=-this.config.autorotateSpeed,this.__animate())}__animate(){let t;(t=v.utils.isNil(this.config.autorotateZoomLvl)?Promise.resolve(!0):this.viewer.animate({zoom:this.config.autorotateZoomLvl,speed:`${2*this.viewer.config.zoomSpeed}rpm`})).then(t=>{t&&(this.viewer.dynamics.position.roll({yaw:this.config.autorotateSpeed<0},Math.abs(this.config.autorotateSpeed/this.viewer.config.moveSpeed)),this.viewer.dynamics.position.goto({pitch:this.config.autorotatePitch??this.viewer.config.defaultPitch},Math.abs(this.config.autorotateSpeed/this.viewer.config.moveSpeed)))})}__reset(){this.state.idx=-1,this.state.curve=[],this.state.startStep=null,this.state.endStep=null,this.state.startTime=null,this.state.stepDuration=null,this.state.remainingPause=null,this.state.lastTime=null,this.state.tooltip=null}__beforeRender(t){(this.state.initialStart||this.config.autostartOnIdle)&&this.viewer.state.idleTime>0&&t-this.viewer.state.idleTime>this.config.autostartDelay&&this.start(),this.isEnabled()&&this.keypoints&&(this.state.startTime||(this.state.endStep=f(this.viewer.getPosition()),this.__nextStep(),this.state.startTime=t,this.state.lastTime=t),this.__nextFrame(t))}__shiftKeypoints(){let t=f(this.viewer.getPosition()),e=this.__findMinIndex(this.keypoints,e=>v.utils.greatArcDistance(e.position,t));this.keypoints.push(...this.keypoints.splice(0,e))}__incrementIdx(){this.state.idx++,this.state.idx===this.keypoints.length&&(this.state.idx=0)}__showTooltip(){let t=this.keypoints[this.state.idx];if(t.tooltip){let e=this.viewer.dataHelper.vector3ToViewerCoords(this.viewer.state.direction);this.state.tooltip=this.viewer.createTooltip({content:t.tooltip.content,position:t.tooltip.position,top:e.y,left:e.x})}else if(t.markerId){let i=this.markers.getMarker(t.markerId);i.showTooltip(),this.state.tooltip=i.tooltip}}__hideTooltip(){if(this.state.tooltip){let t=this.keypoints[this.state.idx];if(t.tooltip)this.state.tooltip.hide();else if(t.markerId){let e=this.markers.getMarker(t.markerId);e.hideTooltip()}this.state.tooltip=null}}__nextPoint(){let t=[];if(-1===this.state.idx){let e=f(this.viewer.getPosition());t.push(e,e,this.keypoints[0].position,this.keypoints[1].position)}else for(let i=-1;i<3;i++){let s=this.state.idx+i<0?this.keypoints[this.keypoints.length-1]:this.keypoints[(this.state.idx+i)%this.keypoints.length];t.push(s.position)}let o=[new g.Vector2(t[0][0],t[0][1])],r=0;for(let n=1;n<=3;n++){let a=t[n-1][0]-t[n][0];a>Math.PI?r+=1:a<-Math.PI&&(r-=1),0!==r&&1===n&&(o[0].x-=2*r*Math.PI,r=0),o.push(new g.Vector2(t[n][0]+2*r*Math.PI,t[n][1]))}let l=new g.SplineCurve(o).getPoints(48).map(t=>[t.x,t.y]);this.state.curve=l.slice(17,33),-1!==this.state.idx?(this.state.remainingPause=this.keypoints[this.state.idx].pause,this.state.remainingPause?this.__showTooltip():this.__incrementIdx()):this.__incrementIdx()}__nextStep(){0===this.state.curve.length&&(this.__nextPoint(),this.state.endStep[0]=v.utils.parseAngle(this.state.endStep[0])),this.state.startStep=this.state.endStep,this.state.endStep=this.state.curve.shift();let t=v.utils.greatArcDistance(this.state.startStep,this.state.endStep);this.state.stepDuration=1e3*t/Math.abs(this.config.autorotateSpeed),0===t&&this.__nextStep()}__nextFrame(t){let e=t-this.state.lastTime;if(this.state.lastTime=t,this.state.remainingPause){if(this.state.remainingPause=Math.max(0,this.state.remainingPause-e),this.state.remainingPause>0)return;this.__hideTooltip(),this.__incrementIdx(),this.state.startTime=t}let i=(t-this.state.startTime)/this.state.stepDuration;i>=1&&(this.__nextStep(),i=0,this.state.startTime=t),this.viewer.rotate({yaw:this.state.startStep[0]+(this.state.endStep[0]-this.state.startStep[0])*i,pitch:this.state.startStep[1]+(this.state.endStep[1]-this.state.startStep[1])*i})}__findMinIndex(t,e){let i=0,s=Number.MAX_VALUE;return t.forEach((t,o)=>{let r=e(t);r<s&&(s=r,i=o)}),i}};y.id="autorotate",y.VERSION="5.6.0",y.configParser=m,y.readonlyOptions=["keypoints"],(0,h.registerButton)(c,"start"),h.DEFAULTS.lang[c.id]="Automatic rotation",((t,e,i,a)=>{if(e&&"object"==typeof e||"function"==typeof e)for(let l of r(e))n.call(t,l)||void 0===l||s(t,l,{get:()=>e[l],enumerable:!(a=o(e,l))||a.enumerable});return t})(s(t,"__esModule",{value:!0}),l)});