!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("three")):"function"==typeof define&&define.amd?define(["exports","three"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).PhotoSphereViewer={},e.THREE)}(this,function(e,t){"use strict";var i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,r=(e,t)=>{for(var s in t)i(e,s,{get:t[s],enumerable:!0})},a={};r(a,{AbstractAdapter:()=>tI,AbstractButton:()=>t4,AbstractComponent:()=>t5,AbstractConfigurablePlugin:()=>tq,AbstractPlugin:()=>tK,CONSTANTS:()=>l,DEFAULTS:()=>tJ,EquirectangularAdapter:()=>tz,PSVError:()=>es,SYSTEM:()=>tH,TypedEvent:()=>eO,VERSION:()=>iP,Viewer:()=>iI,events:()=>eP,registerButton:()=>ia,utils:()=>L});var h=t,l={};r(l,{ACTIONS:()=>b,ANIMATION_MIN_DURATION:()=>p,CAPTURE_EVENTS_CLASS:()=>y,CTRLZOOM_TIMEOUT:()=>w,DBLCLICK_DELAY:()=>v,DEFAULT_TRANSITION:()=>d,EASINGS:()=>C,ICONS:()=>T,IDS:()=>E,INERTIA_WINDOW:()=>g,KEY_CODES:()=>x,LONGTOUCH_DELAY:()=>m,MOVE_THRESHOLD:()=>u,SPHERE_RADIUS:()=>f,TWOFINGERSOVERLAY_DELAY:()=>$,VIEWER_DATA:()=>_});var c,d=1500,p=500,u=4,v=300,m=500,$=100,w=2e3,g=300,f=10,_="photoSphereViewer",y="psv--capture-event",b=((c=b||{}).ROTATE_UP="ROTATE_UP",c.ROTATE_DOWN="ROTATE_DOWN",c.ROTATE_RIGHT="ROTATE_RIGHT",c.ROTATE_LEFT="ROTATE_LEFT",c.ZOOM_IN="ZOOM_IN",c.ZOOM_OUT="ZOOM_OUT",c),E={MENU:"menu",TWO_FINGERS:"twoFingers",CTRL_ZOOM:"ctrlZoom",ERROR:"error",DESCRIPTION:"description"},x={Enter:"Enter",Control:"Control",Escape:"Escape",Space:" ",PageUp:"PageUp",PageDown:"PageDown",ArrowLeft:"ArrowLeft",ArrowUp:"ArrowUp",ArrowRight:"ArrowRight",ArrowDown:"ArrowDown",Delete:"Delete",Plus:"+",Minus:"-"},T={arrow:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path transform="rotate(0, 22, 22)" d="M40 24H8m0 0 14 14M8 24l14-14" stroke="#fff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" opacity=".8"/></svg>\n',close:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity=".8" stroke="#fff"><path d="m30.001 30-12-12m12 0-12 12" stroke-width="2.2" stroke-linecap="round"/><path d="M24.001 44c11.046 0 20-8.954 20-20s-8.954-20-20-20-20 8.954-20 20 8.954 20 20 20Z" stroke-width="2.5"/></g></svg>',download:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M24 30V6m0 24-8-8m8 8 8-8M4 34l1.242 4.97A4 4 0 0 0 9.122 42h29.756a4 4 0 0 0 3.88-3.03L44 34" stroke="#fff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" opacity=".8"/></svg>\n',fullscreenIn:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.6 15.8V7.6h8.2m24.6 8.2V7.6h-8.2M15.8 40.4H7.6v-8.2m24.6 8.2h8.2v-8.2" stroke="#fff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" opacity=".8"/></svg>\n',fullscreenOut:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity=".8" stroke="#fff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="M18.16 8.16v10h-10M29.68 39.68v-10h10M29.68 8.16v10h10M18.16 39.68v-10h-10"/></g></svg>\n',info:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity=".8" stroke="#fff"><path d="M24.001 44c11.046 0 20-8.954 20-20s-8.954-20-20-20-20 8.954-20 20 8.954 20 20 20Z" stroke-width="2.5"/><path d="M24.001 14h.02" stroke-width="2.2" stroke-linecap="round"/><path d="M20.481 22h3.52v10m-4 0h8" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"/></g></svg>\n',menu:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M24 12.82a4.705 4.705 0 1 1 0-9.41 4.705 4.705 0 0 1 0 9.41Zm11.18-4.705a4.705 4.705 0 1 1 9.41 0 4.705 4.705 0 0 1-9.41 0ZM8.116 12.82a4.705 4.705 0 1 1 0-9.41 4.705 4.705 0 0 1 0 9.41ZM24 28.705a4.705 4.705 0 1 1 0-9.41 4.705 4.705 0 0 1 0 9.41Zm15.885 0a4.705 4.705 0 1 1 0-9.41 4.705 4.705 0 0 1 0 9.41Zm-31.77 0a4.705 4.705 0 1 1 0-9.41 4.705 4.705 0 0 1 0 9.41ZM24 44.59a4.705 4.705 0 1 1 0-9.41 4.705 4.705 0 0 1 0 9.41Zm15.885 0a4.705 4.705 0 1 1 0-9.41 4.705 4.705 0 0 1 0 9.41Zm-31.77 0a4.705 4.705 0 1 1 0-9.41 4.705 4.705 0 0 1 0 9.41Z" stroke="#fff" stroke-width="2.5" opacity=".8"/></svg>\n',zoomIn:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m43 42-8.972-8.988M22 14v7m0 0v7m0-7h7m-7 0h-7m24 0a17 17 0 1 1-34 0 17 17 0 0 1 34 0Z" stroke="#fff" stroke-width="2.5" stroke-linecap="round" opacity=".8"/></svg>\n',zoomOut:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m42 42-8.972-8.988M26 21H16m22 0a17 17 0 1 1-34 0 17 17 0 0 1 34 0Z" stroke="#fff" stroke-width="2.5" stroke-linecap="round" opacity=".8"/></svg>\n'},C={linear:e=>e,inQuad:e=>e*e,outQuad:e=>e*(2-e),inOutQuad:e=>e<.5?2*e*e:-1+(4-2*e)*e,inCubic:e=>e*e*e,outCubic:e=>--e*e*e+1,inOutCubic:e=>e<.5?4*e*e*e:(e-1)*(2*e-2)*(2*e-2)+1,inQuart:e=>e*e*e*e,outQuart:e=>1- --e*e*e*e,inOutQuart:e=>e<.5?8*e*e*e*e:1-8*--e*e*e*e,inQuint:e=>e*e*e*e*e,outQuint:e=>1+--e*e*e*e*e,inOutQuint:e=>e<.5?16*e*e*e*e*e:1+16*--e*e*e*e*e,inSine:e=>1-Math.cos(e*(Math.PI/2)),outSine:e=>Math.sin(e*(Math.PI/2)),inOutSine:e=>.5-.5*Math.cos(Math.PI*e),inExpo:e=>Math.pow(2,10*(e-1)),outExpo:e=>1-Math.pow(2,-10*e),inOutExpo:e=>(e=2*e-1)<0?.5*Math.pow(2,10*e):1-.5*Math.pow(2,-10*e),inCirc:e=>1-Math.sqrt(1-e*e),outCirc:e=>Math.sqrt(1-(e-1)*(e-1)),inOutCirc:e=>(e*=2)<1?.5-.5*Math.sqrt(1-e*e):.5+.5*Math.sqrt(1-(e-=2)*e)},L={};function k(e,t){let i=e%t;return i<0&&(i+=t),i}function M(e){return e.reduce((e,t)=>e+t,0)}function I(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function P(e,t){return Math.atan2(t.y-e.y,t.x-e.x)}function O(e,t){return[0,2*Math.PI,-(2*Math.PI)].reduce((i,s)=>{let o=t-e+s;return Math.abs(o)<Math.abs(i)?o:i},1/0)}function S(e,t){return Math.acos(Math.cos(e.pitch)*Math.cos(t.pitch)*Math.cos(e.yaw-t.yaw)+Math.sin(e.pitch)*Math.sin(t.pitch))}function H([e,t],[i,s]){e-i>Math.PI?e-=2*Math.PI:e-i<-Math.PI&&(e+=2*Math.PI);let o=(i-e)*Math.cos((t+s)/2),n=s-t;return Math.sqrt(o*o+n*n)}function z(e){return"string"==typeof e?e.match(/^[a-z]/i)?document.getElementById(e):document.querySelector(e):e}function R(e,t,i){void 0===i?e.classList.toggle(t):i?e.classList.add(t):i||e.classList.remove(t)}function A(e,t){e.classList.add(...t.split(" "))}function D(e,t){e.classList.remove(...t.split(" "))}function N(e,t){let i=e;do{if(i===t)return!0;i=i.parentElement}while(i);return!1}function U(e,t){if(!e?.matches)return null;let i=e;do{if(i.matches(t))return i;i=i.parentElement}while(i);return null}function V(e){let t=0,i=0,s=e;for(;s;)t+=s.offsetLeft-s.scrollLeft+s.clientLeft,i+=s.offsetTop-s.scrollTop+s.clientTop,s=s.offsetParent;return{x:t,y:i}}function W(e,t){return window.getComputedStyle(e).getPropertyValue(t)}function F(e){if(e.touches.length<2)return null;let t={x:e.touches[0].clientX,y:e.touches[0].clientY},i={x:e.touches[1].clientX,y:e.touches[1].clientY};return{distance:I(t,i),angle:P(t,i),center:{x:(t.x+i.x)/2,y:(t.y+i.y)/2}}}function X(e){return(document.fullscreenElement||document.webkitFullscreenElement)===e}function Y(e){(e.requestFullscreen||e.webkitRequestFullscreen).call(e)}function j(){(document.exitFullscreen||document.webkitExitFullscreen).call(document)}function Z(e){return e.replace(/[A-Z](?:(?=[^A-Z])|[A-Z]*(?=[A-Z][^A-Z]|$))/g,(e,t)=>(t>0?"-":"")+e.toLowerCase())}function B(e,t){let i=!1;return function(...s){i||(i=!0,setTimeout(()=>{e.apply(this,s),i=!1},t))}}function G(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function K(e,t){let i=t;return function e(t,s){return Array.isArray(s)?(t&&Array.isArray(t)?t.length=0:t=[],s.forEach((i,s)=>{t[s]=e(null,i)})):"object"==typeof s?((!t||Array.isArray(t))&&(t={}),Object.keys(s).forEach(o=>{"object"==typeof s[o]&&s[o]&&G(s[o])?s[o]!==i&&(t[o]?e(t[o],s[o]):t[o]=e(null,s[o])):t[o]=s[o]})):t=s,t}(e,t)}function q(e){return K(null,e)}function Q(e){return!e||0===Object.keys(e).length&&e.constructor===Object}function J(e){return null==e}function ee(...e){for(let t of e)if(!J(t))return t;return null}function et(e){return"object"==typeof e&&null!==e}r(L,{Animation:()=>eC,Dynamic:()=>e3,MultiDynamic:()=>e2,PressHandler:()=>ek,Slider:()=>eI,SliderDirection:()=>eM,addClasses:()=>A,angle:()=>P,applyEulerInverse:()=>eE,checkStylesheet:()=>ex,checkVersion:()=>eT,cleanCssPosition:()=>ew,clone:()=>q,createTexture:()=>eb,cssPositionIsOrdered:()=>eg,dasherize:()=>Z,deepEqual:()=>function e(t,i){if(t===i)return!0;if(!(et(t)&&et(i))||Object.keys(t).length!==Object.keys(i).length)return!1;for(let s of Object.keys(t))if(!e(t[s],i[s]))return!1;return!0},deepmerge:()=>K,distance:()=>I,exitFullscreen:()=>j,firstNonNull:()=>ee,getAbortError:()=>er,getAngle:()=>S,getClosest:()=>U,getConfigParser:()=>e8,getElement:()=>z,getPosition:()=>V,getShortestArc:()=>O,getStyleProperty:()=>W,getTouchData:()=>F,getXMPValue:()=>ec,greatArcDistance:()=>H,hasParent:()=>N,invertResolvableBoolean:()=>en,isAbortError:()=>ea,isEmpty:()=>Q,isExtendedPosition:()=>el,isFullscreenEnabled:()=>X,isNil:()=>J,isPlainObject:()=>G,logWarn:()=>eh,parseAngle:()=>ey,parsePoint:()=>e$,parseSpeed:()=>ef,removeClasses:()=>D,requestFullscreen:()=>Y,resolveBoolean:()=>eo,speedToDuration:()=>e_,sum:()=>M,throttle:()=>B,toggleClass:()=>R,wrap:()=>k});var ei=t,es=class e extends Error{constructor(t){super(t),this.name="PSVError",Error.captureStackTrace?.(this,e)}};function eo(e,t){G(e)?(t(e.initial,!0),e.promise.then(e=>t(e,!1))):t(e,!0)}function en(e){return{initial:!e.initial,promise:e.promise.then(e=>!e)}}function er(){let e=Error("Loading was aborted.");return e.name="AbortError",e}function ea(e){return e?.name==="AbortError"}function eh(e){console.warn(`PhotoSphereViewer: ${e}`)}function el(e){return!!e&&[["textureX","textureY"],["yaw","pitch"]].some(([t,i])=>void 0!==e[t]&&void 0!==e[i])}function ec(e,t){let i=e.match("<GPano:"+t+">(.*)</GPano:"+t+">");if(null!==i){let s=parseInt(i[1],10);return isNaN(s)?null:s}if(null!==(i=e.match("GPano:"+t+'="(.*?)"'))){let o=parseInt(i[1],10);return isNaN(o)?null:o}return null}var ed={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},ep=["left","center","right"],eu=["top","center","bottom"],ev=[...ep,...eu],em="center";function e$(e){if(!e)return{x:.5,y:.5};if("object"==typeof e)return e;let t=e.toLocaleLowerCase().split(" ").slice(0,2);1===t.length&&(t=ed[t[0]]?[t[0],em]:[t[0],t[0]]);let i="left"!==t[1]&&"right"!==t[1]&&"top"!==t[0]&&"bottom"!==t[0];t=t.map(e=>ed[e]||e),i||t.reverse();let s=t.join(" ").match(/^([0-9.]+)% ([0-9.]+)%$/);return s?{x:parseFloat(s[1])/100,y:parseFloat(s[2])/100}:{x:.5,y:.5}}function ew(e,{allowCenter:t,cssOrder:i}={allowCenter:!0,cssOrder:!0}){return e?("string"==typeof e&&(e=e.split(" ")),1===e.length&&(e[0]===em?e=[em,em]:-1!==ep.indexOf(e[0])?e=[em,e[0]]:-1!==eu.indexOf(e[0])&&(e=[e[0],em])),2!==e.length||-1===ev.indexOf(e[0])||-1===ev.indexOf(e[1]))?(eh(`Unparsable position ${e}`),null):t||e[0]!==em||e[1]!==em?(i&&!eg(e)&&(e=[e[1],e[0]]),e[1]===em&&-1!==ep.indexOf(e[0])&&(e=[em,e[0]]),e[0]===em&&-1!==eu.indexOf(e[1])&&(e=[e[1],em]),e):(eh("Invalid position center center"),null):null}function eg(e){return -1!==eu.indexOf(e[0])&&-1!==ep.indexOf(e[1])}function ef(e){let t;if("string"==typeof e){let i=e.toString().trim(),s=parseFloat(i.replace(/^(-?[0-9]+(?:\.[0-9]*)?).*$/,"$1")),o=i.replace(/^-?[0-9]+(?:\.[0-9]*)?(.*)$/,"$1").trim();switch(o.match(/(pm|per minute)$/)&&(s/=60),o){case"dpm":case"degrees per minute":case"dps":case"degrees per second":t=ei.MathUtils.degToRad(s);break;case"rdpm":case"radians per minute":case"rdps":case"radians per second":t=s;break;case"rpm":case"revolutions per minute":case"rps":case"revolutions per second":t=s*Math.PI*2;break;default:throw new es(`Unknown speed unit "${o}"`)}}else t=e;return t}function e_(e,t){if("number"==typeof e)return Math.abs(e);{let i=ef(e);return t/Math.abs(i)*1e3}}function ey(e,t=!1,i=t){let s;if("string"==typeof e){let o=e.toLowerCase().trim().match(/^(-?[0-9]+(?:\.[0-9]*)?)(.*)$/);if(!o)throw new es(`Unknown angle "${e}"`);let n=parseFloat(o[1]),r=o[2];if(r)switch(r){case"deg":case"degs":s=ei.MathUtils.degToRad(n);break;case"rad":case"rads":s=n;break;default:throw new es(`Unknown angle unit "${r}"`)}else s=n}else if("number"!=typeof e||isNaN(e))throw new es(`Unknown angle "${e}"`);else s=e;return s=k(t?s+Math.PI:s,2*Math.PI),t?ei.MathUtils.clamp(s-Math.PI,-Math.PI/(i?2:1),Math.PI/(i?2:1)):s}function eb(e,t=!1){let i=new ei.Texture(e);return i.needsUpdate=!0,i.minFilter=t?ei.LinearMipmapLinearFilter:ei.LinearFilter,i.generateMipmaps=t,i.anisotropy=t?2:1,i}var e0=new ei.Quaternion;function eE(e,t){e0.setFromEuler(t).invert(),e.applyQuaternion(e0)}function e8(e,t){let i=function(i){if(!i)return q(e);let s=q({...e,...i}),o={};for(let[n,r]of Object.entries(s)){if(t&&n in t)r=t[n](r,{rawConfig:s,defValue:e[n]});else if(!(n in e)){eh(`Unknown option ${n}`);continue}o[n]=r}return o};return i.defaults=e,i.parsers=t||{},i}function ex(e,t){"true"!==W(e,`--psv-${t}-loaded`)&&console.error(`PhotoSphereViewer: stylesheet "@photo-sphere-viewer/${t}/index.css" is not loaded`)}function eT(e,t,i){t&&t!==i&&console.error(`PhotoSphereViewer: @photo-sphere-viewer/${e} is in version ${t} but @photo-sphere-viewer/core is in version ${i}`)}var e1,eC=class{constructor(e){this.easing=C.linear,this.callbacks=[],this.resolved=!1,this.cancelled=!1,this.options=e,e?(e.easing&&(this.easing="function"==typeof e.easing?e.easing:C[e.easing]||C.linear),this.delayTimeout=setTimeout(()=>{this.delayTimeout=void 0,this.animationFrame=window.requestAnimationFrame(e=>this.__run(e))},e.delay||0)):this.resolved=!0}__run(e){if(this.cancelled)return;this.start||(this.start=e);let t=(e-this.start)/this.options.duration,i={};if(t<1){for(let[s,o]of Object.entries(this.options.properties))if(o){let n=o.start+(o.end-o.start)*this.easing(t);i[s]=n}this.options.onTick(i,t),this.animationFrame=window.requestAnimationFrame(e=>this.__run(e))}else{for(let[r,a]of Object.entries(this.options.properties))a&&(i[r]=a.end);this.options.onTick(i,1),this.__resolve(!0),this.animationFrame=void 0}}__resolve(e){e?this.resolved=!0:this.cancelled=!0,this.callbacks.forEach(t=>t(e)),this.callbacks.length=0}then(e){return this.resolved||this.cancelled?Promise.resolve(this.resolved).then(e):new Promise(e=>{this.callbacks.push(e)}).then(e)}cancel(){this.cancelled||this.resolved||(this.__resolve(!1),this.delayTimeout&&(window.clearTimeout(this.delayTimeout),this.delayTimeout=void 0),this.animationFrame&&(window.cancelAnimationFrame(this.animationFrame),this.animationFrame=void 0))}},eL=t,e3=class{constructor(e,t){if(this.fn=e,this.mode=0,this.speed=0,this.speedMult=0,this.currentSpeed=0,this.target=0,this.__current=0,this.min=t.min,this.max=t.max,this.wrap=t.wrap,this.current=t.defaultValue,this.wrap&&0!==this.min)throw new es("invalid config");this.fn&&this.fn(this.current)}get current(){return this.__current}set current(e){this.__current=e}setSpeed(e){this.speed=e}goto(e,t=1){this.mode=2,this.target=this.wrap?k(e,this.max):eL.MathUtils.clamp(e,this.min,this.max),this.speedMult=t}step(e,t=1){0===t?this.setValue(this.current+e):(2!==this.mode&&(this.target=this.current),this.goto(this.target+e,t))}roll(e=!1,t=1){this.mode=1,this.target=e?-1/0:1/0,this.speedMult=t}stop(){this.mode=0}setValue(e){return this.target=this.wrap?k(e,this.max):eL.MathUtils.clamp(e,this.min,this.max),this.mode=0,this.currentSpeed=0,this.target!==this.current&&(this.current=this.target,this.fn&&this.fn(this.current),!0)}update(e){if(2===this.mode){this.wrap&&Math.abs(this.target-this.current)>this.max/2&&(this.current=this.current<this.target?this.current+this.max:this.current-this.max);let t=this.currentSpeed*this.currentSpeed/(this.speed*this.speedMult*4);Math.abs(this.target-this.current)<=t&&(this.mode=0)}let i=0===this.mode?0:this.speed*this.speedMult;this.target<this.current&&(i=-i),this.currentSpeed<i?this.currentSpeed=Math.min(i,this.currentSpeed+e/1e3*this.speed*this.speedMult*2):this.currentSpeed>i&&(this.currentSpeed=Math.max(i,this.currentSpeed-e/1e3*this.speed*this.speedMult*2));let s=null;return this.current>this.target&&this.currentSpeed?s=Math.max(this.target,this.current+this.currentSpeed*e/1e3):this.current<this.target&&this.currentSpeed&&(s=Math.min(this.target,this.current+this.currentSpeed*e/1e3)),null!==s&&(s=this.wrap?k(s,this.max):eL.MathUtils.clamp(s,this.min,this.max))!==this.current&&(this.current=s,this.fn&&this.fn(this.current),!0)}},e2=class{constructor(e,t){this.fn=e,this.dynamics=t,this.fn&&this.fn(this.current)}get current(){return Object.entries(this.dynamics).reduce((e,[t,i])=>(e[t]=i.current,e),{})}setSpeed(e){for(let t of Object.values(this.dynamics))t.setSpeed(e)}goto(e,t=1){for(let[i,s]of Object.entries(e))this.dynamics[i].goto(s,t)}step(e,t=1){if(0===t)this.setValue(Object.keys(e).reduce((t,i)=>(t[i]=e[i]+this.dynamics[i].current,t),{}));else for(let[i,s]of Object.entries(e))this.dynamics[i].step(s,t)}roll(e,t=1){for(let[i,s]of Object.entries(e))this.dynamics[i].roll(s,t)}stop(){for(let e of Object.values(this.dynamics))e.stop()}setValue(e){let t=!1;for(let[i,s]of Object.entries(e))t=this.dynamics[i].setValue(s)||t;return t&&this.fn&&this.fn(this.current),t}update(e){let t=!1;for(let i of Object.values(this.dynamics))t=i.update(e)||t;return t&&this.fn&&this.fn(this.current),t}},ek=class{constructor(e=200){this.delay=e,this.time=0,this.delay=e}get pending(){return 0!==this.time}down(){this.timeout&&(clearTimeout(this.timeout),this.timeout=void 0),this.time=new Date().getTime()}up(e){if(!this.time)return;let t=Date.now()-this.time;t<this.delay?this.timeout=setTimeout(()=>{e(),this.timeout=void 0,this.time=0},this.delay):(e(),this.time=0)}},eM=((e1=eM||{}).VERTICAL="VERTICAL",e1.HORIZONTAL="HORIZONTAL",e1),eI=class{constructor(e,t,i){this.container=e,this.direction=t,this.listener=i,this.mousedown=!1,this.mouseover=!1,this.container.addEventListener("click",this),this.container.addEventListener("mousedown",this),this.container.addEventListener("mouseenter",this),this.container.addEventListener("mouseleave",this),this.container.addEventListener("touchstart",this),this.container.addEventListener("mousemove",this,!0),this.container.addEventListener("touchmove",this,!0),window.addEventListener("mouseup",this),window.addEventListener("touchend",this)}get isVertical(){return"VERTICAL"===this.direction}get isHorizontal(){return"HORIZONTAL"===this.direction}destroy(){window.removeEventListener("mouseup",this),window.removeEventListener("touchend",this)}handleEvent(e){switch(e.type){case"click":e.stopPropagation();break;case"mousedown":this.__onMouseDown(e);break;case"mouseenter":this.__onMouseEnter(e);break;case"mouseleave":this.__onMouseLeave(e);break;case"touchstart":this.__onTouchStart(e);break;case"mousemove":this.__onMouseMove(e);break;case"touchmove":this.__onTouchMove(e);break;case"mouseup":this.__onMouseUp(e);break;case"touchend":this.__onTouchEnd(e)}}__onMouseDown(e){this.mousedown=!0,this.__update(e.clientX,e.clientY,!0)}__onMouseEnter(e){this.mouseover=!0,this.__update(e.clientX,e.clientY,!0)}__onTouchStart(e){this.mouseover=!0,this.mousedown=!0;let t=e.changedTouches[0];this.__update(t.clientX,t.clientY,!0)}__onMouseMove(e){(this.mousedown||this.mouseover)&&(e.stopPropagation(),this.__update(e.clientX,e.clientY,!0))}__onTouchMove(e){if(this.mousedown||this.mouseover){e.stopPropagation();let t=e.changedTouches[0];this.__update(t.clientX,t.clientY,!0)}}__onMouseUp(e){this.mousedown&&(this.mousedown=!1,this.__update(e.clientX,e.clientY,!1))}__onMouseLeave(e){this.mouseover&&(this.mouseover=!1,this.__update(e.clientX,e.clientY,!0))}__onTouchEnd(e){if(this.mousedown){this.mouseover=!1,this.mousedown=!1;let t=e.changedTouches[0];this.__update(t.clientX,t.clientY,!1)}}__update(e,t,i){let s=this.container.getBoundingClientRect(),o=this.isVertical?t:e,n=s[this.isVertical?"bottom":"left"],r=s[this.isVertical?"height":"width"];this.listener({value:Math.abs((n-o)/r),click:!i,mousedown:this.mousedown,mouseover:this.mouseover,cursor:{clientX:e,clientY:t}})}},eP={};r(eP,{BeforeAnimateEvent:()=>ez,BeforeRenderEvent:()=>e4,BeforeRotateEvent:()=>eA,ClickEvent:()=>eN,ConfigChangedEvent:()=>eU,DoubleClickEvent:()=>eW,FullscreenEvent:()=>eX,HideNotificationEvent:()=>ej,HideOverlayEvent:()=>eZ,HidePanelEvent:()=>eG,HideTooltipEvent:()=>eq,KeypressEvent:()=>eJ,LoadProgressEvent:()=>tt,ObjectEnterEvent:()=>tL,ObjectEvent:()=>t1,ObjectHoverEvent:()=>tM,ObjectLeaveEvent:()=>t2,PanoramaErrorEvent:()=>ta,PanoramaLoadEvent:()=>ts,PanoramaLoadedEvent:()=>tn,PositionUpdatedEvent:()=>tl,ReadyEvent:()=>td,RenderEvent:()=>tu,ShowNotificationEvent:()=>tm,ShowOverlayEvent:()=>tw,ShowPanelEvent:()=>tf,ShowTooltipEvent:()=>ty,SizeUpdatedEvent:()=>t0,StopAllEvent:()=>t8,ViewerEvent:()=>e6,ZoomUpdatedEvent:()=>tT});var eO=class extends Event{constructor(e,t=!1){super(e,{cancelable:t})}},eS=class extends EventTarget{dispatchEvent(e){return super.dispatchEvent(e)}addEventListener(e,t,i){super.addEventListener(e,t,i)}removeEventListener(e,t,i){super.removeEventListener(e,t,i)}},e6=class extends eO{},eH=class e extends e6{constructor(t,i){super(e.type,!0),this.position=t,this.zoomLevel=i}};eH.type="before-animate";var ez=eH,e5=class e extends e6{constructor(t,i){super(e.type),this.timestamp=t,this.elapsed=i}};e5.type="before-render";var e4=e5,eR=class e extends e6{constructor(t){super(e.type,!0),this.position=t}};eR.type="before-rotate";var eA=eR,eD=class e extends e6{constructor(t){super(e.type),this.data=t}};eD.type="click";var eN=eD,e7=class e extends e6{constructor(t){super(e.type),this.options=t}containsOptions(...e){return e.some(e=>this.options.includes(e))}};e7.type="config-changed";var eU=e7,eV=class e extends e6{constructor(t){super(e.type),this.data=t}};eV.type="dblclick";var eW=eV,eF=class e extends e6{constructor(t){super(e.type),this.fullscreenEnabled=t}};eF.type="fullscreen";var eX=eF,eY=class e extends e6{constructor(t){super(e.type),this.notificationId=t}};eY.type="hide-notification";var ej=eY,e9=class e extends e6{constructor(t){super(e.type),this.overlayId=t}};e9.type="hide-overlay";var eZ=e9,eB=class e extends e6{constructor(t){super(e.type),this.panelId=t}};eB.type="hide-panel";var eG=eB,eK=class e extends e6{constructor(t){super(e.type),this.tooltipData=t}};eK.type="hide-tooltip";var eq=eK,eQ=class e extends e6{constructor(t){super(e.type,!0),this.key=t}};eQ.type="key-press";var eJ=eQ,te=class e extends e6{constructor(t){super(e.type),this.progress=t}};te.type="load-progress";var tt=te,ti=class e extends e6{constructor(t){super(e.type),this.panorama=t}};ti.type="panorama-load";var ts=ti,to=class e extends e6{constructor(t){super(e.type),this.data=t}};to.type="panorama-loaded";var tn=to,tr=class e extends e6{constructor(t,i){super(e.type),this.panorama=t,this.error=i}};tr.type="panorama-error";var ta=tr,th=class e extends e6{constructor(t){super(e.type),this.position=t}};th.type="position-updated";var tl=th,tc=class e extends e6{constructor(){super(e.type)}};tc.type="ready";var td=tc,tp=class e extends e6{constructor(){super(e.type)}};tp.type="render";var tu=tp,tv=class e extends e6{constructor(t){super(e.type),this.notificationId=t}};tv.type="show-notification";var tm=tv,t$=class e extends e6{constructor(t){super(e.type),this.overlayId=t}};t$.type="show-overlay";var tw=t$,tg=class e extends e6{constructor(t){super(e.type),this.panelId=t}};tg.type="show-panel";var tf=tg,t_=class e extends e6{constructor(t,i){super(e.type),this.tooltip=t,this.tooltipData=i}};t_.type="show-tooltip";var ty=t_,tb=class e extends e6{constructor(t){super(e.type),this.size=t}};tb.type="size-updated";var t0=tb,tE=class e extends e6{constructor(){super(e.type)}};tE.type="stop-all";var t8=tE,tx=class e extends e6{constructor(t){super(e.type),this.zoomLevel=t}};tx.type="zoom-updated";var tT=tx,t1=class extends e6{constructor(e,t,i,s,o){super(e),this.originalEvent=t,this.object=i,this.viewerPoint=s,this.userDataKey=o}},tC=class e extends t1{constructor(t,i,s,o){super(e.type,t,i,s,o)}};tC.type="enter-object";var tL=tC,t3=class e extends t1{constructor(t,i,s,o){super(e.type,t,i,s,o)}};t3.type="leave-object";var t2=t3,tk=class e extends t1{constructor(t,i,s,o){super(e.type,t,i,s,o)}};tk.type="hover-object";var tM=tk,tI=class{constructor(e){this.viewer=e}init(){}destroy(){}supportsTransition(e){return!1}supportsPreload(e){return!1}textureCoordsToSphericalCoords(e,t){throw new es("Current adapter does not support texture coordinates.")}sphericalCoordsToTextureCoords(e,t){throw new es("Current adapter does not support texture coordinates.")}};function tP(e){if(e){for(let[,t]of[["_",e],...Object.entries(e)])if(t.prototype instanceof tI)return eT(t.id,t.VERSION,"5.6.0"),t}return null}tI.supportsDownload=!1;var tO,tS=t,t6=`${_}_touchSupport`,tH={loaded:!1,pixelRatio:1,isWebGLSupported:!1,maxTextureWidth:0,isTouchEnabled:null,fullscreenEvent:null,__maxCanvasWidth:null,get maxCanvasWidth(){return null===this.__maxCanvasWidth&&(this.__maxCanvasWidth=function e(t){let i=document.createElement("canvas"),s=i.getContext("2d");for(i.width=t,i.height=t/2;i.width>1024;){s.fillStyle="white",s.fillRect(0,0,1,1);try{if(s.getImageData(0,0,1,1).data[0]>0)return i.width}catch(o){}i.width/=2,i.height/=2}throw new es("Unable to detect system capabilities")}(this.maxTextureWidth)),this.__maxCanvasWidth},load(){if(!this.loaded){let e=function e(){let t=document.createElement("canvas"),i=null;return t.getContext&&["webgl2","webgl","experimental-webgl","moz-webgl","webkit-3d"].some(e=>{try{return i=t.getContext(e),null!==i}catch(s){return!1}})?i:null}();this.pixelRatio=window.devicePixelRatio||1,this.isWebGLSupported=null!==e,this.maxTextureWidth=e?e.getParameter(e.MAX_TEXTURE_SIZE):0,this.isTouchEnabled=function e(){let t="ontouchstart"in window||navigator.maxTouchPoints>0;t6 in localStorage&&(t="true"===localStorage[t6]);let i=new Promise(e=>{let i=()=>{window.removeEventListener("mousedown",s),window.removeEventListener("touchstart",o),clearTimeout(r)},s=()=>{i(),localStorage[t6]=!1,e(!1)},o=()=>{i(),localStorage[t6]=!0,e(!0)},n=()=>{i(),localStorage[t6]=t,e(t)};window.addEventListener("mousedown",s,!1),window.addEventListener("touchstart",o,!1);let r=setTimeout(n,1e4)});return{initial:t,promise:i}}(),this.fullscreenEvent="exitFullscreen"in document?"fullscreenchange":"webkitExitFullscreen"in document?"webkitfullscreenchange":null,this.loaded=!0}if(!tH.isWebGLSupported)throw new es("WebGL is not supported.");if(0===tH.maxTextureWidth)throw new es("Unable to detect system capabilities")}},tz=class extends tI{constructor(e,t){super(e),this.config=e8({backgroundColor:"#000",interpolateBackground:!1,resolution:64,useXmpData:!0,blur:!1},{resolution(e){if(!e||!tS.MathUtils.isPowerOfTwo(e))throw new es("EquirectangularAdapter resolution must be power of two");return e}})(t),this.config.interpolateBackground&&(window.Worker?this.interpolationWorker=new Worker(URL.createObjectURL(new Blob(["(",(function e(){function t(e){return`rgb(${e.r}, ${e.g}, ${e.b})`}function i(e,t){return{r:Math.round(e.r/2+t.r/2),g:Math.round(e.g/2+t.g/2),b:Math.round(e.b/2+t.b/2)}}function s(e,i,s,o,n){let r=e.createLinearGradient(i,0,s,0);return r.addColorStop(0,t(o)),r.addColorStop(1,t(n)),r}function o(e,t,i,s,o,n){n=Math.round(n);let r=0,a=0,h=0,l=0,c=e.getImageData(t,i,s,o);for(let d=0;d<o;d+=n)for(let p=0;p<s;p+=n){let u=4*(d*s+p);r+=c.data[u],a+=c.data[u+1],h+=c.data[u+2],l++}return r=Math.round(r/l),a=Math.round(a/l),h=Math.round(h/l),{r,g:a,b:h}}self.onmessage=e=>{let n=e.data.panoData,r=new OffscreenCanvas(n.fullWidth,n.fullHeight),a=r.getContext("2d"),h=new OffscreenCanvas(n.croppedWidth,n.croppedHeight),l=h.getContext("2d");l.putImageData(e.data.image,0,0),function e(n,r,a){let h=a.fullHeight-a.croppedHeight-a.croppedY,l=a.fullWidth-a.croppedWidth-a.croppedX,c=a.croppedY+a.croppedHeight/2,d=n.width/32,p=d,u=`blur(${d}px)`,v=n.getContext("2d");if(v.drawImage(r,a.croppedX,a.croppedY,a.croppedWidth,a.croppedHeight),a.croppedY>0){if(a.croppedX>0||l>0){v.filter="none";let m=o(v,a.croppedX,a.croppedY,10,10,2),$=o(v,n.width-l-11,a.croppedY,10,10,2),w=i(m,$);a.croppedX>0&&(v.fillStyle=s(v,0,a.croppedX,w,m),v.fillRect(-p,-p,a.croppedX+2*p,c+p)),l>0&&(v.fillStyle=s(v,n.width-l,n.width,$,w),v.fillRect(n.width-l-p,-p,l+2*p,c+p))}v.filter=u,v.drawImage(r,0,0,r.width,10,a.croppedX,-p,a.croppedWidth,a.croppedY+2*p),v.fillStyle=t(o(v,0,0,n.width,10,10)),v.fillRect(-p,-p,n.width+2*p,2*p)}if(h>0){if(a.croppedX>0||l>0){v.filter="none";let g=o(v,a.croppedX,n.height-h-1-10,10,10,2),f=o(v,n.width-l-1-10,n.height-h-1-10,10,10,2),_=i(g,f);a.croppedX>0&&(v.fillStyle=s(v,0,a.croppedX,_,g),v.fillRect(-p,c,a.croppedX+2*p,n.height-c+p)),l>0&&(v.fillStyle=s(v,n.width-l,n.width,f,_),v.fillRect(n.width-l-p,c,l+2*p,n.height-c+p))}v.filter=u,v.drawImage(r,0,r.height-10,r.width,10,a.croppedX,n.height-h-p,a.croppedWidth,h+2*p),v.fillStyle=t(o(v,0,n.height-1-10,n.width,10,10)),v.fillRect(-p,n.height-p,n.width+2*p,2*p)}a.croppedX>0&&(v.filter=u,v.drawImage(r,r.width-10,0,10,r.height,-p,a.croppedY,2*p,a.croppedHeight),v.drawImage(r,0,0,10,r.height,0,a.croppedY,a.croppedX+p,a.croppedHeight)),l>0&&(v.filter=u,v.drawImage(r,0,0,10,r.height,n.width-p,a.croppedY,2*p,a.croppedHeight),v.drawImage(r,r.width-10,0,10,r.height,n.width-l-p,a.croppedY,l+p,a.croppedHeight)),v.filter="none",v.drawImage(r,a.croppedX,a.croppedY,a.croppedWidth,a.croppedHeight)}(r,h,n),postMessage(a.getImageData(0,0,r.width,r.height))}}).toString(),")()"],{type:"application/javascript"}))):(eh("Web Worker API not available"),this.config.interpolateBackground=!1)),this.SPHERE_SEGMENTS=this.config.resolution,this.SPHERE_HORIZONTAL_SEGMENTS=this.SPHERE_SEGMENTS/2}supportsTransition(){return!0}supportsPreload(){return!0}destroy(){this.interpolationWorker?.terminate(),super.destroy()}textureCoordsToSphericalCoords(e,t){if(J(e.textureX)||J(e.textureY))throw new es("Texture position is missing 'textureX' or 'textureY'");let i=(e.textureX+t.croppedX)/t.fullWidth*Math.PI*2,s=(e.textureY+t.croppedY)/t.fullHeight*Math.PI;return{yaw:i>=Math.PI?i-Math.PI:i+Math.PI,pitch:Math.PI/2-s}}sphericalCoordsToTextureCoords(e,t){let i=e.yaw/Math.PI/2*t.fullWidth,s=e.pitch/Math.PI*t.fullHeight;return{textureX:Math.round(e.yaw<Math.PI?i+t.fullWidth/2:i-t.fullWidth/2)-t.croppedX,textureY:Math.round(t.fullHeight/2-s)-t.croppedY}}async loadTexture(e,t=!0,i,s=this.config.useXmpData){if("string"!=typeof e)return Promise.reject(new es("Invalid panorama url, are you using the right adapter?"));let o=await this.viewer.textureLoader.loadFile(e,t?e=>this.viewer.loader.setProgress(e):null,e),n=s?await this.loadXMP(o):null,r=await this.viewer.textureLoader.blobToImage(o);"function"==typeof i&&(i=i(r,n)),i||n||(i=this.__defaultPanoData(r));let a={isEquirectangular:!0,fullWidth:ee(i?.fullWidth,n?.fullWidth,r.width),fullHeight:ee(i?.fullHeight,n?.fullHeight,r.height),croppedWidth:ee(i?.croppedWidth,n?.croppedWidth,r.width),croppedHeight:ee(i?.croppedHeight,n?.croppedHeight,r.height),croppedX:ee(i?.croppedX,n?.croppedX,0),croppedY:ee(i?.croppedY,n?.croppedY,0),poseHeading:ee(i?.poseHeading,n?.poseHeading,0),posePitch:ee(i?.posePitch,n?.posePitch,0),poseRoll:ee(i?.poseRoll,n?.poseRoll,0)};(a.croppedWidth!==r.width||a.croppedHeight!==r.height)&&eh(`Invalid panoData, croppedWidth/croppedHeight is not coherent with the loaded image.
            panoData: ${a.croppedWidth}x${a.croppedHeight}, image: ${r.width}x${r.height}`),Math.abs(a.fullWidth-2*a.fullHeight)>1&&(eh("Invalid panoData, fullWidth should be twice fullHeight"),a.fullWidth=2*a.fullHeight),a.croppedX+a.croppedWidth>a.fullWidth&&(eh("Invalid panoData, croppedX + croppedWidth > fullWidth"),a.croppedX=a.fullWidth-a.croppedWidth),a.croppedY+a.croppedHeight>a.fullHeight&&(eh("Invalid panoData, croppedY + croppedHeight > fullHeight"),a.croppedY=a.fullHeight-a.croppedHeight);let h=this.createEquirectangularTexture(r,a);return{panorama:e,texture:h,panoData:a,cacheKey:e}}async loadXMP(e){let t=await this.loadBlobAsString(e),i=t.indexOf("<x:xmpmeta"),s=t.indexOf("</x:xmpmeta>"),o=t.substring(i,s);return -1!==i&&-1!==s&&o.includes("GPano:")?{isEquirectangular:!0,fullWidth:ec(o,"FullPanoWidthPixels"),fullHeight:ec(o,"FullPanoHeightPixels"),croppedWidth:ec(o,"CroppedAreaImageWidthPixels"),croppedHeight:ec(o,"CroppedAreaImageHeightPixels"),croppedX:ec(o,"CroppedAreaLeftPixels"),croppedY:ec(o,"CroppedAreaTopPixels"),poseHeading:ec(o,"PoseHeadingDegrees"),posePitch:ec(o,"PosePitchDegrees"),poseRoll:ec(o,"PoseRollDegrees")}:null}loadBlobAsString(e){return new Promise((t,i)=>{let s=new FileReader;s.onload=()=>t(s.result),s.onerror=i,s.readAsText(e)})}createEquirectangularTexture(e,t){if(this.config.blur||t.fullWidth>tH.maxTextureWidth||t.croppedWidth!==t.fullWidth||t.croppedHeight!==t.fullHeight){let i=Math.min(1,tH.maxCanvasWidth/t.fullWidth),s={fullWidth:t.fullWidth*i,fullHeight:t.fullHeight*i,croppedWidth:t.croppedWidth*i,croppedHeight:t.croppedHeight*i,croppedX:t.croppedX*i,croppedY:t.croppedY*i},o=document.createElement("canvas");o.width=s.fullWidth,o.height=s.fullHeight;let n=o.getContext("2d");this.config.backgroundColor&&(n.fillStyle=this.config.backgroundColor,n.fillRect(0,0,o.width,o.height)),this.config.blur&&(n.filter=`blur(${o.width/2048}px)`),n.drawImage(e,s.croppedX,s.croppedY,s.croppedWidth,s.croppedHeight);let r=eb(o);return this.config.interpolateBackground&&(t.croppedWidth!==t.fullWidth||t.croppedHeight!==t.fullHeight)&&(this.interpolationWorker.postMessage({image:n.getImageData(s.croppedX,s.croppedY,s.croppedWidth,s.croppedHeight),panoData:s}),this.interpolationWorker.onmessage=e=>{n.putImageData(e.data,0,0),r.needsUpdate=!0,this.viewer.needsUpdate()}),r}return eb(e)}createMesh(e=1){let t=new tS.SphereGeometry(f*e,this.SPHERE_SEGMENTS,this.SPHERE_HORIZONTAL_SEGMENTS,-Math.PI/2).scale(-1,1,1);return new tS.Mesh(t,new tS.MeshBasicMaterial)}setTexture(e,t){e.material.map=t.texture}setTextureOpacity(e,t){e.material.opacity=t,e.material.transparent=t<1}disposeTexture(e){e.texture?.dispose()}__defaultPanoData(e){let t=Math.max(e.width,2*e.height),i=Math.round(t/2),s=Math.round((t-e.width)/2),o=Math.round((i-e.height)/2);return{isEquirectangular:!0,fullWidth:t,fullHeight:i,croppedWidth:e.width,croppedHeight:e.height,croppedX:s,croppedY:o}}};tz.id="equirectangular",tz.VERSION="5.6.0",tz.supportsDownload=!0;var t5=class e{constructor(t,i){this.parent=t,this.children=[],this.container=document.createElement("div"),this.state={visible:!0},this.viewer=t instanceof e?t.viewer:t,this.container.className=i.className||"",this.parent.children.push(this),this.parent.container.appendChild(this.container)}destroy(){this.parent.container.removeChild(this.container);let e=this.parent.children.indexOf(this);-1!==e&&this.parent.children.splice(e,1),this.children.slice().forEach(e=>e.destroy()),this.children.length=0}toggle(e=!this.isVisible()){e?this.show():this.hide()}hide(e){this.container.style.display="none",this.state.visible=!1}show(e){this.container.style.display="",this.state.visible=!0}isVisible(){return this.state.visible}},t4=class extends t5{constructor(e,t){super(e,{className:`psv-button ${t.hoverScale?"psv-button--hover-scale":""} ${t.className||""}`}),this.state={visible:!0,enabled:!0,supported:!0,collapsed:!1,active:!1,width:0},this.config=e8({id:null,className:null,title:null,hoverScale:!1,collapsable:!1,tabbable:!0,icon:null,iconActive:null})(t),this.config.id=this.constructor.id,t.icon&&this.__setIcon(t.icon),this.state.width=this.container.offsetWidth,this.config.title?this.container.title=this.config.title:this.id&&this.id in this.viewer.config.lang&&(this.container.title=this.viewer.config.lang[this.id]),t.tabbable&&(this.container.tabIndex=0),this.container.addEventListener("click",e=>{this.state.enabled&&this.onClick(),e.stopPropagation()}),this.container.addEventListener("keydown",e=>{e.key===x.Enter&&this.state.enabled&&(this.onClick(),e.stopPropagation())})}get id(){return this.config.id}get title(){return this.container.title}get content(){return this.container.innerHTML}get width(){return this.state.width}get collapsable(){return this.config.collapsable}show(e=!0){!this.isVisible()&&(this.state.visible=!0,this.state.collapsed||(this.container.style.display=""),e&&this.viewer.navbar.autoSize())}hide(e=!0){this.isVisible()&&(this.state.visible=!1,this.container.style.display="none",e&&this.viewer.navbar.autoSize())}checkSupported(){eo(this.isSupported(),(e,t)=>{this.state&&(this.state.supported=e,t?e||this.hide():this.toggle(e))})}autoSize(){}isSupported(){return!0}toggleActive(e=!this.state.active){e!==this.state.active&&(this.state.active=e,R(this.container,"psv-button--active",this.state.active),this.config.iconActive&&this.__setIcon(this.state.active?this.config.iconActive:this.config.icon))}disable(){this.container.classList.add("psv-button--disabled"),this.state.enabled=!1}enable(){this.container.classList.remove("psv-button--disabled"),this.state.enabled=!0}collapse(){this.state.collapsed=!0,this.container.style.display="none"}uncollapse(){this.state.collapsed=!1,this.state.visible&&(this.container.style.display="")}__setIcon(e){this.container.innerHTML=e,A(this.container.querySelector("svg"),"psv-button-svg")}},tR=class extends t4{constructor(e,t){super(e,{className:`psv-custom-button ${t.className||""}`,hoverScale:!1,collapsable:!1!==t.collapsable,tabbable:!1!==t.tabbable,title:t.title}),this.customOnClick=t.onClick,t.id?this.config.id=t.id:this.config.id="psvButton-"+Math.random().toString(36).substring(2),t.content&&("string"==typeof t.content?this.container.innerHTML=t.content:(this.container.classList.add("psv-custom-button--no-padding"),t.content.style.height="100%",t.content.attachViewer?.(this.viewer),this.container.appendChild(t.content))),this.state.width=this.container.offsetWidth,t.disabled&&this.disable(),!1===t.visible&&this.hide()}onClick(){this.customOnClick?.(this.viewer)}},tA=class extends t4{constructor(e){super(e,{className:"psv-description-button",hoverScale:!0,collapsable:!1,tabbable:!0,icon:T.info}),this.mode=0,this.viewer.addEventListener(ej.type,this),this.viewer.addEventListener(tm.type,this),this.viewer.addEventListener(eG.type,this),this.viewer.addEventListener(tf.type,this),this.viewer.addEventListener(eU.type,this)}destroy(){this.viewer.removeEventListener(ej.type,this),this.viewer.removeEventListener(tm.type,this),this.viewer.removeEventListener(eG.type,this),this.viewer.removeEventListener(tf.type,this),this.viewer.removeEventListener(eU.type,this),super.destroy()}handleEvent(e){if(e instanceof eU){e.containsOptions("description")&&this.autoSize(!0);return}if(!this.mode)return;let t=!1;e instanceof ej?t=1===this.mode:e instanceof tm?t=1===this.mode&&e.notificationId!==E.DESCRIPTION:e instanceof eG?t=2===this.mode:e instanceof tf&&(t=2===this.mode&&e.panelId!==E.DESCRIPTION),t&&(this.toggleActive(!1),this.mode=0)}onClick(){this.mode?this.__close():this.__open()}hide(e){super.hide(e),this.mode&&this.__close()}autoSize(e=!1){if(e){let t=this.viewer.navbar.getButton("caption",!1),i=t&&!t.isVisible(),s=!!this.viewer.config.description;i||s?this.show(!1):this.hide(!1)}}__close(){switch(this.mode){case 1:this.viewer.notification.hide(E.DESCRIPTION);break;case 2:this.viewer.panel.hide(E.DESCRIPTION)}}__open(){this.toggleActive(!0),this.viewer.config.description?(this.mode=2,this.viewer.panel.show({id:E.DESCRIPTION,content:(this.viewer.config.caption?`<p>${this.viewer.config.caption}</p>`:"")+this.viewer.config.description})):(this.mode=1,this.viewer.notification.show({id:E.DESCRIPTION,content:this.viewer.config.caption}))}};tA.id="description";var tD=class extends t4{constructor(e){super(e,{className:"psv-download-button",hoverScale:!0,collapsable:!0,tabbable:!0,icon:T.download}),this.viewer.addEventListener(eU.type,this)}destroy(){this.viewer.removeEventListener(eU.type,this),super.destroy()}handleEvent(e){e instanceof eU&&e.containsOptions("downloadUrl")&&this.checkSupported()}onClick(){let e=document.createElement("a");e.href=this.viewer.config.downloadUrl||this.viewer.config.panorama,e.href.startsWith("data:")&&!this.viewer.config.downloadName?e.download="panorama."+e.href.substring(0,e.href.indexOf(";")).split("/").pop():e.download=this.viewer.config.downloadName||e.href.split("/").pop(),e.target="_blank",this.viewer.container.appendChild(e),e.click(),setTimeout(()=>{this.viewer.container.removeChild(e)},100)}checkSupported(){let e=this.viewer.adapter.constructor.supportsDownload||this.viewer.config.downloadUrl;e?this.show():this.hide()}};tD.id="download";var tN=class extends t4{constructor(e){super(e,{className:"psv-fullscreen-button",hoverScale:!0,collapsable:!1,tabbable:!0,icon:T.fullscreenIn,iconActive:T.fullscreenOut}),this.viewer.addEventListener(eX.type,this)}destroy(){this.viewer.removeEventListener(eX.type,this),super.destroy()}handleEvent(e){e instanceof eX&&this.toggleActive(e.fullscreenEnabled)}onClick(){this.viewer.toggleFullscreen()}};tN.id="fullscreen";var t7=(e,t)=>`
<div class="psv-panel-menu psv-panel-menu--stripped">
  <h1 class="psv-panel-menu-title">${T.menu} ${t}</h1>
  <ul class="psv-panel-menu-list">
    ${e.map(e=>`
    <li data-psv-button="${e.id}" class="psv-panel-menu-item" tabindex="0">
      <span class="psv-panel-menu-item-icon">${e.content}</span>
      <span class="psv-panel-menu-item-label">${e.title}</span>
    </li>
    `).join("")}
  </ul>
</div>
`,tU=class extends t4{constructor(e){super(e,{className:"psv-menu-button",hoverScale:!0,collapsable:!1,tabbable:!0,icon:T.menu}),this.viewer.addEventListener(tf.type,this),this.viewer.addEventListener(eG.type,this),super.hide()}destroy(){this.viewer.removeEventListener(tf.type,this),this.viewer.removeEventListener(eG.type,this),super.destroy()}handleEvent(e){e instanceof tf?this.toggleActive(e.panelId===E.MENU):e instanceof eG&&this.toggleActive(!1)}onClick(){this.state.active?this.__hideMenu():this.__showMenu()}hide(e){super.hide(e),this.__hideMenu()}show(e){super.show(e),this.state.active&&this.__showMenu()}__showMenu(){this.viewer.panel.show({id:E.MENU,content:t7(this.viewer.navbar.collapsed,this.viewer.config.lang.menu),noMargin:!0,clickHandler:e=>{let t=e?U(e,"li"):void 0,i=t?t.dataset.psvButton:void 0;i&&(this.viewer.navbar.getButton(i).onClick(),this.__hideMenu())}})}__hideMenu(){this.viewer.panel.hide(E.MENU)}};tU.id="menu";var tV=class extends t4{constructor(e,t){super(e,{className:"psv-move-button",hoverScale:!0,collapsable:!1,tabbable:!0,icon:function e(t){let i=0;switch(t){case 0:i=90;break;case 1:i=-90;break;case 3:i=180;break;default:i=0}return T.arrow.replace("rotate(0",`rotate(${i}`)}(t)}),this.direction=t,this.handler=new ek,this.container.addEventListener("mousedown",this),this.container.addEventListener("keydown",this),this.container.addEventListener("keyup",this),this.viewer.container.addEventListener("mouseup",this),this.viewer.container.addEventListener("touchend",this)}destroy(){this.__onMouseUp(),this.viewer.container.removeEventListener("mouseup",this),this.viewer.container.removeEventListener("touchend",this),super.destroy()}handleEvent(e){switch(e.type){case"mousedown":this.__onMouseDown();break;case"mouseup":case"touchend":this.__onMouseUp();break;case"keydown":e.key===x.Enter&&this.__onMouseDown();break;case"keyup":e.key===x.Enter&&this.__onMouseUp()}}onClick(){}isSupported(){return en(tH.isTouchEnabled)}__onMouseDown(){if(!this.state.enabled)return;let e={};switch(this.direction){case 0:e.pitch=!1;break;case 1:e.pitch=!0;break;case 3:e.yaw=!1;break;default:e.yaw=!0}this.viewer.stopAll(),this.viewer.dynamics.position.roll(e),this.handler.down()}__onMouseUp(){this.state.enabled&&this.handler.up(()=>{this.viewer.dynamics.position.stop(),this.viewer.resetIdleTimer()})}};tV.groupId="move";var tW=class extends tV{constructor(e){super(e,1)}};tW.id="moveDown";var tF=class extends tV{constructor(e){super(e,2)}};tF.id="moveLeft";var tX=class extends tV{constructor(e){super(e,3)}};tX.id="moveRight";var tY=class extends tV{constructor(e){super(e,0)}};tY.id="moveUp";var tj=class extends t4{constructor(e,t,i){super(e,{className:"psv-zoom-button",hoverScale:!0,collapsable:!1,tabbable:!0,icon:t}),this.direction=i,this.handler=new ek,this.container.addEventListener("mousedown",this),this.container.addEventListener("keydown",this),this.container.addEventListener("keyup",this),this.viewer.container.addEventListener("mouseup",this),this.viewer.container.addEventListener("touchend",this)}destroy(){this.__onMouseUp(),this.viewer.container.removeEventListener("mouseup",this),this.viewer.container.removeEventListener("touchend",this),super.destroy()}handleEvent(e){switch(e.type){case"mousedown":this.__onMouseDown();break;case"mouseup":case"touchend":this.__onMouseUp();break;case"keydown":e.key===x.Enter&&this.__onMouseDown();break;case"keyup":e.key===x.Enter&&this.__onMouseUp()}}onClick(){}isSupported(){return en(tH.isTouchEnabled)}__onMouseDown(){this.state.enabled&&(this.viewer.dynamics.zoom.roll(1===this.direction),this.handler.down())}__onMouseUp(){this.state.enabled&&this.handler.up(()=>this.viewer.dynamics.zoom.stop())}};tj.groupId="zoom";var t9=class extends tj{constructor(e){super(e,T.zoomIn,0)}};t9.id="zoomIn";var tZ=class extends tj{constructor(e){super(e,T.zoomOut,1)}};tZ.id="zoomOut";var tB=class extends t4{constructor(e){super(e,{className:"psv-zoom-range",hoverScale:!1,collapsable:!1,tabbable:!1}),this.zoomRange=document.createElement("div"),this.zoomRange.className="psv-zoom-range-line",this.container.appendChild(this.zoomRange),this.zoomValue=document.createElement("div"),this.zoomValue.className="psv-zoom-range-handle",this.zoomRange.appendChild(this.zoomValue),this.slider=new eI(this.container,"HORIZONTAL",e=>this.__onSliderUpdate(e)),this.mediaMinWidth=parseInt(W(this.container,"max-width"),10),this.viewer.addEventListener(tT.type,this),this.viewer.state.ready?this.__moveZoomValue(this.viewer.getZoomLevel()):this.viewer.addEventListener(td.type,this)}destroy(){this.slider.destroy(),this.viewer.removeEventListener(tT.type,this),this.viewer.removeEventListener(td.type,this),super.destroy()}handleEvent(e){e instanceof tT?this.__moveZoomValue(e.zoomLevel):e instanceof td&&this.__moveZoomValue(this.viewer.getZoomLevel())}onClick(){}isSupported(){return en(tH.isTouchEnabled)}autoSize(){this.state.supported&&(this.viewer.state.size.width<=this.mediaMinWidth&&this.state.visible?this.hide(!1):this.viewer.state.size.width>this.mediaMinWidth&&!this.state.visible&&this.show(!1))}__moveZoomValue(e){this.zoomValue.style.left=e/100*this.zoomRange.offsetWidth-this.zoomValue.offsetWidth/2+"px"}__onSliderUpdate(e){e.mousedown&&this.viewer.zoom(100*e.value)}};tB.id="zoomRange",tB.groupId="zoom";var tG=t,tK=class extends eS{constructor(e){super(),this.viewer=e}init(){}destroy(){}},tq=class extends tK{constructor(e,t){super(e),this.config=this.constructor.configParser(t)}setOption(e,t){this.setOptions({[e]:t})}setOptions(e){let t={...this.config,...e},i=this.constructor,s=i.configParser,o=i.readonlyOptions,n=i.id;for(let[r,a]of Object.entries(e)){if(!(r in s.defaults)){eh(`${n}: Unknown option "${r}"`);continue}if(o.includes(r)){eh(`${n}: Option "${r}" cannot be updated`);continue}r in s.parsers&&(a=s.parsers[r](a,{rawConfig:t,defValue:s.defaults[r]})),this.config[r]=a}}};function tQ(e){if(e){for(let[,t]of[["_",e],...Object.entries(e)])if(t.prototype instanceof tK)return eT(t.id,t.VERSION,"5.6.0"),t}return null}tq.readonlyOptions=[];var tJ={panorama:null,container:null,adapter:[tz,null],plugins:[],caption:null,description:null,downloadUrl:null,downloadName:null,loadingImg:null,loadingTxt:"Loading...",size:null,fisheye:0,minFov:30,maxFov:90,defaultZoomLvl:50,defaultYaw:0,defaultPitch:0,sphereCorrection:null,moveSpeed:1,zoomSpeed:1,moveInertia:!0,mousewheel:!0,mousemove:!0,mousewheelCtrlKey:!1,touchmoveTwoFingers:!1,panoData:null,requestHeaders:null,rendererParameters:{alpha:!0,antialias:!0},withCredentials:!1,navbar:["zoom","move","download","description","caption","fullscreen"],lang:{zoom:"Zoom",zoomOut:"Zoom out",zoomIn:"Zoom in",moveUp:"Move up",moveDown:"Move down",moveLeft:"Move left",moveRight:"Move right",download:"Download",fullscreen:"Fullscreen",menu:"Menu",close:"Close",twoFingers:"Use two fingers to navigate",ctrlZoom:"Use ctrl + scroll to zoom the image",loadError:"The panorama can't be loaded"},keyboard:"fullscreen",keyboardActions:{[x.ArrowUp]:"ROTATE_UP",[x.ArrowDown]:"ROTATE_DOWN",[x.ArrowRight]:"ROTATE_RIGHT",[x.ArrowLeft]:"ROTATE_LEFT",[x.PageUp]:"ZOOM_IN",[x.PageDown]:"ZOOM_OUT",[x.Plus]:"ZOOM_IN",[x.Minus]:"ZOOM_OUT"}},ie={panorama:"Use setPanorama method to change the panorama",panoData:"Use setPanorama method to change the panorama",container:"Cannot change viewer container",adapter:"Cannot change adapter",plugins:"Cannot change plugins"},it={container(e){if(!e)throw new es("No value given for container.");return e},adapter(e,{defValue:t}){if(!(e=e?Array.isArray(e)?[tP(e[0]),e[1]]:[tP(e),null]:t)[0])throw new es("An undefined value was given for adapter.");if(!e[0].id)throw new es("Adapter has no id.");return e},defaultYaw:e=>ey(e),defaultPitch:e=>ey(e,!0),defaultZoomLvl:e=>tG.MathUtils.clamp(e,0,100),minFov:(e,{rawConfig:t})=>(t.maxFov<e&&(eh("maxFov cannot be lower than minFov"),e=t.maxFov),tG.MathUtils.clamp(e,1,179)),maxFov:(e,{rawConfig:t})=>(e<t.minFov&&(e=t.minFov),tG.MathUtils.clamp(e,1,179)),lang:e=>(Array.isArray(e.twoFingers)&&(eh("lang.twoFingers must not be an array"),e.twoFingers=e.twoFingers[0]),{...tJ.lang,...e}),keyboard:e=>!!e&&("object"==typeof e?(eh("Use keyboardActions to configure the keyboard actions, keyboard option must be either true, false, 'fullscreen' or 'always'"),"fullscreen"):"always"===e?"always":"fullscreen"),keyboardActions:(e,{rawConfig:t})=>t.keyboard&&"object"==typeof t.keyboard?t.keyboard:e,fisheye:e=>!0===e?1:!1===e?0:e,requestHeaders:e=>e&&"object"==typeof e?()=>e:"function"==typeof e?e:null,rendererParameters:(e,{defValue:t})=>({...e,...t}),plugins:e=>e.map((e,t)=>{if(!(e=Array.isArray(e)?[tQ(e[0]),e[1]]:[tQ(e),null])[0])throw new es(`An undefined value was given for plugin ${t}.`);if(!e[0].id)throw new es(`Plugin ${t} has no id.`);return e}),navbar:e=>!1===e?null:!0===e?q(tJ.navbar):"string"==typeof e?e.split(/[ ,]/):e},ii=e8(tJ,it),is=class extends t4{constructor(e){super(e,{className:"psv-caption",hoverScale:!1,collapsable:!1,tabbable:!0}),this.contentWidth=0,this.state.width=0,this.contentElt=document.createElement("div"),this.contentElt.className="psv-caption-content",this.container.appendChild(this.contentElt),this.setCaption(this.viewer.config.caption)}hide(){this.contentElt.style.display="none",this.state.visible=!1}show(){this.contentElt.style.display="",this.state.visible=!0}onClick(){}setCaption(e){this.show(),this.contentElt.innerHTML=e??"",this.contentElt.innerHTML?this.contentWidth=this.contentElt.offsetWidth:this.contentWidth=0,this.autoSize()}autoSize(){this.toggle(this.container.offsetWidth>=this.contentWidth),this.__refreshButton()}__refreshButton(){this.viewer.navbar.getButton(tA.id,!1)?.autoSize(!0)}};is.id="caption";var io={},ir={};function ia(e,t){if(!e.id)throw new es("Button id is required");if(io[e.id]=e,e.groupId&&(ir[e.groupId]=ir[e.groupId]||[]).push(e),t){let i=tJ.navbar;switch(t){case"start":i.unshift(e.id);break;case"end":i.push(e.id);break;default:{let[s,o]=t.split(":"),n=i.indexOf(s);if(!s||!o||-1===n)throw new es(`Invalid defaultPosition ${t}`);i.splice(n+("right"===o?1:0),0,e.id)}}}}[tZ,tB,t9,tA,is,tD,tN,tF,tX,tY,tW].forEach(e=>ia(e));var ih=class extends t5{constructor(e){super(e,{className:`psv-navbar ${y}`}),this.collapsed=[],this.state.visible=!1}show(){this.viewer.container.classList.add("psv--has-navbar"),this.container.classList.add("psv-navbar--open"),this.state.visible=!0}hide(){this.viewer.container.classList.remove("psv--has-navbar"),this.container.classList.remove("psv-navbar--open"),this.state.visible=!1}setButtons(e){this.children.slice().forEach(e=>e.destroy()),this.children.length=0,-1!==e.indexOf(is.id)&&-1===e.indexOf(tA.id)&&e.splice(e.indexOf(is.id),0,tA.id),e.forEach(e=>{"object"==typeof e?new tR(this,e):io[e]?new io[e](this):ir[e]?ir[e].forEach(e=>{new e(this)}):eh(`Unknown button ${e}`)}),new tU(this),this.children.forEach(e=>{e instanceof t4&&e.checkSupported()}),this.autoSize()}setCaption(e){this.children.some(t=>t instanceof is&&(t.setCaption(e),!0))}getButton(e,t=!0){let i=this.children.find(t=>t instanceof t4&&t.id===e);return!i&&t&&eh(`button "${e}" not found in the navbar`),i}autoSize(){this.children.forEach(e=>{e instanceof t4&&e.autoSize()});let e=this.container.offsetWidth,t=0,i=[];this.children.forEach(e=>{e.isVisible()&&e instanceof t4&&(t+=e.width,e.collapsable&&i.push(e))}),0!==t&&(e<t&&i.length>0?(i.forEach(e=>e.collapse()),this.collapsed=i,this.getButton(tU.id).show(!1)):e>=t&&this.collapsed.length>0&&(this.collapsed.forEach(e=>e.uncollapse()),this.collapsed=[],this.getButton(tU.id).hide(!1)),this.getButton(is.id,!1)?.autoSize())}},il=class extends t5{constructor(e){super(e,{className:"psv-loader-container"}),this.loader=document.createElement("div"),this.loader.className="psv-loader",this.container.appendChild(this.loader),this.size=this.loader.offsetWidth,this.canvas=document.createElementNS("http://www.w3.org/2000/svg","svg"),this.canvas.setAttribute("class","psv-loader-canvas"),this.canvas.setAttribute("viewBox",`0 0 ${this.size} ${this.size}`),this.loader.appendChild(this.canvas),this.textColor=W(this.loader,"color"),this.color=W(this.canvas,"color"),this.border=parseInt(W(this.loader,"--psv-loader-border"),10),this.thickness=parseInt(W(this.loader,"--psv-loader-tickness"),10),this.viewer.addEventListener(eU.type,this),this.__updateContent(),this.hide()}destroy(){this.viewer.removeEventListener(eU.type,this),super.destroy()}handleEvent(e){e instanceof eU&&e.containsOptions("loadingImg","loadingTxt")&&this.__updateContent()}setProgress(e){let t=Math.min(e,99.999)/100*Math.PI*2,i=this.size/2,s=this.thickness/2+this.border,o=(this.size-this.thickness)/2-this.border;this.canvas.innerHTML=`
            <circle cx="${i}" cy="${i}" r="${i}" fill="${this.color}"/>
            <path d="M ${i} ${s} A ${o} ${o} 0 ${e>50?"1":"0"} 1 ${Math.sin(t)*o+i} ${-Math.cos(t)*o+i}" 
                  fill="none" stroke="${this.textColor}" stroke-width="${this.thickness}" stroke-linecap="round"/>
        `,this.viewer.dispatchEvent(new tt(Math.round(e)))}__updateContent(){let e=this.loader.querySelector(".psv-loader-image, .psv-loader-text");e&&this.loader.removeChild(e);let t;if(this.viewer.config.loadingImg?((t=document.createElement("img")).className="psv-loader-image",t.src=this.viewer.config.loadingImg):this.viewer.config.loadingTxt&&((t=document.createElement("div")).className="psv-loader-text",t.innerHTML=this.viewer.config.loadingTxt),t){let i=Math.round(Math.sqrt(2*Math.pow(this.size/2-this.thickness/2-this.border,2)));t.style.maxWidth=i+"px",t.style.maxHeight=i+"px",this.loader.appendChild(t)}}},ic=class extends t5{constructor(e){super(e,{className:"psv-notification"}),this.state={visible:!1,contentId:null,timeout:null},this.content=document.createElement("div"),this.content.className="psv-notification-content",this.container.appendChild(this.content),this.content.addEventListener("click",()=>this.hide())}isVisible(e){return this.state.visible&&(!e||!this.state.contentId||this.state.contentId===e)}toggle(){throw new es("Notification cannot be toggled")}show(e){this.state.timeout&&(clearTimeout(this.state.timeout),this.state.timeout=null),"string"==typeof e&&(e={content:e}),this.state.contentId=e.id||null,this.content.innerHTML=e.content,this.container.classList.add("psv-notification--visible"),this.state.visible=!0,this.viewer.dispatchEvent(new tm(e.id)),e.timeout&&(this.state.timeout=setTimeout(()=>this.hide(this.state.contentId),e.timeout))}hide(e){if(this.isVisible(e)){let t=this.state.contentId;this.container.classList.remove("psv-notification--visible"),this.state.visible=!1,this.state.contentId=null,this.viewer.dispatchEvent(new ej(t))}}},id=class extends t5{constructor(e){super(e,{className:`psv-overlay ${y}`}),this.state={visible:!1,contentId:null,dissmisable:!0},this.image=document.createElement("div"),this.image.className="psv-overlay-image",this.container.appendChild(this.image),this.title=document.createElement("div"),this.title.className="psv-overlay-title",this.container.appendChild(this.title),this.text=document.createElement("div"),this.text.className="psv-overlay-text",this.container.appendChild(this.text),this.container.addEventListener("click",this),this.viewer.addEventListener(eJ.type,this),super.hide()}destroy(){this.viewer.removeEventListener(eJ.type,this),super.destroy()}handleEvent(e){"click"===e.type?this.isVisible()&&this.state.dissmisable&&(this.hide(),e.stopPropagation()):e instanceof eJ&&this.isVisible()&&this.state.dissmisable&&e.key===x.Escape&&(this.hide(),e.preventDefault())}isVisible(e){return this.state.visible&&(!e||!this.state.contentId||this.state.contentId===e)}toggle(){throw new es("Overlay cannot be toggled")}show(e){"string"==typeof e&&(e={title:e}),this.state.contentId=e.id||null,this.state.dissmisable=!1!==e.dissmisable,this.image.innerHTML=e.image||"",this.title.innerHTML=e.title||"",this.text.innerHTML=e.text||"",super.show(),this.viewer.dispatchEvent(new tw(e.id))}hide(e){if(this.isVisible(e)){let t=this.state.contentId;super.hide(),this.state.contentId=null,this.viewer.dispatchEvent(new eZ(t))}}},ip="psv-panel-content--no-interaction",iu=class extends t5{constructor(e){super(e,{className:`psv-panel ${y}`}),this.state={visible:!1,contentId:null,mouseX:0,mouseY:0,mousedown:!1,clickHandler:null,keyHandler:null,width:{}};let t=document.createElement("div");t.className="psv-panel-resizer",this.container.appendChild(t);let i=document.createElement("div");i.className="psv-panel-close-button",i.innerHTML=T.close,i.title=e.config.lang.close,this.container.appendChild(i),this.content=document.createElement("div"),this.content.className="psv-panel-content",this.container.appendChild(this.content),this.container.addEventListener("wheel",e=>e.stopPropagation()),i.addEventListener("click",()=>this.hide()),t.addEventListener("mousedown",this),t.addEventListener("touchstart",this),this.viewer.container.addEventListener("mouseup",this),this.viewer.container.addEventListener("touchend",this),this.viewer.container.addEventListener("mousemove",this),this.viewer.container.addEventListener("touchmove",this),this.viewer.addEventListener(eJ.type,this)}destroy(){this.viewer.removeEventListener(eJ.type,this),this.viewer.container.removeEventListener("mousemove",this),this.viewer.container.removeEventListener("touchmove",this),this.viewer.container.removeEventListener("mouseup",this),this.viewer.container.removeEventListener("touchend",this),super.destroy()}handleEvent(e){switch(e.type){case"mousedown":this.__onMouseDown(e);break;case"touchstart":this.__onTouchStart(e);break;case"mousemove":this.__onMouseMove(e);break;case"touchmove":this.__onTouchMove(e);break;case"mouseup":this.__onMouseUp(e);break;case"touchend":this.__onTouchEnd(e);break;case eJ.type:this.__onKeyPress(e)}}isVisible(e){return this.state.visible&&(!e||!this.state.contentId||this.state.contentId===e)}toggle(){throw new es("Panel cannot be toggled")}show(e){"string"==typeof e&&(e={content:e});let t=this.isVisible(e.id);this.state.contentId=e.id||null,this.state.visible=!0,this.state.clickHandler&&(this.content.removeEventListener("click",this.state.clickHandler),this.content.removeEventListener("keydown",this.state.keyHandler),this.state.clickHandler=null,this.state.keyHandler=null),e.id&&this.state.width[e.id]?this.container.style.width=this.state.width[e.id]:e.width?this.container.style.width=e.width:this.container.style.width=null,this.content.innerHTML=e.content,this.content.scrollTop=0,this.container.classList.add("psv-panel--open"),R(this.content,"psv-panel-content--no-margin",!0===e.noMargin),e.clickHandler&&(this.state.clickHandler=t=>{e.clickHandler(t.target)},this.state.keyHandler=t=>{t.key===x.Enter&&e.clickHandler(t.target)},this.content.addEventListener("click",this.state.clickHandler),this.content.addEventListener("keydown",this.state.keyHandler),t||setTimeout(()=>{this.content.querySelector("a,button,[tabindex]")?.focus()},300)),this.viewer.dispatchEvent(new tf(e.id))}hide(e){if(this.isVisible(e)){let t=this.state.contentId;this.state.visible=!1,this.state.contentId=null,this.content.innerHTML=null,this.container.classList.remove("psv-panel--open"),this.state.clickHandler&&(this.content.removeEventListener("click",this.state.clickHandler),this.state.clickHandler=null),this.viewer.dispatchEvent(new eG(t))}}__onMouseDown(e){e.stopPropagation(),this.__startResize(e.clientX,e.clientY)}__onTouchStart(e){if(e.stopPropagation(),1===e.touches.length){let t=e.touches[0];this.__startResize(t.clientX,t.clientY)}}__onMouseUp(e){this.state.mousedown&&(e.stopPropagation(),this.state.mousedown=!1,this.content.classList.remove(ip))}__onTouchEnd(e){this.state.mousedown&&(e.stopPropagation(),0===e.touches.length&&(this.state.mousedown=!1,this.content.classList.remove(ip)))}__onMouseMove(e){this.state.mousedown&&(e.stopPropagation(),this.__resize(e.clientX,e.clientY))}__onTouchMove(e){if(this.state.mousedown){let t=e.touches[0];this.__resize(t.clientX,t.clientY)}}__onKeyPress(e){this.isVisible()&&e.key===x.Escape&&(this.hide(),e.preventDefault())}__startResize(e,t){this.state.mouseX=e,this.state.mouseY=t,this.state.mousedown=!0,this.content.classList.add(ip)}__resize(e,t){let i=e,s=Math.max(200,this.container.offsetWidth-(i-this.state.mouseX))+"px";this.state.contentId&&(this.state.width[this.state.contentId]=s),this.container.style.width=s,this.state.mouseX=i,this.state.mouseY=t}},iv=class extends t5{constructor(e,t){super(e,{className:"psv-tooltip"}),this.state={visible:!0,arrow:0,border:0,state:0,width:0,height:0,pos:"",config:null,data:null},this.content=document.createElement("div"),this.content.className="psv-tooltip-content",this.container.appendChild(this.content),this.arrow=document.createElement("div"),this.arrow.className="psv-tooltip-arrow",this.container.appendChild(this.arrow),this.container.addEventListener("transitionend",this),this.container.addEventListener("touchdown",e=>e.stopPropagation()),this.container.addEventListener("mousedown",e=>e.stopPropagation()),this.container.style.top="-1000px",this.container.style.left="-1000px",this.show(t)}handleEvent(e){"transitionend"===e.type&&this.__onTransitionEnd(e)}destroy(){delete this.state.data,super.destroy()}toggle(){throw new es("Tooltip cannot be toggled")}show(e){if(0!==this.state.state)throw new es("Initialized tooltip cannot be re-initialized");e.className&&A(this.container,e.className),e.style&&Object.assign(this.container.style,e.style),this.state.state=3,this.update(e.content,e),this.state.data=e.data,this.state.state=1,this.viewer.dispatchEvent(new ty(this,this.state.data)),this.__waitImages()}update(e,t){this.content.innerHTML=e;let i=this.container.getBoundingClientRect();this.state.width=i.right-i.left,this.state.height=i.bottom-i.top,this.state.arrow=parseInt(W(this.arrow,"border-top-width"),10),this.state.border=parseInt(W(this.container,"border-top-left-radius"),10),this.move(t??this.state.config),this.__waitImages()}move(e){if(1!==this.state.state&&3!==this.state.state)throw new es("Uninitialized tooltip cannot be moved");e.box=e.box??this.state.config?.box??{width:0,height:0},this.state.config=e;let t=this.container,i=this.arrow,s={posClass:ew(e.position,{allowCenter:!1,cssOrder:!1})||["top","center"],width:this.state.width,height:this.state.height,top:0,left:0,arrowTop:0,arrowLeft:0};this.__computeTooltipPosition(s,e);let o=null,n=null;if(s.top<0?o="bottom":s.top+s.height>this.viewer.state.size.height&&(o="top"),s.left<0?n="right":s.left+s.width>this.viewer.state.size.width&&(n="left"),n||o){let r=eg(s.posClass);o&&(s.posClass[r?0:1]=o),n&&(s.posClass[r?1:0]=n),this.__computeTooltipPosition(s,e)}t.style.top=s.top+"px",t.style.left=s.left+"px",i.style.top=s.arrowTop+"px",i.style.left=s.arrowLeft+"px";let a=s.posClass.join("-");a!==this.state.pos&&(t.classList.remove(`psv-tooltip--${this.state.pos}`),this.state.pos=a,t.classList.add(`psv-tooltip--${this.state.pos}`))}hide(){this.container.classList.remove("psv-tooltip--visible"),this.state.state=2,this.viewer.dispatchEvent(new eq(this.state.data))}__onTransitionEnd(e){if("transform"===e.propertyName)switch(this.state.state){case 1:this.container.classList.add("psv-tooltip--visible"),this.state.state=3;break;case 2:this.state.state=0,this.destroy()}}__computeTooltipPosition(e,t){let i=this.state.arrow,s=t.top,o=e.height,n=t.left,r=e.width,a=i+this.state.border,h=t.box.width/2+2*i,l=t.box.height/2+2*i;switch(e.posClass.join("-")){case"top-left":e.top=s-l-o,e.left=n+a-r,e.arrowTop=o,e.arrowLeft=r-a-i;break;case"top-center":e.top=s-l-o,e.left=n-r/2,e.arrowTop=o,e.arrowLeft=r/2-i;break;case"top-right":e.top=s-l-o,e.left=n-a,e.arrowTop=o,e.arrowLeft=i;break;case"bottom-left":e.top=s+l,e.left=n+a-r,e.arrowTop=-(2*i),e.arrowLeft=r-a-i;break;case"bottom-center":e.top=s+l,e.left=n-r/2,e.arrowTop=-(2*i),e.arrowLeft=r/2-i;break;case"bottom-right":e.top=s+l,e.left=n-a,e.arrowTop=-(2*i),e.arrowLeft=i;break;case"left-top":e.top=s+a-o,e.left=n-h-r,e.arrowTop=o-a-i,e.arrowLeft=r;break;case"center-left":e.top=s-o/2,e.left=n-h-r,e.arrowTop=o/2-i,e.arrowLeft=r;break;case"left-bottom":e.top=s-a,e.left=n-h-r,e.arrowTop=i,e.arrowLeft=r;break;case"right-top":e.top=s+a-o,e.left=n+h,e.arrowTop=o-a-i,e.arrowLeft=-(2*i);break;case"center-right":e.top=s-o/2,e.left=n+h,e.arrowTop=o/2-i,e.arrowLeft=-(2*i);break;case"right-bottom":e.top=s-a,e.left=n+h,e.arrowTop=i,e.arrowLeft=-(2*i)}}__waitImages(){let e=this.content.querySelectorAll("img");if(e.length>0){let t=[];e.forEach(e=>{e.complete||t.push(new Promise(t=>{e.onload=t,e.onerror=t}))}),t.length&&Promise.all(t).then(()=>{if(1===this.state.state||3===this.state.state){let e=this.container.getBoundingClientRect();this.state.width=e.right-e.left,this.state.height=e.bottom-e.top,this.move(this.state.config)}})}}},im=t,i$={enabled:!0,maxItems:10,ttl:600,items:{},purgeInterval:null,init(){im.Cache.enabled&&(eh("ThreeJS cache should be disabled"),im.Cache.enabled=!1),!this.purgeInterval&&this.enabled&&(this.purgeInterval=setInterval(()=>this.purge(),6e4))},add(e,t,i){this.enabled&&t&&(this.items[t]=this.items[t]??{files:{},lastAccess:null},this.items[t].files[e]=i,this.items[t].lastAccess=Date.now())},get(e,t){if(this.enabled&&t&&this.items[t])return this.items[t].lastAccess=Date.now(),this.items[t].files[e]},remove(e,t){this.enabled&&t&&this.items[t]&&(delete this.items[t].files[e],0===Object.keys(this.items[t].files).length&&delete this.items[t])},purge(){Object.entries(this.items).sort(([,e],[,t])=>t.lastAccess-e.lastAccess).forEach(([e,{lastAccess:t}],i)=>{i>0&&(Date.now()-t>=1e3*this.ttl||i>=this.maxItems)&&delete this.items[e]})}},iw=t,ig=class{constructor(e){this.viewer=e,this.config=e.config,this.state=e.state}destroy(){}},i_=new iw.Vector3,iy=new iw.Euler(0,0,0,"ZXY"),ib=class extends ig{constructor(e){super(e)}fovToZoomLevel(e){let t=Math.round((e-this.config.minFov)/(this.config.maxFov-this.config.minFov)*100);return t-2*(t-50)}zoomLevelToFov(e){return this.config.maxFov+e/100*(this.config.minFov-this.config.maxFov)}vFovToHFov(e){return iw.MathUtils.radToDeg(2*Math.atan(Math.tan(iw.MathUtils.degToRad(e)/2)*this.state.aspect))}getAnimationProperties(e,t,i){let s=!J(t),o=!J(i),n={},r=null;if(s){let a=this.viewer.getPosition(),h=O(a.yaw,t.yaw);n.yaw={start:a.yaw,end:a.yaw+h},n.pitch={start:a.pitch,end:t.pitch},r=e_(e,S(a,t))}if(o){let l=this.viewer.getZoomLevel();n.zoom={start:l,end:i},null===r&&(r=e_(e,Math.PI/4*Math.abs(i-l)/100))}return r=null===r?"number"==typeof e?e:p:Math.max(p,r),{duration:r,properties:n}}textureCoordsToSphericalCoords(e){if(!this.state.textureData?.panoData)throw new es("Current adapter does not support texture coordinates or no texture has been loaded");let t=this.viewer.adapter.textureCoordsToSphericalCoords(e,this.state.textureData.panoData);return iy.equals(this.viewer.renderer.panoramaPose)&&iy.equals(this.viewer.renderer.sphereCorrection)?t:(this.sphericalCoordsToVector3(t,i_),i_.applyEuler(this.viewer.renderer.panoramaPose),i_.applyEuler(this.viewer.renderer.sphereCorrection),this.vector3ToSphericalCoords(i_))}sphericalCoordsToTextureCoords(e){if(!this.state.textureData?.panoData)throw new es("Current adapter does not support texture coordinates or no texture has been loaded");return iy.equals(this.viewer.renderer.panoramaPose)&&iy.equals(this.viewer.renderer.sphereCorrection)||(this.sphericalCoordsToVector3(e,i_),eE(i_,this.viewer.renderer.sphereCorrection),eE(i_,this.viewer.renderer.panoramaPose),e=this.vector3ToSphericalCoords(i_)),this.viewer.adapter.sphericalCoordsToTextureCoords(e,this.state.textureData.panoData)}sphericalCoordsToVector3(e,t,i=f){return t||(t=new iw.Vector3),t.x=-(i*Math.cos(e.pitch))*Math.sin(e.yaw),t.y=i*Math.sin(e.pitch),t.z=i*Math.cos(e.pitch)*Math.cos(e.yaw),t}vector3ToSphericalCoords(e){let t=Math.acos(e.y/Math.sqrt(e.x*e.x+e.y*e.y+e.z*e.z)),i=Math.atan2(e.x,e.z);return{yaw:i<0?-i:2*Math.PI-i,pitch:Math.PI/2-t}}viewerCoordsToVector3(e){let t=this.viewer.renderer.getIntersections(e).filter(e=>e.object.userData[_]);return t.length?t[0].point:null}viewerCoordsToSphericalCoords(e){let t=this.viewerCoordsToVector3(e);return t?this.vector3ToSphericalCoords(t):null}vector3ToViewerCoords(e){let t=e.clone();return t.project(this.viewer.renderer.camera),{x:Math.round((t.x+1)/2*this.state.size.width),y:Math.round((1-t.y)/2*this.state.size.height)}}sphericalCoordsToViewerCoords(e){return this.sphericalCoordsToVector3(e,i_),this.vector3ToViewerCoords(i_)}isPointVisible(e){let t,i;if(e instanceof iw.Vector3)t=e,i=this.vector3ToViewerCoords(e);else{if(!el(e))return!1;t=this.sphericalCoordsToVector3(e,i_),i=this.vector3ToViewerCoords(t)}return t.dot(this.viewer.state.direction)>0&&i.x>=0&&i.x<=this.viewer.state.size.width&&i.y>=0&&i.y<=this.viewer.state.size.height}cleanPosition(e){return"yaw"in e&&"pitch"in e?{yaw:ey(e.yaw),pitch:ey(e.pitch,!this.state.littlePlanet)}:this.textureCoordsToSphericalCoords(e)}cleanSphereCorrection(e){return{pan:ey(e?.pan||0),tilt:ey(e?.tilt||0,!0),roll:ey(e?.roll||0,!0,!1)}}cleanPanoramaPose(e){return{pan:iw.MathUtils.degToRad(e?.poseHeading||0),tilt:iw.MathUtils.degToRad(e?.posePitch||0),roll:iw.MathUtils.degToRad(e?.poseRoll||0)}}},i0=t,iE=class e{constructor(){this.$=e.IDLE}is(...e){return e.some(e=>this.$&e)}set(e){this.$=e}add(e){this.$|=e}remove(e){this.$&=~e}};iE.IDLE=0,iE.CLICK=1,iE.MOVING=2,iE.INERTIA=4;var i8=iE,ix=class extends ig{constructor(e){super(e),this.data={startMouseX:0,startMouseY:0,mouseX:0,mouseY:0,mouseHistory:[],pinchDist:0,ctrlKeyDown:!1,dblclickData:null,dblclickTimeout:null,longtouchTimeout:null,twofingersTimeout:null,ctrlZoomTimeout:null},this.step=new i8,this.keyHandler=new ek,this.resizeObserver=new ResizeObserver(B(()=>this.viewer.autoSize(),50)),this.moveThreshold=u*tH.pixelRatio}init(){window.addEventListener("keydown",this,{passive:!1}),window.addEventListener("keyup",this),this.viewer.container.addEventListener("mousedown",this),window.addEventListener("mousemove",this,{passive:!1}),window.addEventListener("mouseup",this),this.viewer.container.addEventListener("touchstart",this,{passive:!1}),window.addEventListener("touchmove",this,{passive:!1}),window.addEventListener("touchend",this,{passive:!1}),this.viewer.container.addEventListener("wheel",this,{passive:!1}),document.addEventListener(tH.fullscreenEvent,this),this.resizeObserver.observe(this.viewer.container)}destroy(){window.removeEventListener("keydown",this),window.removeEventListener("keyup",this),this.viewer.container.removeEventListener("mousedown",this),window.removeEventListener("mousemove",this),window.removeEventListener("mouseup",this),this.viewer.container.removeEventListener("touchstart",this),window.removeEventListener("touchmove",this),window.removeEventListener("touchend",this),this.viewer.container.removeEventListener("wheel",this),document.removeEventListener(tH.fullscreenEvent,this),this.resizeObserver.disconnect(),clearTimeout(this.data.dblclickTimeout),clearTimeout(this.data.longtouchTimeout),clearTimeout(this.data.twofingersTimeout),clearTimeout(this.data.ctrlZoomTimeout),super.destroy()}handleEvent(e){switch(e.type){case"keydown":this.__onKeyDown(e);break;case"keyup":this.__onKeyUp();break;case"mousemove":this.__onMouseMove(e);break;case"mouseup":this.__onMouseUp(e);break;case"touchmove":this.__onTouchMove(e);break;case"touchend":this.__onTouchEnd(e);break;case tH.fullscreenEvent:this.__onFullscreenChange()}if(!U(e.target,"."+y))switch(e.type){case"mousedown":this.__onMouseDown(e);break;case"touchstart":this.__onTouchStart(e);break;case"wheel":this.__onMouseWheel(e)}}__onKeyDown(e){if(this.config.mousewheelCtrlKey&&(this.data.ctrlKeyDown=e.key===x.Control,this.data.ctrlKeyDown&&(clearTimeout(this.data.ctrlZoomTimeout),this.viewer.overlay.hide(E.CTRL_ZOOM))),!this.viewer.dispatchEvent(new eJ(e.key))||!this.state.keyboardEnabled)return;let t=this.config.keyboardActions?.[e.key];if("function"==typeof t)t(this.viewer),e.preventDefault();else if(t&&!this.keyHandler.pending){switch("ZOOM_IN"!==t&&"ZOOM_OUT"!==t&&this.viewer.stopAll(),t){case"ROTATE_UP":this.viewer.dynamics.position.roll({pitch:!1});break;case"ROTATE_DOWN":this.viewer.dynamics.position.roll({pitch:!0});break;case"ROTATE_RIGHT":this.viewer.dynamics.position.roll({yaw:!1});break;case"ROTATE_LEFT":this.viewer.dynamics.position.roll({yaw:!0});break;case"ZOOM_IN":this.viewer.dynamics.zoom.roll(!1);break;case"ZOOM_OUT":this.viewer.dynamics.zoom.roll(!0)}this.keyHandler.down(),e.preventDefault()}}__onKeyUp(){this.data.ctrlKeyDown=!1,this.state.keyboardEnabled&&this.keyHandler.up(()=>{this.viewer.dynamics.position.stop(),this.viewer.dynamics.zoom.stop(),this.viewer.resetIdleTimer()})}__onMouseDown(e){this.step.add(i8.CLICK),this.data.startMouseX=e.clientX,this.data.startMouseY=e.clientY}__onMouseUp(e){this.step.is(i8.CLICK,i8.MOVING)&&this.__stopMove(e.clientX,e.clientY,e.target,2===e.button)}__onMouseMove(e){this.config.mousemove&&this.step.is(i8.CLICK,i8.MOVING)&&(e.preventDefault(),this.__doMove(e.clientX,e.clientY)),this.__handleObjectsEvents(e)}__onTouchStart(e){1===e.touches.length?(this.step.add(i8.CLICK),this.data.startMouseX=e.touches[0].clientX,this.data.startMouseY=e.touches[0].clientY,this.data.longtouchTimeout||(this.data.longtouchTimeout=setTimeout(()=>{let t=e.touches[0];this.__stopMove(t.clientX,t.clientY,t.target,!0),this.data.longtouchTimeout=null},m))):2===e.touches.length&&(this.step.set(i8.IDLE),this.__cancelLongTouch(),this.config.mousemove&&(this.__cancelTwoFingersOverlay(),this.__startMoveZoom(e),e.preventDefault()))}__onTouchEnd(e){if(this.__cancelLongTouch(),this.step.is(i8.CLICK,i8.MOVING)){if(e.preventDefault(),this.__cancelTwoFingersOverlay(),1===e.touches.length)this.__stopMove(this.data.mouseX,this.data.mouseY);else if(0===e.touches.length){let t=e.changedTouches[0];this.__stopMove(t.clientX,t.clientY,t.target)}}}__onTouchMove(e){if(this.__cancelLongTouch(),this.config.mousemove){if(1===e.touches.length){if(this.config.touchmoveTwoFingers)this.step.is(i8.CLICK)&&!this.data.twofingersTimeout&&(this.data.twofingersTimeout=setTimeout(()=>{this.viewer.overlay.show({id:E.TWO_FINGERS,image:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path fill="currentColor" d="M33.38 33.2a1.96 1.96 0 0 0 1.5-3.23 10.61 10.61 0 0 1 7.18-17.51c.7-.06 1.31-.49 1.61-1.12a13.02 13.02 0 0 1 11.74-7.43c7.14 0 12.96 5.8 12.96 12.9 0 3.07-1.1 6.05-3.1 8.38-.7.82-.61 2.05.21 2.76.83.7 2.07.6 2.78-.22a16.77 16.77 0 0 0 4.04-10.91C72.3 7.54 64.72 0 55.4 0a16.98 16.98 0 0 0-14.79 8.7 14.6 14.6 0 0 0-12.23 14.36c0 3.46 1.25 6.82 3.5 9.45.4.45.94.69 1.5.69m45.74 43.55a22.13 22.13 0 0 1-5.23 12.4c-4 4.55-9.53 6.86-16.42 6.86-12.6 0-20.1-10.8-20.17-10.91a1.82 1.82 0 0 0-.08-.1c-5.3-6.83-14.55-23.82-17.27-28.87-.05-.1 0-.21.02-.23a6.3 6.3 0 0 1 8.24 1.85l9.38 12.59a1.97 1.97 0 0 0 3.54-1.17V25.34a4 4 0 0 1 1.19-2.87 3.32 3.32 0 0 1 2.4-.95c1.88.05 3.4 1.82 3.4 3.94v24.32a1.96 1.96 0 0 0 3.93 0v-33.1a3.5 3.5 0 0 1 7 0v35.39a1.96 1.96 0 0 0 3.93 0v-.44c.05-2.05 1.6-3.7 3.49-3.7 1.93 0 3.5 1.7 3.5 3.82v5.63c0 .24.04.48.13.71l.1.26a1.97 1.97 0 0 0 3.76-.37c.33-1.78 1.77-3.07 3.43-3.07 1.9 0 3.45 1.67 3.5 3.74l-1.77 18.1zM77.39 51c-1.25 0-2.45.32-3.5.9v-.15c0-4.27-3.33-7.74-7.42-7.74-1.26 0-2.45.33-3.5.9V16.69a7.42 7.42 0 0 0-14.85 0v1.86a7 7 0 0 0-3.28-.94 7.21 7.21 0 0 0-5.26 2.07 7.92 7.92 0 0 0-2.38 5.67v37.9l-5.83-7.82a10.2 10.2 0 0 0-13.35-2.92 4.1 4.1 0 0 0-1.53 5.48C20 64.52 28.74 80.45 34.07 87.34c.72 1.04 9.02 12.59 23.4 12.59 7.96 0 14.66-2.84 19.38-8.2a26.06 26.06 0 0 0 6.18-14.6l1.78-18.2v-.2c0-4.26-3.32-7.73-7.42-7.73z"/><!--Created by AomAm from the Noun Project--></svg>\n',title:this.config.lang.twoFingers})},$));else if(this.step.is(i8.CLICK,i8.MOVING)){e.preventDefault();let t=e.touches[0];this.__doMove(t.clientX,t.clientY)}}else this.__doMoveZoom(e),this.__cancelTwoFingersOverlay()}}__cancelLongTouch(){this.data.longtouchTimeout&&(clearTimeout(this.data.longtouchTimeout),this.data.longtouchTimeout=null)}__cancelTwoFingersOverlay(){this.config.touchmoveTwoFingers&&(this.data.twofingersTimeout&&(clearTimeout(this.data.twofingersTimeout),this.data.twofingersTimeout=null),this.viewer.overlay.hide(E.TWO_FINGERS))}__onMouseWheel(e){if(!this.config.mousewheel||!e.deltaY)return;if(this.config.mousewheelCtrlKey&&!this.data.ctrlKeyDown){this.viewer.overlay.show({id:E.CTRL_ZOOM,image:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="10 17 79 79"><path fill="currentColor" d="M38.1 29.27c-.24 0-.44.2-.44.45v10.7a.45.45 0 00.9 0v-10.7c0-.25-.2-.45-.45-.45zm10.2 26.66a11.54 11.54 0 01-8.48-6.14.45.45 0 10-.8.41 12.45 12.45 0 009.22 6.62.45.45 0 00.07-.9zm24.55-13.08a23.04 23.04 0 00-22.56-23v7.07l-.01.05a2.83 2.83 0 012.39 2.78v14.03l.09-.02h8.84v-9.22a.45.45 0 11.9 0v9.22h10.35v-.9zm0 27.33V44.66H62.5c-.02 2.01-.52 4-1.47 5.76a.45.45 0 01-.61.18.45.45 0 01-.19-.61 11.54 11.54 0 001.36-5.33h-8.83l-.1-.01a2.83 2.83 0 01-2.83 2.84h-.04-.04a2.83 2.83 0 01-2.83-2.83v-14.9a2.82 2.82 0 012.47-2.8v-7.11a23.04 23.04 0 00-22.57 23v.91h14.72V29.88a8.2 8.2 0 015.02-7.57c.22-.1.5.01.59.24.1.23-.01.5-.24.6a7.3 7.3 0 00-4.47 6.73v13.88h3.9a.45.45 0 110 .9h-3.9v.15a7.32 7.32 0 0011.23 6.17.45.45 0 01.49.76 8.22 8.22 0 01-12.62-6.93v-.15H26.82v25.52a23.04 23.04 0 0023.01 23.01 23.04 23.04 0 0023.02-23.01zm1.8-27.33v27.33A24.85 24.85 0 0149.84 95a24.85 24.85 0 01-24.82-24.82V42.85a24.85 24.85 0 0124.82-24.82 24.85 24.85 0 0124.83 24.82zM57.98 29.88v9.36a.45.45 0 11-.9 0v-9.36a7.28 7.28 0 00-3.4-6.17.45.45 0 01.49-.76 8.18 8.18 0 013.8 6.93z"/><!-- Created by Icon Island from the Noun Project --></svg>\n',title:this.config.lang.ctrlZoom}),clearTimeout(this.data.ctrlZoomTimeout),this.data.ctrlZoomTimeout=setTimeout(()=>this.viewer.overlay.hide(E.CTRL_ZOOM),w);return}e.preventDefault(),e.stopPropagation();let t=e.deltaY/Math.abs(e.deltaY)*5*this.config.zoomSpeed;0!==t&&this.viewer.dynamics.zoom.step(-t,5)}__onFullscreenChange(){let e=this.viewer.isFullscreenEnabled();"fullscreen"===this.config.keyboard&&(e?this.viewer.startKeyboardControl():this.viewer.stopKeyboardControl()),this.viewer.dispatchEvent(new eX(e))}__resetMove(){this.step.set(i8.IDLE),this.data.mouseX=0,this.data.mouseY=0,this.data.startMouseX=0,this.data.startMouseY=0,this.data.mouseHistory.length=0}__startMoveZoom(e){this.viewer.stopAll(),this.__resetMove();let t=F(e);this.step.set(i8.MOVING),({distance:this.data.pinchDist,center:{x:this.data.mouseX,y:this.data.mouseY}}=t),this.__logMouseMove(this.data.mouseX,this.data.mouseY)}__stopMove(e,t,i,s=!1){this.step.is(i8.MOVING)?this.config.moveInertia?(this.__logMouseMove(e,t),this.__stopMoveInertia(e,t)):(this.__resetMove(),this.viewer.resetIdleTimer()):(this.step.is(i8.CLICK)&&!this.__moveThresholdReached(e,t)&&this.__doClick(e,t,i,s),this.step.remove(i8.CLICK),this.step.is(i8.INERTIA)||(this.__resetMove(),this.viewer.resetIdleTimer()))}__stopMoveInertia(e,t){let i=new i0.SplineCurve(this.data.mouseHistory.map(([,e,t])=>new i0.Vector2(e,t))),s=i.getTangent(1),o=this.data.mouseHistory.reduce(({total:e,prev:t},i)=>({total:t?e+I({x:t[1],y:t[2]},{x:i[1],y:i[2]})/(i[0]-t[0]):0,prev:i}),{total:0,prev:null}).total/this.data.mouseHistory.length;if(!o){this.__resetMove(),this.viewer.resetIdleTimer();return}this.step.set(i8.INERTIA);let n=e,r=t;this.state.animation=new eC({properties:{speed:{start:o,end:0}},duration:1e3,easing:"outQuad",onTick:e=>{n+=e.speed*s.x*3*tH.pixelRatio,r+=e.speed*s.y*3*tH.pixelRatio,this.__applyMove(n,r)}}),this.state.animation.then(e=>{this.state.animation=null,e&&(this.__resetMove(),this.viewer.resetIdleTimer())})}__doClick(e,t,i,s=!1){let o=this.viewer.container.getBoundingClientRect(),n=e-o.left,r=t-o.top,a=this.viewer.renderer.getIntersections({x:n,y:r}),h=a.find(e=>e.object.userData[_]);if(h){let l=this.viewer.dataHelper.vector3ToSphericalCoords(h.point),c={rightclick:s,target:i,clientX:e,clientY:t,viewerX:n,viewerY:r,yaw:l.yaw,pitch:l.pitch,objects:a.map(e=>e.object).filter(e=>!e.userData[_])};try{let d=this.viewer.dataHelper.sphericalCoordsToTextureCoords(c);Object.assign(c,d)}catch(p){}this.data.dblclickTimeout?(Math.abs(this.data.dblclickData.clientX-c.clientX)<this.moveThreshold&&Math.abs(this.data.dblclickData.clientY-c.clientY)<this.moveThreshold&&this.viewer.dispatchEvent(new eW(this.data.dblclickData)),clearTimeout(this.data.dblclickTimeout),this.data.dblclickTimeout=null,this.data.dblclickData=null):(this.viewer.dispatchEvent(new eN(c)),this.data.dblclickData=q(c),this.data.dblclickTimeout=setTimeout(()=>{this.data.dblclickTimeout=null,this.data.dblclickData=null},v))}}__handleObjectsEvents(e){if(!Q(this.state.objectsObservers)&&N(e.target,this.viewer.container)){let t=V(this.viewer.container),i={x:e.clientX-t.x,y:e.clientY-t.y},s=this.viewer.renderer.getIntersections(i),o=(t,s,o)=>{this.viewer.dispatchEvent(new o(e,t,i,s))};for(let[n,r]of Object.entries(this.state.objectsObservers)){let a=s.find(e=>e.object.userData[n]);a?(r&&a.object!==r&&(o(r,n,t2),this.state.objectsObservers[n]=null),r?o(a.object,n,tM):(this.state.objectsObservers[n]=a.object,o(a.object,n,tL))):r&&(o(r,n,t2),this.state.objectsObservers[n]=null)}}}__doMove(e,t){this.step.is(i8.CLICK)&&this.__moveThresholdReached(e,t)?(this.viewer.stopAll(),this.__resetMove(),this.step.set(i8.MOVING),this.data.mouseX=e,this.data.mouseY=t,this.__logMouseMove(e,t)):this.step.is(i8.MOVING)&&(this.__applyMove(e,t),this.__logMouseMove(e,t))}__moveThresholdReached(e,t){return Math.abs(e-this.data.startMouseX)>=this.moveThreshold||Math.abs(t-this.data.startMouseY)>=this.moveThreshold}__applyMove(e,t){let i={yaw:this.config.moveSpeed*((e-this.data.mouseX)/this.state.size.width)*i0.MathUtils.degToRad(this.state.littlePlanet?90:this.state.hFov),pitch:this.config.moveSpeed*((t-this.data.mouseY)/this.state.size.height)*i0.MathUtils.degToRad(this.state.littlePlanet?90:this.state.vFov)},s=this.viewer.getPosition();this.viewer.rotate({yaw:s.yaw-i.yaw,pitch:s.pitch+i.pitch}),this.data.mouseX=e,this.data.mouseY=t}__doMoveZoom(e){if(this.step.is(i8.MOVING)){e.preventDefault();let t=F(e),i=(t.distance-this.data.pinchDist)/tH.pixelRatio*this.config.zoomSpeed;this.viewer.zoom(this.viewer.getZoomLevel()+i),this.__doMove(t.center.x,t.center.y),this.data.pinchDist=t.distance}}__logMouseMove(e,t){let i=Date.now(),s=this.data.mouseHistory.length?this.data.mouseHistory[this.data.mouseHistory.length-1]:[0,-1,-1];s[1]===e&&s[2]===t?s[0]=i:i===s[0]?(s[1]=e,s[2]=t):this.data.mouseHistory.push([i,e,t]);let o=null;for(let n=0;n<this.data.mouseHistory.length;)this.data.mouseHistory[n][0]<i-g?this.data.mouseHistory.splice(n,1):o&&this.data.mouseHistory[n][0]-o>g/10?(this.data.mouseHistory.splice(0,n),n=0,o=this.data.mouseHistory[n][0]):(o=this.data.mouseHistory[n][0],n++)}},iT=t,i1=new iT.Vector2,iC=new iT.Matrix4,iL=new iT.Box3,i3=class extends ig{constructor(e){super(e),this.frustumNeedsUpdate=!0,this.renderer=new iT.WebGLRenderer(this.config.rendererParameters),this.renderer.setPixelRatio(tH.pixelRatio),this.renderer.outputColorSpace=iT.LinearSRGBColorSpace,this.renderer.domElement.className="psv-canvas",this.scene=new iT.Scene,this.camera=new iT.PerspectiveCamera(50,16/9,.1,2*f),this.camera.matrixWorldAutoUpdate=!1,this.mesh=this.viewer.adapter.createMesh(),this.mesh.userData={[_]:!0},this.meshContainer=new iT.Group,this.meshContainer.add(this.mesh),this.scene.add(this.meshContainer),this.raycaster=new iT.Raycaster,this.frustum=new iT.Frustum,this.container=document.createElement("div"),this.container.className="psv-canvas-container",this.container.appendChild(this.renderer.domElement),this.viewer.container.appendChild(this.container),this.viewer.addEventListener(t0.type,this),this.viewer.addEventListener(tT.type,this),this.viewer.addEventListener(tl.type,this),this.viewer.addEventListener(eU.type,this),this.hide()}get panoramaPose(){return this.mesh.rotation}get sphereCorrection(){return this.meshContainer.rotation}init(){this.show(),this.renderer.setAnimationLoop(e=>this.__renderLoop(e))}destroy(){this.renderer.setAnimationLoop(null),this.cleanScene(this.scene),this.viewer.container.removeChild(this.container),this.viewer.removeEventListener(t0.type,this),this.viewer.removeEventListener(tT.type,this),this.viewer.removeEventListener(tl.type,this),this.viewer.removeEventListener(eU.type,this),super.destroy()}handleEvent(e){switch(e.type){case t0.type:this.__onSizeUpdated();break;case tT.type:this.__onZoomUpdated();break;case tl.type:this.__onPositionUpdated();break;case eU.type:e.containsOptions("fisheye")&&this.__onPositionUpdated()}}hide(){this.container.style.opacity="0"}show(){this.container.style.opacity="1"}setCustomRenderer(e){e?this.customRenderer=e(this.renderer):this.customRenderer=null,this.viewer.needsUpdate()}__onSizeUpdated(){this.renderer.setSize(this.state.size.width,this.state.size.height),this.camera.aspect=this.state.aspect,this.camera.updateProjectionMatrix(),this.viewer.needsUpdate(),this.frustumNeedsUpdate=!0}__onZoomUpdated(){this.camera.fov=this.state.vFov,this.camera.updateProjectionMatrix(),this.viewer.needsUpdate(),this.frustumNeedsUpdate=!0}__onPositionUpdated(){this.camera.position.set(0,0,0),this.camera.lookAt(this.state.direction),this.config.fisheye&&this.camera.position.copy(this.state.direction).multiplyScalar(this.config.fisheye/2).negate(),this.camera.updateMatrixWorld(),this.viewer.needsUpdate(),this.frustumNeedsUpdate=!0}__renderLoop(e){let t=this.timestamp?e-this.timestamp:0;this.timestamp=e,this.viewer.dispatchEvent(new e4(e,t)),this.viewer.dynamics.update(t),(this.state.needsUpdate||this.state.continuousUpdateCount>0)&&((this.customRenderer||this.renderer).render(this.scene,this.camera),this.viewer.dispatchEvent(new tu),this.state.needsUpdate=!1)}setTexture(e){this.state.textureData&&this.viewer.adapter.disposeTexture(this.state.textureData),this.state.textureData=e,this.viewer.adapter.setTexture(this.mesh,e),this.viewer.needsUpdate()}setPanoramaPose(e,t=this.mesh){let i=this.viewer.dataHelper.cleanPanoramaPose(e);t.rotation.set(-i.tilt,-i.pan,-i.roll,"ZXY")}setSphereCorrection(e,t=this.meshContainer){let i=this.viewer.dataHelper.cleanSphereCorrection(e);t.rotation.set(i.tilt,i.pan,i.roll,"ZXY")}transition(e,t){let i=!J(t.position),s=!J(t.zoom),o=new ez(i?this.viewer.dataHelper.cleanPosition(t.position):void 0,t.zoom);this.viewer.dispatchEvent(o);let n=new iT.Group,r=this.viewer.adapter.createMesh(.5);if(this.viewer.adapter.setTexture(r,e,!0),this.viewer.adapter.setTextureOpacity(r,0),this.setPanoramaPose(e.panoData,r),this.setSphereCorrection(t.sphereCorrection,n),i&&"fade-only"===t.transition){let a=this.viewer.getPosition(),h=new iT.Vector3(0,1,0);n.rotateOnWorldAxis(h,o.position.yaw-a.yaw);let l=new iT.Vector3(0,1,0).cross(this.camera.getWorldDirection(new iT.Vector3)).normalize();n.rotateOnWorldAxis(l,o.position.pitch-a.pitch)}n.add(r),this.scene.add(n),this.renderer.setRenderTarget(new iT.WebGLRenderTarget),this.renderer.render(this.scene,this.camera),this.renderer.setRenderTarget(null);let{duration:c,properties:d}=this.viewer.dataHelper.getAnimationProperties(t.speed,!0===t.transition?o.position:null,o.zoomLevel),p=new eC({properties:{...d,opacity:{start:0,end:1}},duration:c,easing:"inOutCubic",onTick:e=>{this.viewer.adapter.setTextureOpacity(r,e.opacity),i&&!0===t.transition&&this.viewer.dynamics.position.setValue({yaw:e.yaw,pitch:e.pitch}),s&&this.viewer.dynamics.zoom.setValue(e.zoom),this.viewer.needsUpdate()}});return p.then(s=>{s?(this.setTexture(e),this.viewer.adapter.setTextureOpacity(this.mesh,1),this.setPanoramaPose(e.panoData),this.setSphereCorrection(t.sphereCorrection),i&&"fade-only"===t.transition&&this.viewer.rotate(t.position)):this.viewer.adapter.disposeTexture(e),this.scene.remove(n),r.geometry.dispose(),r.geometry=null}),p}getIntersections(e){i1.x=2*e.x/this.state.size.width-1,i1.y=-2*e.y/this.state.size.height+1,this.raycaster.setFromCamera(i1,this.camera);let t=this.raycaster.intersectObjects(this.scene.children,!0).filter(e=>e.object.isMesh&&!!e.object.userData);return this.customRenderer?.getIntersections&&t.push(...this.customRenderer.getIntersections(this.raycaster,i1)),t}isObjectVisible(e){if(!e)return!1;if(this.frustumNeedsUpdate&&(iC.multiplyMatrices(this.camera.projectionMatrix,this.camera.matrixWorldInverse),this.frustum.setFromProjectionMatrix(iC),this.frustumNeedsUpdate=!1),e.isVector3)return this.frustum.containsPoint(e);if(e.isMesh&&e.geometry){let t=e;return t.geometry.boundingBox||t.geometry.computeBoundingBox(),iL.copy(t.geometry.boundingBox).applyMatrix4(t.matrixWorld),this.frustum.intersectsBox(iL)}return!!e.isObject3D&&this.frustum.intersectsObject(e)}addObject(e){this.scene.add(e)}removeObject(e){this.scene.remove(e)}cleanScene(e){let t=e=>{e.map?.dispose(),e.uniforms&&Object.values(e.uniforms).forEach(e=>{e.value?.dispose?.()}),e.dispose()};e.traverse(i=>{i.geometry?.dispose(),i.material&&(Array.isArray(i.material)?i.material.forEach(e=>{t(e)}):t(i.material)),i instanceof iT.Scene||i.dispose?.(),i!==e&&this.cleanScene(i)})}},i2=t,ik=class extends ig{constructor(e){super(e),this.fileLoader=new i2.FileLoader,this.fileLoader.setResponseType("blob"),this.imageLoader=new i2.ImageLoader,this.config.withCredentials&&(this.fileLoader.setWithCredentials(!0),this.imageLoader.setCrossOrigin("use-credentials"))}destroy(){this.abortLoading(),super.destroy()}abortLoading(){}loadFile(e,t,i){let s=i$.get(e,i);if(s){if(s instanceof Blob)return t?.(100),Promise.resolve(s);i$.remove(e,i)}return this.config.requestHeaders&&this.fileLoader.setRequestHeader(this.config.requestHeaders(e)),new Promise((s,o)=>{let n=0;t?.(n),this.fileLoader.load(e,o=>{t?.(n=100),i$.add(e,i,o),s(o)},e=>{if(e.lengthComputable){let i=e.loaded/e.total*100;i>n&&t?.(n=i)}},e=>{o(e)})})}loadImage(e,t,i){let s=i$.get(e,i);return s?(t?.(100),s instanceof Blob)?this.blobToImage(s):Promise.resolve(s):t||this.config.requestHeaders?this.loadFile(e,t,i).then(e=>this.blobToImage(e)):this.imageLoader.loadAsync(e).then(t=>(i$.add(e,i,t),t))}blobToImage(e){return new Promise((t,i)=>{let s=document.createElement("img");s.onload=()=>{URL.revokeObjectURL(s.src),t(s)},s.onerror=i,s.src=URL.createObjectURL(e)})}preloadPanorama(e){return this.viewer.adapter.supportsPreload(e)?this.viewer.adapter.loadTexture(e):Promise.reject(new es("Current adapter does not support preload"))}},iM=t,iI=class extends eS{constructor(e){for(let i of(super(),this.plugins={},this.children=[],this.onResize=B(()=>this.navbar.autoSize(),500),i$.init(),tH.load(),this.state=new class{constructor(){this.ready=!1,this.needsUpdate=!1,this.continuousUpdateCount=0,this.keyboardEnabled=!1,this.direction=new t.Vector3(0,0,f),this.vFov=60,this.hFov=60,this.aspect=1,this.animation=null,this.transitionAnimation=null,this.loadingPromise=null,this.littlePlanet=!1,this.idleTime=-1,this.objectsObservers={},this.size={width:0,height:0}}},this.config=ii(e),this.parent=z(e.container),this.parent[_]=this,this.container=document.createElement("div"),this.container.classList.add("psv-container"),this.parent.appendChild(this.container),ex(this.container,"core"),this.adapter=new this.config.adapter[0](this,this.config.adapter[1]),this.renderer=new i3(this),this.textureLoader=new ik(this),this.eventsHandler=new ix(this),this.dataHelper=new ib(this),this.dynamics=new class extends ig{constructor(e){super(e),this.zoom=new e3(e=>{this.viewer.state.vFov=this.viewer.dataHelper.zoomLevelToFov(e),this.viewer.state.hFov=this.viewer.dataHelper.vFovToHFov(this.viewer.state.vFov),this.viewer.dispatchEvent(new tT(e))},{defaultValue:this.viewer.config.defaultZoomLvl,min:0,max:100,wrap:!1}),this.position=new e2(e=>{this.viewer.dataHelper.sphericalCoordsToVector3(e,this.viewer.state.direction),this.viewer.dispatchEvent(new tl(e))},{yaw:new e3(null,{defaultValue:this.config.defaultYaw,min:0,max:2*Math.PI,wrap:!0}),pitch:new e3(null,{defaultValue:this.config.defaultPitch,min:this.viewer.state.littlePlanet?0:-Math.PI/2,max:this.viewer.state.littlePlanet?2*Math.PI:Math.PI/2,wrap:this.viewer.state.littlePlanet})}),this.updateSpeeds()}updateSpeeds(){this.zoom.setSpeed(50*this.config.zoomSpeed),this.position.setSpeed(iM.MathUtils.degToRad(50*this.config.moveSpeed))}update(e){this.zoom.update(e),this.position.update(e)}}(this),this.adapter.init?.(),this.loader=new il(this),this.navbar=new ih(this),this.panel=new iu(this),this.notification=new ic(this),this.overlay=new id(this),this.resize(this.config.size),this.setCursor(null),eo(tH.isTouchEnabled,e=>{R(this.container,"psv--is-touch",e)}),this.config.plugins.forEach(([e,t])=>{this.plugins[e.id]=new e(this,t)}),Object.values(this.plugins)))i.init?.();this.config.navbar&&this.navbar.setButtons(this.config.navbar),this.state.loadingPromise||(this.config.panorama?this.setPanorama(this.config.panorama):this.loader.show())}destroy(){for(let[e,t]of(this.stopAll(),this.stopKeyboardControl(),this.exitFullscreen(),Object.entries(this.plugins)))t.destroy(),delete this.plugins[e];this.children.slice().forEach(e=>e.destroy()),this.children.length=0,this.eventsHandler.destroy(),this.renderer.destroy(),this.textureLoader.destroy(),this.dataHelper.destroy(),this.adapter.destroy(),this.dynamics.destroy(),this.parent.removeChild(this.container),delete this.parent[_]}init(){this.eventsHandler.init(),this.renderer.init(),this.config.navbar&&this.navbar.show(),"always"===this.config.keyboard&&this.startKeyboardControl(),this.resetIdleTimer(),this.state.ready=!0,this.dispatchEvent(new td)}resetIdleTimer(){this.state.idleTime=performance.now()}disableIdleTimer(){this.state.idleTime=-1}getPlugin(e){if("string"==typeof e)return this.plugins[e];{let t=tQ(e);return t?this.plugins[t.id]:null}}getPosition(){return this.dataHelper.cleanPosition(this.dynamics.position.current)}getZoomLevel(){return this.dynamics.zoom.current}getSize(){return{...this.state.size}}isFullscreenEnabled(){return X(this.parent)}needsUpdate(){this.state.needsUpdate=!0}needsContinuousUpdate(e){e?this.state.continuousUpdateCount++:this.state.continuousUpdateCount>0&&this.state.continuousUpdateCount--}autoSize(){(this.container.clientWidth!==this.state.size.width||this.container.clientHeight!==this.state.size.height)&&(this.state.size.width=Math.round(this.container.clientWidth),this.state.size.height=Math.round(this.container.clientHeight),this.state.aspect=this.state.size.width/this.state.size.height,this.state.hFov=this.dataHelper.vFovToHFov(this.state.vFov),this.dispatchEvent(new t0(this.getSize())),this.onResize())}setPanorama(e,t={}){this.textureLoader.abortLoading(),this.state.transitionAnimation?.cancel(),this.state.ready||["sphereCorrection","panoData"].forEach(e=>{e in t||(t[e]=this.config[e])}),void 0===t.transition&&(t.transition=!0),void 0===t.speed&&(t.speed=d),void 0===t.showLoader&&(t.showLoader=!0),void 0===t.caption&&(t.caption=this.config.caption),void 0===t.description&&(t.description=this.config.description),t.panoData||"function"!=typeof this.config.panoData||(t.panoData=this.config.panoData);let i=!J(t.position),s=!J(t.zoom);(i||s)&&this.stopAll(),this.hideError(),this.resetIdleTimer(),this.config.panorama=e,this.config.caption=t.caption,this.config.description=t.description;let o=t=>{if(this.loader.hide(),this.state.loadingPromise=null,ea(t))return!1;if(!t)return this.navbar.setCaption(this.config.caption),!0;throw this.navbar.setCaption(""),this.showError(this.config.lang.loadError),console.error(t),this.dispatchEvent(new ta(e,t)),t};this.navbar.setCaption(`<em>${this.config.loadingTxt||""}</em>`),(t.showLoader||!this.state.ready)&&this.loader.show(),this.dispatchEvent(new ts(e));let n=this.adapter.loadTexture(this.config.panorama,!0,t.panoData).then(e=>{if(e.panorama!==this.config.panorama)throw this.adapter.disposeTexture(e),er();return e});return t.transition&&this.state.ready&&this.adapter.supportsTransition(this.config.panorama)?this.state.loadingPromise=n.then(e=>(this.loader.hide(),this.dispatchEvent(new tn(e)),this.state.transitionAnimation=this.renderer.transition(e,t),this.state.transitionAnimation)).then(e=>{if(this.state.transitionAnimation=null,!e)throw er()}).then(()=>o(),e=>o(e)):this.state.loadingPromise=n.then(e=>{this.renderer.show(),this.renderer.setTexture(e),this.renderer.setPanoramaPose(e.panoData),this.renderer.setSphereCorrection(t.sphereCorrection),this.state.ready||this.init(),this.dispatchEvent(new tn(e)),s&&this.zoom(t.zoom),i&&this.rotate(t.position)}).then(()=>o(),e=>o(e)),this.state.loadingPromise}setOptions(e){let t={...this.config,...e};for(let[i,s]of Object.entries(e)){if(!(i in tJ)){eh(`Unknown option ${i}`);continue}if(i in ie){eh(ie[i]);continue}switch(i in it&&(s=it[i](s,{rawConfig:t,defValue:tJ[i]})),this.config[i]=s,i){case"mousemove":this.state.cursorOverride||this.setCursor(null);break;case"caption":this.navbar.setCaption(this.config.caption);break;case"size":this.resize(this.config.size);break;case"sphereCorrection":this.renderer.setSphereCorrection(this.config.sphereCorrection);break;case"navbar":case"lang":this.navbar.setButtons(this.config.navbar);break;case"moveSpeed":case"zoomSpeed":this.dynamics.updateSpeeds();break;case"minFov":case"maxFov":this.dynamics.zoom.setValue(this.dataHelper.fovToZoomLevel(this.state.vFov)),this.dispatchEvent(new tT(this.getZoomLevel()));break;case"keyboard":"always"===this.config.keyboard?this.startKeyboardControl():this.stopKeyboardControl()}}this.needsUpdate(),this.dispatchEvent(new eU(Object.keys(e)))}setOption(e,t){this.setOptions({[e]:t})}showError(e){this.overlay.show({id:E.ERROR,image:'<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g opacity=".8" stroke="#797f8f"><path d="M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Z" stroke-width="2.5"/><path d="M24 14v12m0 7v1" stroke-width="2.2" stroke-linecap="round"/></g></svg>\n',title:e,dissmisable:!1})}hideError(){this.overlay.hide(E.ERROR)}rotate(e){let t=new eA(this.dataHelper.cleanPosition(e));this.dispatchEvent(t),!t.defaultPrevented&&this.dynamics.position.setValue(t.position)}zoom(e){this.dynamics.zoom.setValue(e)}zoomIn(e=1){this.dynamics.zoom.step(e)}zoomOut(e=1){this.dynamics.zoom.step(-e)}animate(e){let t=el(e),i=!J(e.zoom),s=new ez(t?this.dataHelper.cleanPosition(e):void 0,e.zoom);if(this.dispatchEvent(s),s.defaultPrevented)return;this.stopAll();let{duration:o,properties:n}=this.dataHelper.getAnimationProperties(e.speed,s.position,s.zoomLevel);return o?(this.state.animation=new eC({properties:n,duration:o,easing:"inOutSine",onTick:e=>{t&&this.dynamics.position.setValue({yaw:e.yaw,pitch:e.pitch}),i&&this.dynamics.zoom.setValue(e.zoom)}}),this.state.animation.then(()=>{this.state.animation=null,this.resetIdleTimer()}),this.state.animation):(t&&this.rotate(s.position),i&&this.zoom(s.zoomLevel),new eC(null))}stopAnimation(){return this.state.animation?(this.state.animation.cancel(),this.state.animation):Promise.resolve()}resize(e){let t=e;["width","height"].forEach(i=>{e&&t[i]&&(/^[0-9.]+$/.test(t[i])&&(t[i]+="px"),this.parent.style[i]=t[i])}),this.autoSize()}enterFullscreen(){this.isFullscreenEnabled()||Y(this.parent)}exitFullscreen(){this.isFullscreenEnabled()&&j()}toggleFullscreen(){this.isFullscreenEnabled()?this.exitFullscreen():this.enterFullscreen()}startKeyboardControl(){this.state.keyboardEnabled=!0}stopKeyboardControl(){this.state.keyboardEnabled=!1}createTooltip(e){return new iv(this,e)}setCursor(e){this.state.cursorOverride=e,e?this.container.style.cursor=e:this.container.style.cursor=this.config.mousemove?"move":"default"}observeObjects(e){this.state.objectsObservers[e]||(this.state.objectsObservers[e]=null)}unobserveObjects(e){delete this.state.objectsObservers[e]}stopAll(){return this.dispatchEvent(new t8),this.disableIdleTimer(),this.stopAnimation()}};h.Cache.enabled=!1,h.ColorManagement.enabled=!1;var iP="5.6.0";((e,t,r,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let h of o(t))n.call(e,h)||void 0===h||i(e,h,{get:()=>t[h],enumerable:!(a=s(t,h))||a.enumerable});return e})(i(e,"__esModule",{value:!0}),a)});