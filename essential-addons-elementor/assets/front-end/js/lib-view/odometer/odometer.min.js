(function(){var u,t,s,d,F,o,l,h,i,p,n,e,c,A,r,a,m,f,g,v=[].slice;function w(t){var e,n,i,o,r,s,a,u,d,l=this;if(this.options=t,this.el=this.options.el,null!=this.el.odometer)return this.el.odometer;for(e in this.el.odometer=this,a=w.options)i=a[e],null==this.options[e]&&(this.options[e]=i);null==(o=this.options).duration&&(o.duration=2e3),this.MAX_VALUES=this.options.duration/(1e3/30)/2|0,this.resetFormat(),this.value=this.cleanValue(null!=(u=this.options.value)?u:""),this.renderInside(),this.render();try{for(r=0,s=(d=["innerHTML","innerText","textContent"]).length;r<s;r++)n=d[r],null!=this.el[n]&&function(e){Object.defineProperty(l.el,e,{get:function(){var t;return"innerHTML"===e?l.inside.outerHTML:null!=(t=l.inside.innerText)?t:l.inside.textContent},set:function(t){return l.update(t)}})}(n)}catch(t){this.watchForMutations()}}u=/^\(?([^)]*)\)?(?:(.)(d+))?$/,e=document.createElement("div").style,d=null!=e.transition||null!=e.webkitTransition||null!=e.mozTransition||null!=e.oTransition,p=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame,t=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver,o=function(t){var e;return(e=document.createElement("div")).innerHTML=t,e.children[0]},i=function(t,e){return t.className=t.className.replace(new RegExp("(^| )"+e.split(" ").join("|")+"( |$)","gi")," ")},F=function(t,e){return i(t,e),t.className+=" "+e},c=function(t,e){var n;return null!=document.createEvent?((n=document.createEvent("HTMLEvents")).initEvent(e,!0,!0),t.dispatchEvent(n)):void 0},h=function(){var t,e;return null!=(t=null!=(e=window.performance)&&"function"==typeof e.now?e.now():void 0)?t:+new Date},n=function(t,e){return null==e&&(e=0),e?(t*=Math.pow(10,e),t+=.5,t=Math.floor(t),t/=Math.pow(10,e)):Math.round(t)},A=function(t){return t<0?Math.ceil(t):Math.floor(t)},a=!(l=function(t){return t-n(t)}),(r=function(){var t,e,n,i,o;if(!a&&null!=window.jQuery){for(a=!0,o=[],e=0,n=(i=["html","text"]).length;e<n;e++)t=i[e],o.push(function(){var n;return n=window.jQuery.fn[t],window.jQuery.fn[t]=function(t){var e;return null==t||null==(null!=(e=this[0])?e.odometer:void 0)?n.apply(this,arguments):this[0].odometer.update(t)}}());return o}})(),setTimeout(r,0),w.prototype.renderInside=function(){return this.inside=document.createElement("div"),this.inside.className="odometer-inside",this.el.innerHTML="",this.el.appendChild(this.inside)},w.prototype.watchForMutations=function(){var n=this;if(null!=t)try{return null==this.observer&&(this.observer=new t(function(t){var e;return e=n.el.innerText,n.renderInside(),n.render(n.value),n.update(e)})),this.watchMutations=!0,this.startWatchingMutations()}catch(t){}},w.prototype.startWatchingMutations=function(){return this.watchMutations?this.observer.observe(this.el,{childList:!0}):void 0},w.prototype.stopWatchingMutations=function(){var t;return null!=(t=this.observer)?t.disconnect():void 0},w.prototype.cleanValue=function(t){var e;return"string"==typeof t&&(t=(t=(t=t.replace(null!=(e=this.format.radix)?e:".","<radix>")).replace(/[.,]/g,"")).replace("<radix>","."),t=parseFloat(t,10)||0),n(t,this.format.precision)},w.prototype.bindTransitionEnd=function(){var t,e,n,i,o,r,s=this;if(!this.transitionEndBound){for(this.transitionEndBound=!0,e=!1,r=[],n=0,i=(o="transitionend webkitTransitionEnd oTransitionEnd otransitionend MSTransitionEnd".split(" ")).length;n<i;n++)t=o[n],r.push(this.el.addEventListener(t,function(){return e||(e=!0,setTimeout(function(){return s.render(),e=!1,c(s.el,"odometerdone")},0)),!0},!1));return r}},w.prototype.resetFormat=function(){var t,e,n,i,o,r,s,a;if(t=(t=null!=(s=this.options.format)?s:"(,ddd).dd")||"d",!(n=u.exec(t)))throw new Error("Odometer: Unparsable digit format");return r=(a=n.slice(1,4))[0],o=a[1],i=(null!=(e=a[2])?e.length:void 0)||0,this.format={repeating:r,radix:o,precision:i}},w.prototype.render=function(t){var e,n,i,o,r,s,a;for(null==t&&(t=this.value),this.stopWatchingMutations(),this.resetFormat(),this.inside.innerHTML="",r=this.options.theme,o=[],s=0,a=(e=this.el.className.split(" ")).length;s<a;s++)(n=e[s]).length&&((i=/^odometer-theme-(.+)$/.exec(n))?r=i[1]:/^odometer(-|$)/.test(n)||o.push(n));return o.push("odometer"),d||o.push("odometer-no-transitions"),r?o.push("odometer-theme-"+r):o.push("odometer-auto-theme"),this.el.className=o.join(" "),this.ribbons={},this.formatDigits(t),this.startWatchingMutations()},w.prototype.formatDigits=function(t){var e,n,i,o,r,s,a,u,d;if(this.digits=[],this.options.formatFunction)for(o=0,s=(u=this.options.formatFunction(t).split("").reverse()).length;o<s;o++)(n=u[o]).match(/0-9/)?((e=this.renderDigit()).querySelector(".odometer-value").innerHTML=n,this.digits.push(e),this.insertDigit(e)):this.addSpacer(n);else for(i=!this.format.precision||!l(t)||!1,r=0,a=(d=t.toString().split("").reverse()).length;r<a;r++)"."===(e=d[r])&&(i=!0),this.addDigit(e,i)},w.prototype.update=function(t){var e,n=this;return(e=(t=this.cleanValue(t))-this.value)?(i(this.el,"odometer-animating-up odometer-animating-down odometer-animating"),F(this.el,0<e?"odometer-animating-up":"odometer-animating-down"),this.stopWatchingMutations(),this.animate(t),this.startWatchingMutations(),setTimeout(function(){return n.el.offsetHeight,F(n.el,"odometer-animating")},0),this.value=t):void 0},w.prototype.renderDigit=function(){return o('<span class="odometer-digit"><span class="odometer-digit-spacer">8</span><span class="odometer-digit-inner"><span class="odometer-ribbon"><span class="odometer-ribbon-inner"><span class="odometer-value"></span></span></span></span></span>')},w.prototype.insertDigit=function(t,e){return null!=e?this.inside.insertBefore(t,e):this.inside.children.length?this.inside.insertBefore(t,this.inside.children[0]):this.inside.appendChild(t)},w.prototype.addSpacer=function(t,e,n){var i;return(i=o('<span class="odometer-formatting-mark"></span>')).innerHTML=t,n&&F(i,n),this.insertDigit(i,e)},w.prototype.addDigit=function(t,e){var n,i,o,r;if(null==e&&(e=!0),"-"===t)return this.addSpacer(t,null,"odometer-negation-mark");if("."===t)return this.addSpacer(null!=(r=this.format.radix)?r:".",null,"odometer-radix-mark");if(e)for(o=!1;;){if(!this.format.repeating.length){if(o)throw new Error("Bad odometer format without digits");this.resetFormat(),o=!0}if(n=this.format.repeating[this.format.repeating.length-1],this.format.repeating=this.format.repeating.substring(0,this.format.repeating.length-1),"d"===n)break;this.addSpacer(n)}return(i=this.renderDigit()).querySelector(".odometer-value").innerHTML=t,this.digits.push(i),this.insertDigit(i)},w.prototype.animate=function(t){return d&&"count"!==this.options.animation?this.animateSlide(t):this.animateCount(t)},w.prototype.animateCount=function(n){var i,o,r,s,a,u=this;if(o=n-this.value)return s=r=h(),i=this.value,(a=function(){var t,e;return h()-s>u.options.duration?(u.value=n,u.render(),void c(u.el,"odometerdone")):(50<(t=h()-r)&&(r=h(),e=t/u.options.duration,i+=o*e,u.render(Math.round(i))),null!=p?p(a):setTimeout(a,50))})()},w.prototype.getDigitCount=function(){var t,e,n,i,o,r;for(t=o=0,r=(i=1<=arguments.length?v.call(arguments,0):[]).length;o<r;t=++o)n=i[t],i[t]=Math.abs(n);return e=Math.max.apply(Math,i),Math.ceil(Math.log(e+1)/Math.log(10))},w.prototype.getFractionalDigitCount=function(){var t,e,n,i,o,r,s;for(e=/^\-?\d*\.(\d*?)0*$/,t=r=0,s=(o=1<=arguments.length?v.call(arguments,0):[]).length;r<s;t=++r)i=o[t],o[t]=i.toString(),n=e.exec(o[t]),o[t]=null==n?0:n[1].length;return Math.max.apply(Math,o)},w.prototype.resetDigits=function(){return this.digits=[],this.ribbons=[],this.inside.innerHTML="",this.resetFormat()},w.prototype.animateSlide=function(t){var e,n,i,o,r,s,a,u,d,l,h,p,c,m,f,g,v,w,M,y,b,T,E,x,S,D,L;if(g=this.value,(u=this.getFractionalDigitCount(g,t))&&(t*=Math.pow(10,u),g*=Math.pow(10,u)),i=t-g){for(this.bindTransitionEnd(),o=this.getDigitCount(g,t),r=[],h=M=e=0;0<=o?M<o:o<M;h=0<=o?++M:--M){if(v=A(g/Math.pow(10,o-h-1)),s=(a=A(t/Math.pow(10,o-h-1)))-v,Math.abs(s)>this.MAX_VALUES){for(l=[],p=s/(this.MAX_VALUES+this.MAX_VALUES*e*.5),n=v;0<s&&n<a||s<0&&a<n;)l.push(Math.round(n)),n+=p;l[l.length-1]!==a&&l.push(a),e++}else l=function(){L=[];for(var t=v;v<=a?t<=a:a<=t;v<=a?t++:t--)L.push(t);return L}.apply(this);for(h=y=0,T=l.length;y<T;h=++y)d=l[h],l[h]=Math.abs(d%10);r.push(l)}for(this.resetDigits(),h=b=0,E=(D=r.reverse()).length;b<E;h=++b)for(l=D[h],this.digits[h]||this.addDigit(" ",u<=h),null==(w=this.ribbons)[h]&&(w[h]=this.digits[h].querySelector(".odometer-ribbon-inner")),this.ribbons[h].innerHTML="",i<0&&(l=l.reverse()),c=S=0,x=l.length;S<x;c=++S)d=l[c],(f=document.createElement("div")).className="odometer-value",f.innerHTML=d,this.ribbons[h].appendChild(f),c===l.length-1&&F(f,"odometer-last-value"),0===c&&F(f,"odometer-first-value");return v<0&&this.addDigit("-"),null!=(m=this.inside.querySelector(".odometer-radix-mark"))&&m.parent.removeChild(m),u?this.addSpacer(this.format.radix,this.digits[u-1],"odometer-radix-mark"):void 0}},(s=w).options=null!=(f=window.odometerOptions)?f:{},setTimeout(function(){var t,e,n,i,o;if(window.odometerOptions){for(t in o=[],i=window.odometerOptions)e=i[t],o.push(null!=(n=s.options)[t]?(n=s.options)[t]:n[t]=e);return o}},0),s.init=function(){var t,e,n,i,o,r;if(null!=document.querySelectorAll){for(r=[],n=0,i=(e=document.querySelectorAll(s.options.selector||".odometer")).length;n<i;n++)t=e[n],r.push(t.odometer=new s({el:t,value:null!=(o=t.innerText)?o:t.textContent}));return r}},null!=(null!=(g=document.documentElement)?g.doScroll:void 0)&&null!=document.createEventObject?(m=document.onreadystatechange,document.onreadystatechange=function(){return"complete"===document.readyState&&!1!==s.options.auto&&s.init(),null!=m?m.apply(this,arguments):void 0}):document.addEventListener("DOMContentLoaded",function(){return!1!==s.options.auto?s.init():void 0},!1),"function"==typeof define&&define.amd?define([],function(){return s}):"undefined"!=typeof exports&&null!==exports?module.exports=s:window.Odometer=s}).call(this),jQuery(document).ready(function(t){jQuery(".pa-counter-container").waypoint(function(){jQuery(".pa-counter-value").each(function(){var t=jQuery(this).data("to"),e=jQuery(this).data("speed"),n=new Odometer({el:this,value:0,duration:e});n.render(),setInterval(function(){n.update(t)})})},{offset:"80%",triggerOnce:!0})});