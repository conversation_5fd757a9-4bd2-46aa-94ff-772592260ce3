!function(g){g.fn.eatwentytwenty=function(m){m=g.extend({default_offset_pct:.5,orientation:"horizontal",before_label:"Before",after_label:"After",no_overlay:!1,move_slider_on_hover:!1,move_with_handle_only:!0,click_to_move:!1},m);return this.each(function(){var e=m.default_offset_pct,s=g(this),c=m.orientation,t="vertical"===c?"down":"left",n="vertical"===c?"up":"right";s.wrap("<div class='twentytwenty-wrapper twentytwenty-"+c+"'></div>"),m.no_overlay||s.append("<div class='twentytwenty-overlay'></div>");var r=s.find("img:first"),d=s.find("img:last");s.append("<div class='twentytwenty-handle'></div>");var l=s.find(".twentytwenty-handle");l.append("<span class='twentytwenty-"+t+"-arrow'></span>"),l.append("<span class='twentytwenty-"+n+"-arrow'></span>"),s.addClass("twentytwenty-container"),r.addClass("twentytwenty-before"),d.addClass("twentytwenty-after");var a=s.find(".twentytwenty-overlay");a.append("<div class='twentytwenty-before-label' data-content='"+m.before_label+"'></div>"),a.append("<div class='twentytwenty-after-label' data-content='"+m.after_label+"'></div>");function i(t){var e,n,a,i,o=(e=t,n=r.width(),a=r.height(),{w:n+"px",h:a+"px",cw:e*n+"px",ch:e*a+"px"});l.css("vertical"===c?"top":"left","vertical"===c?o.ch:o.cw),i=o,"vertical"===c?(r.css("clip","rect(0,"+i.w+","+i.ch+",0)"),d.css("clip","rect("+i.ch+","+i.w+","+i.h+",0)")):(r.css("clip","rect(0,"+i.cw+","+i.h+",0)"),d.css("clip","rect(0,"+i.w+","+i.h+","+i.cw+")")),s.css("height",i.h)}function o(t,e){var n,a,i;return n="vertical"===c?(e-y)/u:(t-p)/h,a=0,i=1,Math.max(a,Math.min(i,n))}g(window).on("resize.eatwentytwenty",function(t){i(e)});function w(t){((t.distX>t.distY&&t.distX<-t.distY||t.distX<t.distY&&t.distX>-t.distY)&&"vertical"!==c||(t.distX<t.distY&&t.distX<-t.distY||t.distX>t.distY&&t.distX>-t.distY)&&"vertical"===c)&&t.preventDefault(),s.addClass("active"),p=s.offset().left,y=s.offset().top,h=r.width(),u=r.height()}function f(t){s.hasClass("active")&&(e=o(t.pageX,t.pageY),i(e))}function v(){s.removeClass("active")}var p=0,y=0,h=0,u=0,_=m.move_with_handle_only?l:s;_.on("movestart",w),_.on("move",f),_.on("moveend",v),m.move_slider_on_hover&&(s.on("mouseenter",w),s.on("mousemove",f),s.on("mouseleave",v)),l.on("touchmove",function(t){t.preventDefault()}),s.find("img").on("mousedown",function(t){t.preventDefault()}),m.click_to_move&&s.on("click",function(t){p=s.offset().left,y=s.offset().top,h=r.width(),u=r.height(),e=o(t.pageX,t.pageY),i(e)}),g(window).trigger("resize.eatwentytwenty")})}}(jQuery);