!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("popper.js")):"function"==typeof define&&define.amd?define(["popper.js"],e):(t=t||self).tippy=e(t.Popper)}(this,function(R){"use strict";R=R&&R.hasOwnProperty("default")?R.default:R;function V(){return(V=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(t[r]=a[r])}return t}).apply(this,arguments)}var n="undefined"!=typeof window,t=n&&navigator.userAgent,W=/MSIE |Trident\//.test(t),i=/UCBrowser\//.test(t),e=n&&/iPhone|iPad|iPod/.test(navigator.platform)&&!window.MSStream,B={a11y:!0,allowHTML:!0,animateFill:!0,animation:"shift-away",appendTo:function(){return document.body},aria:"describedby",arrow:!1,arrowType:"sharp",boundary:"scrollParent",content:"",delay:[0,20],distance:10,duration:[325,275],flip:!0,flipBehavior:"flip",flipOnUpdate:!1,followCursor:!1,hideOnClick:!0,ignoreAttributes:!1,inertia:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,lazy:!0,maxWidth:350,multiple:!1,offset:0,onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},placement:"top",popperOptions:{},role:"tooltip",showOnInit:!1,size:"regular",sticky:!1,target:"",theme:"dark",touch:!0,touchHold:!1,trigger:"mouseenter focus",updateDuration:0,wait:null,zIndex:9999},U=["arrow","arrowType","boundary","distance","flip","flipBehavior","flipOnUpdate","offset","placement","popperOptions"],q={POPPER:".tippy-popper",TOOLTIP:".tippy-tooltip",CONTENT:".tippy-content",BACKDROP:".tippy-backdrop",ARROW:".tippy-arrow",ROUND_ARROW:".tippy-roundarrow"},a=n?Element.prototype:{},j=a.matches||a.matchesSelector||a.webkitMatchesSelector||a.mozMatchesSelector||a.msMatchesSelector;function F(t){return[].slice.call(t)}function K(t,e){return(a.closest||function(t){for(var e=this;e;){if(j.call(e,t))return e;e=e.parentElement}}).call(t,e)}function J(t,e){for(;t;){if(e(t))return t;t=t.parentElement}}function p(t){return"[object Object]"==={}.toString.call(t)}function G(t,e){return{}.hasOwnProperty.call(t,e)}function Q(t,e,a){if(Array.isArray(t)){var r=t[e];return null==r?a:r}return t}function Z(a,r){var n;return function(){var t=this,e=arguments;clearTimeout(n),n=setTimeout(function(){return a.apply(t,e)},r)}}function $(t,e){return t&&t.modifiers&&t.modifiers[e]}function tt(t,e){return-1<t.indexOf(e)}function o(t){return p(t)||t instanceof Element}function r(){return"innerHTML"}function et(t,e){return"function"==typeof t?t.apply(null,e):t}function at(t,e){t.filter(function(t){return"flip"===t.name})[0].enabled=e}function rt(){return document.createElement("div")}function s(t,e){t[r()]=e instanceof Element?e[r()]:e}function nt(t,e){e.content instanceof Element?(s(t,""),t.appendChild(e.content)):t[e.allowHTML?"innerHTML":"textContent"]=e.content}function it(t){return{tooltip:t.querySelector(q.TOOLTIP),backdrop:t.querySelector(q.BACKDROP),content:t.querySelector(q.CONTENT),arrow:t.querySelector(q.ARROW)||t.querySelector(q.ROUND_ARROW)}}function pt(t){t.setAttribute("data-inertia","")}function ot(t){var e=rt();return"round"===t?(e.className="tippy-roundarrow",s(e,'<svg viewBox="0 0 24 8" xmlns="http://www.w3.org/2000/svg"><path d="M3 8s2.021-.015 5.253-4.218C9.584 2.051 10.797 1.007 12 1c1.203-.007 2.416 1.035 3.761 2.782C19.012 8.005 21 8 21 8H3z"/></svg>')):e.className="tippy-arrow",e}function st(){var t=rt();return t.className="tippy-backdrop",t.setAttribute("data-state","hidden"),t}function lt(t,e){t.setAttribute("tabindex","-1"),e.setAttribute("data-interactive","")}function ct(t,e){t.forEach(function(t){t&&(t.style.transitionDuration="".concat(e,"ms"))})}function dt(t,e,a){var r=i&&void 0!==document.body.style.WebkitTransition?"webkitTransitionEnd":"transitionend";t[e+"EventListener"](r,a)}function ft(t){var e=t.getAttribute("x-placement");return e?e.split("-")[0]:""}function mt(t,e){t.forEach(function(t){t&&t.setAttribute("data-state",e)})}function ut(e,a,t){t.split(" ").forEach(function(t){e.classList[a](t+"-theme")})}function l(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},a=t.checkHideOnClick,r=t.exclude,n=t.duration;F(document.querySelectorAll(q.POPPER)).forEach(function(t){var e=t._tippy;!e||a&&!0!==e.props.hideOnClick||r&&t===r.popper||e.hide(n)})}var bt={passive:!0},yt=3,ht=!1;function c(){ht||(ht=!0,e&&document.body.classList.add("tippy-iOS"),window.performance&&document.addEventListener("mousemove",f))}var d=0;function f(){var t=performance.now();t-d<20&&(ht=!1,document.removeEventListener("mousemove",f),e||document.body.classList.remove("tippy-iOS")),d=t}function m(t){var e=t.target;if(!(e instanceof Element))return l();var a=K(e,q.POPPER);if(!(a&&a._tippy&&a._tippy.props.interactive)){var r=J(e,function(t){return t._tippy&&t._tippy.reference===t});if(r){var n=r._tippy,i=tt(n.props.trigger,"click");if(ht||i)return l({exclude:n,checkHideOnClick:!0});if(!0!==n.props.hideOnClick||i)return;n.clearDelayTimeouts()}l({checkHideOnClick:!0})}}function u(){var t=document.activeElement;t&&t.blur&&t._tippy&&t.blur()}var b=Object.keys(B);function vt(t,e){var n,a=V({},e,{content:et(e.content,[t])},e.ignoreAttributes?{}:(n=t,b.reduce(function(e,a){var r=(n.getAttribute("data-tippy-".concat(a))||"").trim();if(!r)return e;if("content"===a)e[a]=r;else try{e[a]=JSON.parse(r)}catch(t){e[a]=r}return e},{})));return(a.arrow||i)&&(a.animateFill=!1),a}function xt(t,e){var a=0<arguments.length&&void 0!==t?t:{},r=1<arguments.length?e:void 0;Object.keys(a).forEach(function(t){if(!G(r,t))throw new Error("[tippy]: `".concat(t,"` is not a valid option"))})}var wt=1;function gt(t,n){var e=vt(t,n);if(!e.multiple&&t._tippy)return null;var d={},u=null,i=0,a=0,p=!1,o=function(){},s=[],f=0<e.interactiveDebounce?Z(k,e.interactiveDebounce):k,m=null,r=wt++,l=function(t,e){var a=rt();a.className="tippy-popper",a.id="tippy-".concat(t),a.style.zIndex=e.zIndex,e.role&&a.setAttribute("role",e.role);var r=rt();r.className="tippy-tooltip",r.style.maxWidth=e.maxWidth+("number"==typeof e.maxWidth?"px":""),r.setAttribute("data-size",e.size),r.setAttribute("data-animation",e.animation),r.setAttribute("data-state","hidden"),ut(r,"add",e.theme);var n=rt();return n.className="tippy-content",n.setAttribute("data-state","hidden"),e.interactive&&lt(a,r),e.arrow&&r.appendChild(ot(e.arrowType)),e.animateFill&&(r.appendChild(st()),r.setAttribute("data-animatefill","")),e.inertia&&pt(r),nt(n,e),r.appendChild(n),a.appendChild(r),a}(r,e);l.addEventListener("mouseenter",function(t){b.props.interactive&&b.state.isVisible&&"mouseenter"===d.type&&h(t)}),l.addEventListener("mouseleave",function(){b.props.interactive&&"mouseenter"===d.type&&document.addEventListener("mousemove",f)});var c,b={id:r,reference:t,popper:l,popperChildren:it(l),popperInstance:null,props:e,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},clearDelayTimeouts:H,set:M,setContent:function(t){M({content:t})},show:_,hide:D,enable:function(){b.state.isEnabled=!0},disable:function(){b.state.isEnabled=!1},destroy:N};return S(),e.lazy||(Y(),b.popperInstance.disableEventListeners()),e.showOnInit&&h(),e.a11y&&!e.target&&((c=t)instanceof Element&&(!j.call(c,"a[href],area[href],button,details,input,textarea,select,iframe,[tabindex]")||c.hasAttribute("disabled")))&&t.setAttribute("tabindex","0"),t._tippy=b,l._tippy=b;function y(t){var e=u=t,a=e.clientX,r=e.clientY;if(b.popperInstance){var n=ft(b.popper),i=b.popperChildren.arrow?yt+16:yt,p=tt(["top","bottom"],n),o=tt(["left","right"],n),s=p?Math.max(i,a):a,l=o?Math.max(i,r):r;p&&i<s&&(s=Math.min(a,window.innerWidth-i)),o&&i<l&&(l=Math.min(r,window.innerHeight-i));var c=b.reference.getBoundingClientRect(),d=b.props.followCursor,f="horizontal"===d,m="vertical"===d;b.popperInstance.reference={getBoundingClientRect:function(){return{width:0,height:0,top:f?c.top:l,bottom:f?c.bottom:l,left:m?c.left:s,right:m?c.right:s}},clientWidth:0,clientHeight:0},b.popperInstance.scheduleUpdate(),"initial"===d&&b.state.isVisible&&x()}}function h(t){if(H(),!b.state.isVisible)if(b.props.target)(a=K((e=t).target,b.props.target))&&!a._tippy&&(gt(a,V({},b.props,{content:et(n.content,[a]),appendTo:n.appendTo,target:"",showOnInit:!0})),h(e));else{var e,a;if(p=!0,b.props.wait)return b.props.wait(b,t);L()&&!b.state.isMounted&&document.addEventListener("mousemove",y);var r=Q(b.props.delay,0,B.delay);r?i=setTimeout(function(){_()},r):_()}}function v(){if(H(),!b.state.isVisible)return x();p=!1;var t=Q(b.props.delay,1,B.delay);t?a=setTimeout(function(){b.state.isVisible&&D()},t):D()}function x(){document.removeEventListener("mousemove",y),u=null}function w(){document.body.removeEventListener("mouseleave",v),document.removeEventListener("mousemove",f)}function g(t){b.state.isEnabled&&!X(t)&&(b.state.isVisible||(d=t,ht&&tt(t.type,"mouse")&&(u=t)),"click"===t.type&&!1!==b.props.hideOnClick&&b.state.isVisible?v():h(t))}function k(t){var e=J(t.target,function(t){return t._tippy}),a=K(t.target,q.POPPER)===b.popper,r=e===b.reference;a||r||!function(t,e,a,r){if(!t)return 1;var n=a.clientX,i=a.clientY,p=r.interactiveBorder,o=r.distance,s=e.top-i>("top"===t?p+o:p),l=i-e.bottom>("bottom"===t?p+o:p),c=e.left-n>("left"===t?p+o:p),d=n-e.right>("right"===t?p+o:p);return s||l||c||d}(ft(b.popper),b.popper.getBoundingClientRect(),t,b.props)||(w(),v())}function E(t){if(!X(t))return b.props.interactive?(document.body.addEventListener("mouseleave",v),void document.addEventListener("mousemove",f)):void v()}function A(t){t.target===b.reference&&(b.props.interactive&&t.relatedTarget&&b.popper.contains(t.relatedTarget)||v())}function C(t){K(t.target,b.props.target)&&h(t)}function O(t){K(t.target,b.props.target)&&v()}function X(t){var e="ontouchstart"in window,a=tt(t.type,"touch"),r=b.props.touchHold;return e&&ht&&r&&!a||ht&&!r&&a}function Y(){var t=b.props.popperOptions,e=b.popperChildren,a=e.tooltip,r=e.arrow;b.popperInstance=new R(b.reference,b.popper,V({placement:b.props.placement},t,{modifiers:V({},t?t.modifiers:{},{preventOverflow:V({boundariesElement:b.props.boundary,padding:yt},$(t,"preventOverflow")),arrow:V({element:r,enabled:!!r},$(t,"arrow")),flip:V({enabled:b.props.flip,padding:b.props.distance+yt,behavior:b.props.flipBehavior},$(t,"flip")),offset:V({offset:b.props.offset},$(t,"offset"))}),onUpdate:function(t){b.props.flipOnUpdate||(t.flipped&&(b.popperInstance.options.placement=t.placement),at(b.popperInstance.modifiers,!1));var e=a.style;e.top="",e.bottom="",e.left="",e.right="",e[ft(b.popper)]=-(b.props.distance-10)+"px"}}))}function L(){return b.props.followCursor&&!ht&&"focus"!==d.type}function T(t,e){if(0===t)return e();function a(t){t.target===r&&(dt(r,"remove",a),e())}var r=b.popperChildren.tooltip;dt(r,"remove",o),dt(r,"add",a),o=a}function I(t,e,a){var r=2<arguments.length&&void 0!==a&&a;b.reference.addEventListener(t,e,r),s.push({eventType:t,handler:e,options:r})}function S(){b.props.touchHold&&!b.props.target&&(I("touchstart",g,bt),I("touchend",E,bt)),b.props.trigger.trim().split(" ").forEach(function(t){if("manual"!==t)if(b.props.target)switch(t){case"mouseenter":I("mouseover",C),I("mouseout",O);break;case"focus":I("focusin",C),I("focusout",O);break;case"click":I(t,C)}else switch(I(t,g),t){case"mouseenter":I("mouseleave",E);break;case"focus":I(W?"focusout":"blur",A)}})}function P(){s.forEach(function(t){var e=t.eventType,a=t.handler,r=t.options;b.reference.removeEventListener(e,a,r)}),s=[]}function z(){return[b.popperChildren.tooltip,b.popperChildren.backdrop,b.popperChildren.content]}function H(){clearTimeout(i),clearTimeout(a)}function M(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};xt(e,B);var t,a,r,n,i,p,o,s,l,c=b.props,d=vt(b.reference,V({},b.props,e,{ignoreAttributes:!0}));d.ignoreAttributes=G(e,"ignoreAttributes")?e.ignoreAttributes:c.ignoreAttributes,b.props=d,(G(e,"trigger")||G(e,"touchHold"))&&(P(),S()),G(e,"interactiveDebounce")&&(w(),f=Z(k,e.interactiveDebounce)),t=b.popper,a=c,r=d,i=it(t),p=i.tooltip,o=i.content,s=i.backdrop,l=i.arrow,t.style.zIndex=r.zIndex,p.setAttribute("data-size",r.size),p.setAttribute("data-animation",r.animation),p.style.maxWidth=r.maxWidth+("number"==typeof r.maxWidth?"px":""),r.role?t.setAttribute("role",r.role):t.removeAttribute("role"),a.content!==r.content&&nt(o,r),!a.animateFill&&r.animateFill?(p.appendChild(st()),p.setAttribute("data-animatefill","")):a.animateFill&&!r.animateFill&&(p.removeChild(s),p.removeAttribute("data-animatefill")),!a.arrow&&r.arrow?p.appendChild(ot(r.arrowType)):a.arrow&&!r.arrow&&p.removeChild(l),a.arrow&&r.arrow&&a.arrowType!==r.arrowType&&p.replaceChild(ot(r.arrowType),l),!a.interactive&&r.interactive?lt(t,p):a.interactive&&!r.interactive&&(n=p,t.removeAttribute("tabindex"),n.removeAttribute("data-interactive")),!a.inertia&&r.inertia?pt(p):a.inertia&&!r.inertia&&p.removeAttribute("data-inertia"),a.theme!==r.theme&&(ut(p,"remove",a.theme),ut(p,"add",r.theme)),b.popperChildren=it(b.popper),b.popperInstance&&(b.popperInstance.update(),U.some(function(t){return G(e,t)})&&(b.popperInstance.destroy(),Y(),b.state.isVisible||b.popperInstance.disableEventListeners(),b.props.followCursor&&u&&y(u)))}function _(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:Q(b.props.duration,0,B.duration[0]);if(!b.state.isDestroyed&&b.state.isEnabled&&(!ht||b.props.touch))return b.reference.isVirtual||document.documentElement.contains(b.reference)?void(b.reference.hasAttribute("disabled")||!1!==b.props.onShow(b)&&(b.popper.style.visibility="visible",b.state.isVisible=!0,b.props.interactive&&b.reference.classList.add("tippy-active"),ct([b.popper,b.popperChildren.tooltip,b.popperChildren.backdrop],0),function(t){var e=!(L()||"initial"===b.props.followCursor&&ht);b.popperInstance?(L()||(b.popperInstance.scheduleUpdate(),e&&b.popperInstance.enableEventListeners()),at(b.popperInstance.modifiers,!0)):(Y(),e||b.popperInstance.disableEventListeners()),b.popperInstance.reference=b.reference;var a,r,n,i,p,o,s=b.popperChildren.arrow;if(L()){s&&(s.style.margin="0");var l=Q(b.props.delay,0,B.delay);d.type&&y(l&&u?u:d)}else s&&(s.style.margin="");a=b.popperInstance,r=t,n=a.popper,i=a.options,p=i.onCreate,o=i.onUpdate,i.onCreate=i.onUpdate=function(t){n.offsetHeight,r(),o(t),i.onCreate=p,i.onUpdate=o};var c=b.props.appendTo;(m="parent"===c?b.reference.parentNode:et(c,[b.reference])).contains(b.popper)||(m.appendChild(b.popper),b.props.onMount(b),b.state.isMounted=!0)}(function(){b.state.isVisible&&(L()||b.popperInstance.update(),ht&&"initial"===b.props.followCursor&&y(u),ct([b.popper],e.updateDuration),ct(z(),t),b.popperChildren.backdrop&&(b.popperChildren.content.style.transitionDelay=Math.round(t/12)+"ms"),b.props.sticky&&(ct([b.popper],W?0:b.props.updateDuration),function t(){b.popperInstance&&b.popperInstance.scheduleUpdate(),b.state.isMounted?requestAnimationFrame(t):ct([b.popper],0)}()),mt(z(),"visible"),T(t,function(){b.popperChildren.tooltip.classList.add("tippy-notransition"),b.props.aria&&b.reference.setAttribute("aria-".concat(b.props.aria),b.popper.id),b.props.onShown(b),b.state.isShown=!0}))}))):N()}function D(){var t,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:Q(b.props.duration,1,B.duration[1]);!b.state.isDestroyed&&b.state.isEnabled&&!1!==b.props.onHide(b)&&(b.popperChildren.tooltip.classList.remove("tippy-notransition"),b.props.interactive&&b.reference.classList.remove("tippy-active"),b.popper.style.visibility="hidden",b.state.isVisible=!1,b.state.isShown=!1,ct(z(),e),mt(z(),"hidden"),t=function(){p||x(),b.props.aria&&b.reference.removeAttribute("aria-".concat(b.props.aria)),b.popperInstance.disableEventListeners(),b.popperInstance.options.placement=b.props.placement,m.removeChild(b.popper),b.props.onHidden(b),b.state.isMounted=!1},T(e,function(){!b.state.isVisible&&m&&m.contains(b.popper)&&t()}))}function N(t){b.state.isDestroyed||(b.state.isMounted&&D(0),P(),delete b.reference._tippy,b.props.target&&t&&F(b.reference.querySelectorAll(b.props.target)).forEach(function(t){t._tippy&&t._tippy.destroy()}),b.popperInstance&&b.popperInstance.destroy(),b.state.isDestroyed=!0)}}var y=!1;function h(t,e){xt(e,B),y||(document.addEventListener("click",m,!0),document.addEventListener("touchstart",c,bt),window.addEventListener("blur",u),y=!0);var r=V({},B,e);p(t)&&function(a){var t={isVirtual:!0,attributes:a.attributes||{},setAttribute:function(t,e){a.attributes[t]=e},getAttribute:function(t){return a.attributes[t]},removeAttribute:function(t){delete a.attributes[t]},hasAttribute:function(t){return t in a.attributes},addEventListener:function(){},removeEventListener:function(){},classList:{classNames:{},add:function(t){a.classList.classNames[t]=!0},remove:function(t){delete a.classList.classNames[t]},contains:function(t){return t in a.classList.classNames}}};for(var e in t)a[e]=t[e]}(t);var a=function(t){if(o(t))return[t];if(t instanceof NodeList)return F(t);if(Array.isArray(t))return t;try{return F(document.querySelectorAll(t))}catch(t){return[]}}(t).reduce(function(t,e){var a=e&&gt(e,r);return a&&t.push(a),t},[]);return o(t)?a[0]:a}return h.version="4.0.2",h.defaults=B,h.setDefaults=function(e){Object.keys(e).forEach(function(t){B[t]=e[t]})},h.hideAll=l,h.group=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},a=t.delay,r=void 0===a?e[0].props.delay:a,n=t.duration,i=void 0===n?0:n,p=!1;function o(t){p=t,d()}function s(t){t._originalProps.onShow(t),e.forEach(function(t){t.set({duration:i}),t.hide()}),o(!0)}function l(t){t._originalProps.onHide(t),o(!1)}function c(t){t._originalProps.onShown(t),t.set({duration:t._originalProps.duration})}function d(){e.forEach(function(t){t.set({onShow:s,onShown:c,onHide:l,delay:p?[0,Array.isArray(r)?r[1]:r]:r,duration:p?i:t._originalProps.duration})})}e.forEach(function(t){t._originalProps={duration:t.props.duration,onHide:t.props.onHide,onShow:t.props.onShow,onShown:t.props.onShown}}),d()},n&&setTimeout(function(){F(document.querySelectorAll("[data-tippy]")).forEach(function(t){var e=t.getAttribute("data-tippy");e&&h(t,{content:e})})}),function(t){if(n){var e=document.createElement("style");e.type="text/css",e.textContent=t;var a=document.head,r=a.firstChild;r?a.insertBefore(e,r):a.appendChild(e)}}('.tippy-iOS{cursor:pointer!important}.tippy-notransition{transition:none}.tippy-popper{transition-timing-function:cubic-bezier(.165,.84,.44,1);max-width:calc(100% - 10px);pointer-events:none;outline:0}.tippy-popper[x-placement^=top] .tippy-backdrop{border-radius:40% 40% 0 0}.tippy-popper[x-placement^=top] .tippy-roundarrow{bottom:-8px;-webkit-transform-origin:50% 0;transform-origin:50% 0}.tippy-popper[x-placement^=top] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(180deg);transform:rotate(180deg)}.tippy-popper[x-placement^=top] .tippy-arrow{border-top:8px solid #333;border-right:8px solid transparent;border-left:8px solid transparent;bottom:-7px;margin:0 6px;-webkit-transform-origin:50% 0;transform-origin:50% 0}.tippy-popper[x-placement^=top] .tippy-backdrop{-webkit-transform-origin:0 25%;transform-origin:0 25%}.tippy-popper[x-placement^=top] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-55%);transform:scale(1) translate(-50%,-55%)}.tippy-popper[x-placement^=top] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-50%,-45%);transform:scale(.2) translate(-50%,-45%);opacity:0}.tippy-popper[x-placement^=top] [data-animation=shift-toward][data-state=visible]{-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateY(-20px);transform:translateY(-20px)}.tippy-popper[x-placement^=top] [data-animation=perspective]{-webkit-transform-origin:bottom;transform-origin:bottom}.tippy-popper[x-placement^=top] [data-animation=perspective][data-state=visible]{-webkit-transform:perspective(700px) translateY(-10px) rotateX(0);transform:perspective(700px) translateY(-10px) rotateX(0)}.tippy-popper[x-placement^=top] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:perspective(700px) translateY(0) rotateX(60deg);transform:perspective(700px) translateY(0) rotateX(60deg)}.tippy-popper[x-placement^=top] [data-animation=fade][data-state=visible]{-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=shift-away][data-state=visible]{-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateY(0);transform:translateY(0)}.tippy-popper[x-placement^=top] [data-animation=scale]{-webkit-transform-origin:bottom;transform-origin:bottom}.tippy-popper[x-placement^=top] [data-animation=scale][data-state=visible]{-webkit-transform:translateY(-10px) scale(1);transform:translateY(-10px) scale(1)}.tippy-popper[x-placement^=top] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateY(-10px) scale(.5);transform:translateY(-10px) scale(.5)}.tippy-popper[x-placement^=bottom] .tippy-backdrop{border-radius:0 0 30% 30%}.tippy-popper[x-placement^=bottom] .tippy-roundarrow{top:-8px;-webkit-transform-origin:50% 100%;transform-origin:50% 100%}.tippy-popper[x-placement^=bottom] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(0);transform:rotate(0)}.tippy-popper[x-placement^=bottom] .tippy-arrow{border-bottom:8px solid #333;border-right:8px solid transparent;border-left:8px solid transparent;top:-7px;margin:0 6px;-webkit-transform-origin:50% 100%;transform-origin:50% 100%}.tippy-popper[x-placement^=bottom] .tippy-backdrop{-webkit-transform-origin:0 -50%;transform-origin:0 -50%}.tippy-popper[x-placement^=bottom] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-45%);transform:scale(1) translate(-50%,-45%)}.tippy-popper[x-placement^=bottom] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-50%);transform:scale(.2) translate(-50%);opacity:0}.tippy-popper[x-placement^=bottom] [data-animation=shift-toward][data-state=visible]{-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateY(20px);transform:translateY(20px)}.tippy-popper[x-placement^=bottom] [data-animation=perspective]{-webkit-transform-origin:top;transform-origin:top}.tippy-popper[x-placement^=bottom] [data-animation=perspective][data-state=visible]{-webkit-transform:perspective(700px) translateY(10px) rotateX(0);transform:perspective(700px) translateY(10px) rotateX(0)}.tippy-popper[x-placement^=bottom] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:perspective(700px) translateY(0) rotateX(-60deg);transform:perspective(700px) translateY(0) rotateX(-60deg)}.tippy-popper[x-placement^=bottom] [data-animation=fade][data-state=visible]{-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=shift-away][data-state=visible]{-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateY(0);transform:translateY(0)}.tippy-popper[x-placement^=bottom] [data-animation=scale]{-webkit-transform-origin:top;transform-origin:top}.tippy-popper[x-placement^=bottom] [data-animation=scale][data-state=visible]{-webkit-transform:translateY(10px) scale(1);transform:translateY(10px) scale(1)}.tippy-popper[x-placement^=bottom] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateY(10px) scale(.5);transform:translateY(10px) scale(.5)}.tippy-popper[x-placement^=left] .tippy-backdrop{border-radius:50% 0 0 50%}.tippy-popper[x-placement^=left] .tippy-roundarrow{right:-16px;-webkit-transform-origin:33.33333333% 50%;transform-origin:33.33333333% 50%}.tippy-popper[x-placement^=left] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(90deg);transform:rotate(90deg)}.tippy-popper[x-placement^=left] .tippy-arrow{border-left:8px solid #333;border-top:8px solid transparent;border-bottom:8px solid transparent;right:-7px;margin:3px 0;-webkit-transform-origin:0 50%;transform-origin:0 50%}.tippy-popper[x-placement^=left] .tippy-backdrop{-webkit-transform-origin:50% 0;transform-origin:50% 0}.tippy-popper[x-placement^=left] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-50%);transform:scale(1) translate(-50%,-50%)}.tippy-popper[x-placement^=left] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-75%,-50%);transform:scale(.2) translate(-75%,-50%);opacity:0}.tippy-popper[x-placement^=left] [data-animation=shift-toward][data-state=visible]{-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateX(-20px);transform:translateX(-20px)}.tippy-popper[x-placement^=left] [data-animation=perspective]{-webkit-transform-origin:right;transform-origin:right}.tippy-popper[x-placement^=left] [data-animation=perspective][data-state=visible]{-webkit-transform:perspective(700px) translateX(-10px) rotateY(0);transform:perspective(700px) translateX(-10px) rotateY(0)}.tippy-popper[x-placement^=left] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:perspective(700px) translateX(0) rotateY(-60deg);transform:perspective(700px) translateX(0) rotateY(-60deg)}.tippy-popper[x-placement^=left] [data-animation=fade][data-state=visible]{-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=shift-away][data-state=visible]{-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateX(0);transform:translateX(0)}.tippy-popper[x-placement^=left] [data-animation=scale]{-webkit-transform-origin:right;transform-origin:right}.tippy-popper[x-placement^=left] [data-animation=scale][data-state=visible]{-webkit-transform:translateX(-10px) scale(1);transform:translateX(-10px) scale(1)}.tippy-popper[x-placement^=left] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateX(-10px) scale(.5);transform:translateX(-10px) scale(.5)}.tippy-popper[x-placement^=right] .tippy-backdrop{border-radius:0 50% 50% 0}.tippy-popper[x-placement^=right] .tippy-roundarrow{left:-16px;-webkit-transform-origin:66.66666666% 50%;transform-origin:66.66666666% 50%}.tippy-popper[x-placement^=right] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}.tippy-popper[x-placement^=right] .tippy-arrow{border-right:8px solid #333;border-top:8px solid transparent;border-bottom:8px solid transparent;left:-7px;margin:3px 0;-webkit-transform-origin:100% 50%;transform-origin:100% 50%}.tippy-popper[x-placement^=right] .tippy-backdrop{-webkit-transform-origin:-50% 0;transform-origin:-50% 0}.tippy-popper[x-placement^=right] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-50%);transform:scale(1) translate(-50%,-50%)}.tippy-popper[x-placement^=right] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-25%,-50%);transform:scale(.2) translate(-25%,-50%);opacity:0}.tippy-popper[x-placement^=right] [data-animation=shift-toward][data-state=visible]{-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateX(20px);transform:translateX(20px)}.tippy-popper[x-placement^=right] [data-animation=perspective]{-webkit-transform-origin:left;transform-origin:left}.tippy-popper[x-placement^=right] [data-animation=perspective][data-state=visible]{-webkit-transform:perspective(700px) translateX(10px) rotateY(0);transform:perspective(700px) translateX(10px) rotateY(0)}.tippy-popper[x-placement^=right] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:perspective(700px) translateX(0) rotateY(60deg);transform:perspective(700px) translateX(0) rotateY(60deg)}.tippy-popper[x-placement^=right] [data-animation=fade][data-state=visible]{-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=shift-away][data-state=visible]{-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateX(0);transform:translateX(0)}.tippy-popper[x-placement^=right] [data-animation=scale]{-webkit-transform-origin:left;transform-origin:left}.tippy-popper[x-placement^=right] [data-animation=scale][data-state=visible]{-webkit-transform:translateX(10px) scale(1);transform:translateX(10px) scale(1)}.tippy-popper[x-placement^=right] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateX(10px) scale(.5);transform:translateX(10px) scale(.5)}.tippy-tooltip{position:relative;color:#fff;border-radius:4px;font-size:.9rem;padding:.3rem .6rem;line-height:1.4;text-align:center;will-change:transform;background-color:#333}.tippy-tooltip[data-size=small]{padding:.2rem .4rem;font-size:.75rem}.tippy-tooltip[data-size=large]{padding:.4rem .8rem;font-size:1rem}.tippy-tooltip[data-animatefill]{overflow:hidden;background-color:transparent}.tippy-tooltip[data-interactive],.tippy-tooltip[data-interactive] path{pointer-events:auto}.tippy-tooltip[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-tooltip[data-inertia][data-state=hidden]{transition-timing-function:ease}.tippy-arrow,.tippy-roundarrow{position:absolute;width:0;height:0}.tippy-roundarrow{width:24px;height:8px;fill:#333;pointer-events:none}.tippy-backdrop{position:absolute;will-change:transform;background-color:#333;border-radius:50%;width:calc(110% + 2rem);left:50%;top:50%;z-index:-1;transition:all cubic-bezier(.46,.1,.52,.98);-webkit-backface-visibility:hidden;backface-visibility:hidden}.tippy-backdrop:after{content:"";float:left;padding-top:100%}.tippy-backdrop+.tippy-content{transition-property:opacity;will-change:opacity}.tippy-backdrop+.tippy-content[data-state=visible]{opacity:1}.tippy-backdrop+.tippy-content[data-state=hidden]{opacity:0}'),h});