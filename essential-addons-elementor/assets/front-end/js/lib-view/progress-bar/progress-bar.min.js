!function(l){l.fn.eaelProgressBar=function(){var a=l(this),i=a.data("layout"),e=a.data("count"),r=a.data("duration");100<e&&(e=100),a.one("inview",function(){"line"==i||"line_rainbow"==i?l(".eael-progressbar-line-fill",a).css({width:e+"%"}):"half_circle"==i||"half_circle_fill"==i?l(".eael-progressbar-circle-half",a).css({transform:"rotate("+1.8*e+"deg)"}):"box"==i&&l(".eael-progressbar-box-fill",a).css({height:e+"%"}),l(".eael-progressbar-count",a).prop({counter:0}).animate({counter:e},{duration:r,easing:"linear",step:function(e){if("circle"==i||"circle_fill"==i){var r=3.6*e;l(".eael-progressbar-circle-half-left",a).css({transform:"rotate("+r+"deg)"}),180<r&&(l(".eael-progressbar-circle-pie",a).css({"clip-path":"inset(0)"}),l(".eael-progressbar-circle-half-right",a).css({visibility:"visible"}))}l(this).text(Math.ceil(e))}})})}}(jQuery);