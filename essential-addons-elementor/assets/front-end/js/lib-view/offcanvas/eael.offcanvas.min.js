!function(n){window.EAELOffcanvasContent=function(t){(this.node=t).find(".eael-offcanvas-toggle").length<1||(this.wrap=t.find(".eael-offcanvas-content-wrap"),this.content=t.find(".eael-offcanvas-content"),this.button=t.find(".eael-offcanvas-toggle"),this.settings=this.wrap.data("settings"),this.id=this.settings.content_id,this.transition=this.settings.transition,this.esc_close=this.settings.esc_close,this.body_click_close=this.settings.body_click_close,this.open_offcanvas=this.settings.open_offcanvas,this.direction=this.settings.direction,this.duration=500,this.init())},EAELOffcanvasContent.prototype={id:"",node:"",wrap:"",content:"",button:"",settings:{},transition:"",duration:400,initialized:!1,animations:["slide","slide-along","reveal","push"],init:function(){this.wrap.length&&(n("html").addClass("eael-offcanvas-content-widget"),0===n(".eael-offcanvas-container").length&&(n("body").wrapInner('<div class="eael-offcanvas-container eael-offcanvas-container-'+this.id+'" />'),this.content.insertBefore(".eael-offcanvas-container")),0<this.wrap.find(".eael-offcanvas-content").length&&(0<n(".eael-offcanvas-container > .eael-offcanvas-content-"+this.id).length&&n(".eael-offcanvas-container > .eael-offcanvas-content-"+this.id).remove(),0<n("body > .eael-offcanvas-content-"+this.id).length&&n("body > .eael-offcanvas-content-"+this.id).remove(),n("body").prepend(this.wrap.find(".eael-offcanvas-content"))),this.bindEvents())},destroy:function(){this.close(),this.animations.forEach(function(t){n("html").hasClass("eael-offcanvas-content-"+t)&&n("html").removeClass("eael-offcanvas-content-"+t)}),n("body > .eael-offcanvas-content-"+this.id).length},bindEvents:function(){"yes"===this.open_offcanvas&&this.show(),this.button.on("click",n.proxy(this.toggleContent,this)),n("body").delegate(".eael-offcanvas-content .eael-offcanvas-close","click",n.proxy(this.close,this)),"yes"===this.esc_close&&this.closeESC(),"yes"===this.body_click_close&&this.closeClick()},toggleContent:function(){n("html").hasClass("eael-offcanvas-content-open")?this.close():this.show()},show:function(){n(".eael-offcanvas-content-"+this.id).addClass("eael-offcanvas-content-visible"),n("html").addClass("eael-offcanvas-content-"+this.transition),n("html").addClass("eael-offcanvas-content-"+this.direction),n("html").addClass("eael-offcanvas-content-open"),n("html").addClass("eael-offcanvas-content-"+this.id+"-open"),n("html").addClass("eael-offcanvas-content-reset")},close:function(){n("html").removeClass("eael-offcanvas-content-open"),n("html").removeClass("eael-offcanvas-content-"+this.id+"-open"),setTimeout(n.proxy(function(){n("html").removeClass("eael-offcanvas-content-reset"),n("html").removeClass("eael-offcanvas-content-"+this.transition),n("html").removeClass("eael-offcanvas-content-"+this.direction),n(".eael-offcanvas-content-"+this.id).removeClass("eael-offcanvas-content-visible")},this),500)},closeESC:function(){var e=this;""!==e.settings.esc_close&&n(document).on("keydown",function(t){27===t.keyCode&&e.close()})},closeClick:function(){var e=this;n(document).on("click",function(t){n(t.target).is(".eael-offcanvas-content")||0<n(t.target).parents(".eael-offcanvas-content").length||n(t.target).is(".eael-offcanvas-toggle")||0<n(t.target).parents(".eael-offcanvas-toggle").length||e.close()})}}}(jQuery);