!function(n){"use strict";window.interactiveCards=function(t){var i={container:t.containerId,frontAnimation:t.frontAnimation,rearAnimation:t.rearAnimation,contentAnimation:t.contentAnimation,revealTime:t.revealTime};n("#"+i.container);var a=n("#"+i.container+" .front-content"),e=n("#"+i.container+" .front-content .image-screen");e.data("bg");var o=n("#"+i.container+" .content"),r=n("#"+i.container+" .close-me");e.on("click",function(){n(this).removeClass(i.frontAnimation.end).addClass(i.frontAnimation.start),setTimeout(function(){a.removeClass(i.rearAnimation.end).addClass(i.rearAnimation.start),setTimeout(function(){o.addClass(i.contentAnimation)},2*t.revealTime)},t.revealTime)}),r.on("click",function(){o.removeClass(i.contentAnimation),setTimeout(function(){a.removeClass(i.rearAnimation.start).addClass(i.rearAnimation.end),setTimeout(function(){e.removeClass(i.frontAnimation.start).addClass(i.frontAnimation.end)},2*t.revealTime)},t.revealTime)});var c=n("#"+t.containerId+" .carousel-container"),s=c.find("ul"),m=s.find("li"),l=c.width(),d=m.first().children("img").width(),f=s.children("li").length,v=1;function A(n,t){s.animate({left:-(n-1)*t})}s.css("width",d*f+"px"),m.css("width",l+"px"),n("#"+t.containerId+" a.nav").on("click",function(t){t.preventDefault();var i=n(this).data("nav");"next"===i?v===f?A(v=1,d):A(++v,d):"prev"===i&&(1==v?A(v=f,d):A(--v,d))})}}(jQuery);