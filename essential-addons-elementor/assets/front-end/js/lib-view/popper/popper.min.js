!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.Popper=t()}(this,function(){"use strict";function i(e){return e&&"[object Function]"==={}.toString.call(e)}function y(e,t){if(1!==e.nodeType)return[];var n=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?n[t]:n}function h(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function m(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=y(e),n=t.overflow,o=t.overflowX,r=t.overflowY;return/(auto|scroll|overlay)/.test(n+r+o)?e:m(h(e))}function g(e){return 11===e?z:10!==e&&z||G}function w(e){if(!e)return document.documentElement;for(var t=g(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var o=n&&n.nodeName;return o&&"BODY"!==o&&"HTML"!==o?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===y(n,"position")?w(n):n:e?e.ownerDocument.documentElement:document.documentElement}function l(e){return null===e.parentNode?e:l(e.parentNode)}function v(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,o=n?e:t,r=n?t:e,i=document.createRange();i.setStart(o,0),i.setEnd(r,0);var s,f,a=i.commonAncestorContainer;if(e!==a&&t!==a||o.contains(r))return"BODY"===(f=(s=a).nodeName)||"HTML"!==f&&w(s.firstElementChild)!==s?w(a):a;var p=l(e);return p.host?v(p.host,t):v(e,l(t).host)}function b(e,t){var n="top"===(1<arguments.length&&void 0!==t?t:"top")?"scrollTop":"scrollLeft",o=e.nodeName;if("BODY"!==o&&"HTML"!==o)return e[n];var r=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||r)[n]}function u(e,t){var n="x"===t?"Left":"Top",o="Left"==n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"],10)+parseFloat(e["border"+o+"Width"],10)}function r(e,t,n,o){return j(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],g(10)?parseInt(n["offset"+e])+parseInt(o["margin"+("Height"===e?"Top":"Left")])+parseInt(o["margin"+("Height"===e?"Bottom":"Right")]):0)}function E(e){var t=e.body,n=e.documentElement,o=g(10)&&getComputedStyle(n);return{height:r("Height",t,n,o),width:r("Width",t,n,o)}}function x(e){return X({},e,{right:e.left+e.width,bottom:e.top+e.height})}function O(e){var t={};try{if(g(10)){t=e.getBoundingClientRect();var n=b(e,"top"),o=b(e,"left");t.top+=n,t.left+=o,t.bottom+=n,t.right+=o}else t=e.getBoundingClientRect()}catch(e){}var r={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},i="HTML"===e.nodeName?E(e.ownerDocument):{},s=i.width||e.clientWidth||r.right-r.left,f=i.height||e.clientHeight||r.bottom-r.top,a=e.offsetWidth-s,p=e.offsetHeight-f;if(a||p){var l=y(e);a-=u(l,"x"),p-=u(l,"y"),r.width-=a,r.height-=p}return x(r)}function L(e,t,n){var o=2<arguments.length&&void 0!==n&&n,r=g(10),i="HTML"===t.nodeName,s=O(e),f=O(t),a=m(e),p=y(t),l=parseFloat(p.borderTopWidth,10),u=parseFloat(p.borderLeftWidth,10);o&&i&&(f.top=j(f.top,0),f.left=j(f.left,0));var d=x({top:s.top-f.top-l,left:s.left-f.left-u,width:s.width,height:s.height});if(d.marginTop=0,d.marginLeft=0,!r&&i){var c=parseFloat(p.marginTop,10),h=parseFloat(p.marginLeft,10);d.top-=l-c,d.bottom-=l-c,d.left-=u-h,d.right-=u-h,d.marginTop=c,d.marginLeft=h}return(r&&!o?t.contains(a):t===a&&"BODY"!==a.nodeName)&&(d=function(e,t,n){var o=2<arguments.length&&void 0!==n&&n,r=b(t,"top"),i=b(t,"left"),s=o?-1:1;return e.top+=r*s,e.bottom+=r*s,e.left+=i*s,e.right+=i*s,e}(d,t)),d}function T(e){if(!e||!e.parentElement||g())return document.documentElement;for(var t=e.parentElement;t&&"none"===y(t,"transform");)t=t.parentElement;return t||document.documentElement}function c(e,t,n,o,r){var i=4<arguments.length&&void 0!==r&&r,s={top:0,left:0},f=i?T(e):v(e,t);if("viewport"===o)s=function(e,t){var n=1<arguments.length&&void 0!==t&&t,o=e.ownerDocument.documentElement,r=L(e,o),i=j(o.clientWidth,window.innerWidth||0),s=j(o.clientHeight,window.innerHeight||0),f=n?0:b(o),a=n?0:b(o,"left");return x({top:f-r.top+r.marginTop,left:a-r.left+r.marginLeft,width:i,height:s})}(f,i);else{var a;"scrollParent"===o?"BODY"===(a=m(h(t))).nodeName&&(a=e.ownerDocument.documentElement):a="window"===o?e.ownerDocument.documentElement:o;var p=L(a,f,i);if("HTML"!==a.nodeName||function e(t){var n=t.nodeName;if("BODY"===n||"HTML"===n)return!1;if("fixed"===y(t,"position"))return!0;var o=h(t);return!!o&&e(o)}(f))s=p;else{var l=E(e.ownerDocument),u=l.height,d=l.width;s.top+=p.top-p.marginTop,s.bottom=u+p.top,s.left+=p.left-p.marginLeft,s.right=d+p.left}}var c="number"==typeof(n=n||0);return s.left+=c?n:n.left||0,s.top+=c?n:n.top||0,s.right-=c?n:n.right||0,s.bottom-=c?n:n.bottom||0,s}function f(e,t,o,n,r,i){var s=5<arguments.length&&void 0!==i?i:0;if(-1===e.indexOf("auto"))return e;var f=c(o,n,s,r),a={top:{width:f.width,height:t.top-f.top},right:{width:f.right-t.right,height:f.height},bottom:{width:f.width,height:f.bottom-t.bottom},left:{width:t.left-f.left,height:f.height}},p=Object.keys(a).map(function(e){return X({key:e},a[e],{area:(t=a[e]).width*t.height});var t}).sort(function(e,t){return t.area-e.area}),l=p.filter(function(e){var t=e.width,n=e.height;return t>=o.clientWidth&&n>=o.clientHeight}),u=0<l.length?l[0].key:p[0].key,d=e.split("-")[1];return u+(d?"-"+d:"")}function a(e,t,n,o){var r=3<arguments.length&&void 0!==o?o:null;return L(n,r?T(t):v(t,n),r)}function D(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),o=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+o,height:e.offsetHeight+n}}function N(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function F(e,t,n){n=n.split("-")[0];var o=D(e),r={width:o.width,height:o.height},i=-1!==["right","left"].indexOf(n),s=i?"top":"left",f=i?"left":"top",a=i?"height":"width",p=i?"width":"height";return r[s]=t[s]+t[a]/2-o[a]/2,r[f]=n===f?t[f]-o[p]:t[N(f)],r}function k(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function H(e,n,t){return(void 0===t?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===n});var o=k(e,function(e){return e[t]===n});return e.indexOf(o)}(e,"name",t))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var t=e.function||e.fn;e.enabled&&i(t)&&(n.offsets.popper=x(n.offsets.popper),n.offsets.reference=x(n.offsets.reference),n=t(n,e))}),n}function e(e,n){return e.some(function(e){var t=e.name;return e.enabled&&t===n})}function A(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),o=0;o<t.length;o++){var r=t[o],i=r?""+r+n:e;if(void 0!==document.body.style[i])return i}return null}function s(e){var t=e.ownerDocument;return t?t.defaultView:window}function t(e,t,n,o){n.updateBound=o,s(e).addEventListener("resize",n.updateBound,{passive:!0});var r=m(e);return function e(t,n,o,r){var i="BODY"===t.nodeName,s=i?t.ownerDocument.defaultView:t;s.addEventListener(n,o,{passive:!0}),i||e(m(s.parentNode),n,o,r),r.push(s)}(r,"scroll",n.updateBound,n.scrollParents),n.scrollElement=r,n.eventsEnabled=!0,n}function n(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,s(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function d(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function p(n,o){Object.keys(o).forEach(function(e){var t="";-1!==["width","height","top","right","bottom","left"].indexOf(e)&&d(o[e])&&(t="px"),n.style[e]=o[e]+t})}function C(e,t){function n(e){return e}var o=e.offsets,r=o.popper,i=o.reference,s=B,f=s(i.width),a=s(r.width),p=-1!==["left","right"].indexOf(e.placement),l=-1!==e.placement.indexOf("-"),u=t?p||l||f%2==a%2?s:W:n,d=t?s:n;return{left:u(1==f%2&&1==a%2&&!l&&t?r.left-1:r.left),top:d(r.top),bottom:d(r.bottom),right:u(r.right)}}function M(e,t,n){var o=k(e,function(e){return e.name===t}),r=!!o&&e.some(function(e){return e.name===n&&e.enabled&&e.order<o.order});if(!r){var i="`"+t+"`";console.warn("`"+n+"` modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")}return r}function o(e,t){var n=1<arguments.length&&void 0!==t&&t,o=Q.indexOf(e),r=Q.slice(o+1).concat(Q.slice(0,o));return n?r.reverse():r}function P(e,r,i,t){var s=[0,0],f=-1!==["right","left"].indexOf(t),n=e.split(/(\+|\-)/).map(function(e){return e.trim()}),o=n.indexOf(k(n,function(e){return-1!==e.search(/,|\s/)}));n[o]&&-1===n[o].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var a=/\s*,\s*|\s+/,p=-1===o?[n]:[n.slice(0,o).concat([n[o].split(a)[0]]),[n[o].split(a)[1]].concat(n.slice(o+1))];return(p=p.map(function(e,t){var n=(1===t?!f:f)?"height":"width",o=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,o=!0,e):o?(e[e.length-1]+=t,o=!1,e):e.concat(t)},[]).map(function(e){return function(e,t,n,o){var r,i=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),s=+i[1],f=i[2];if(!s)return e;if(0!==f.indexOf("%"))return"vh"!==f&&"vw"!==f?s:("vh"===f?j(document.documentElement.clientHeight,window.innerHeight||0):j(document.documentElement.clientWidth,window.innerWidth||0))/100*s;switch(f){case"%p":r=n;break;case"%":case"%r":default:r=o}return x(r)[t]/100*s}(e,n,r,i)})})).forEach(function(n,o){n.forEach(function(e,t){d(e)&&(s[o]+=e*("-"===n[t-1]?-1:1))})}),s}for(var S=Math.min,W=Math.floor,B=Math.round,j=Math.max,I="undefined"!=typeof window&&"undefined"!=typeof document,R=["Edge","Trident","Firefox"],U=0,Y=0;Y<R.length;Y+=1)if(I&&0<=navigator.userAgent.indexOf(R[Y])){U=1;break}function q(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var V=I&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},U))}},z=I&&!(!window.MSInputMethodContext||!document.documentMode),G=I&&/MSIE 10/.test(navigator.userAgent),_=function(e,t,n){return t&&oe(e.prototype,t),n&&oe(e,n),e},X=Object.assign||function(e){for(var t,n=1;n<arguments.length;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},J=I&&/Firefox/i.test(navigator.userAgent),K=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Q=K.slice(3),Z="flip",$="clockwise",ee="counterclockwise",te=(_(ne,[{key:"update",value:function(){return function(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=a(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=f(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=F(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=H(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,e(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[A("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=t(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return n.call(this)}}]),ne);function ne(e,t){var n=this,o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,ne),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=V(this.update.bind(this)),this.options=X({},ne.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=t&&t.jquery?t[0]:t,this.options.modifiers={},Object.keys(X({},ne.Defaults.modifiers,o.modifiers)).forEach(function(e){n.options.modifiers[e]=X({},ne.Defaults.modifiers[e]||{},o.modifiers?o.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return X({name:e},n.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&i(e.onLoad)&&e.onLoad(n.reference,n.popper,n.options,e,n.state)}),this.update();var r=this.options.eventsEnabled;r&&this.enableEventListeners(),this.state.eventsEnabled=r}function oe(e,t){for(var n,o=0;o<t.length;o++)(n=t[o]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}return te.Utils=("undefined"==typeof window?global:window).PopperUtils,te.placements=K,te.Defaults={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,n=t.split("-")[0],o=t.split("-")[1];if(o){var r=e.offsets,i=r.reference,s=r.popper,f=-1!==["bottom","top"].indexOf(n),a=f?"left":"top",p=f?"width":"height",l={start:q({},a,i[a]),end:q({},a,i[a]+i[p]-s[p])};e.offsets.popper=X({},s,l[o])}return e}},offset:{order:200,enabled:!0,fn:function(e,t){var n,o=t.offset,r=e.placement,i=e.offsets,s=i.popper,f=i.reference,a=r.split("-")[0];return n=d(+o)?[+o,0]:P(o,s,f,a),"left"===a?(s.top+=n[0],s.left-=n[1]):"right"===a?(s.top+=n[0],s.left+=n[1]):"top"===a?(s.left+=n[0],s.top-=n[1]):"bottom"===a&&(s.left+=n[0],s.top+=n[1]),e.popper=s,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,o){var t=o.boundariesElement||w(e.instance.popper);e.instance.reference===t&&(t=w(t));var n=A("transform"),r=e.instance.popper.style,i=r.top,s=r.left,f=r[n];r.top="",r.left="",r[n]="";var a=c(e.instance.popper,e.instance.reference,o.padding,t,e.positionFixed);r.top=i,r.left=s,r[n]=f,o.boundaries=a;var p=o.priority,l=e.offsets.popper,u={primary:function(e){var t=l[e];return l[e]<a[e]&&!o.escapeWithReference&&(t=j(l[e],a[e])),q({},e,t)},secondary:function(e){var t="right"===e?"left":"top",n=l[t];return l[e]>a[e]&&!o.escapeWithReference&&(n=S(l[t],a[e]-("right"===e?l.width:l.height))),q({},t,n)}};return p.forEach(function(e){var t=-1===["left","top"].indexOf(e)?"secondary":"primary";l=X({},l,u[t](e))}),e.offsets.popper=l,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,o=t.reference,r=e.placement.split("-")[0],i=W,s=-1!==["top","bottom"].indexOf(r),f=s?"right":"bottom",a=s?"left":"top",p=s?"width":"height";return n[f]<i(o[a])&&(e.offsets.popper[a]=i(o[a])-n[p]),n[a]>i(o[f])&&(e.offsets.popper[a]=i(o[f])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){var n;if(!M(e.instance.modifiers,"arrow","keepTogether"))return e;var o=t.element;if("string"==typeof o){if(!(o=e.instance.popper.querySelector(o)))return e}else if(!e.instance.popper.contains(o))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var r=e.placement.split("-")[0],i=e.offsets,s=i.popper,f=i.reference,a=-1!==["left","right"].indexOf(r),p=a?"height":"width",l=a?"Top":"Left",u=l.toLowerCase(),d=a?"left":"top",c=a?"bottom":"right",h=D(o)[p];f[c]-h<s[u]&&(e.offsets.popper[u]-=s[u]-(f[c]-h)),f[u]+h>s[c]&&(e.offsets.popper[u]+=f[u]+h-s[c]),e.offsets.popper=x(e.offsets.popper);var m=f[u]+f[p]/2-h/2,g=y(e.instance.popper),v=parseFloat(g["margin"+l],10),b=parseFloat(g["border"+l+"Width"],10),w=m-e.offsets.popper[u]-v-b;return w=j(S(s[p]-h,w),0),e.arrowElement=o,e.offsets.arrow=(q(n={},u,B(w)),q(n,d,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(h,m){if(e(h.instance.modifiers,"inner"))return h;if(h.flipped&&h.placement===h.originalPlacement)return h;var g=c(h.instance.popper,h.instance.reference,m.padding,m.boundariesElement,h.positionFixed),v=h.placement.split("-")[0],b=N(v),w=h.placement.split("-")[1]||"",y=[];switch(m.behavior){case Z:y=[v,b];break;case $:y=o(v);break;case ee:y=o(v,!0);break;default:y=m.behavior}return y.forEach(function(e,t){if(v!==e||y.length===t+1)return h;v=h.placement.split("-")[0],b=N(v);var n,o=h.offsets.popper,r=h.offsets.reference,i=W,s="left"===v&&i(o.right)>i(r.left)||"right"===v&&i(o.left)<i(r.right)||"top"===v&&i(o.bottom)>i(r.top)||"bottom"===v&&i(o.top)<i(r.bottom),f=i(o.left)<i(g.left),a=i(o.right)>i(g.right),p=i(o.top)<i(g.top),l=i(o.bottom)>i(g.bottom),u="left"===v&&f||"right"===v&&a||"top"===v&&p||"bottom"===v&&l,d=-1!==["top","bottom"].indexOf(v),c=!!m.flipVariations&&(d&&"start"===w&&f||d&&"end"===w&&a||!d&&"start"===w&&p||!d&&"end"===w&&l);(s||u||c)&&(h.flipped=!0,(s||u)&&(v=y[t+1]),c&&(w="end"===(n=w)?"start":"start"===n?"end":n),h.placement=v+(w?"-"+w:""),h.offsets.popper=X({},h.offsets.popper,F(h.instance.popper,h.offsets.reference,h.placement)),h=H(h.instance.modifiers,h,"flip"))}),h},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],o=e.offsets,r=o.popper,i=o.reference,s=-1!==["left","right"].indexOf(n),f=-1===["top","left"].indexOf(n);return r[s?"left":"top"]=i[n]-(f?r[s?"width":"height"]:0),e.placement=N(t),e.offsets.popper=x(r),e}},hide:{order:800,enabled:!0,fn:function(e){if(!M(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=k(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n=t.x,o=t.y,r=e.offsets.popper,i=k(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration;void 0!==i&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var s,f,a=void 0===i?t.gpuAcceleration:i,p=w(e.instance.popper),l=O(p),u={position:r.position},d=C(e,window.devicePixelRatio<2||!J),c="bottom"===n?"top":"bottom",h="right"===o?"left":"right",m=A("transform");if(f="bottom"==c?"HTML"===p.nodeName?-p.clientHeight+d.bottom:-l.height+d.bottom:d.top,s="right"==h?"HTML"===p.nodeName?-p.clientWidth+d.right:-l.width+d.right:d.left,a&&m)u[m]="translate3d("+s+"px, "+f+"px, 0)",u[c]=0,u[h]=0,u.willChange="transform";else{var g="bottom"==c?-1:1,v="right"==h?-1:1;u[c]=f*g,u[h]=s*v,u.willChange=c+", "+h}var b={"x-placement":e.placement};return e.attributes=X({},b,e.attributes),e.styles=X({},u,e.styles),e.arrowStyles=X({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){return p(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach(function(e){!1===n[e]?t.removeAttribute(e):t.setAttribute(e,n[e])}),e.arrowElement&&Object.keys(e.arrowStyles).length&&p(e.arrowElement,e.arrowStyles),e;var t,n},onLoad:function(e,t,n,o,r){var i=a(r,t,e,n.positionFixed),s=f(n.placement,i,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",s),p(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},te});