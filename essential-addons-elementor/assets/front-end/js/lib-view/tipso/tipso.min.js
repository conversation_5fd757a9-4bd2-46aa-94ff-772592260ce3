!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t(require("jquery")):t(jQuery)}(function(t){var o="tipso",e={speed:400,background:"#55b555",titleBackground:"#333333",color:"#ffffff",titleColor:"#ffffff",titleContent:"",showArrow:!0,position:"top",width:200,maxWidth:"",delay:200,hideDelay:0,animationIn:"",animationOut:"",offsetX:0,offsetY:0,arrowWidth:8,tooltipHover:!1,content:null,ajaxContentUrl:null,ajaxContentBuffer:0,contentElementId:null,useTitle:!1,templateEngineFunc:null,onBeforeShow:null,onShow:null,onHide:null};function r(r,s){this.element=r,this.$element=t(this.element),this.doc=t(document),this.win=t(window),this.settings=t.extend({},e,s),"object"==typeof this.$element.data("tipso")&&t.extend(this.settings,this.$element.data("tipso"));for(var n=Object.keys(this.$element.data()),a={},l=0;l<n.length;l++){var d=n[l].replace(o,"");if(""!==d)for(var p in a[d=d.charAt(0).toLowerCase()+d.slice(1)]=this.$element.data(n[l]),this.settings)p.toLowerCase()==d&&(this.settings[p]=a[d])}this._defaults=e,this._name=o,this._title=this.$element.attr("title"),this.mode="hide",this.ieFade=!i,this.settings.preferedPosition=this.settings.position,this.init()}function s(o){var e=o.clone();e.css("visibility","hidden"),t("body").append(e);var r=e.outerHeight(),s=e.outerWidth();return e.remove(),{width:s,height:r}}t.extend(r.prototype,{init:function(){var t=this,e=this.$element;if(this.doc,e.addClass("tipso_style").removeAttr("title"),t.settings.tooltipHover){var r=null,s=null;e.on("mouseover."+o,function(){clearTimeout(r),clearTimeout(s),s=setTimeout(function(){t.show()},150)}),e.on("mouseout."+o,function(){clearTimeout(r),clearTimeout(s),r=setTimeout(function(){t.hide()},200),t.tooltip().on("mouseover."+o,function(){t.mode="tooltipHover"}).on("mouseout."+o,function(){t.mode="show",clearTimeout(r),r=setTimeout(function(){t.hide()},200)})})}else e.on("mouseover."+o,function(){t.show()}),e.on("mouseout."+o,function(){t.hide()});t.settings.ajaxContentUrl&&(t.ajaxContent=null)},tooltip:function(){return this.tipso_bubble||(this.tipso_bubble=t('<div class="tipso_bubble"><div class="tipso_title"></div><div class="tipso_content"></div><div class="tipso_arrow"></div></div>')),this.tipso_bubble},show:function(){var e=this.tooltip(),r=this,s=this.win;!1===r.settings.showArrow?e.find(".tipso_arrow").hide():e.find(".tipso_arrow").show(),"hide"===r.mode&&(t.isFunction(r.settings.onBeforeShow)&&r.settings.onBeforeShow(r.$element,r.element,r),r.settings.size&&e.addClass(r.settings.size),r.settings.width?e.css({background:r.settings.background,color:r.settings.color,width:r.settings.width}).hide():r.settings.maxWidth?e.css({background:r.settings.background,color:r.settings.color,maxWidth:r.settings.maxWidth}).hide():e.css({background:r.settings.background,color:r.settings.color,width:200}).hide(),e.find(".tipso_title").css({background:r.settings.titleBackground,color:r.settings.titleColor}),e.find(".tipso_content").html(DOMPurify.sanitize(r.content())),e.find(".tipso_title").html(r.titleContent()),a(r),s.on("resize."+o,function t(){r.settings.position=r.settings.preferedPosition,a(r)}),window.clearTimeout(r.timeout),r.timeout=null,r.timeout=window.setTimeout(function(){r.ieFade||""===r.settings.animationIn||""===r.settings.animationOut?e.appendTo("body").stop(!0,!0).fadeIn(r.settings.speed,function(){r.mode="show",t.isFunction(r.settings.onShow)&&r.settings.onShow(r.$element,r.element,r)}):e.remove().appendTo("body").stop(!0,!0).removeClass("animated "+r.settings.animationOut).addClass("noAnimation").removeClass("noAnimation").addClass("animated "+r.settings.animationIn).fadeIn(r.settings.speed,function(){t(this).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){t(this).removeClass("animated "+r.settings.animationIn)}),r.mode="show",t.isFunction(r.settings.onShow)&&r.settings.onShow(r.$element,r.element,r),s.off("resize."+o,null,"tipsoResizeHandler")})},r.settings.delay))},hide:function(e){var r=this,s=this.win,i=this.tooltip(),n=r.settings.hideDelay;e&&(n=0,r.mode="show"),window.clearTimeout(r.timeout),r.timeout=null,r.timeout=window.setTimeout(function(){"tooltipHover"!==r.mode&&(r.ieFade||""===r.settings.animationIn||""===r.settings.animationOut?i.stop(!0,!0).fadeOut(r.settings.speed,function(){t(this).remove(),t.isFunction(r.settings.onHide)&&"show"===r.mode&&r.settings.onHide(r.$element,r.element,r),r.mode="hide",s.off("resize."+o,null,"tipsoResizeHandler")}):i.stop(!0,!0).removeClass("animated "+r.settings.animationIn).addClass("noAnimation").removeClass("noAnimation").addClass("animated "+r.settings.animationOut).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){t(this).removeClass("animated "+r.settings.animationOut).remove(),t.isFunction(r.settings.onHide)&&"show"===r.mode&&r.settings.onHide(r.$element,r.element,r),r.mode="hide",s.off("resize."+o,null,"tipsoResizeHandler")}))},n)},close:function(){this.hide(!0)},destroy:function(){var t=this.$element,e=this.win;this.doc,t.off("."+o),e.off("resize."+o,null,"tipsoResizeHandler"),t.removeData(o),t.removeClass("tipso_style").attr("title",this._title)},titleContent:function(){var t,o=this.$element;return this.settings.titleContent?this.settings.titleContent:o.data("tipso-title")},content:function(){var o,e=this.$element,r=this,s=this._title;return r.settings.ajaxContentUrl?r._ajaxContent?o=r._ajaxContent:(r._ajaxContent=o=t.ajax({type:"GET",url:r.settings.ajaxContentUrl,async:!1}).responseText,r.settings.ajaxContentBuffer>0?setTimeout(function(){r._ajaxContent=null},r.settings.ajaxContentBuffer):r._ajaxContent=null):r.settings.contentElementId?o=t("#"+r.settings.contentElementId).text():r.settings.content?o=r.settings.content:!0===r.settings.useTitle?o=s:"string"==typeof e.data("tipso")&&(o=e.data("tipso")),null!==r.settings.templateEngineFunc&&(o=r.settings.templateEngineFunc(o)),o},update:function(t,o){var e=this;if(!o)return e.settings[t];e.settings[t]=o}});var i=function(){var t=document.createElement("p").style,o=["ms","O","Moz","Webkit"];if(""===t.transition)return!0;for(;o.length;)if(o.pop()+"Transition" in t)return!0;return!1}();function n(t){t.removeClass("top_right_corner bottom_right_corner top_left_corner bottom_left_corner"),t.find(".tipso_title").removeClass("top_right_corner bottom_right_corner top_left_corner bottom_left_corner")}function a(o){var e,r,i,l=o.tooltip(),d=o.$element,p=o,f=t(window),g=p.settings.background,c=p.titleContent();switch(void 0!==c&&""!==c&&(g=p.settings.titleBackground),d.parent().outerWidth()>f.outerWidth()&&(f=d.parent()),p.settings.position){case"top-right":r=d.offset().left+d.outerWidth(),e=d.offset().top-s(l).height-10,l.find(".tipso_arrow").css({marginLeft:-p.settings.arrowWidth,marginTop:""}),e<f.scrollTop()?(e=d.offset().top+d.outerHeight()+10,l.find(".tipso_arrow").css({"border-bottom-color":g,"border-top-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.addClass("bottom_right_corner"),l.find(".tipso_title").addClass("bottom_right_corner"),l.find(".tipso_arrow").css({"border-left-color":g}),l.removeClass("top-right top bottom left right"),l.addClass("bottom")):(l.find(".tipso_arrow").css({"border-top-color":p.settings.background,"border-bottom-color":"transparent ","border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.addClass("top_right_corner"),l.find(".tipso_arrow").css({"border-left-color":p.settings.background}),l.removeClass("top bottom left right"),l.addClass("top"));break;case"top-left":r=d.offset().left-s(l).width,e=d.offset().top-s(l).height-10,l.find(".tipso_arrow").css({marginLeft:-p.settings.arrowWidth,marginTop:""}),e<f.scrollTop()?(e=d.offset().top+d.outerHeight()+10,l.find(".tipso_arrow").css({"border-bottom-color":g,"border-top-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.addClass("bottom_left_corner"),l.find(".tipso_title").addClass("bottom_left_corner"),l.find(".tipso_arrow").css({"border-right-color":g}),l.removeClass("top-right top bottom left right"),l.addClass("bottom")):(l.find(".tipso_arrow").css({"border-top-color":p.settings.background,"border-bottom-color":"transparent ","border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.addClass("top_left_corner"),l.find(".tipso_arrow").css({"border-right-color":p.settings.background}),l.removeClass("top bottom left right"),l.addClass("top"));break;case"bottom-right":r=d.offset().left+d.outerWidth(),e=d.offset().top+d.outerHeight()+10,l.find(".tipso_arrow").css({marginLeft:-p.settings.arrowWidth,marginTop:""}),e+s(l).height>f.scrollTop()+f.outerHeight()?(e=d.offset().top-s(l).height-10,l.find(".tipso_arrow").css({"border-bottom-color":"transparent","border-top-color":p.settings.background,"border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.addClass("top_right_corner"),l.find(".tipso_title").addClass("top_left_corner"),l.find(".tipso_arrow").css({"border-left-color":p.settings.background}),l.removeClass("top-right top bottom left right"),l.addClass("top")):(l.find(".tipso_arrow").css({"border-top-color":"transparent","border-bottom-color":g,"border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.addClass("bottom_right_corner"),l.find(".tipso_title").addClass("bottom_right_corner"),l.find(".tipso_arrow").css({"border-left-color":g}),l.removeClass("top bottom left right"),l.addClass("bottom"));break;case"bottom-left":r=d.offset().left-s(l).width,e=d.offset().top+d.outerHeight()+10,l.find(".tipso_arrow").css({marginLeft:-p.settings.arrowWidth,marginTop:""}),e+s(l).height>f.scrollTop()+f.outerHeight()?(e=d.offset().top-s(l).height-10,l.find(".tipso_arrow").css({"border-bottom-color":"transparent","border-top-color":p.settings.background,"border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.addClass("top_left_corner"),l.find(".tipso_title").addClass("top_left_corner"),l.find(".tipso_arrow").css({"border-right-color":p.settings.background}),l.removeClass("top-right top bottom left right"),l.addClass("top")):(l.find(".tipso_arrow").css({"border-top-color":"transparent","border-bottom-color":g,"border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.addClass("bottom_left_corner"),l.find(".tipso_title").addClass("bottom_left_corner"),l.find(".tipso_arrow").css({"border-right-color":g}),l.removeClass("top bottom left right"),l.addClass("bottom"));break;case"top":r=d.offset().left+d.outerWidth()/2-s(l).width/2,e=d.offset().top-s(l).height-10,l.find(".tipso_arrow").css({marginLeft:-p.settings.arrowWidth,marginTop:""}),e<f.scrollTop()?(e=d.offset().top+d.outerHeight()+10,l.find(".tipso_arrow").css({"border-bottom-color":g,"border-top-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),l.removeClass("top bottom left right"),l.addClass("bottom")):(l.find(".tipso_arrow").css({"border-top-color":p.settings.background,"border-bottom-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),l.removeClass("top bottom left right"),l.addClass("top"));break;case"bottom":r=d.offset().left+d.outerWidth()/2-s(l).width/2,e=d.offset().top+d.outerHeight()+10,l.find(".tipso_arrow").css({marginLeft:-p.settings.arrowWidth,marginTop:""}),e+s(l).height>f.scrollTop()+f.outerHeight()?(e=d.offset().top-s(l).height-10,l.find(".tipso_arrow").css({"border-top-color":p.settings.background,"border-bottom-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),l.removeClass("top bottom left right"),l.addClass("top")):(l.find(".tipso_arrow").css({"border-bottom-color":g,"border-top-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),l.removeClass("top bottom left right"),l.addClass(p.settings.position));break;case"left":r=d.offset().left-s(l).width-10,e=d.offset().top+d.outerHeight()/2-s(l).height/2,l.find(".tipso_arrow").css({marginTop:-p.settings.arrowWidth,marginLeft:""}),r<f.scrollLeft()?(r=d.offset().left+d.outerWidth()+10,l.find(".tipso_arrow").css({"border-right-color":p.settings.background,"border-left-color":"transparent","border-top-color":"transparent","border-bottom-color":"transparent"}),l.removeClass("top bottom left right"),l.addClass("right")):(l.find(".tipso_arrow").css({"border-left-color":p.settings.background,"border-right-color":"transparent","border-top-color":"transparent","border-bottom-color":"transparent"}),l.removeClass("top bottom left right"),l.addClass(p.settings.position));break;case"right":r=d.offset().left+d.outerWidth()+10,e=d.offset().top+d.outerHeight()/2-s(l).height/2,l.find(".tipso_arrow").css({marginTop:-p.settings.arrowWidth,marginLeft:""}),r+10+p.settings.width>f.scrollLeft()+f.outerWidth()?(r=d.offset().left-s(l).width-10,l.find(".tipso_arrow").css({"border-left-color":p.settings.background,"border-right-color":"transparent","border-top-color":"transparent","border-bottom-color":"transparent"}),l.removeClass("top bottom left right"),l.addClass("left")):(l.find(".tipso_arrow").css({"border-right-color":p.settings.background,"border-left-color":"transparent","border-top-color":"transparent","border-bottom-color":"transparent"}),l.removeClass("top bottom left right"),l.addClass(p.settings.position))}if("top-right"===p.settings.position&&l.find(".tipso_arrow").css({"margin-left":-p.settings.width/2}),"top-left"===p.settings.position){var h=l.find(".tipso_arrow").eq(0);h.css({"margin-left":p.settings.width/2-2*p.settings.arrowWidth})}if("bottom-right"===p.settings.position){var h=l.find(".tipso_arrow").eq(0);h.css({"margin-left":-p.settings.width/2,"margin-top":""})}if("bottom-left"===p.settings.position){var h=l.find(".tipso_arrow").eq(0);h.css({"margin-left":p.settings.width/2-2*p.settings.arrowWidth,"margin-top":""})}r<f.scrollLeft()&&("bottom"===p.settings.position||"top"===p.settings.position)&&(l.find(".tipso_arrow").css({marginLeft:r-p.settings.arrowWidth}),r=0),r+p.settings.width>f.outerWidth()&&("bottom"===p.settings.position||"top"===p.settings.position)&&(i=f.outerWidth()-(r+p.settings.width),l.find(".tipso_arrow").css({marginLeft:-i-p.settings.arrowWidth,marginTop:""}),r+=i),r<f.scrollLeft()&&("left"===p.settings.position||"right"===p.settings.position||"top-right"===p.settings.position||"top-left"===p.settings.position||"bottom-right"===p.settings.position||"bottom-left"===p.settings.position)&&(r=d.offset().left+d.outerWidth()/2-s(l).width/2,l.find(".tipso_arrow").css({marginLeft:-p.settings.arrowWidth,marginTop:""}),(e=d.offset().top-s(l).height-10)<f.scrollTop()?(e=d.offset().top+d.outerHeight()+10,l.find(".tipso_arrow").css({"border-bottom-color":g,"border-top-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),l.removeClass("top bottom left right"),n(l),l.addClass("bottom")):(l.find(".tipso_arrow").css({"border-top-color":p.settings.background,"border-bottom-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),l.removeClass("top bottom left right"),n(l),l.addClass("top")),r+p.settings.width>f.outerWidth()&&(i=f.outerWidth()-(r+p.settings.width),l.find(".tipso_arrow").css({marginLeft:-i-p.settings.arrowWidth,marginTop:""}),r+=i),r<f.scrollLeft()&&(l.find(".tipso_arrow").css({marginLeft:r-p.settings.arrowWidth}),r=0)),r+p.settings.width>f.outerWidth()&&("left"===p.settings.position||"right"===p.settings.position||"top-right"===p.settings.position||"top-left"===p.settings.position||"bottom-right"===p.settings.position||"bottom-right"===p.settings.position)&&(r=d.offset().left+d.outerWidth()/2-s(l).width/2,l.find(".tipso_arrow").css({marginLeft:-p.settings.arrowWidth,marginTop:""}),(e=d.offset().top-s(l).height-10)<f.scrollTop()?(e=d.offset().top+d.outerHeight()+10,l.find(".tipso_arrow").css({"border-bottom-color":g,"border-top-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.removeClass("top bottom left right"),l.addClass("bottom")):(l.find(".tipso_arrow").css({"border-top-color":p.settings.background,"border-bottom-color":"transparent","border-left-color":"transparent","border-right-color":"transparent"}),n(l),l.removeClass("top bottom left right"),l.addClass("top")),r+p.settings.width>f.outerWidth()&&(i=f.outerWidth()-(r+p.settings.width),l.find(".tipso_arrow").css({marginLeft:-i-p.settings.arrowWidth,marginTop:""}),r+=i),r<f.scrollLeft()&&(l.find(".tipso_arrow").css({marginLeft:r-p.settings.arrowWidth}),r=0)),l.css({left:r+p.settings.offsetX,top:e+p.settings.offsetY}),e<f.scrollTop()&&("right"===p.settings.position||"left"===p.settings.position)&&(d.tipso("update","position","bottom"),a(p)),e+s(l).height>f.scrollTop()+f.outerHeight()&&("right"===p.settings.position||"left"===p.settings.position)&&(d.tipso("update","position","top"),a(p))}t[o]=t.fn[o]=function(s){var i,n=arguments;return void 0===s||"object"==typeof s?(this instanceof t||t.extend(e,s),this.each(function(){t.data(this,"plugin_"+o)||t.data(this,"plugin_"+o,new r(this,s))})):"string"==typeof s&&"_"!==s[0]&&"init"!==s?(this.each(function(){var e=t.data(this,"plugin_"+o);e||(e=t.data(this,"plugin_"+o,new r(this,s))),e instanceof r&&"function"==typeof e[s]&&(i=e[s].apply(e,Array.prototype.slice.call(n,1))),"destroy"===s&&t.data(this,"plugin_"+o,null)}),void 0!==i?i:this):void 0}});