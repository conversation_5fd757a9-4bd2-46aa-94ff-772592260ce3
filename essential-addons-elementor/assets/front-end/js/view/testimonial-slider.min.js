!function(e){var t={};function a(i){if(t[i])return t[i].exports;var o=t[i]={i:i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,a),o.l=!0,o.exports}a.m=e,a.c=t,a.d=function(e,t,i){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(a.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(i,o,function(t){return e[t]}.bind(null,o));return i},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=33)}({33:function(e,t){function a(e,t){var a=n("items_tablet",t),i=n("items_mobile",t),o=void 0!==e.data("pagination")?e.data("pagination"):".swiper-pagination",r=void 0!==e.data("arrow-next")?e.data("arrow-next"):".swiper-button-next",d=void 0!==e.data("arrow-prev")?e.data("arrow-prev"):".swiper-button-prev",l=void 0!==e.data("items")?e.data("items"):3,s=void 0!==a?a:3,u=void 0!==i?i:3,c=void 0!==e.data("slide-items")?e.data("slide-items"):1,p=void 0!==e.data("slide-items-tablet")?e.data("slide-items-tablet"):1,f=void 0!==e.data("slide-items-mobile")?e.data("slide-items-mobile"):1,v=void 0!==e.data("margin")?e.data("margin"):10,m=void 0!==e.data("margin-tablet")?e.data("margin-tablet"):10,g=void 0!==e.data("margin-mobile")?e.data("margin-mobile"):10,b=void 0!==e.data("effect")?e.data("effect"):"slide",w=void 0!==e.data("speed")?e.data("speed"):400,h=void 0!==e.data("autoplay_speed")?e.data("autoplay_speed"):999999,y=void 0!==e.data("loop")?e.data("loop"):0,_=void 0!==e.data("grab-cursor")?e.data("grab-cursor"):0,x="coverflow"==b,P={pause_on_hover:void 0!==e.data("pause-on-hover")?e.data("pause-on-hover"):"",direction:"horizontal",speed:w,effect:b,centeredSlides:x,grabCursor:_,autoHeight:!0,loop:y,autoplay:{delay:h,disableOnInteraction:!1},pagination:{el:o,clickable:!0},navigation:{nextEl:r,prevEl:d}};return"slide"===b||"coverflow"===b?P.breakpoints={1024:{slidesPerView:l,spaceBetween:v,slidesPerGroup:c},768:{slidesPerView:s,spaceBetween:m,slidesPerGroup:p},320:{slidesPerView:u,spaceBetween:g,slidesPerGroup:f}}:P.items=1,"fade"===b&&(P.fadeEffect={crossFade:!0}),P}function i(e,t,a){var i;0===t.autoplay.delay&&(null==e||null===(i=e.autoplay)||void 0===i||i.stop());t.pause_on_hover&&0!==t.autoplay.delay&&(e.on("mouseenter",(function(){var e;null==a||null===(e=a.autoplay)||void 0===e||e.stop()})),e.on("mouseleave",(function(){var e;null==a||null===(e=a.autoplay)||void 0===e||e.start()}))),a.update()}var o=function(e,t){var o=e.find(".eael-testimonial-slider-main").eq(0),n=a(o,e);r(o,n).then((function(t){i(o,n,t);var a=e.find(".eael-testimonial-slider .eael-testimonial-gallary-pagination").eq(0);a.length>0&&r(a,{spaceBetween:20,centeredSlides:!0,touchRatio:.2,slideToClickedSlide:!0,loop:!0,slidesPerGroup:1,loopedSlides:$items,slidesPerView:3}).then((function(e){t.controller.control=e,e.controller.control=t}))})),t(e).on("click",".eael-testimonial-read-more-btn",(function(e){e.preventDefault();var a=t(this).closest(".eael-testimonial-item-inner");t(".eael-testimonial-text-excerpt",a).toggle(),t(".eael-testimonial-text-full-text",a).toggle(),t(".swiper-wrapper").height("auto"),window.dispatchEvent(new Event("resize"))}));var d=function(e){var o=t(e).find(".eael-testimonial-slider-main");o.length&&o.each((function(){if(t(this)[0].swiper){t(this)[0].swiper.destroy(!0,!0);var o=a(t(this),e),n=t(this);r(t(this)[0],o).then((function(e){i(n,o,e)}))}}))};eael.hooks.addAction("ea-toggle-triggered","ea",d),eael.hooks.addAction("ea-lightbox-triggered","ea",d),eael.hooks.addAction("ea-advanced-tabs-triggered","ea",d),eael.hooks.addAction("ea-advanced-accordion-triggered","ea",d)},n=function(e,t){var a,i,o;return eael.isEditMode?null===(a=elementorFrontend.config.elements)||void 0===a||null===(a=a.data[null===(i=t[0])||void 0===i?void 0:i.dataset.modelCid])||void 0===a||null===(a=a.attributes[e])||void 0===a?void 0:a.size:null===(o=t=jQuery(t))||void 0===o||null===(o=o.data("settings"))||void 0===o||null===(o=o[e])||void 0===o?void 0:o.size},r=function(e,t){return"undefined"==typeof Swiper||"function"==typeof Swiper?new(0,elementorFrontend.utils.swiper)(e,t).then((function(e){return e})):d(e,t)},d=function(e,t){return new Promise((function(a,i){a(new Swiper(e,t))}))};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("testimonialLoad"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-testimonial-slider.default",o)}))}});