!function(e){var t={};function a(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,a),r.l=!0,r.exports}a.m=e,a.c=t,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)a.d(n,r,function(t){return e[t]}.bind(null,r));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=35)}({35:function(e,t){function a(e){var t=void 0!==e.data("pagination")?e.data("pagination"):".swiper-pagination",a=void 0!==e.data("arrow-next")?e.data("arrow-next"):".swiper-button-next",n=void 0!==e.data("arrow-prev")?e.data("arrow-prev"):".swiper-button-prev",r=void 0!==e.data("items")?e.data("items"):3,o=void 0!==e.data("items-tablet")?e.data("items-tablet"):3,i=void 0!==e.data("items-mobile")?e.data("items-mobile"):3,d=void 0!==e.data("margin")?e.data("margin"):10,l=""!==e.data("margin-tablet")?e.data("margin-tablet"):10,u=""!==e.data("margin-mobile")?e.data("margin-mobile"):10,s=void 0!==e.data("effect")?e.data("effect"):"slide",c=void 0!==e.data("speed")?e.data("speed"):400,f=void 0!==e.data("autoplay")?e.data("autoplay"):0,p=void 0!==e.data("loop")?e.data("loop"):0,v=void 0!==e.data("grab-cursor")?e.data("grab-cursor"):0,g="coverflow"===s,b={pause_on_hover:void 0!==e.data("pause-on-hover")?e.data("pause-on-hover"):"",direction:"horizontal",speed:c,effect:s,fadeEffect:{crossFade:!0},centeredSlides:g,grabCursor:v,autoHeight:!0,loop:p,autoplay:{delay:f,disableOnInteraction:!1},pagination:{el:t,clickable:!0},navigation:{nextEl:a,prevEl:n}};return 0===f&&(b.autoplay=!1),"slide"===s||"coverflow"===s?b.breakpoints={1024:{slidesPerView:r,spaceBetween:d},768:{slidesPerView:o,spaceBetween:l},320:{slidesPerView:i,spaceBetween:u}}:b.items=1,b}function n(e,t,a){var n;0===t.autoplay.delay&&(null==a||null===(n=a.autoplay)||void 0===n||n.stop());t.pause_on_hover&&0!==t.autoplay.delay&&(e.on("mouseenter",(function(){var e;null==a||null===(e=a.autoplay)||void 0===e||e.stop()})),e.on("mouseleave",(function(){var e;null==a||null===(e=a.autoplay)||void 0===e||e.start()})))}var r=function(e,t){var r=t(".eael-twitter-feed-carousel",e),i=a(r);o(r,i).then((function(e){n(r,i,e)}));var d=function(e){var r=t(e).find(".eael-twitter-feed-carousel");r.length&&r.each((function(){var e=t(this);if(e[0].swiper){e[0].swiper.destroy(!0,!0);var r=a(e);o(t(this)[0],r).then((function(t){n(e,r,t)}))}}))};eael.hooks.addAction("ea-toggle-triggered","ea",d),eael.hooks.addAction("ea-lightbox-triggered","ea",d),eael.hooks.addAction("ea-advanced-tabs-triggered","ea",d),eael.hooks.addAction("ea-advanced-accordion-triggered","ea",d),isEditMode&&elementor.hooks.addAction("panel/open_editor/widget/eael-twitter-feed-carousel",(function(e,t,a){e.content.el.onclick=function(e){if("ea:cache:clear"==e.target.dataset.event){var a=e.target;a.innerHTML="Clearing...",jQuery.ajax({url:localize.ajaxurl,type:"post",data:{action:"eael_clear_widget_cache_data",security:localize.nonce,ac_name:t.attributes.settings.attributes.eael_twitter_feed_ac_name,hastag:t.attributes.settings.attributes.eael_twitter_feed_hashtag_name,c_key:t.attributes.settings.attributes.eael_twitter_feed_consumer_key,c_secret:t.attributes.settings.attributes.eael_twitter_feed_consumer_secret},success:function(e){e.success?a.innerHTML="Clear":a.innerHTML="Failed"},error:function(){a.innerHTML="Failed"}})}}}))},o=function(e,t){return"undefined"==typeof Swiper||"function"==typeof Swiper?new(0,elementorFrontend.utils.swiper)(e,t).then((function(e){return e})):i(e,t)},i=function(e,t){return new Promise((function(a,n){a(new Swiper(e,t))}))};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("twitterFeedLoad"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-twitter-feed-carousel.default",r)}))}});