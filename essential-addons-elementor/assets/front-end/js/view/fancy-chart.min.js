!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=8)}({8:function(e,t){var n=function(e,t){var n=t(".eael_fanct_chart_wrapper",e).data("options");"undefined"===n.tooltip.y.prefix&&"undefined"===n.tooltip.y.suffix||(n.tooltip.y.formatter=function(e){return n.tooltip.y.prefix+e+n.tooltip.y.suffix});var r=t(".eael_fancy_chart",e).attr("id");void 0!==n&&new ApexCharts(document.querySelector("#"+r),n).render()};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("fancyChart"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-fancy-chart.default",n)}))}});