!function(e){var t={};function a(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,a),o.l=!0,o.exports}a.m=e,a.c=t,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(n,o,function(t){return e[t]}.bind(null,o));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=32)}({32:function(e,t){var a=function(e,t){var a=e.find(".eael-tm-carousel").eq(0),o=void 0!==a.data("pagination")?a.data("pagination"):".swiper-pagination",r=void 0!==a.data("arrow-next")?a.data("arrow-next"):".swiper-button-next",i=void 0!==a.data("arrow-prev")?a.data("arrow-prev"):".swiper-button-prev",d=void 0!==a.data("items")?a.data("items"):3,l=void 0!==a.data("margin")?a.data("margin"):10,s=void 0!==a.data("speed")?a.data("speed"):400,u=void 0!==a.data("autoplay")?a.data("autoplay"):999999,p=void 0!==a.data("loop")?a.data("loop"):0,c=void 0!==a.data("grab-cursor")?a.data("grab-cursor"):0,f=(void 0!==a.data("id")&&a.data("id"),void 0!==a.data("pause-on-hover")?a.data("pause-on-hover"):""),v={},b={},m=0,w=localize.el_breakpoints.widescreen.is_enabled?localize.el_breakpoints.widescreen.value-1:4800;v[m]={breakpoint:0,slidesPerView:0,spaceBetween:0},m++,localize.el_breakpoints.desktop={is_enabled:!0,value:w},t.each(["mobile","mobile_extra","tablet","tablet_extra","laptop","desktop","widescreen"],(function(e,t){var n=localize.el_breakpoints[t];if(n.is_enabled){var o=a.data("items-"+t),r=a.data("margin-"+t);$margin=void 0!==r?r:"desktop"===t?l:10,$items=void 0!==o&&""!==o?o:"desktop"===t?d:3,v[m]={breakpoint:n.value,slidesPerView:$items,spaceBetween:$margin},m++}})),t.each(v,(function(e,t){var a=parseInt(e);void 0!==v[a+1]&&(b[t.breakpoint]={slidesPerView:v[a+1].slidesPerView,spaceBetween:v[a+1].spaceBetween})}));var g={direction:"horizontal",speed:s,grabCursor:c,loop:p,observer:!0,observeParents:!0,autoplay:{delay:u,disableOnInteraction:!1},pagination:{el:o,clickable:!0},navigation:{nextEl:r,prevEl:i},breakpoints:b};n(a,g).then((function(e){0==u&&e.autoplay.stop(),f&&0!==u&&(a.on("mouseenter",(function(){e.autoplay.stop()})),a.on("mouseleave",(function(){e.autoplay.start()}))),e.update()}));var y=function(e){var a=t(e).find(".eael-tm-carousel");a.length&&a.each((function(){t(this)[0].swiper&&(t(this)[0].swiper.destroy(!0,!0),n(t(this)[0],g))}))};eael.hooks.addAction("ea-lightbox-triggered","ea",y),eael.hooks.addAction("ea-advanced-tabs-triggered","ea",y),eael.hooks.addAction("ea-advanced-accordion-triggered","ea",y)},n=function(e,t){return"undefined"==typeof Swiper||"function"==typeof Swiper?new(0,elementorFrontend.utils.swiper)(e,t).then((function(e){return e})):o(e,t)},o=function(e,t){return new Promise((function(a,n){a(new Swiper(e,t))}))};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("teamMemberSliderLoad"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-team-member-carousel.default",a)}))}});