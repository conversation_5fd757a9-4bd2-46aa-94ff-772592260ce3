!function(e){var a={};function o(t){if(a[t])return a[t].exports;var n=a[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,o),n.l=!0,n.exports}o.m=e,o.c=a,o.d=function(e,a,t){o.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:t})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,a){if(1&a&&(e=o(e)),8&a)return e;if(4&a&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(o.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&a&&"string"!=typeof e)for(var n in e)o.d(t,n,function(a){return e[a]}.bind(null,n));return t},o.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(a,"a",a),a},o.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)},o.p="",o(o.s=36)}({36:function(e,a){var o=function(e,a){a(".eael-account-dashboard-wrapper",e);var o=a(".eael-account-dashboard-wrap",e),t=o.data("eawct-icons");if(o.hasClass("eawct-has-custom-tab")&&t&&(t=JSON.parse(atob(t)))&&a.each(t,(function(e,o){o&&a(".eael-custom-tab-"+e).removeClass("eael-wcd-icon").find("a").prepend(o)})),elementorFrontend.isEditMode()&&a(".eael-account-dashboard-navbar li, .woocommerce-orders-table__cell-order-actions .view",e).on("click",(function(){var o="woocommerce-MyAccount-navigation-link",t=a(this).attr("class").split(" "),n="";t.length&&t.forEach((function(e){e.includes(o+"--")&&(n=e.replace(o+"--",""))}));var r=a(this).attr("class");r.includes("woocommerce-button")&&r.includes("view")&&(n="view-order"),a(".eael-account-dashboard-body .".concat(o),e).removeClass("is-active"),a(".eael-account-dashboard-body .".concat(o,"--").concat(n),e).addClass("is-active"),a(".eael-account-dashboard-body .tab-content",e).removeClass("active"),a(".eael-account-dashboard-body .tab-".concat(n),e).addClass("active");var c=n[0].toUpperCase()+n.substring(1);a(".eael-account-dashboard-header h3",e).html(c)})),a("body").hasClass("theme-oceanwp")){var n=a(".eael-account-dashboard-navbar .woocommerce-MyAccount-navigation");a(".eael-account-dashboard-navbar .woocommerce-MyAccount-tabs").remove(),a(".eael-account-dashboard-navbar").append(n)}};eael.hooks.addAction("init","ea",(function(){if(eael.elementStatusCheck("eaelAccountDashboard"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-woo-account-dashboard.default",o)}))}});