!function(e){var t={};function a(i){if(t[i])return t[i].exports;var o=t[i]={i:i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,a),o.l=!0,o.exports}a.m=e,a.c=t,a.d=function(e,t,i){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(a.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(i,o,function(t){return e[t]}.bind(null,o));return i},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=19)}({19:function(e,t){var a=function(e,t){var o=n("items_tablet",e),d=n("items_mobile",e),l=e.find(".eael-logo-carousel").eq(0),s=void 0!==l.data("items")?l.data("items"):3,u=void 0!==o?o:3,c=void 0!==d?d:3,p=void 0!==l.data("margin")?l.data("margin"):10,f=void 0!==l.data("margin-tablet")?l.data("margin-tablet"):10,v=void 0!==l.data("margin-mobile")?l.data("margin-mobile"):10,g=void 0!==l.data("effect")?l.data("effect"):"slide",b=void 0!==l.data("speed")?l.data("speed"):400,m=void 0!==l.data("autoplay")?l.data("autoplay"):999999,w=void 0!==l.data("loop")?l.data("loop"):0,h=void 0!==l.data("grab-cursor")?l.data("grab-cursor"):0,y=void 0!==l.data("pagination")?l.data("pagination"):".swiper-pagination",_=void 0!==l.data("arrow-next")?l.data("arrow-next"):".swiper-button-next",k=void 0!==l.data("arrow-prev")?l.data("arrow-prev"):".swiper-button-prev",P=void 0!==l.data("pause-on-hover")?l.data("pause-on-hover"):"",x={direction:"horizontal",speed:b,effect:g,grabCursor:h,paginationClickable:!0,autoHeight:!0,loop:w,observer:!0,observeParents:!0,autoplay:{delay:m,disableOnInteraction:!1},pagination:{el:y,clickable:!0},navigation:{nextEl:_,prevEl:k}};if("slide"===g||"coverflow"===g)if("string"==typeof localize.el_breakpoints)x.breakpoints={1024:{slidesPerView:s,spaceBetween:p},768:{slidesPerView:u,spaceBetween:f},320:{slidesPerView:c,spaceBetween:v}};else{var z={},S={},O=0,j=localize.el_breakpoints.widescreen.is_enabled?localize.el_breakpoints.widescreen.value-1:4800;z[O]={breakpoint:0,slidesPerView:0,spaceBetween:0},O++,localize.el_breakpoints.desktop={is_enabled:!0,value:j},t.each(["mobile","mobile_extra","tablet","tablet_extra","laptop","desktop","widescreen"],(function(t,a){var i=localize.el_breakpoints[a];if(i.is_enabled){var o=n("items_"+a,e),r=l.data("margin-"+a);$margin=void 0!==r?r:"desktop"===a?p:10,$items=void 0!==o&&""!==o?o:"desktop"===a?s:3,z[O]={breakpoint:i.value,slidesPerView:$items,spaceBetween:$margin},O++}})),t.each(z,(function(e,t){var a=parseInt(e);void 0!==z[a+1]&&(S[t.breakpoint]={slidesPerView:z[a+1].slidesPerView,spaceBetween:z[a+1].spaceBetween})})),x.breakpoints=S}else x.items=1;i(l,x).then((function(a){P&&(l.on("mouseenter",(function(){a.autoplay.stop()})),l.on("mouseleave",(function(){a.autoplay.start()}))),r(e,t)}));var B=function(e){var a=t(e).find(".eael-logo-carousel");a.length&&a.each((function(){t(this)[0].swiper&&(t(this)[0].swiper.destroy(!0,!0),i(t(this)[0],x))}))};eael.hooks.addAction("ea-lightbox-triggered","ea",B),eael.hooks.addAction("ea-advanced-tabs-triggered","ea",B),eael.hooks.addAction("ea-advanced-accordion-triggered","ea",B),t(window).on("resize",(function(){(l=t("body").find(".eael-logo-carousel")).length>0&&l.each((function(){e=t(this).closest(".elementor-widget"),t(this)[0].swiper&&t(this)[0].swiper.destroy(!0,!0),a(e,t)}))}))},i=function(e,t){return"undefined"==typeof Swiper||"function"==typeof Swiper?new(0,elementorFrontend.utils.swiper)(e,t).then((function(e){return e})):o(e,t)},o=function(e,t){return new Promise((function(a,i){a(new Swiper(e,t))}))},n=function(e,t){var a,i,o;return eael.isEditMode?null===(a=elementorFrontend.config.elements)||void 0===a||null===(a=a.data[null===(i=t[0])||void 0===i?void 0:i.dataset.modelCid])||void 0===a||null===(a=a.attributes[e])||void 0===a?void 0:a.size:null==t||null===(o=t.data("settings"))||void 0===o||null===(o=o[e])||void 0===o?void 0:o.size},r=function(e,t){if(t.fn.tooltipster){var a=e.find(".swiper-slide-duplicate");a.length>0&&t.each(a,(function(e,a){var i=t(a).find(".eael-lc-tooltip").attr("id");t(a).find(".eael-lc-tooltip").attr("id",i+"-duplicate-"+e)}));var i=e.find(".eael-lc-tooltip");i.length>0&&t.each(i,(function(e,a){var i=t(a),o=void 0!==i.data("content")?i.data("content"):null,n=void 0!==i.data("side")&&i.data("side"),r=void 0!==i.data("trigger")?i.data("trigger"):"hover",d=void 0!==i.data("animation")?i.data("animation"):"fade",l=void 0!==i.data("animation_duration")?i.data("animation_duration"):300,s=void 0!==i.data("theme")?i.data("theme"):"default",u="yes"==i.data("arrow");i.tooltipster({animation:d,trigger:r,content:DOMPurify.sanitize(o),contentAsHTML:!0,side:n,delay:l,arrow:u,contentCloning:!0,theme:"tooltipster-"+s})}))}};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("eaelLogoSliderLoad"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-logo-carousel.default",a)}))}});