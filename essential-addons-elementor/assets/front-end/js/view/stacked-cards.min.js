!function(t){var r={};function e(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:n})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,r){if(1&r&&(t=e(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var o in t)e.d(n,o,function(r){return t[r]}.bind(null,o));return n},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},e.p="",e(e.s=31)}({31:function(t,r){function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function o(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?n(Object(e),!0).forEach((function(r){a(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):n(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function a(t,r,n){var o;return o=function(t,r){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,r||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(r,"string"),(r="symbol"==e(o)?o:o+"")in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}var i=function(t,r){window.onbeforeunload=function(){window.scrollTo({top:0,behavior:"smooth",duration:400})},gsap.registerPlugin(ScrollTrigger);var e=t.find(".eael-stacked-cards__container"),n=e.attr("data-cadr_style"),o=JSON.parse(e.attr("data-scrolltrigger")),a={verticalCards:gsap.utils.toArray(t.find(".eael-stacked-cards__item")),horizontalCards:gsap.utils.toArray(t.find(".eael-stacked-cards__item_hr")),container:e};c(t,r),"vertical"===n?d(a,o):"horizontal"===n&&s(a,o)},c=function(t,r){t.find(".eael-stacked-cards__link").each((function(t,e){var n=r(e),o=n.attr("style"),a=n.attr("data-hover-style");n.on("mouseenter",(function(){return n.attr("style",a)})).on("mouseleave",(function(){return n.attr("style",o)}))}))},d=function(t,r){var e=u(t.verticalCards);l(t.container,e,r,t.verticalCards.length)},u=function(t){var r=gsap.timeline();return t&&t.length?(t.forEach((function(t,r){var e=jQuery(t),n=e.data("start_form"),o=t.getAttribute("data-bgColor"),a=e.data("stacked_card"),i=1.1===(null==a?void 0:a.opacity)?0:null==a?void 0:a.opacity;gsap.set(t,{position:"absolute",top:0,left:0,filter:"blur(0px)",y:0===r?0:n*(r+1),opacity:0===r?1:i,rotation:0,backgroundColor:o})})),t.forEach((function(e,n){var a=jQuery(e).data("stacked_card")||{},i=parseInt(e.getAttribute("data-yaxis")),c=t[n-1],d=n===t.length-1;n>0?(window.addEventListener("resize",(function(){ScrollTrigger.refresh()})),r.to(e,{opacity:1,y:d?(a.y||0)+i:a.y||0,duration:1,ease:"Power2.out"},n),c&&r.to(c,o({},a),n)):window.addEventListener("load",(function(){window.addEventListener("resize",(function(){ScrollTrigger.refresh()})),r.to(e,{opacity:1,y:a.y||0,duration:1,ease:"Power2.out"},n),c&&r.to(c,o({},a),n)}))})),r):r},l=function(t,r,e,n){var o=n*window.innerHeight,a="default"===e.end?o:e.end;ScrollTrigger.create({trigger:t,invalidateOnRefresh:!0,animation:r,start:"top top+=".concat(e.start),end:"+=".concat(a),scrub:!0,pin:!0,markers:e.marker})},s=function(t,r){var e=f(t.container,r);g(t.horizontalCards,e)},f=function(t,r){return gsap.timeline({scrollTrigger:{trigger:t,pin:!0,scrub:.5,start:"top ".concat(r.start),end:"bottom ".concat(r.end),markers:r.marker,invalidateOnRefresh:!0}})},g=function(t,r){var e=gsap.matchMedia();e.add("(min-width: 1000px)",(function(){t.forEach((function(t,e){var n=t.getAttribute("data-bgColor"),a=jQuery(t).data("stacked_card_hr");gsap.set(t,{backgroundColor:n}),e>0&&(window.addEventListener("resize",(function(){ScrollTrigger.refresh()})),r.fromTo(t,{y:0,x:window.innerWidth/1.125+0*e,stagger:.5,backgroundColor:n,opacity:0},o(o({y:0,opacity:1},a),{},{stagger:.5,backgroundColor:n,zIndex:e+1})))}))})),e.add("(min-width: 800px) and (max-width: 999px)",(function(){t.forEach((function(t,e){var n=t.getAttribute("data-bgColor"),o=jQuery(t).data("stacked_card_hr");gsap.set(t,{backgroundColor:n}),e>0&&(window.addEventListener("resize",(function(){ScrollTrigger.refresh()})),r.fromTo(t,{x:0,y:window.innerWidth/1.125+0*e,stagger:.5,backgroundColor:n,opacity:0},{x:0,y:o.x,opacity:1,rotation:o.rotation,stagger:.5,backgroundColor:n,zIndex:e+1}))}))})),e.add("(max-width: 799px)",(function(){t.forEach((function(t,e){var n=t.getAttribute("data-bgColor"),o=jQuery(t).data("stacked_card_hr");gsap.set(t,{backgroundColor:n}),e>0&&(window.addEventListener("resize",(function(){ScrollTrigger.refresh()})),r.fromTo(t,{x:0,y:window.innerWidth/1.125+0*e,stagger:.5,backgroundColor:n,opacity:0},{x:0,y:o.x,opacity:1,rotation:o.rotation,stagger:.5,backgroundColor:n,zIndex:e+1}))}))}))};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("stackedCard"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-stacked-cards.default",i)}))}});