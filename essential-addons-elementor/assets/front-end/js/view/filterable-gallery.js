/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./src/js/view/filterable-gallery.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./src/js/view/filterable-gallery.js":
/*!*******************************************!*\
  !*** ./src/js/view/filterable-gallery.js ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t[\"return\"] || t[\"return\"](); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar filterableGallery = function filterableGallery($scope, $) {\n  var galleryScope = $(\".eael-filter-gallery-container\", $scope);\n  var dataSettings = galleryScope.data(\"settings\");\n\n  //Overlay Transition Duration\n  var transition_settings = $(\".eael-grid-fg-overlay\", $scope);\n  var transition_settings_data = transition_settings.data(\"transition\");\n\n  //Adjust the height of each grib box\n  document.addEventListener(\"DOMContentLoaded\", function () {\n    var imgHeight = document.querySelectorAll(\".eael-grid-fg-img\");\n    imgHeight.forEach(function (height) {\n      var gridBox = height.closest(\".eael-grid-fg-item\");\n      var shadow = gridBox.querySelector(\".eael-grid-fg-box\");\n      shadow.style.height = height.offsetHeight + \"px\";\n    });\n  });\n  var lineEq = function lineEq(y2, y1, x2, x1, currentVal) {\n    // y = mx + b\n    var m = (y2 - y1) / (x2 - x1),\n      b = y1 - m * x1;\n    return m * currentVal + b;\n  };\n  var getRandomInt = function getRandomInt(min, max) {\n    return Math.floor(Math.random() * (max - min + 1)) + min;\n  };\n  var getRandomFloat = function getRandomFloat(min, max) {\n    return (Math.random() * (max - min) + min).toFixed(2);\n  };\n  var setRange = function setRange(obj) {\n    for (var k in obj) {\n      if (obj[k] == undefined) {\n        obj[k] = [0, 0];\n      } else if (typeof obj[k] === \"number\") {\n        obj[k] = [-1 * obj[k], obj[k]];\n      }\n    }\n  };\n  var getMousePos = function getMousePos(e) {\n    var posx = 0;\n    var posy = 0;\n    if (!e) e = window.event;\n    if (e.pageX || e.pageY) {\n      posx = e.pageX;\n      posy = e.pageY;\n    } else if (e.clientX || e.clientY) {\n      posx = e.clientX + document.body.scrollLeft + document.documentElement.scrollLeft;\n      posy = e.clientY + document.body.scrollTop + document.documentElement.scrollTop;\n    }\n    return {\n      x: posx,\n      y: posy\n    };\n  };\n  var EAEL_GridFlowItem = /*#__PURE__*/function () {\n    function EAEL_GridFlowItem(el, options) {\n      _classCallCheck(this, EAEL_GridFlowItem);\n      this.DOM = {\n        el: el\n      };\n      this.options = {\n        image: {\n          translation: {\n            x: -10,\n            y: -10,\n            z: 0\n          },\n          rotation: {\n            x: 0,\n            y: 0,\n            z: 0\n          }\n        },\n        title: {\n          translation: {\n            x: 20,\n            y: 10,\n            z: 0\n          }\n        },\n        text: {\n          translation: {\n            x: 20,\n            y: 10,\n            z: 0\n          },\n          rotation: {\n            x: 0,\n            y: 0,\n            z: -5\n          }\n        },\n        icon: {\n          translation: {\n            x: -20,\n            y: 0,\n            z: 0\n          },\n          rotation: {\n            x: 0,\n            y: 0,\n            z: 3\n          }\n        },\n        shadow: {\n          translation: {\n            x: 20,\n            y: 10,\n            z: 0\n          },\n          rotation: {\n            x: 0,\n            y: 0,\n            z: -2\n          },\n          reverseAnimation: {\n            duration: 2,\n            ease: \"Back.easeOut\"\n          }\n        },\n        content: {\n          translation: {\n            x: 5,\n            y: 3,\n            z: 0\n          }\n        }\n      };\n      Object.assign(this.options, options);\n      this.DOM.animatable = {};\n      this.DOM.animatable.image = this.DOM.el.querySelector(\".eael-grid-fg-box__img\");\n      this.DOM.animatable.title = this.DOM.el.querySelector(\".eael-grid-fg-title\");\n      this.DOM.animatable.text = this.DOM.el.querySelector(\".eael-grid-fg-control-name\");\n      this.DOM.animatable.icon = this.DOM.el.querySelector(\".eael-grid-fg-icon\");\n      this.DOM.animatable.shadow = this.DOM.el.querySelector(\".box__shadow\");\n      this.DOM.animatable.content = this.DOM.el.querySelector(\".eael-gf-box__content\");\n      this.initEvents();\n    }\n    return _createClass(EAEL_GridFlowItem, [{\n      key: \"initEvents\",\n      value: function initEvents() {\n        var _this = this;\n        var enter = false;\n        this.mouseenterFn = function () {\n          if (enter) {\n            enter = false;\n          }\n          clearTimeout(_this.mousetime);\n          _this.mousetime = setTimeout(function () {\n            return enter = true;\n          }, 40);\n        };\n        this.mousemoveFn = function (ev) {\n          return requestAnimationFrame(function () {\n            if (!enter) return;\n            _this.tilt(ev);\n          });\n        };\n        this.mouseleaveFn = function (ev) {\n          return requestAnimationFrame(function () {\n            if (!enter || !allowTilt) return;\n            enter = false;\n            clearTimeout(_this.mousetime);\n            for (var key in _this.DOM.animatable) {\n              if (_this.DOM.animatable[key] == undefined || _this.options[key] == undefined) {\n                continue;\n              }\n              gsap.to(_this.DOM.animatable[key], _this.options[key].reverseAnimation != undefined ? _this.options[key].reverseAnimation.duration || 0 : 1.5, {\n                ease: _this.options[key].reverseAnimation != undefined ? _this.options[key].reverseAnimation.ease || \"Power2.easeOut\" : \"Power2.easeOut\",\n                x: 0,\n                y: 0,\n                z: 0,\n                rotationX: 0,\n                rotationY: 0,\n                rotationZ: 0\n              });\n            }\n          });\n        };\n        this.DOM.el.addEventListener(\"mouseenter\", this.mouseenterFn);\n        this.DOM.el.addEventListener(\"mousemove\", this.mousemoveFn);\n        this.DOM.el.addEventListener(\"mouseleave\", this.mouseleaveFn);\n      }\n    }, {\n      key: \"tilt\",\n      value: function tilt(ev) {\n        if (!allowTilt) return;\n        var mousepos = getMousePos(ev);\n        // Document scrolls.\n        var docScrolls = {\n          left: document.body.scrollLeft + document.documentElement.scrollLeft,\n          top: document.body.scrollTop + document.documentElement.scrollTop\n        };\n        var bounds = this.DOM.el.getBoundingClientRect();\n        // Mouse position relative to the main element (this.DOM.el).\n        var relmousepos = {\n          x: mousepos.x - bounds.left - docScrolls.left,\n          y: mousepos.y - bounds.top - docScrolls.top\n        };\n\n        // Movement settings for the animatable elements.\n        for (var key in this.DOM.animatable) {\n          if (this.DOM.animatable[key] == undefined || this.options[key] == undefined) {\n            continue;\n          }\n          var t = this.options[key] != undefined ? this.options[key].translation || {\n              x: 0,\n              y: 0,\n              z: 0\n            } : {\n              x: 0,\n              y: 0,\n              z: 0\n            },\n            r = this.options[key] != undefined ? this.options[key].rotation || {\n              x: 0,\n              y: 0,\n              z: 0\n            } : {\n              x: 0,\n              y: 0,\n              z: 0\n            };\n          setRange(t);\n          setRange(r);\n          var transforms = {\n            translation: {\n              x: (t.x[1] - t.x[0]) / bounds.width * relmousepos.x + t.x[0],\n              y: (t.y[1] - t.y[0]) / bounds.height * relmousepos.y + t.y[0],\n              z: (t.z[1] - t.z[0]) / bounds.height * relmousepos.y + t.z[0]\n            },\n            rotation: {\n              x: (r.x[1] - r.x[0]) / bounds.height * relmousepos.y + r.x[0],\n              y: (r.y[1] - r.y[0]) / bounds.width * relmousepos.x + r.y[0],\n              z: (r.z[1] - r.z[0]) / bounds.width * relmousepos.x + r.z[0]\n            }\n          };\n          gsap.to(this.DOM.animatable[key], 1.5, {\n            ease: \"Power1.easeOut\",\n            x: transforms.translation.x,\n            y: transforms.translation.y,\n            z: transforms.translation.z,\n            rotationX: transforms.rotation.x,\n            rotationY: transforms.rotation.y,\n            rotationZ: transforms.rotation.z\n          });\n        }\n      }\n    }]);\n  }();\n  var EAEL_GridFlowOverlay = /*#__PURE__*/function () {\n    function EAEL_GridFlowOverlay() {\n      _classCallCheck(this, EAEL_GridFlowOverlay);\n      this.DOM = {\n        el: document.querySelector(\"#overlay-\".concat(dataSettings.widget_id))\n      };\n      this.DOM.reveal = this.DOM.el.querySelector(\".overlay__reveal\");\n      this.DOM.items = this.DOM.el.querySelectorAll(\".overlay__item\");\n      this.DOM.close = this.DOM.el.querySelector(\".overlay__close\");\n    }\n    return _createClass(EAEL_GridFlowOverlay, [{\n      key: \"show\",\n      value: function show(contentItem) {\n        var _this2 = this;\n        this.contentItem = contentItem;\n        this.DOM.el.classList.add(\"overlay--open\");\n        // show revealer\n        gsap.to(this.DOM.reveal, transition_settings_data.transition_duration, {\n          ease: \"Power1.easeInOut\",\n          x: \"0%\",\n          onComplete: function onComplete() {\n            // hide scroll\n            document.body.classList.add(\"preview-open\");\n            // show preview\n            _this2.revealItem(contentItem);\n            // hide revealer\n            gsap.to(_this2.DOM.reveal, transition_settings_data.transition_duration, {\n              delay: 0.2,\n              ease: \"Power3.easeOut\",\n              x: \"-100%\"\n            });\n            _this2.DOM.close.style.opacity = 1;\n          }\n        });\n      }\n    }, {\n      key: \"revealItem\",\n      value: function revealItem() {\n        this.contentItem.style.opacity = 1;\n        var itemElems = [];\n        itemElems.push(this.contentItem.querySelector(\".box__shadow\"));\n        itemElems.push(this.contentItem.querySelector(\".eael-grid-fg-box__img\"));\n        itemElems.push(this.contentItem.querySelector(\".eael-grid-fg-title\"));\n        itemElems.push(this.contentItem.querySelector(\".eael-grid-fg-control-name\"));\n        itemElems.push(this.contentItem.querySelector(\".eael-grid-fg-icon\"));\n        itemElems.push(this.contentItem.querySelector(\".overlay__content\"));\n        for (var _i = 0, _itemElems = itemElems; _i < _itemElems.length; _i++) {\n          var el = _itemElems[_i];\n          if (el == null) continue;\n          var bounds = el.getBoundingClientRect();\n          var win = {\n            width: window.innerWidth,\n            height: window.innerHeight\n          };\n          gsap.to(el, lineEq(0.8, 1.2, win.width, 0, Math.abs(bounds.left + bounds.width - win.width)), {\n            ease: \"Expo.easeOut\",\n            delay: 0.2,\n            startAt: {\n              x: \"\".concat(lineEq(0, 800, win.width, 0, Math.abs(bounds.left + bounds.width - win.width))),\n              y: \"\".concat(lineEq(-100, 100, win.height, 0, Math.abs(bounds.top + bounds.height - win.height))),\n              rotationZ: \"\".concat(lineEq(5, 30, 0, win.width, Math.abs(bounds.left + bounds.width - win.width)))\n            },\n            x: 0,\n            y: 0,\n            rotationZ: 0\n          });\n        }\n      }\n    }, {\n      key: \"hide\",\n      value: function hide() {\n        var _this3 = this;\n        this.DOM.el.classList.remove(\"overlay--open\");\n\n        // show revealer\n        gsap.to(this.DOM.reveal, transition_settings_data.transition_duration, {\n          //delay: 0.15,\n          ease: \"Power3.easeOut\",\n          x: \"0%\",\n          onComplete: function onComplete() {\n            _this3.DOM.close.style.opacity = 0;\n            // show scroll\n            document.body.classList.remove(\"preview-open\");\n            // hide preview\n            _this3.contentItem.style.opacity = 0;\n            // hide revealer\n            gsap.to(_this3.DOM.reveal, transition_settings_data.transition_duration, {\n              delay: 0,\n              ease: \"Power3.easeOut\",\n              x: \"100%\"\n            });\n          }\n        });\n      }\n    }]);\n  }();\n  var EAEL_GridFlowGallery = /*#__PURE__*/function () {\n    function EAEL_GridFlowGallery(el) {\n      var _this4 = this;\n      _classCallCheck(this, EAEL_GridFlowGallery);\n      this.DOM = {\n        el: el\n      };\n      this.items = [];\n      //Check if not null\n      if (this.DOM.el) {\n        this.initializeItems();\n        this.overlay = new EAEL_GridFlowOverlay();\n        this.overlay.DOM.close.addEventListener(\"click\", function () {\n          return _this4.closeItem();\n        });\n      }\n    }\n    return _createClass(EAEL_GridFlowGallery, [{\n      key: \"initializeItems\",\n      value: function initializeItems() {\n        var _this5 = this;\n        Array.from(this.DOM.el.querySelectorAll(\".eael-grid__item\")).forEach(function (item) {\n          // Skip if item is already initialized\n          if (item.hasAttribute(\"data-initialized\")) {\n            return;\n          }\n          var itemObj = new EAEL_GridFlowItem(item);\n          _this5.items.push(itemObj);\n          if (!item.classList.contains(\"grid__item--noclick\")) {\n            itemObj.DOM.el.addEventListener(\"click\", function (ev) {\n              ev.preventDefault();\n              _this5.openItem(document.querySelector(item.getAttribute(\"data-item\")));\n            });\n          }\n\n          // Mark item as initialized\n          item.setAttribute(\"data-initialized\", \"true\");\n        });\n      }\n    }, {\n      key: \"openItem\",\n      value: function openItem(contentItem) {\n        if (this.isPreviewOpen) return;\n        this.isPreviewOpen = true;\n        allowTilt = false;\n        this.overlay.show(contentItem);\n        // \"explode\" grid..\n        var _iterator = _createForOfIteratorHelper(this.items),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var item = _step.value;\n            for (var key in item.DOM.animatable) {\n              var el = item.DOM.animatable[key];\n              if (el) {\n                var bounds = el.getBoundingClientRect();\n                var x = void 0;\n                var y = void 0;\n                var win = {\n                  width: window.innerWidth,\n                  height: window.innerHeight\n                };\n                if (bounds.top + bounds.height / 2 < win.height / 2 - win.height * 0.1) {\n                  //x = getRandomInt(-250,-50);\n                  //y = getRandomInt(20,100)*-1;\n                  x = -1 * lineEq(20, 600, 0, win.width, Math.abs(bounds.left + bounds.width - win.width));\n                  y = -1 * lineEq(20, 600, 0, win.width, Math.abs(bounds.left + bounds.width - win.width));\n                } else if (bounds.top + bounds.height / 2 > win.height / 2 + win.height * 0.1) {\n                  //x = getRandomInt(-250,-50);\n                  //y = getRandomInt(20,100);\n                  x = -1 * lineEq(20, 600, 0, win.width, Math.abs(bounds.left + bounds.width - win.width));\n                  y = lineEq(20, 600, 0, win.width, Math.abs(bounds.left + bounds.width - win.width));\n                } else {\n                  //x = getRandomInt(300,700)*-1;\n                  x = -1 * lineEq(10, 700, 0, win.width, Math.abs(bounds.left + bounds.width - win.width));\n                  y = getRandomInt(-25, 25);\n                }\n                gsap.to(el, 0.4, {\n                  ease: \"Power3.easeOut\",\n                  delay: lineEq(0, 0.3, 0, win.width, Math.abs(bounds.left + bounds.width - win.width)),\n                  x: x,\n                  y: y,\n                  rotationZ: getRandomInt(-10, 10),\n                  opacity: 0\n                });\n              }\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      }\n    }, {\n      key: \"closeItem\",\n      value: function closeItem() {\n        if (!this.isPreviewOpen) return;\n        this.isPreviewOpen = false;\n        this.overlay.hide();\n        var _iterator2 = _createForOfIteratorHelper(this.items),\n          _step2;\n        try {\n          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n            var item = _step2.value;\n            for (var key in item.DOM.animatable) {\n              var el = item.DOM.animatable[key];\n              if (el) {\n                var bounds = el.getBoundingClientRect();\n                var win = {\n                  width: window.innerWidth\n                };\n                gsap.to(el, 0.6, {\n                  ease: \"Expo.easeOut\",\n                  delay: 0.55 + lineEq(0, 0.2, 0, win.width, Math.abs(bounds.left + bounds.width - win.width)),\n                  x: 0,\n                  y: 0,\n                  rotationZ: 0,\n                  opacity: 1\n                });\n              }\n            }\n          }\n        } catch (err) {\n          _iterator2.e(err);\n        } finally {\n          _iterator2.f();\n        }\n        allowTilt = true;\n        if (this.DOM.el) {\n          gsap.set(this.DOM.el, {\n            pointerEvents: \"auto\"\n          });\n        }\n      }\n    }]);\n  }();\n  var allowTilt = true;\n\n  //Initialize the Grid Flow Gallery\n  var eael_grid_flow_gallery = document.querySelector(\"#eael-grid-fg-\".concat(dataSettings.widget_id));\n  if (eael_grid_flow_gallery) {\n    eael_grid_flow_gallery.eaelGallery = new EAEL_GridFlowGallery(eael_grid_flow_gallery);\n  }\n\n  // Preload all the images in the page..\n  imagesLoaded(document.querySelectorAll(\".eael-grid-fg-box__img\"), function () {\n    return document.body.classList.remove(\"loading\");\n  });\n\n  /** -----------------------\n   * Harmonic Gallery\n  --------------------------*/\n  // Preload images\n  var preloadImages = function preloadImages() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"img\";\n    return new Promise(function (resolve) {\n      imagesLoaded(document.querySelectorAll(selector), {\n        background: true\n      }, resolve);\n    });\n  };\n  var calcWinsize = function calcWinsize() {\n    return {\n      width: window.innerWidth,\n      height: window.innerHeight\n    };\n  };\n  var getScrollValues = function getScrollValues() {\n    var supportPageOffset = window.pageXOffset !== undefined;\n    var isCSS1Compat = (document.compatMode || \"\") === \"CSS1Compat\";\n    var x = supportPageOffset ? window.pageXOffset : isCSS1Compat ? document.documentElement.scrollLeft : document.body.scrollLeft;\n    var y = supportPageOffset ? window.pageYOffset : isCSS1Compat ? document.documentElement.scrollTop : document.body.scrollTop;\n    return {\n      x: x,\n      y: y\n    };\n  };\n  var wrapLines = function wrapLines(elems, wrapType, wrapClass) {\n    elems.forEach(function (_char) {\n      var wrapEl = document.createElement(wrapType);\n      wrapEl.classList = wrapClass;\n      _char.parentNode.appendChild(wrapEl);\n      wrapEl.appendChild(_char);\n    });\n  };\n  var adjustedBoundingRect = function adjustedBoundingRect(el) {\n    var rect = el.getBoundingClientRect();\n    var style = getComputedStyle(el);\n    var tx = style.transform;\n    if (tx) {\n      var sx, sy, dx, dy;\n      if (tx.startsWith(\"matrix3d(\")) {\n        var ta = tx.slice(9, -1).split(/, /);\n        sx = +ta[0];\n        sy = +ta[5];\n        dx = +ta[12];\n        dy = +ta[13];\n      } else if (tx.startsWith(\"matrix(\")) {\n        var ta = tx.slice(7, -1).split(/, /);\n        sx = +ta[0];\n        sy = +ta[3];\n        dx = +ta[4];\n        dy = +ta[5];\n      } else {\n        return rect;\n      }\n      var to = style.transformOrigin;\n      var x = rect.x - dx - (1 - sx) * parseFloat(to);\n      var y = rect.y - dy - (1 - sy) * parseFloat(to.slice(to.indexOf(\" \") + 1));\n      var w = sx ? rect.width / sx : el.offsetWidth;\n      var h = sy ? rect.height / sy : el.offsetHeight;\n      return {\n        x: x,\n        y: y,\n        width: w,\n        height: h,\n        top: y,\n        right: x + w,\n        bottom: y + h,\n        left: x\n      };\n    } else {\n      return rect;\n    }\n  };\n\n  /**\n   * Class TextReveal\n   */\n  var TextReveal = /*#__PURE__*/function () {\n    function TextReveal(el) {\n      _classCallCheck(this, TextReveal);\n      this.DOM = {\n        outer: el,\n        inner: Array.isArray(el) ? el.map(function (outer) {\n          return outer.querySelector(\".eael-split-oh__inner\");\n        }) : el.querySelector(\".eael-split-oh__inner\")\n      };\n    }\n    return _createClass(TextReveal, [{\n      key: \"in\",\n      value: function _in() {\n        if (this.outTimeline && this.outTimeline.isActive()) {\n          this.outTimeline.kill();\n        }\n        this.inTimeline = gsap.timeline({\n          defaults: {\n            duration: 1.2,\n            ease: \"expo\"\n          }\n        }).set(this.DOM.inner, {\n          y: \"120%\",\n          rotate: 15\n        }).to(this.DOM.inner, {\n          y: \"0%\",\n          rotate: 0,\n          stagger: 0.03\n        });\n        return this.inTimeline;\n      }\n    }, {\n      key: \"out\",\n      value: function out() {\n        if (this.inTimeline && this.inTimeline.isActive()) {\n          this.inTimeline.kill();\n        }\n        this.outTimeline = gsap.timeline({\n          defaults: {\n            duration: 0.5,\n            ease: \"expo.in\"\n          }\n        }).to(this.DOM.inner, {\n          y: \"-120%\",\n          rotate: -5,\n          stagger: 0.03\n        });\n        return this.outTimeline;\n      }\n    }]);\n  }();\n  /**\n   * Class TextLinesReveal\n   */\n  var TextLinesReveal = /*#__PURE__*/function () {\n    function TextLinesReveal(animationElems) {\n      _classCallCheck(this, TextLinesReveal);\n      this.DOM = {\n        animationElems: Array.isArray(animationElems) ? animationElems : [animationElems]\n      };\n\n      // array of SplitType instances\n      this.SplitTypeInstances = [];\n      // array of all HTML .line\n      this.lines = [];\n      var _iterator3 = _createForOfIteratorHelper(this.DOM.animationElems),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var el = _step3.value;\n          var SplitTypeInstance = new SplitType(el, {\n            types: \"lines\"\n          });\n          // wrap the lines (div with class .oh)\n          // the inner child will be the one animating the transform\n          wrapLines(SplitTypeInstance.lines, \"div\", \"eael-split-oh\");\n          this.lines.push(SplitTypeInstance.lines);\n          // keep a reference to the SplitType instance\n          this.SplitTypeInstances.push(SplitTypeInstance);\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n      this.initEvents();\n    }\n    return _createClass(TextLinesReveal, [{\n      key: \"in\",\n      value: function _in() {\n        // lines are visible\n        this.isVisible = true;\n\n        // animation\n        gsap.killTweensOf(this.lines);\n        return gsap.timeline({\n          defaults: {\n            duration: 1.2,\n            ease: \"expo\"\n          }\n        }).set(this.lines, {\n          y: \"150%\",\n          rotate: 15\n        }).to(this.lines, {\n          y: \"0%\",\n          rotate: 0,\n          stagger: 0.04\n        });\n      }\n    }, {\n      key: \"out\",\n      value: function out() {\n        // lines are invisible\n        this.isVisible = false;\n\n        // animation\n        gsap.killTweensOf(this.lines);\n        return gsap.timeline({\n          defaults: {\n            duration: 0.5,\n            ease: \"expo.in\"\n          }\n        }).to(this.lines, {\n          y: \"-150%\",\n          rotate: -5,\n          stagger: 0.02\n        });\n      }\n    }, {\n      key: \"initEvents\",\n      value: function initEvents() {\n        var _this6 = this;\n        window.addEventListener(\"resize\", function () {\n          // empty the lines array\n          _this6.lines = [];\n          // re initialize the Split Text\n          var _iterator4 = _createForOfIteratorHelper(_this6.SplitTypeInstances),\n            _step4;\n          try {\n            for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {\n              var instance = _step4.value;\n              // re-split text\n              // https://github.com/lukePeavey/SplitType#instancesplitoptions-void\n              instance.split();\n\n              // need to wrap again the new lines elements (div with class .oh)\n              wrapLines(instance.lines, \"div\", \"eael-split-oh\");\n              _this6.lines.push(instance.lines);\n            }\n            // hide the lines\n          } catch (err) {\n            _iterator4.e(err);\n          } finally {\n            _iterator4.f();\n          }\n          if (!_this6.isVisible) {\n            gsap.set(_this6.lines, {\n              y: \"-150%\"\n            });\n          }\n        });\n      }\n    }]);\n  }();\n  /**\n   * Class representing a content item (.content__item).\n   */\n  var ContentItem = /*#__PURE__*/_createClass(\n  /**\n   * Constructor.\n   * @param {Element} DOM_el - the .content__item element.\n   */\n  function ContentItem(DOM_el) {\n    _classCallCheck(this, ContentItem);\n    // DOM elements\n    _defineProperty(this, \"DOM\", {\n      // Main element (.content__item)\n      el: null\n    });\n    // TextReveal obj to animate the texts (slide in/out)\n    _defineProperty(this, \"textReveal\", null);\n    // TextLinesReveal obj to animate the ,ulti line texts (slide in/out)\n    _defineProperty(this, \"textLinesReveal\", null);\n    this.DOM.el = DOM_el;\n    this.DOM.nav = {\n      prev: this.DOM.el.querySelector(\".slide-nav__img--prev\"),\n      next: this.DOM.el.querySelector(\".slide-nav__img--next\")\n    };\n\n    // Text animations\n    this.textReveal = new TextReveal(_toConsumableArray(this.DOM.el.querySelectorAll(\".eael-split-oh\")));\n    // Text lines animations\n    this.textLinesReveal = new TextLinesReveal(this.DOM.el.querySelector(\".eael-hg-content__item-text\"));\n  });\n  /**\n   * Class representing a image cell (.grid__cell-img).\n   */\n  var ImageCell = /*#__PURE__*/_createClass(\n  /**\n   * Constructor.\n   * @param {Element} DOM_el - the .grid__cell-img element.\n   */\n  function ImageCell(DOM_el) {\n    _classCallCheck(this, ImageCell);\n    // DOM elements\n    _defineProperty(this, \"DOM\", {\n      // Main element (.grid__cell-img)\n      el: null,\n      // Inner element\n      inner: null,\n      // The ImageCell's content item id.\n      contentId: null,\n      // The ContentItem instance\n      contentItem: null\n    });\n    this.DOM.el = DOM_el;\n    this.DOM.inner = this.DOM.el.querySelector(\".grid__cell-img-inner\");\n\n    // The ImageCell's content item id.\n    this.contentId = this.DOM.inner.dataset.item;\n    // The ContentItem instance\n    this.contentItem = new ContentItem(document.querySelector(\"#\".concat(this.contentId)));\n  }); // body element\n  var bodyEl = document.body;\n\n  // Calculate the viewport size\n  var winsize = calcWinsize();\n  window.addEventListener(\"resize\", function () {\n    return winsize = calcWinsize();\n  });\n\n  /**\n   * Class representing a grid of images, where the grid can be zoomed to the clicked image cell\n   */\n  var Harmonic_Gallery = /*#__PURE__*/function () {\n    function Harmonic_Gallery(DOM_el) {\n      _classCallCheck(this, Harmonic_Gallery);\n      _defineProperty(this, \"DOM\", {\n        el: null,\n        imageCells: null,\n        content: null,\n        backCtrl: null,\n        miniGrid: {\n          el: null,\n          cells: null\n        }\n      });\n      _defineProperty(this, \"imageCellArr\", []);\n      _defineProperty(this, \"currentCell\", -1);\n      _defineProperty(this, \"isGridView\", true);\n      _defineProperty(this, \"isAnimating\", false);\n      _defineProperty(this, \"textReveal\", null);\n      this.DOM.el = DOM_el;\n      this.initializeGallery();\n    }\n    return _createClass(Harmonic_Gallery, [{\n      key: \"initializeGallery\",\n      value: function initializeGallery() {\n        this.initializeItems();\n        this.initializeContent();\n        this.initializeMiniGrid();\n        this.initializeTextAnimations();\n        this.initEvents();\n      }\n    }, {\n      key: \"initializeItems\",\n      value: function initializeItems() {\n        var _this7 = this;\n        // Get all current image cells\n        var allImageCells = this.DOM.el.querySelectorAll(\".eael-hg-grid__cell-img\");\n\n        // Update the DOM.imageCells array\n        this.DOM.imageCells = _toConsumableArray(allImageCells);\n\n        // Only add new items that haven't been initialized\n        allImageCells.forEach(function (el) {\n          if (!el.hasAttribute(\"data-initialized\")) {\n            _this7.imageCellArr.push(new ImageCell(el));\n            el.setAttribute(\"data-initialized\", \"true\");\n          }\n        });\n      }\n    }, {\n      key: \"initializeContent\",\n      value: function initializeContent() {\n        this.DOM.content = document.querySelector(\"#eael-hg-content-\".concat(dataSettings.widget_id));\n        if (this.DOM.content) {\n          this.DOM.backCtrl = this.DOM.content.querySelector(\".eael-hg-back\");\n        }\n      }\n    }, {\n      key: \"initializeMiniGrid\",\n      value: function initializeMiniGrid() {\n        var _this8 = this;\n        if (this.DOM.content) {\n          this.DOM.miniGrid.el = this.DOM.content.querySelector(\".eael-hg-grid--mini\");\n          if (this.DOM.miniGrid.el) {\n            this.DOM.miniGrid.cells = _toConsumableArray(this.DOM.miniGrid.el.querySelectorAll(\".eael-hg-grid__cell\"));\n\n            // Initialize miniGrid cells if they exist\n            if (this.DOM.miniGrid.cells && this.DOM.miniGrid.cells.length > 0) {\n              this.DOM.miniGrid.cells.forEach(function (cell, position) {\n                cell.addEventListener(\"click\", function () {\n                  if (_this8.isAnimating || _this8.currentCell === position) {\n                    return false;\n                  }\n                  _this8.isAnimating = true;\n                  _this8.changeContent(position);\n                });\n              });\n            }\n          }\n        }\n      }\n    }, {\n      key: \"initializeTextAnimations\",\n      value: function initializeTextAnimations() {\n        var textElements = this.DOM.el.querySelectorAll(\".eael-split-oh\");\n        if (textElements.length > 0) {\n          this.textReveal = new TextReveal(_toConsumableArray(textElements));\n        } else {\n          this.textReveal = null;\n        }\n      }\n\n      /**\n       * Track which cells are visible (inside the viewport)\n       * by adding/removing the 'in-view' class when scrolling.\n       * This will be used to animate only the ones that are visible.\n       */\n    }, {\n      key: \"trackVisibleCells\",\n      value: function trackVisibleCells() {\n        var observer = new IntersectionObserver(function (entries, observer) {\n          entries.forEach(function (entry) {\n            if (entry.intersectionRatio > 0) {\n              entry.target.classList.add(\"in-view\");\n            } else {\n              entry.target.classList.remove(\"in-view\");\n            }\n          });\n        });\n        this.DOM.imageCells.forEach(function (img) {\n          return observer.observe(img);\n        });\n      }\n\n      /**\n       * Init/Bind events.\n       */\n    }, {\n      key: \"initEvents\",\n      value: function initEvents() {\n        var _this9 = this;\n        // for every imageCell\n        var _iterator5 = _createForOfIteratorHelper(this.imageCellArr.entries()),\n          _step5;\n        try {\n          var _loop = function _loop() {\n            var _step5$value = _slicedToArray(_step5.value, 2),\n              position = _step5$value[0],\n              imageCell = _step5$value[1];\n            // Open the imageCell and reveal its content\n            imageCell.DOM.el.addEventListener(\"click\", function () {\n              if (!_this9.isGridView || _this9.isAnimating) {\n                return false;\n              }\n              _this9.isAnimating = true;\n              _this9.isGridView = false;\n\n              // Update the mini grid current cell\n              if (_this9.currentCell !== -1 && _this9.DOM.miniGrid && _this9.DOM.miniGrid.cells && _this9.DOM.miniGrid.cells[_this9.currentCell]) {\n                _this9.DOM.miniGrid.cells[_this9.currentCell].classList.remove(\"grid__cell--current\");\n              }\n\n              // Update currentCell\n              _this9.currentCell = position;\n\n              // Add current class to new cell if miniGrid exists\n              if (_this9.DOM.miniGrid && _this9.DOM.miniGrid.cells && _this9.DOM.miniGrid.cells[_this9.currentCell]) {\n                _this9.DOM.miniGrid.cells[_this9.currentCell].classList.add(\"grid__cell--current\");\n              }\n              _this9.showContent(imageCell);\n            });\n\n            // Hover on the image cell will scale down the outer element and scale up the inner element.\n            imageCell.DOM.el.addEventListener(\"mouseenter\", function () {\n              if (!_this9.isGridView) {\n                return false;\n              }\n              gsap.killTweensOf([imageCell.DOM.el, imageCell.DOM.inner]);\n              gsap.timeline({\n                defaults: {\n                  duration: 2.4,\n                  ease: \"expo\"\n                }\n              }).to(imageCell.DOM.el, {\n                scale: 0.95\n              }, 0).to(imageCell.DOM.inner, {\n                scale: 1.4\n              }, 0);\n            });\n\n            // Hovering out will reverse the scale values.\n            imageCell.DOM.el.addEventListener(\"mouseleave\", function () {\n              if (!_this9.isGridView) {\n                return false;\n              }\n              gsap.killTweensOf([imageCell.DOM.el, imageCell.DOM.inner]);\n              gsap.timeline({\n                defaults: {\n                  duration: 2.4,\n                  ease: \"expo\"\n                }\n              }).to([imageCell.DOM.el, imageCell.DOM.inner], {\n                scale: 1\n              }, 0);\n            });\n          };\n          for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {\n            _loop();\n          }\n\n          // Close the imageCell and reveal the grid\n        } catch (err) {\n          _iterator5.e(err);\n        } finally {\n          _iterator5.f();\n        }\n        if (this.DOM.backCtrl) {\n          this.DOM.backCtrl.addEventListener(\"click\", function () {\n            if (_this9.isAnimating) {\n              return false;\n            }\n            _this9.isAnimating = true;\n            _this9.isGridView = true;\n            _this9.closeContent();\n          });\n        }\n      }\n\n      /**\n       * Scale up the image and reveal its content.\n       * @param {ImageCell} imageCell - the imageCell element.\n       */\n    }, {\n      key: \"showContent\",\n      value: function showContent(imageCell) {\n        var _this0 = this;\n        if (!imageCell) return;\n        // Calculate the transform to apply to the image cell\n        var imageTransform = this.calcTransformImage();\n        // All the others (that are inside the viewport)\n        this.otherImageCells = this.DOM.imageCells.filter(function (el) {\n          return el != imageCell.DOM.el;\n        });\n        gsap.killTweensOf([imageCell.DOM.el, imageCell.DOM.inner, this.otherImageCells]);\n        gsap.timeline({\n          defaults: {\n            duration: 1.2,\n            ease: \"expo.inOut\"\n          },\n          // overflow hidden\n          onStart: function onStart() {\n            return bodyEl.classList.add(\"eael-split-oh\");\n          },\n          onComplete: function onComplete() {\n            _this0.isAnimating = false;\n          }\n        }).addLabel(\"start\", 0).add(function () {\n          // Hide grid texts\n          if (_this0.textReveal) {\n            _this0.textReveal.out();\n          }\n        }, \"start\").set(this.DOM.el, {\n          pointerEvents: \"none\"\n        }, \"start\").set(imageCell.DOM.el, {\n          zIndex: 1001\n        }, \"start\").set([imageCell.DOM.el, imageCell.DOM.inner, this.otherImageCells], {\n          willChange: \"transform, opacity\"\n        }, \"start\").to(imageCell.DOM.el, {\n          scale: imageTransform.scale,\n          // 2.88\n          // scale: 2.5, // 2.88\n          x: imageTransform.x,\n          // 668\n          y: imageTransform.y,\n          // 57.6094\n          onComplete: function onComplete() {\n            return gsap.set(imageCell.DOM.el, {\n              willChange: \"\"\n            });\n          }\n        }, \"start\").to(imageCell.DOM.inner, {\n          scale: 1,\n          onComplete: function onComplete() {\n            return gsap.set(imageCell.DOM.inner, {\n              willChange: \"\"\n            });\n          }\n        }, \"start\").to([imageCell.contentItem.DOM.nav.prev, imageCell.contentItem.DOM.nav.next], {\n          y: 0\n        }, \"start\").to(this.otherImageCells, {\n          opacity: 0,\n          scale: 0.8,\n          onComplete: function onComplete() {\n            return gsap.set(_this0.otherImageCells, {\n              willChange: \"\"\n            });\n          },\n          stagger: {\n            grid: \"auto\",\n            amount: 0.17,\n            from: this.currentCell\n          }\n        }, \"start\").addLabel(\"showContent\", \"start+=0.45\").to(this.DOM.backCtrl, {\n          ease: \"expo\",\n          startAt: {\n            x: \"50%\"\n          },\n          x: \"0%\",\n          opacity: 1\n        }, \"showContent\").set(this.DOM.miniGrid.el, {\n          opacity: 1\n        }, \"showContent\").set(this.DOM.miniGrid.cells, {\n          opacity: 0\n        }, \"showContent\").to(this.DOM.miniGrid.cells, {\n          duration: 1,\n          ease: \"expo\",\n          opacity: 1,\n          startAt: {\n            scale: 0.8\n          },\n          scale: 1,\n          stagger: {\n            grid: \"auto\",\n            amount: 0.3,\n            from: this.currentCell\n          }\n        }, \"showContent+=0.2\").add(function () {\n          imageCell.contentItem.textReveal[\"in\"]();\n          imageCell.contentItem.textLinesReveal[\"in\"]();\n          _this0.DOM.content.classList.add(\"content--open\");\n        }, \"showContent\").add(function () {\n          return imageCell.contentItem.DOM.el.classList.add(\"content__item--current\");\n        }, \"showContent+=0.02\");\n      }\n\n      /**\n       * Scale down the image and reveal the grid again.\n       */\n    }, {\n      key: \"closeContent\",\n      value: function closeContent() {\n        var _this1 = this;\n        // Current imageCell\n        var imageCell = this.imageCellArr[this.currentCell];\n        this.otherImageCells = this.DOM.imageCells.filter(function (el) {\n          return el != imageCell.DOM.el;\n        });\n        gsap.timeline({\n          defaults: {\n            duration: 1,\n            ease: \"expo.inOut\"\n          },\n          // overflow hidden\n          onStart: function onStart() {\n            bodyEl.classList.remove(\"eael-split-oh\");\n            // Add fade-out animation before removing the class\n            gsap.to(imageCell.contentItem.DOM.el, {\n              duration: 0.2,\n              ease: \"power2.out\",\n              onComplete: function onComplete() {\n                imageCell.contentItem.DOM.el.classList.remove(\"content__item--current\");\n                gsap.set(imageCell.contentItem.DOM.el, {\n                  y: 0\n                });\n              }\n            });\n          },\n          onComplete: function onComplete() {\n            _this1.isAnimating = false;\n          }\n        }).addLabel(\"start\", 0).to(this.DOM.backCtrl, {\n          x: \"50%\",\n          opacity: 0\n        }, \"start\").to(this.DOM.miniGrid.cells, {\n          duration: 0.5,\n          ease: \"expo.in\",\n          opacity: 0,\n          scale: 0.8,\n          stagger: {\n            grid: \"auto\",\n            amount: 0.1,\n            from: -this.currentCell\n          },\n          onComplete: function onComplete() {\n            if (_this1.DOM.miniGrid.el) {\n              gsap.set(_this1.DOM.miniGrid.el, {\n                opacity: 0\n              });\n            }\n          }\n        }, \"start\").add(function () {\n          if (_this1.textReveal) {\n            _this1.textReveal.out();\n          }\n          if (_this1.DOM.content) {\n            _this1.DOM.content.classList.remove(\"content--open\");\n          }\n        }, \"start\").addLabel(\"showGrid\", 0).set([imageCell.DOM.el, this.otherImageCells], {\n          willChange: \"transform, opacity\"\n        }, \"showGrid\").to(imageCell.DOM.el, {\n          scale: 1,\n          x: 0,\n          y: 0,\n          onComplete: function onComplete() {\n            return gsap.set(imageCell.DOM.el, {\n              willChange: \"\",\n              zIndex: 1\n            });\n          }\n        }, \"showGrid\").to(imageCell.contentItem.DOM.nav.prev, {\n          y: \"-100%\"\n        }, \"showGrid\").to(imageCell.contentItem.DOM.nav.next, {\n          y: \"100%\"\n        }, \"showGrid\").to(this.otherImageCells, {\n          opacity: 1,\n          scale: 1,\n          onComplete: function onComplete() {\n            gsap.set(_this1.otherImageCells, {\n              willChange: \"\"\n            });\n            gsap.set(_this1.DOM.el, {\n              pointerEvents: \"auto\"\n            });\n          },\n          stagger: {\n            grid: \"auto\",\n            amount: 0.17,\n            from: -this.currentCell\n          }\n        }, \"showGrid\").add(function () {\n          // Show grid texts\n          if (_this1.textReveal) {\n            _this1.textReveal[\"in\"]();\n          }\n        }, \"showGrid+=0.3\");\n      }\n      /**\n       *\n       */\n    }, {\n      key: \"changeContent\",\n      value: function changeContent(position) {\n        var _this10 = this;\n        // Current imageCell\n        var imageCell = this.imageCellArr[this.currentCell];\n        // Upcoming imageCell\n        var upcomingImageCell = this.imageCellArr[position];\n        if (!imageCell || !upcomingImageCell) return;\n\n        // Check if miniGrid cells exist\n        if (this.DOM.miniGrid && this.DOM.miniGrid.cells) {\n          // Remove current class from previous cell\n          if (this.currentCell !== -1 && this.DOM.miniGrid.cells[this.currentCell]) {\n            this.DOM.miniGrid.cells[this.currentCell].classList.remove(\"grid__cell--current\");\n          }\n\n          // Update current cell position\n          this.currentCell = position;\n\n          // Add current class to new cell\n          if (this.DOM.miniGrid.cells[this.currentCell]) {\n            this.DOM.miniGrid.cells[this.currentCell].classList.add(\"grid__cell--current\");\n          }\n        }\n\n        // Calculate the transform to apply to the image cell\n        var imageTransform = this.calcTransformImage();\n        gsap.timeline({\n          defaults: {\n            duration: 1,\n            ease: \"expo.inOut\"\n          },\n          onComplete: function onComplete() {\n            _this10.isAnimating = false;\n          }\n        }).addLabel(\"start\", 0).add(function () {\n          if (imageCell.contentItem && imageCell.contentItem.textReveal) {\n            imageCell.contentItem.textReveal.out();\n          }\n          if (imageCell.contentItem && imageCell.contentItem.textLinesReveal) {\n            imageCell.contentItem.textLinesReveal.out();\n          }\n        }, \"start\").add(function () {\n          if (imageCell.contentItem && imageCell.contentItem.DOM && imageCell.contentItem.DOM.el) {\n            imageCell.contentItem.DOM.el.classList.remove(\"content__item--current\");\n          }\n        }).set([imageCell.DOM.el, upcomingImageCell.DOM.el], {\n          willChange: \"transform, opacity\"\n        }, \"start\").to(imageCell.DOM.el, {\n          opacity: 0,\n          scale: 0.8,\n          x: 0,\n          y: 0,\n          onComplete: function onComplete() {\n            return gsap.set(imageCell.DOM.el, {\n              willChange: \"\",\n              zIndex: 1\n            });\n          }\n        }, \"start\").to(imageCell.contentItem.DOM.nav.prev, {\n          y: \"-100%\"\n        }, \"start\").to(imageCell.contentItem.DOM.nav.next, {\n          y: \"100%\"\n        }, \"start\").addLabel(\"showContent\", \">-=0.4\").set(upcomingImageCell.DOM.el, {\n          zIndex: 1001\n        }, \"start\").to(upcomingImageCell.DOM.el, {\n          scale: imageTransform.scale,\n          x: imageTransform.x,\n          y: imageTransform.y,\n          opacity: 1,\n          onComplete: function onComplete() {\n            return gsap.set(upcomingImageCell.DOM.el, {\n              willChange: \"\"\n            });\n          }\n        }, \"start\").to([upcomingImageCell.contentItem.DOM.nav.prev, upcomingImageCell.contentItem.DOM.nav.next], {\n          ease: \"expo\",\n          y: 0\n        }, \"showContent\").add(function () {\n          if (upcomingImageCell.contentItem && upcomingImageCell.contentItem.textReveal) {\n            upcomingImageCell.contentItem.textReveal[\"in\"]();\n          }\n          if (upcomingImageCell.contentItem && upcomingImageCell.contentItem.textLinesReveal) {\n            upcomingImageCell.contentItem.textLinesReveal[\"in\"]();\n          }\n        }, \"showContent\").add(function () {\n          if (upcomingImageCell.contentItem && upcomingImageCell.contentItem.DOM && upcomingImageCell.contentItem.DOM.el) {\n            upcomingImageCell.contentItem.DOM.el.classList.add(\"content__item--current\");\n          }\n        }, \"showContent+=0.02\");\n      }\n      /**\n       * Calculates the scale and translation values to apply to the image cell when we click on it.\n       * Also used to recalculate those values on resize.\n       * @return {JSON} the translation and scale values\n       */\n    }, {\n      key: \"calcTransformImage\",\n      value: function calcTransformImage() {\n        var cellrect = adjustedBoundingRect(this.imageCellArr[this.currentCell].DOM.el);\n        return {\n          scale: winsize.width * 0.35 / cellrect.width,\n          x: winsize.width * 0.65 - (cellrect.left + cellrect.width / 2),\n          y: winsize.height * 0.5 - (cellrect.top + cellrect.height / 2)\n        };\n      }\n    }]);\n  }(); // Initialize the Harmonic Gallery\n  var eael_harmonic_gallery = document.querySelector(\"#eael-hg-items-\".concat(dataSettings.widget_id));\n  if (eael_harmonic_gallery) {\n    eael_harmonic_gallery.eaelHarmonicGallery = new Harmonic_Gallery(eael_harmonic_gallery);\n\n    // Update navigation items based on filter\n    $scope.on(\"click\", \".control\", function () {\n      var buttonFilter = $(this).attr(\"data-filter\");\n      if ($scope.find(\".eael-hg-mini-wrapper\").length > 0) {\n        if (buttonFilter === \"*\") {\n          $scope.find(\".eael-hg-mini-wrapper .eael-hg-grid__cell\").show();\n          $scope.find(\".eael-hg-content .eael-hg-content__item\").show();\n        } else {\n          $scope.find(\".eael-hg-mini-wrapper .eael-hg-grid__cell\").hide();\n          $scope.find(\".eael-hg-mini-wrapper .eael-hg-grid__cell\" + buttonFilter).show();\n          $scope.find(\".eael-hg-content .eael-hg-content__item\").hide();\n          $scope.find(\".eael-hg-content .eael-hg-content__item\" + buttonFilter).show();\n        }\n      }\n    });\n  }\n\n  // Preload images then remove loader (loading class) from body\n  preloadImages(\".grid__cell-img-inner, .slide-nav__img\").then(function () {\n    return document.body.classList.remove(\"loading\");\n  });\n};\njQuery(window).on(\"elementor/frontend/init\", function () {\n  if (eael.elementStatusCheck(\"filterableGallery\")) {\n    return false;\n  }\n  elementorFrontend.hooks.addAction(\"frontend/element_ready/eael-filterable-gallery.default\", filterableGallery);\n\n  // Listen for custom event when new items are loaded in Harmonic Layout\n  jQuery(document).on(\"eael:filterable-gallery:items-loaded\", function (e, galleryId) {\n    var gallery = document.querySelector(\"#eael-hg-items-\".concat(galleryId));\n    if (gallery && gallery.eaelHarmonicGallery) {\n      gallery.eaelHarmonicGallery.initializeGallery();\n    }\n  });\n\n  // Listen for custom event when new items are loaded in Grid Flow Layout\n  jQuery(document).on(\"eael:filterable-gallery:items-loaded\", function (e, galleryId) {\n    var gallery = document.querySelector(\"#eael-grid-fg-\".concat(galleryId));\n    if (gallery && gallery.eaelGallery) {\n      gallery.eaelGallery.initializeItems();\n    }\n  });\n});\n\n//# sourceURL=webpack:///./src/js/view/filterable-gallery.js?");

/***/ })

/******/ });