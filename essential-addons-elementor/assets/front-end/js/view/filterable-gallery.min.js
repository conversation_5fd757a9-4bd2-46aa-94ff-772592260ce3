!function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(i,r,function(t){return e[t]}.bind(null,r));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=9)}({9:function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,o,l,a=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(i=o.call(n)).done)&&(a.push(i.value),a.length!==t);s=!0);}catch(e){c=!0,r=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(c)throw r}}return a}}(e,t)||a(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e){return function(e){if(Array.isArray(e))return s(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||a(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t,n){return(t=d(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=a(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){s=!0,o=e},f:function(){try{l||null==n.return||n.return()}finally{if(s)throw o}}}}function a(e,t){if(e){if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,d(i.key),i)}}function h(e,t,n){return t&&u(e.prototype,t),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function d(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}var m=function(e,t){var n=t(".eael-filter-gallery-container",e).data("settings"),a=t(".eael-grid-fg-overlay",e).data("transition");document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll(".eael-grid-fg-img").forEach((function(e){e.closest(".eael-grid-fg-item").querySelector(".eael-grid-fg-box").style.height=e.offsetHeight+"px"}))}));var s=function(e,t,n,i,r){var o=(e-t)/(n-i);return o*r+(t-o*i)},u=function(e,t){return Math.floor(Math.random()*(t-e+1))+e},d=function(e){for(var t in e)null==e[t]?e[t]=[0,0]:"number"==typeof e[t]&&(e[t]=[-1*e[t],e[t]])},m=function(){return h((function e(t,n){c(this,e),this.DOM={el:t},this.options={image:{translation:{x:-10,y:-10,z:0},rotation:{x:0,y:0,z:0}},title:{translation:{x:20,y:10,z:0}},text:{translation:{x:20,y:10,z:0},rotation:{x:0,y:0,z:-5}},icon:{translation:{x:-20,y:0,z:0},rotation:{x:0,y:0,z:3}},shadow:{translation:{x:20,y:10,z:0},rotation:{x:0,y:0,z:-2},reverseAnimation:{duration:2,ease:"Back.easeOut"}},content:{translation:{x:5,y:3,z:0}}},Object.assign(this.options,n),this.DOM.animatable={},this.DOM.animatable.image=this.DOM.el.querySelector(".eael-grid-fg-box__img"),this.DOM.animatable.title=this.DOM.el.querySelector(".eael-grid-fg-title"),this.DOM.animatable.text=this.DOM.el.querySelector(".eael-grid-fg-control-name"),this.DOM.animatable.icon=this.DOM.el.querySelector(".eael-grid-fg-icon"),this.DOM.animatable.shadow=this.DOM.el.querySelector(".box__shadow"),this.DOM.animatable.content=this.DOM.el.querySelector(".eael-gf-box__content"),this.initEvents()}),[{key:"initEvents",value:function(){var e=this,t=!1;this.mouseenterFn=function(){t&&(t=!1),clearTimeout(e.mousetime),e.mousetime=setTimeout((function(){return t=!0}),40)},this.mousemoveFn=function(n){return requestAnimationFrame((function(){t&&e.tilt(n)}))},this.mouseleaveFn=function(n){return requestAnimationFrame((function(){if(t&&g)for(var n in t=!1,clearTimeout(e.mousetime),e.DOM.animatable)null!=e.DOM.animatable[n]&&null!=e.options[n]&&gsap.to(e.DOM.animatable[n],null!=e.options[n].reverseAnimation?e.options[n].reverseAnimation.duration||0:1.5,{ease:null!=e.options[n].reverseAnimation&&e.options[n].reverseAnimation.ease||"Power2.easeOut",x:0,y:0,z:0,rotationX:0,rotationY:0,rotationZ:0})}))},this.DOM.el.addEventListener("mouseenter",this.mouseenterFn),this.DOM.el.addEventListener("mousemove",this.mousemoveFn),this.DOM.el.addEventListener("mouseleave",this.mouseleaveFn)}},{key:"tilt",value:function(e){if(g){var t,n,i,r=(n=0,i=0,(t=e)||(t=window.event),t.pageX||t.pageY?(n=t.pageX,i=t.pageY):(t.clientX||t.clientY)&&(n=t.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,i=t.clientY+document.body.scrollTop+document.documentElement.scrollTop),{x:n,y:i}),o=document.body.scrollLeft+document.documentElement.scrollLeft,l=document.body.scrollTop+document.documentElement.scrollTop,a=this.DOM.el.getBoundingClientRect(),s=r.x-a.left-o,c=r.y-a.top-l;for(var u in this.DOM.animatable)if(null!=this.DOM.animatable[u]&&null!=this.options[u]){var h=null!=this.options[u]&&this.options[u].translation||{x:0,y:0,z:0},m=null!=this.options[u]&&this.options[u].rotation||{x:0,y:0,z:0};d(h),d(m);var f={translation:{x:(h.x[1]-h.x[0])/a.width*s+h.x[0],y:(h.y[1]-h.y[0])/a.height*c+h.y[0],z:(h.z[1]-h.z[0])/a.height*c+h.z[0]},rotation:{x:(m.x[1]-m.x[0])/a.height*c+m.x[0],y:(m.y[1]-m.y[0])/a.width*s+m.y[0],z:(m.z[1]-m.z[0])/a.width*s+m.z[0]}};gsap.to(this.DOM.animatable[u],1.5,{ease:"Power1.easeOut",x:f.translation.x,y:f.translation.y,z:f.translation.z,rotationX:f.rotation.x,rotationY:f.rotation.y,rotationZ:f.rotation.z})}}}}])}(),f=function(){return h((function e(){c(this,e),this.DOM={el:document.querySelector("#overlay-".concat(n.widget_id))},this.DOM.reveal=this.DOM.el.querySelector(".overlay__reveal"),this.DOM.items=this.DOM.el.querySelectorAll(".overlay__item"),this.DOM.close=this.DOM.el.querySelector(".overlay__close")}),[{key:"show",value:function(e){var t=this;this.contentItem=e,this.DOM.el.classList.add("overlay--open"),gsap.to(this.DOM.reveal,a.transition_duration,{ease:"Power1.easeInOut",x:"0%",onComplete:function(){document.body.classList.add("preview-open"),t.revealItem(e),gsap.to(t.DOM.reveal,a.transition_duration,{delay:.2,ease:"Power3.easeOut",x:"-100%"}),t.DOM.close.style.opacity=1}})}},{key:"revealItem",value:function(){this.contentItem.style.opacity=1;var e=[];e.push(this.contentItem.querySelector(".box__shadow")),e.push(this.contentItem.querySelector(".eael-grid-fg-box__img")),e.push(this.contentItem.querySelector(".eael-grid-fg-title")),e.push(this.contentItem.querySelector(".eael-grid-fg-control-name")),e.push(this.contentItem.querySelector(".eael-grid-fg-icon")),e.push(this.contentItem.querySelector(".overlay__content"));for(var t=0,n=e;t<n.length;t++){var i=n[t];if(null!=i){var r=i.getBoundingClientRect(),o={width:window.innerWidth,height:window.innerHeight};gsap.to(i,s(.8,1.2,o.width,0,Math.abs(r.left+r.width-o.width)),{ease:"Expo.easeOut",delay:.2,startAt:{x:"".concat(s(0,800,o.width,0,Math.abs(r.left+r.width-o.width))),y:"".concat(s(-100,100,o.height,0,Math.abs(r.top+r.height-o.height))),rotationZ:"".concat(s(5,30,0,o.width,Math.abs(r.left+r.width-o.width)))},x:0,y:0,rotationZ:0})}}}},{key:"hide",value:function(){var e=this;this.DOM.el.classList.remove("overlay--open"),gsap.to(this.DOM.reveal,a.transition_duration,{ease:"Power3.easeOut",x:"0%",onComplete:function(){e.DOM.close.style.opacity=0,document.body.classList.remove("preview-open"),e.contentItem.style.opacity=0,gsap.to(e.DOM.reveal,a.transition_duration,{delay:0,ease:"Power3.easeOut",x:"100%"})}})}}])}(),y=function(){return h((function e(t){var n=this;c(this,e),this.DOM={el:t},this.items=[],this.DOM.el&&(this.initializeItems(),this.overlay=new f,this.overlay.DOM.close.addEventListener("click",(function(){return n.closeItem()})))}),[{key:"initializeItems",value:function(){var e=this;Array.from(this.DOM.el.querySelectorAll(".eael-grid__item")).forEach((function(t){if(!t.hasAttribute("data-initialized")){var n=new m(t);e.items.push(n),t.classList.contains("grid__item--noclick")||n.DOM.el.addEventListener("click",(function(n){n.preventDefault(),e.openItem(document.querySelector(t.getAttribute("data-item")))})),t.setAttribute("data-initialized","true")}}))}},{key:"openItem",value:function(e){if(!this.isPreviewOpen){this.isPreviewOpen=!0,g=!1,this.overlay.show(e);var t,n=l(this.items);try{for(n.s();!(t=n.n()).done;){var i=t.value;for(var r in i.DOM.animatable){var o=i.DOM.animatable[r];if(o){var a=o.getBoundingClientRect(),c=void 0,h=void 0,d={width:window.innerWidth,height:window.innerHeight};a.top+a.height/2<d.height/2-.1*d.height?(c=-1*s(20,600,0,d.width,Math.abs(a.left+a.width-d.width)),h=-1*s(20,600,0,d.width,Math.abs(a.left+a.width-d.width))):a.top+a.height/2>d.height/2+.1*d.height?(c=-1*s(20,600,0,d.width,Math.abs(a.left+a.width-d.width)),h=s(20,600,0,d.width,Math.abs(a.left+a.width-d.width))):(c=-1*s(10,700,0,d.width,Math.abs(a.left+a.width-d.width)),h=u(-25,25)),gsap.to(o,.4,{ease:"Power3.easeOut",delay:s(0,.3,0,d.width,Math.abs(a.left+a.width-d.width)),x:c,y:h,rotationZ:u(-10,10),opacity:0})}}}}catch(e){n.e(e)}finally{n.f()}}}},{key:"closeItem",value:function(){if(this.isPreviewOpen){this.isPreviewOpen=!1,this.overlay.hide();var e,t=l(this.items);try{for(t.s();!(e=t.n()).done;){var n=e.value;for(var i in n.DOM.animatable){var r=n.DOM.animatable[i];if(r){var o=r.getBoundingClientRect(),a={width:window.innerWidth};gsap.to(r,.6,{ease:"Expo.easeOut",delay:.55+s(0,.2,0,a.width,Math.abs(o.left+o.width-a.width)),x:0,y:0,rotationZ:0,opacity:1})}}}}catch(e){t.e(e)}finally{t.f()}g=!0,this.DOM.el&&gsap.set(this.DOM.el,{pointerEvents:"auto"})}}}])}(),g=!0,v=document.querySelector("#eael-grid-fg-".concat(n.widget_id));v&&(v.eaelGallery=new y(v)),imagesLoaded(document.querySelectorAll(".eael-grid-fg-box__img"),(function(){return document.body.classList.remove("loading")}));var p=function(){return{width:window.innerWidth,height:window.innerHeight}},O=function(e,t,n){e.forEach((function(e){var i=document.createElement(t);i.classList=n,e.parentNode.appendChild(i),i.appendChild(e)}))},M=function(){return h((function e(t){c(this,e),this.DOM={outer:t,inner:Array.isArray(t)?t.map((function(e){return e.querySelector(".eael-split-oh__inner")})):t.querySelector(".eael-split-oh__inner")}}),[{key:"in",value:function(){return this.outTimeline&&this.outTimeline.isActive()&&this.outTimeline.kill(),this.inTimeline=gsap.timeline({defaults:{duration:1.2,ease:"expo"}}).set(this.DOM.inner,{y:"120%",rotate:15}).to(this.DOM.inner,{y:"0%",rotate:0,stagger:.03}),this.inTimeline}},{key:"out",value:function(){return this.inTimeline&&this.inTimeline.isActive()&&this.inTimeline.kill(),this.outTimeline=gsap.timeline({defaults:{duration:.5,ease:"expo.in"}}).to(this.DOM.inner,{y:"-120%",rotate:-5,stagger:.03}),this.outTimeline}}])}(),D=function(){return h((function e(t){c(this,e),this.DOM={animationElems:Array.isArray(t)?t:[t]},this.SplitTypeInstances=[],this.lines=[];var n,i=l(this.DOM.animationElems);try{for(i.s();!(n=i.n()).done;){var r=n.value,o=new SplitType(r,{types:"lines"});O(o.lines,"div","eael-split-oh"),this.lines.push(o.lines),this.SplitTypeInstances.push(o)}}catch(e){i.e(e)}finally{i.f()}this.initEvents()}),[{key:"in",value:function(){return this.isVisible=!0,gsap.killTweensOf(this.lines),gsap.timeline({defaults:{duration:1.2,ease:"expo"}}).set(this.lines,{y:"150%",rotate:15}).to(this.lines,{y:"0%",rotate:0,stagger:.04})}},{key:"out",value:function(){return this.isVisible=!1,gsap.killTweensOf(this.lines),gsap.timeline({defaults:{duration:.5,ease:"expo.in"}}).to(this.lines,{y:"-150%",rotate:-5,stagger:.02})}},{key:"initEvents",value:function(){var e=this;window.addEventListener("resize",(function(){e.lines=[];var t,n=l(e.SplitTypeInstances);try{for(n.s();!(t=n.n()).done;){var i=t.value;i.split(),O(i.lines,"div","eael-split-oh"),e.lines.push(i.lines)}}catch(e){n.e(e)}finally{n.f()}e.isVisible||gsap.set(e.lines,{y:"-150%"})}))}}])}(),w=h((function e(t){c(this,e),o(this,"DOM",{el:null}),o(this,"textReveal",null),o(this,"textLinesReveal",null),this.DOM.el=t,this.DOM.nav={prev:this.DOM.el.querySelector(".slide-nav__img--prev"),next:this.DOM.el.querySelector(".slide-nav__img--next")},this.textReveal=new M(r(this.DOM.el.querySelectorAll(".eael-split-oh"))),this.textLinesReveal=new D(this.DOM.el.querySelector(".eael-hg-content__item-text"))})),b=h((function e(t){c(this,e),o(this,"DOM",{el:null,inner:null,contentId:null,contentItem:null}),this.DOM.el=t,this.DOM.inner=this.DOM.el.querySelector(".grid__cell-img-inner"),this.contentId=this.DOM.inner.dataset.item,this.contentItem=new w(document.querySelector("#".concat(this.contentId)))})),x=document.body,C=p();window.addEventListener("resize",(function(){return C=p()}));var I=function(){return h((function e(t){c(this,e),o(this,"DOM",{el:null,imageCells:null,content:null,backCtrl:null,miniGrid:{el:null,cells:null}}),o(this,"imageCellArr",[]),o(this,"currentCell",-1),o(this,"isGridView",!0),o(this,"isAnimating",!1),o(this,"textReveal",null),this.DOM.el=t,this.initializeGallery()}),[{key:"initializeGallery",value:function(){this.initializeItems(),this.initializeContent(),this.initializeMiniGrid(),this.initializeTextAnimations(),this.initEvents()}},{key:"initializeItems",value:function(){var e=this,t=this.DOM.el.querySelectorAll(".eael-hg-grid__cell-img");this.DOM.imageCells=r(t),t.forEach((function(t){t.hasAttribute("data-initialized")||(e.imageCellArr.push(new b(t)),t.setAttribute("data-initialized","true"))}))}},{key:"initializeContent",value:function(){this.DOM.content=document.querySelector("#eael-hg-content-".concat(n.widget_id)),this.DOM.content&&(this.DOM.backCtrl=this.DOM.content.querySelector(".eael-hg-back"))}},{key:"initializeMiniGrid",value:function(){var e=this;this.DOM.content&&(this.DOM.miniGrid.el=this.DOM.content.querySelector(".eael-hg-grid--mini"),this.DOM.miniGrid.el&&(this.DOM.miniGrid.cells=r(this.DOM.miniGrid.el.querySelectorAll(".eael-hg-grid__cell")),this.DOM.miniGrid.cells&&this.DOM.miniGrid.cells.length>0&&this.DOM.miniGrid.cells.forEach((function(t,n){t.addEventListener("click",(function(){if(e.isAnimating||e.currentCell===n)return!1;e.isAnimating=!0,e.changeContent(n)}))}))))}},{key:"initializeTextAnimations",value:function(){var e=this.DOM.el.querySelectorAll(".eael-split-oh");e.length>0?this.textReveal=new M(r(e)):this.textReveal=null}},{key:"trackVisibleCells",value:function(){var e=new IntersectionObserver((function(e,t){e.forEach((function(e){e.intersectionRatio>0?e.target.classList.add("in-view"):e.target.classList.remove("in-view")}))}));this.DOM.imageCells.forEach((function(t){return e.observe(t)}))}},{key:"initEvents",value:function(){var e,t=this,n=l(this.imageCellArr.entries());try{var r=function(){var n=i(e.value,2),r=n[0],o=n[1];o.DOM.el.addEventListener("click",(function(){if(!t.isGridView||t.isAnimating)return!1;t.isAnimating=!0,t.isGridView=!1,-1!==t.currentCell&&t.DOM.miniGrid&&t.DOM.miniGrid.cells&&t.DOM.miniGrid.cells[t.currentCell]&&t.DOM.miniGrid.cells[t.currentCell].classList.remove("grid__cell--current"),t.currentCell=r,t.DOM.miniGrid&&t.DOM.miniGrid.cells&&t.DOM.miniGrid.cells[t.currentCell]&&t.DOM.miniGrid.cells[t.currentCell].classList.add("grid__cell--current"),t.showContent(o)})),o.DOM.el.addEventListener("mouseenter",(function(){if(!t.isGridView)return!1;gsap.killTweensOf([o.DOM.el,o.DOM.inner]),gsap.timeline({defaults:{duration:2.4,ease:"expo"}}).to(o.DOM.el,{scale:.95},0).to(o.DOM.inner,{scale:1.4},0)})),o.DOM.el.addEventListener("mouseleave",(function(){if(!t.isGridView)return!1;gsap.killTweensOf([o.DOM.el,o.DOM.inner]),gsap.timeline({defaults:{duration:2.4,ease:"expo"}}).to([o.DOM.el,o.DOM.inner],{scale:1},0)}))};for(n.s();!(e=n.n()).done;)r()}catch(e){n.e(e)}finally{n.f()}this.DOM.backCtrl&&this.DOM.backCtrl.addEventListener("click",(function(){if(t.isAnimating)return!1;t.isAnimating=!0,t.isGridView=!0,t.closeContent()}))}},{key:"showContent",value:function(e){var t=this;if(e){var n=this.calcTransformImage();this.otherImageCells=this.DOM.imageCells.filter((function(t){return t!=e.DOM.el})),gsap.killTweensOf([e.DOM.el,e.DOM.inner,this.otherImageCells]),gsap.timeline({defaults:{duration:1.2,ease:"expo.inOut"},onStart:function(){return x.classList.add("eael-split-oh")},onComplete:function(){t.isAnimating=!1}}).addLabel("start",0).add((function(){t.textReveal&&t.textReveal.out()}),"start").set(this.DOM.el,{pointerEvents:"none"},"start").set(e.DOM.el,{zIndex:1001},"start").set([e.DOM.el,e.DOM.inner,this.otherImageCells],{willChange:"transform, opacity"},"start").to(e.DOM.el,{scale:n.scale,x:n.x,y:n.y,onComplete:function(){return gsap.set(e.DOM.el,{willChange:""})}},"start").to(e.DOM.inner,{scale:1,onComplete:function(){return gsap.set(e.DOM.inner,{willChange:""})}},"start").to([e.contentItem.DOM.nav.prev,e.contentItem.DOM.nav.next],{y:0},"start").to(this.otherImageCells,{opacity:0,scale:.8,onComplete:function(){return gsap.set(t.otherImageCells,{willChange:""})},stagger:{grid:"auto",amount:.17,from:this.currentCell}},"start").addLabel("showContent","start+=0.45").to(this.DOM.backCtrl,{ease:"expo",startAt:{x:"50%"},x:"0%",opacity:1},"showContent").set(this.DOM.miniGrid.el,{opacity:1},"showContent").set(this.DOM.miniGrid.cells,{opacity:0},"showContent").to(this.DOM.miniGrid.cells,{duration:1,ease:"expo",opacity:1,startAt:{scale:.8},scale:1,stagger:{grid:"auto",amount:.3,from:this.currentCell}},"showContent+=0.2").add((function(){e.contentItem.textReveal.in(),e.contentItem.textLinesReveal.in(),t.DOM.content.classList.add("content--open")}),"showContent").add((function(){return e.contentItem.DOM.el.classList.add("content__item--current")}),"showContent+=0.02")}}},{key:"closeContent",value:function(){var e=this,t=this.imageCellArr[this.currentCell];this.otherImageCells=this.DOM.imageCells.filter((function(e){return e!=t.DOM.el})),gsap.timeline({defaults:{duration:1,ease:"expo.inOut"},onStart:function(){x.classList.remove("eael-split-oh"),gsap.to(t.contentItem.DOM.el,{duration:.2,ease:"power2.out",onComplete:function(){t.contentItem.DOM.el.classList.remove("content__item--current"),gsap.set(t.contentItem.DOM.el,{y:0})}})},onComplete:function(){e.isAnimating=!1}}).addLabel("start",0).to(this.DOM.backCtrl,{x:"50%",opacity:0},"start").to(this.DOM.miniGrid.cells,{duration:.5,ease:"expo.in",opacity:0,scale:.8,stagger:{grid:"auto",amount:.1,from:-this.currentCell},onComplete:function(){e.DOM.miniGrid.el&&gsap.set(e.DOM.miniGrid.el,{opacity:0})}},"start").add((function(){e.textReveal&&e.textReveal.out(),e.DOM.content&&e.DOM.content.classList.remove("content--open")}),"start").addLabel("showGrid",0).set([t.DOM.el,this.otherImageCells],{willChange:"transform, opacity"},"showGrid").to(t.DOM.el,{scale:1,x:0,y:0,onComplete:function(){return gsap.set(t.DOM.el,{willChange:"",zIndex:1})}},"showGrid").to(t.contentItem.DOM.nav.prev,{y:"-100%"},"showGrid").to(t.contentItem.DOM.nav.next,{y:"100%"},"showGrid").to(this.otherImageCells,{opacity:1,scale:1,onComplete:function(){gsap.set(e.otherImageCells,{willChange:""}),gsap.set(e.DOM.el,{pointerEvents:"auto"})},stagger:{grid:"auto",amount:.17,from:-this.currentCell}},"showGrid").add((function(){e.textReveal&&e.textReveal.in()}),"showGrid+=0.3")}},{key:"changeContent",value:function(e){var t=this,n=this.imageCellArr[this.currentCell],i=this.imageCellArr[e];if(n&&i){this.DOM.miniGrid&&this.DOM.miniGrid.cells&&(-1!==this.currentCell&&this.DOM.miniGrid.cells[this.currentCell]&&this.DOM.miniGrid.cells[this.currentCell].classList.remove("grid__cell--current"),this.currentCell=e,this.DOM.miniGrid.cells[this.currentCell]&&this.DOM.miniGrid.cells[this.currentCell].classList.add("grid__cell--current"));var r=this.calcTransformImage();gsap.timeline({defaults:{duration:1,ease:"expo.inOut"},onComplete:function(){t.isAnimating=!1}}).addLabel("start",0).add((function(){n.contentItem&&n.contentItem.textReveal&&n.contentItem.textReveal.out(),n.contentItem&&n.contentItem.textLinesReveal&&n.contentItem.textLinesReveal.out()}),"start").add((function(){n.contentItem&&n.contentItem.DOM&&n.contentItem.DOM.el&&n.contentItem.DOM.el.classList.remove("content__item--current")})).set([n.DOM.el,i.DOM.el],{willChange:"transform, opacity"},"start").to(n.DOM.el,{opacity:0,scale:.8,x:0,y:0,onComplete:function(){return gsap.set(n.DOM.el,{willChange:"",zIndex:1})}},"start").to(n.contentItem.DOM.nav.prev,{y:"-100%"},"start").to(n.contentItem.DOM.nav.next,{y:"100%"},"start").addLabel("showContent",">-=0.4").set(i.DOM.el,{zIndex:1001},"start").to(i.DOM.el,{scale:r.scale,x:r.x,y:r.y,opacity:1,onComplete:function(){return gsap.set(i.DOM.el,{willChange:""})}},"start").to([i.contentItem.DOM.nav.prev,i.contentItem.DOM.nav.next],{ease:"expo",y:0},"showContent").add((function(){i.contentItem&&i.contentItem.textReveal&&i.contentItem.textReveal.in(),i.contentItem&&i.contentItem.textLinesReveal&&i.contentItem.textLinesReveal.in()}),"showContent").add((function(){i.contentItem&&i.contentItem.DOM&&i.contentItem.DOM.el&&i.contentItem.DOM.el.classList.add("content__item--current")}),"showContent+=0.02")}}},{key:"calcTransformImage",value:function(){var e=function(e){var t=e.getBoundingClientRect(),n=getComputedStyle(e),i=n.transform;if(i){var r,o,l,a;if(i.startsWith("matrix3d(")){r=+(s=i.slice(9,-1).split(/, /))[0],o=+s[5],l=+s[12],a=+s[13]}else{if(!i.startsWith("matrix("))return t;var s;r=+(s=i.slice(7,-1).split(/, /))[0],o=+s[3],l=+s[4],a=+s[5]}var c=n.transformOrigin,u=t.x-l-(1-r)*parseFloat(c),h=t.y-a-(1-o)*parseFloat(c.slice(c.indexOf(" ")+1)),d=r?t.width/r:e.offsetWidth,m=o?t.height/o:e.offsetHeight;return{x:u,y:h,width:d,height:m,top:h,right:u+d,bottom:h+m,left:u}}return t}(this.imageCellArr[this.currentCell].DOM.el);return{scale:.35*C.width/e.width,x:.65*C.width-(e.left+e.width/2),y:.5*C.height-(e.top+e.height/2)}}}])}(),_=document.querySelector("#eael-hg-items-".concat(n.widget_id));_&&(_.eaelHarmonicGallery=new I(_)),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"img";return new Promise((function(t){imagesLoaded(document.querySelectorAll(e),{background:!0},t)}))}(".grid__cell-img-inner, .slide-nav__img").then((function(){return document.body.classList.remove("loading")}))};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("filterableGallery"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-filterable-gallery.default",m),jQuery(document).on("eael:filterable-gallery:items-loaded",(function(e,t){var n=document.querySelector("#eael-hg-items-".concat(t));n&&n.eaelHarmonicGallery&&n.eaelHarmonicGallery.initializeGallery()})),jQuery(document).on("eael:filterable-gallery:items-loaded",(function(e,t){var n=document.querySelector("#eael-grid-fg-".concat(t));n&&n.eaelGallery&&n.eaelGallery.initializeItems()}))}))}});