!function(e){var t={};function n(o){if(t[o])return t[o].exports;var a=t[o]={i:o,l:!1,exports:{}};return e[o].call(a.exports,a,a.exports,n),a.l=!0,a.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(o,a,function(t){return e[t]}.bind(null,a));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=23)}({23:function(e,t){!function(e){"use strict";var t=function(e,t){var n=e.find(".eael-one-page-nav").eq(0),o="#"+n.data("section-id"),a=n.data("section-ids"),i=n.data("top-offset"),r=n.data("scroll-speed"),l=n.data("scroll-wheel"),c=n.data("scroll-touch"),d=n.data("scroll-keys"),u=o+" .eael-one-page-nav-item.active";function f(){t.each(a,(function(e,n){var a,i,r,l=t("#"+n);(null===(a=l.offset())||void 0===a?void 0:a.top)-t(window).height()/2<t(window).scrollTop()&&((null===(i=l.offset())||void 0===i?void 0:i.top)>=t(window).scrollTop()||(null===(r=l.offset())||void 0===r?void 0:r.top)+l.height()-t(window).height()/2>t(window).scrollTop())?t(o+' .eael-one-page-nav-item a[data-row-id="'+l.attr("id")+'"]').parent().addClass("active"):t(o+' .eael-one-page-nav-item a[data-row-id="'+l.attr("id")+'"]').parent().removeClass("active")}))}if(t(o+" .eael-one-page-nav-item a").on("click",(function(e){var n;if(e.preventDefault(),e.stopPropagation(),0!==t("#"+t(this).data("row-id")).length&&!t("html, body").is(":animated"))return t("html, body").animate({scrollTop:(null===(n=t("#"+t(this).data("row-id")))||void 0===n||null===(n=n.offset())||void 0===n?void 0:n.top)-i},r),t(o+" .eael-one-page-nav-item").removeClass("active"),t(this).parent().addClass("active"),!1})),f(),t(window).on("scroll",(function(){f()})),"on"==l){var s,v=0;t(document).on("mousewheel DOMMouseScroll",(function(e){var n=(new Date).getTime();if(n-v<1300)e.preventDefault();else{var o=e.originalEvent.detail<0||e.originalEvent.wheelDelta>0?1:-1;t("html,body").is(":animated")||(o<0?t(u).next().length>0&&t(u).next().find("a").trigger("click"):t(u).prev().length>0&&t(u).prev().find("a").trigger("click")),v=n}})),"on"==c&&t(document).on("pointerdown touchstart",(function(e){var t=e.originalEvent.touches;t&&t.length&&(s=t[0].screenY,e.originalEvent.timeStamp)})).on("touchmove",(function(e){t("html,body").is(":animated")&&e.preventDefault()})).on("pointerup touchend",(function(e){var n=e.originalEvent;if("touch"===n.pointerType||"touchend"===e.type){var o=n.screenY||n.changedTouches[0].screenY,a=s-o;n.timeStamp;if(a<0&&t(u).prev().length>0&&t(u).prev().find("a").trigger("click"),a>0&&t(u).next().length>0&&t(u).next().find("a").trigger("click"),Math.abs(a)<2)return}}))}"on"==d&&t(document).keydown((function(e){var n=e.target.tagName.toLowerCase();if("input"!==n||"textarea"!==n)switch(e.which){case 38:t(u).prev().find("a").trigger("click");break;case 40:t(u).next().find("a").trigger("click");break;case 33:t(u).prev().find("a").trigger("click");break;case 36:t(u).next().find("a").trigger("click");break;default:return}}))};e(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/eael-one-page-nav.default",t)}))}(jQuery)}});