/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./src/js/view/smooth-animation.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./src/js/view/smooth-animation.js":
/*!*****************************************!*\
  !*** ./src/js/view/smooth-animation.js ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("var SmoothAnimationHandler = function SmoothAnimationHandler($scope, $) {\n  var _$eventFunction, _$eventFunction2, _$translateX, _$translateX2, _$translateX3, _$translateY, _$translateY2, _$translateY3, _$opacityData, _$rotationData, _$scaleData, _$scaleXData, _$scaleYData, _$skewXData, _$skewYData, _$durationData, _$delayData, _$repeatData, _$easeData, _$yoyoData, _$staggerData, _$transformOriginXDat, _$transformOriginYDat, _$bgColorData, _$elementStartData, _$controllerStartData, _$customElementStartD, _$customElementStartD2, _$customControllerSta, _$customControllerSta2, _$elementEndData, _$controllerEndData, _$customElementEndDat, _$customElementEndDat2, _$customControllerEnd, _$customControllerEnd2, _$toggleActionsOnEnte, _$toggleActionsOnLeav, _$toggleAactionsOnEnt, _$toggleActionsOnLeav2, _$markersData, _$scrubData, _$scrubData2, _$pinData;\n  // console.log('Scope',  $scope[0].className);\n\n  // if( $scope[0].className.includes('eael_smooth_animation') === false ) {\n  //     return;\n  // }\n\n  var $scopeId = $scope.data('id'),\n    $presetData = $scope.data('preset'),\n    $eventFunction = $scope.data('event_function'),\n    $coreTriggerData = $scope.data('coretrigger'),\n    $translateX = $scope.data('translatex'),\n    $translateY = $scope.data('translatey'),\n    $opacityData = $scope.data('opacity'),\n    $rotationData = $scope.data('rotation'),\n    $scaleData = $scope.data('scale'),\n    $scaleXData = $scope.data('scalex'),\n    $scaleYData = $scope.data('scaley'),\n    $skewData = $scope.data('skew'),\n    $skewXData = $scope.data('skewx'),\n    $skewYData = $scope.data('skewy'),\n    $durationData = $scope.data('duration'),\n    $delayData = $scope.data('delay'),\n    $repeatData = $scope.data('repeat'),\n    $easeData = $scope.data('ease'),\n    $yoyoData = $scope.data('yoyo'),\n    $staggerData = $scope.data('stagger'),\n    $transformOriginXData = $scope.data('transformoriginx'),\n    $customTransformOriginXData = $scope.data('custom_transformoriginx'),\n    $transformOriginYData = $scope.data('transformoriginy'),\n    $customTransFormOriginYData = $scope.data('custom_transformoriginy'),\n    $bgColorData = $scope.data('bg_color'),\n    // ScrollTrigger\n\n    //Start\n    $elementStartData = $scope.data('element_start'),\n    $customElementStartData = $scope.data('custom_element_start'),\n    $controllerStartData = $scope.data('controller_start'),\n    $customControllerStartData = $scope.data('custom_controller_start'),\n    //End\n    $elementEndData = $scope.data('element_end'),\n    $customElementEndData = $scope.data('custom_element_end'),\n    $controllerEndData = $scope.data('controller_end'),\n    $customControllerEndData = $scope.data('custom_controller_end'),\n    $markersData = $scope.data('markers'),\n    $scrollon = $scope.data('scrollon'),\n    $scrubData = $scope.data('scrub'),\n    $toggleActionsOnEnterData = $scope.data('toggle_actions_on_enter'),\n    $toggleActionsOnLeaveData = $scope.data('toggle_actions_on_leave'),\n    $toggleAactionsOnEnterBackData = $scope.data('toggle_actions_on_enter_back'),\n    $toggleActionsOnLeaveBackData = $scope.data('toggle_actions_on_leave_back'),\n    $pinData = $scope.data('pin');\n\n  //Is Editor\n  if (window.isEditMode) {\n    if (window[\"eael_sa_\".concat($scopeId)] === true) {\n      return;\n    }\n    if (window[\"eael_sa_\".concat($scopeId)] === undefined && window.isEditMode || 1) {\n      var getHoverEffectSettingsVal = function getHoverEffectSettingsVal($el) {\n        $.each($el, function (i, el) {\n          var $getSettings = el.attributes.settings.attributes;\n          if (el.attributes.elType === 'widget') {\n            if ($getSettings['eael_smooth_animation_section'] === 'yes') {\n              eaelEditModeSettings[el.attributes.id] = el.attributes.settings.attributes;\n            }\n          }\n          if (el.attributes.elType === 'container') {\n            getHoverEffectSettingsVal(el.attributes.elements.models);\n          }\n          if (el.attributes.elType === 'section') {\n            getHoverEffectSettingsVal(el.attributes.elements.models);\n          }\n          if (el.attributes.elType === 'column') {\n            getHoverEffectSettingsVal(el.attributes.elements.models);\n          }\n        });\n      };\n      window[\"eael_sa_\".concat($scopeId)] = true;\n      var eaelEditModeSettings = [];\n      getHoverEffectSettingsVal(window.elementor.elements.models);\n    }\n\n    //\n    for (var key in eaelEditModeSettings) {\n      if ($scopeId === key) {\n        var _eaelEditModeSettings, _eaelEditModeSettings2, _eaelEditModeSettings3, _eaelEditModeSettings4, _eaelEditModeSettings5, _eaelEditModeSettings6, _eaelEditModeSettings7, _eaelEditModeSettings8, _eaelEditModeSettings9, _eaelEditModeSettings10, _eaelEditModeSettings11, _eaelEditModeSettings12, _eaelEditModeSettings13;\n        var smoothAnimationTransformSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings = eaelEditModeSettings[key]) === null || _eaelEditModeSettings === void 0 ? void 0 : _eaelEditModeSettings['eael_smooth_animation_event_transform_setting'];\n        var smoothAnimationTransformOrigin = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings2 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings2 === void 0 ? void 0 : _eaelEditModeSettings2['eael_smooth_animation_event_transform_orign_setting'];\n        var smoothAnimationScaleSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings3 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings3 === void 0 ? void 0 : _eaelEditModeSettings3['eael_smooth_animation_event_scale_setting'];\n        var smoothAnimationSkewSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings4 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings4 === void 0 ? void 0 : _eaelEditModeSettings4['eael_smooth_animation_event_skew_setting'];\n        var smoothAnimationColorSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings5 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings5 === void 0 ? void 0 : _eaelEditModeSettings5['eael_smooth_animation_event_color_setting'];\n        var smoothAnimationAnimationSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings6 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings6 === void 0 ? void 0 : _eaelEditModeSettings6['eael_smooth_animation_event_animation_setting'];\n        var smoothAnimationManualSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings7 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings7 === void 0 ? void 0 : _eaelEditModeSettings7['eael_smooth_animation_event_manual_setting'];\n        var smoothAnimationMarkersSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings8 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings8 === void 0 ? void 0 : _eaelEditModeSettings8['eael_smooth_animation_event_markers'];\n        var smoothAnimationStartSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings9 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings9 === void 0 ? void 0 : _eaelEditModeSettings9['eael_smooth_animation_event_canvas_start'];\n        var smoothAnimationEndSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings10 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings10 === void 0 ? void 0 : _eaelEditModeSettings10['eael_smooth_animation_event_canvas_end'];\n        var smoothAnimationScarbSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings11 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings11 === void 0 ? void 0 : _eaelEditModeSettings11['eael_smooth_animation_event_canvas_scrub'];\n        var smoothAnimationPinSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings12 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings12 === void 0 ? void 0 : _eaelEditModeSettings12['eael_smooth_animation_event_pin'];\n        var smoothAnimationToggleActionsSettings = eaelEditModeSettings === null || eaelEditModeSettings === void 0 || (_eaelEditModeSettings13 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings13 === void 0 ? void 0 : _eaelEditModeSettings13['eael_smooth_animation_toggle_actions'];\n        var _eaelEditModeSettings14 = eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : eaelEditModeSettings[key],\n          eventFunction = _eaelEditModeSettings14.eael_smooth_animation_event_function,\n          translateXData = _eaelEditModeSettings14.eael_smooth_animation_event_transform_translatex,\n          translateYData = _eaelEditModeSettings14.eael_smooth_animation_event_transform_translatey,\n          transformOriginXData = _eaelEditModeSettings14.eael_smooth_animation_transform_originx,\n          customTransformOriginXData = _eaelEditModeSettings14.eael_smooth_animation_trans_originx_custom,\n          transformOriginYData = _eaelEditModeSettings14.eael_smooth_animation_transform_originy,\n          customTransFormOriginYData = _eaelEditModeSettings14.eael_smooth_animation_trans_originy_custom,\n          opacityData = _eaelEditModeSettings14.eael_smooth_animation_event_transform_opacity,\n          rotationData = _eaelEditModeSettings14.eael_smooth_animation_event_transform_rotate,\n          scaleXYData = _eaelEditModeSettings14.eael_smooth_animation_event_scalexy,\n          scaleData = _eaelEditModeSettings14.eael_smooth_animation_event_scale,\n          scaleXData = _eaelEditModeSettings14.eael_smooth_animation_event_scalex,\n          scaleYData = _eaelEditModeSettings14.eael_smooth_animation_event_scaley,\n          skewData = _eaelEditModeSettings14.eael_smooth_animation_event_skew,\n          skewXData = _eaelEditModeSettings14.eael_smooth_animation_event_skewx,\n          skewYData = _eaelEditModeSettings14.eael_smooth_animation_event_skewy,\n          bgColorData = _eaelEditModeSettings14.eael_smooth_animation_event_bg_color,\n          easeData = _eaelEditModeSettings14.eael_smooth_animation_event_animation_easing,\n          yoyoData = _eaelEditModeSettings14.eael_smooth_animation_event_animation_yoyo,\n          staggerData = _eaelEditModeSettings14.eael_smooth_animation_event_animation_stagger,\n          durationData = _eaelEditModeSettings14.eael_smooth_animation_event_duration,\n          delayData = _eaelEditModeSettings14.eael_smooth_animation_event_delay,\n          repeatData = _eaelEditModeSettings14.eael_smooth_animation_event_loop,\n          markersData = _eaelEditModeSettings14.eael_smooth_animation_event_markers,\n          elementStartData = _eaelEditModeSettings14.eael_smooth_animation_event_canvas_element_start,\n          controllerStartData = _eaelEditModeSettings14.eael_smooth_animation_event_canvas_controller_start,\n          customElementStartData = _eaelEditModeSettings14.eael_smooth_animation_event_canvas_element_start_custom,\n          customControllerStartData = _eaelEditModeSettings14.eael_smooth_animation_event_canvas_controller_start_custom,\n          elementEndData = _eaelEditModeSettings14.eael_smooth_animation_event_canvas_element_end,\n          controllerEndData = _eaelEditModeSettings14.eael_smooth_animation_event_canvas_controller_end,\n          customElementEndData = _eaelEditModeSettings14.eael_smooth_animation_event_canvas_element_end_custom,\n          customControllerEndData = _eaelEditModeSettings14.eael_smooth_animation_event_canvas_controller_end_custom,\n          scrubData = _eaelEditModeSettings14.eael_smooth_animation_event_scrub_settings,\n          scrubDataDefault = _eaelEditModeSettings14.eael_smooth_animation_event_scrub_setting_default,\n          scrubDataCustom = _eaelEditModeSettings14.eael_smooth_animation_event_canvas_scrub_custom,\n          toggleActionsOnEnterData = _eaelEditModeSettings14.eael_smooth_animation_toggle_actions_on_enter,\n          toggleActionsOnLeaveData = _eaelEditModeSettings14.eael_smooth_animation_toggle_actions_on_leave,\n          toggleAactionsOnEnterBackData = _eaelEditModeSettings14.eael_smooth_animation_toggle_actions_on_enter_back,\n          toggleActionsOnLeaveBackData = _eaelEditModeSettings14.eael_smooth_animation_toggle_actions_on_leave_back,\n          pinData = _eaelEditModeSettings14.eael_smooth_animation_event_pin_setting_default;\n        $eventFunction = eventFunction ? {\n          tween: eventFunction\n        } : 'to';\n        if ('yes' === smoothAnimationTransformSettings) {\n          $translateX = translateXData !== null && translateXData !== void 0 && translateXData['size'] ? {\n            size: translateXData['size'],\n            unit: translateXData['unit']\n          } : null;\n          $translateY = translateYData !== null && translateYData !== void 0 && translateYData['size'] ? {\n            size: translateYData['size'],\n            unit: translateYData['unit']\n          } : null;\n          $opacityData = opacityData !== null && opacityData !== void 0 && opacityData['size'] ? {\n            opacity: opacityData['size']\n          } : null;\n          $rotationData = rotationData !== null && rotationData !== void 0 && rotationData['size'] ? {\n            rotation: rotationData['size']\n          } : null;\n        }\n\n        //Transform Origin\n        if ('yes' === smoothAnimationTransformOrigin) {\n          //Transform Origin X\n          if ('custom' !== transformOriginXData) {\n            $transformOriginXData = transformOriginXData ? {\n              transformoriginx: transformOriginXData\n            } : '';\n          } else {\n            $customTransformOriginXData = customTransformOriginXData !== null && customTransformOriginXData !== void 0 && customTransformOriginXData['size'] ? {\n              size: customTransformOriginXData['size'],\n              unit: customTransformOriginXData['unit']\n            } : null;\n          }\n\n          //Transform Origin Y\n          if ('custom' !== transformOriginYData) {\n            $transformOriginYData = transformOriginYData ? {\n              transformoriginy: transformOriginYData\n            } : '';\n          } else {\n            $customTransFormOriginYData = customTransFormOriginYData !== null && customTransFormOriginYData !== void 0 && customTransFormOriginYData['size'] ? {\n              size: customTransFormOriginYData['size'],\n              unit: customTransFormOriginYData['unit']\n            } : null;\n          }\n        }\n\n        //Color\n        if ('yes' === smoothAnimationColorSettings) {\n          $bgColorData = bgColorData ? {\n            bg_color: bgColorData\n          } : null;\n        }\n\n        //Scale\n        if ('yes' === smoothAnimationScaleSettings) {\n          if ('yes' === scaleXYData) {\n            $scaleXData = scaleXData !== null && scaleXData !== void 0 && scaleXData['size'] ? {\n              scalex: scaleXData['size']\n            } : null;\n            $scaleYData = scaleYData !== null && scaleYData !== void 0 && scaleYData['size'] ? {\n              scaley: scaleYData['size']\n            } : null;\n          } else {\n            $scaleData = scaleData !== null && scaleData !== void 0 && scaleData['size'] ? {\n              scale: scaleData['size']\n            } : null;\n          }\n        }\n\n        //Skew\n        if ('yes' === smoothAnimationSkewSettings) {\n          $skewXData = skewXData !== null && skewXData !== void 0 && skewXData['size'] ? {\n            skewx: skewXData['size']\n          } : null;\n          $skewYData = skewYData !== null && skewYData !== void 0 && skewYData['size'] ? {\n            skewy: skewYData['size']\n          } : null;\n        }\n\n        //Animation Settings\n        if ('yes' === smoothAnimationAnimationSettings) {\n          $easeData = easeData ? {\n            ease: easeData\n          } : null;\n          $yoyoData = yoyoData ? {\n            yoyo: yoyoData\n          } : false;\n          $staggerData = staggerData ? {\n            stagger: staggerData\n          } : '';\n        }\n\n        //Manual Settings\n        if ('yes' === smoothAnimationManualSettings) {\n          $durationData = durationData ? {\n            duration: durationData['size']\n          } : '';\n          $delayData = delayData ? {\n            delay: delayData['size']\n          } : '';\n          $repeatData = repeatData ? {\n            repeat: repeatData\n          } : '';\n        }\n\n        //ScrollTrigger Options\n        //Start\n        if ('yes' === smoothAnimationStartSettings) {\n          if ('custom' !== elementStartData) {\n            $elementStartData = elementStartData ? {\n              element_start: elementStartData\n            } : '';\n          } else {\n            $customElementStartData = customElementStartData !== null && customElementStartData !== void 0 && customElementStartData['size'] ? {\n              size: customElementStartData['size'],\n              unit: customElementStartData['unit']\n            } : null;\n          }\n          if ('custom' !== controllerStartData) {\n            $controllerStartData = controllerStartData ? {\n              controller_start: controllerStartData\n            } : '';\n          } else {\n            $customControllerStartData = customControllerStartData !== null && customControllerStartData !== void 0 && customControllerStartData['size'] ? {\n              size: customControllerStartData['size'],\n              unit: customControllerStartData['unit']\n            } : null;\n          }\n        }\n\n        //End\n        if ('yes' === smoothAnimationEndSettings) {\n          if ('custom' !== elementEndData) {\n            $elementEndData = elementEndData ? {\n              element_end: elementEndData\n            } : '';\n          } else {\n            $customElementEndData = customElementEndData !== null && customElementEndData !== void 0 && customElementEndData['size'] ? {\n              size: customElementEndData['size'],\n              unit: customElementEndData['unit']\n            } : null;\n          }\n          if ('custom' !== controllerEndData) {\n            $controllerEndData = controllerEndData ? {\n              controller_end: controllerEndData\n            } : '';\n          } else {\n            $customControllerEndData = customControllerEndData !== null && customControllerEndData !== void 0 && customControllerEndData['size'] ? {\n              size: customControllerEndData['size'],\n              unit: customControllerEndData['unit']\n            } : null;\n          }\n        }\n\n        //Markers Settings\n        if ('true' === smoothAnimationMarkersSettings) {\n          $markersData = markersData ? {\n            markers: markersData\n          } : '';\n        }\n\n        //Scrub Settings\n        if ('yes' === smoothAnimationScarbSettings) {\n          if ('custom' !== scrubData) {\n            $scrubData = scrubDataDefault ? {\n              scrub: scrubDataDefault\n            } : '';\n          } else {\n            $scrubData = scrubDataCustom ? {\n              scrub: scrubDataCustom['size']\n            } : '';\n          }\n        }\n\n        //ToggleActions\n        if ('yes' === smoothAnimationToggleActionsSettings) {\n          $toggleActionsOnEnterData = toggleActionsOnEnterData ? {\n            toggle_actions_on_enter: toggleActionsOnEnterData\n          } : '';\n          $toggleActionsOnLeaveData = toggleActionsOnLeaveData ? {\n            toggle_actions_on_leave: toggleActionsOnLeaveData\n          } : '';\n          $toggleAactionsOnEnterBackData = toggleAactionsOnEnterBackData ? {\n            toggle_actions_on_enter_back: toggleAactionsOnEnterBackData\n          } : '';\n          $toggleActionsOnLeaveBackData = toggleActionsOnLeaveBackData ? {\n            toggle_actions_on_leave_back: toggleActionsOnLeaveBackData\n          } : '';\n        }\n\n        //Pin Settings\n        if ('yes' === smoothAnimationPinSettings) {\n          $pinData = pinData ? {\n            pin: pinData\n          } : '';\n        }\n      }\n    }\n  }\n  var $eventTarget = \".elementor-element-\".concat($scopeId);\n  var $coreTrigger = $coreTriggerData ? \"[data-coretrigger=\\\"\".concat($coreTriggerData, \"\\\"]\") : $eventTarget;\n  var tweenInstance = (_$eventFunction = $eventFunction) !== null && _$eventFunction !== void 0 && _$eventFunction.tween ? (_$eventFunction2 = $eventFunction) === null || _$eventFunction2 === void 0 ? void 0 : _$eventFunction2.tween : 'to';\n  var $x = (_$translateX = $translateX) !== null && _$translateX !== void 0 && _$translateX.size ? \"\".concat((_$translateX2 = $translateX) === null || _$translateX2 === void 0 ? void 0 : _$translateX2.size).concat((_$translateX3 = $translateX) === null || _$translateX3 === void 0 ? void 0 : _$translateX3.unit) : '';\n  var $y = (_$translateY = $translateY) !== null && _$translateY !== void 0 && _$translateY.size ? \"\".concat((_$translateY2 = $translateY) === null || _$translateY2 === void 0 ? void 0 : _$translateY2.size).concat((_$translateY3 = $translateY) === null || _$translateY3 === void 0 ? void 0 : _$translateY3.unit) : '';\n  var $opacity = $opacityData ? (_$opacityData = $opacityData) === null || _$opacityData === void 0 ? void 0 : _$opacityData.opacity : '';\n  var $rotation = $rotationData ? (_$rotationData = $rotationData) === null || _$rotationData === void 0 ? void 0 : _$rotationData.rotation : '';\n  var $scale = $scaleData ? (_$scaleData = $scaleData) === null || _$scaleData === void 0 ? void 0 : _$scaleData.scale : '';\n  var $scaleX = $scaleXData ? (_$scaleXData = $scaleXData) === null || _$scaleXData === void 0 ? void 0 : _$scaleXData.scalex : '';\n  var $scaleY = $scaleYData ? (_$scaleYData = $scaleYData) === null || _$scaleYData === void 0 ? void 0 : _$scaleYData.scaley : '';\n  // let $skew = $skewData ? $skewData?.skew : '';\n  var $skewX = $skewXData ? (_$skewXData = $skewXData) === null || _$skewXData === void 0 ? void 0 : _$skewXData.skewx : '';\n  var $skewY = $skewYData ? (_$skewYData = $skewYData) === null || _$skewYData === void 0 ? void 0 : _$skewYData.skewy : '';\n  var $duration = $durationData ? (_$durationData = $durationData) === null || _$durationData === void 0 ? void 0 : _$durationData.duration : '';\n  var $delay = $delayData ? (_$delayData = $delayData) === null || _$delayData === void 0 ? void 0 : _$delayData.delay : '';\n  var $repeat = $repeatData ? (_$repeatData = $repeatData) === null || _$repeatData === void 0 ? void 0 : _$repeatData.repeat : '';\n  var $ease = $easeData ? (_$easeData = $easeData) === null || _$easeData === void 0 ? void 0 : _$easeData.ease : '';\n  var $yoyo = $yoyoData ? (_$yoyoData = $yoyoData) === null || _$yoyoData === void 0 ? void 0 : _$yoyoData.yoyo : false;\n  var $stagger = $staggerData ? (_$staggerData = $staggerData) === null || _$staggerData === void 0 ? void 0 : _$staggerData.stagger : '';\n  //transformOrigin\n  var $transformOriginX = ((_$transformOriginXDat = $transformOriginXData) === null || _$transformOriginXDat === void 0 ? void 0 : _$transformOriginXDat.transformoriginx) || '';\n  var $transformOriginY = ((_$transformOriginYDat = $transformOriginYData) === null || _$transformOriginYDat === void 0 ? void 0 : _$transformOriginYDat.transformoriginy) || '';\n  var $customTransformOriginX = $customTransformOriginXData ? \"\".concat($customTransformOriginXData.size).concat($customTransformOriginXData.unit) : '';\n  var $customTransformOriginY = $customTransFormOriginYData ? \"\".concat($customTransFormOriginYData.size).concat($customTransFormOriginYData.unit) : '';\n  var $transformOrigin = [$transformOriginX, $transformOriginY, $customTransformOriginX, $customTransformOriginY].filter(Boolean).join(' ').trim();\n  var $bgColor = $bgColorData ? (_$bgColorData = $bgColorData) === null || _$bgColorData === void 0 ? void 0 : _$bgColorData.bg_color : '';\n\n  // Initialize an empty object to store properties\n  var properties = {};\n  if ($x !== undefined && $x !== null && $x !== '') {\n    properties.x = $x;\n  }\n  if ($y !== undefined && $y !== null && $y !== '') {\n    properties.y = $y;\n  }\n  if ($opacity !== undefined && $opacity !== null && $opacity !== '') {\n    properties.opacity = $opacity;\n  }\n  if ($rotation !== undefined && $rotation !== null && $rotation !== '') {\n    properties.rotation = $rotation;\n  }\n  if ($duration !== undefined && $duration !== null && $duration !== '') {\n    properties.duration = $duration;\n  }\n  if ($delay !== undefined && $delay !== null && $delay !== '') {\n    properties.delay = $delay;\n  }\n  if ($repeat !== undefined && $repeat !== null && $repeat !== '') {\n    properties.repeat = $repeat;\n  }\n  if ($scale !== undefined && $scale !== null && $scale !== '') {\n    properties.scale = $scale;\n  }\n  if ($scaleX !== undefined && $scaleX !== null && $scaleX !== '') {\n    properties.scaleX = $scaleX;\n  }\n  if ($scaleY !== undefined && $scaleY !== null && $scaleY !== '') {\n    properties.scaleY = $scaleY;\n  }\n\n  // if ($skew !== undefined && $skew !== null && $skew !== '') {\n  //     properties.skew = $skew;\n  // }\n  if ($skewX !== undefined && $skewX !== null && $skewX !== '') {\n    properties.skewX = $skewX;\n  }\n  if ($skewY !== undefined && $skewY !== null && $skewY !== '') {\n    properties.skewY = $skewY;\n  }\n  if ($ease !== undefined && $ease !== null && $ease !== '') {\n    properties.ease = $ease;\n  }\n  if ($yoyo !== undefined && $yoyo !== null && $yoyo !== '') {\n    properties.yoyo = $yoyo;\n  }\n  if ($stagger !== undefined && $stagger !== null && $stagger !== '') {\n    properties.stagger = $stagger;\n  }\n  if ($bgColor !== undefined && $bgColor !== null && $bgColor !== '') {\n    properties.backgroundColor = $bgColor;\n  }\n  if ($transformOrigin !== undefined && $transformOrigin !== null && $transformOrigin !== '') {\n    properties.transformOrigin = $transformOrigin;\n  }\n\n  // let element = document.querySelector('.eael_smooth_animation');\n  // let $scrolltrigger = element.id;\n  var $scrolltrigger = '';\n\n  //Start\n  var $elementStart = ((_$elementStartData = $elementStartData) === null || _$elementStartData === void 0 ? void 0 : _$elementStartData.element_start) || '';\n  var $controllerStart = ((_$controllerStartData = $controllerStartData) === null || _$controllerStartData === void 0 ? void 0 : _$controllerStartData.controller_start) || '';\n  var $start = [$elementStart, $controllerStart, \"\".concat(((_$customElementStartD = $customElementStartData) === null || _$customElementStartD === void 0 ? void 0 : _$customElementStartD.size) || '').concat(((_$customElementStartD2 = $customElementStartData) === null || _$customElementStartD2 === void 0 ? void 0 : _$customElementStartD2.unit) || ''), \"\".concat(((_$customControllerSta = $customControllerStartData) === null || _$customControllerSta === void 0 ? void 0 : _$customControllerSta.size) || '').concat(((_$customControllerSta2 = $customControllerStartData) === null || _$customControllerSta2 === void 0 ? void 0 : _$customControllerSta2.unit) || '')].filter(Boolean).join(' ').trim();\n\n  //End\n  var $elementEnd = ((_$elementEndData = $elementEndData) === null || _$elementEndData === void 0 ? void 0 : _$elementEndData.element_end) || '';\n  var $controllerEnd = ((_$controllerEndData = $controllerEndData) === null || _$controllerEndData === void 0 ? void 0 : _$controllerEndData.controller_end) || '';\n  var $end = [$elementEnd, $controllerEnd, \"\".concat(((_$customElementEndDat = $customElementEndData) === null || _$customElementEndDat === void 0 ? void 0 : _$customElementEndDat.size) || '').concat(((_$customElementEndDat2 = $customElementEndData) === null || _$customElementEndDat2 === void 0 ? void 0 : _$customElementEndDat2.unit) || ''), \"\".concat(((_$customControllerEnd = $customControllerEndData) === null || _$customControllerEnd === void 0 ? void 0 : _$customControllerEnd.size) || '').concat(((_$customControllerEnd2 = $customControllerEndData) === null || _$customControllerEnd2 === void 0 ? void 0 : _$customControllerEnd2.unit) || '')].filter(Boolean).join(' ').trim();\n  var $toggleActionsOnEnter = $toggleActionsOnEnterData ? (_$toggleActionsOnEnte = $toggleActionsOnEnterData) === null || _$toggleActionsOnEnte === void 0 ? void 0 : _$toggleActionsOnEnte.toggle_actions_on_enter : '';\n  var $toggleActionsOnLeave = $toggleActionsOnLeaveData ? (_$toggleActionsOnLeav = $toggleActionsOnLeaveData) === null || _$toggleActionsOnLeav === void 0 ? void 0 : _$toggleActionsOnLeav.toggle_actions_on_leave : '';\n  var $toggleAactionsOnEnterBack = $toggleAactionsOnEnterBackData ? (_$toggleAactionsOnEnt = $toggleAactionsOnEnterBackData) === null || _$toggleAactionsOnEnt === void 0 ? void 0 : _$toggleAactionsOnEnt.toggle_actions_on_enter_back : '';\n  var $toggleActionsOnLeaveBack = $toggleActionsOnLeaveBackData ? (_$toggleActionsOnLeav2 = $toggleActionsOnLeaveBackData) === null || _$toggleActionsOnLeav2 === void 0 ? void 0 : _$toggleActionsOnLeav2.toggle_actions_on_leave_back : '';\n  var $toggleActions = $toggleActionsOnEnter || $toggleActionsOnLeave || $toggleAactionsOnEnterBack || $toggleActionsOnLeaveBack ? \"\".concat($toggleActionsOnEnter, \" \").concat($toggleActionsOnLeave, \" \").concat($toggleAactionsOnEnterBack, \" \").concat($toggleActionsOnLeaveBack) : '';\n  var $markers = $markersData ? (_$markersData = $markersData) === null || _$markersData === void 0 ? void 0 : _$markersData.markers : '';\n  var $scrub = (_$scrubData = $scrubData) !== null && _$scrubData !== void 0 && _$scrubData.scrub ? (_$scrubData2 = $scrubData) === null || _$scrubData2 === void 0 ? void 0 : _$scrubData2.scrub : '';\n  var $pin = $pinData ? (_$pinData = $pinData) === null || _$pinData === void 0 ? void 0 : _$pinData.pin : '';\n  var scrolltriggerVal = {};\n  scrolltriggerVal.trigger = $scrolltrigger ? \"#\".concat($scrolltrigger) : $eventTarget;\n\n  // if ($eventTarget !== undefined && $eventTarget !== null && $eventTarget !== '') {\n  // scrolltriggerVal.trigger = $eventTarget;\n  // }\n  if ($start !== undefined && $start !== null && $start !== '') {\n    scrolltriggerVal.start = $start;\n  }\n  if ($end !== undefined && $end !== null && $end !== '') {\n    scrolltriggerVal.end = $end;\n  }\n  if ($scrub !== undefined && $scrub !== null && $scrub !== '') {\n    scrolltriggerVal.scrub = $scrub;\n  }\n  if ($markers !== undefined && $markers !== null && $markers !== '') {\n    scrolltriggerVal.markers = $markers;\n  }\n  if ($toggleActions !== undefined && $toggleActions !== null && $toggleActions !== '') {\n    scrolltriggerVal.toggleActions = $toggleActions ? $toggleActions : 'play';\n  }\n  if ($pin !== undefined && $pin !== null && $pin !== '') {\n    scrolltriggerVal.pin = Boolean($pin);\n  }\n\n  //\n  properties.scrollTrigger = scrolltriggerVal;\n\n  // gsap code here!\n  if ($scrollon === 'on') {\n    gsap.registerPlugin(ScrollTrigger);\n  }\n  gsap[tweenInstance]($coreTrigger, properties);\n};\njQuery(window).on(\"elementor/frontend/init\", function () {\n  if (eael.elementStatusCheck('eaelSmoothAnimation')) {\n    return false;\n  }\n  elementorFrontend.hooks.addAction(\"frontend/element_ready/widget\", SmoothAnimationHandler);\n  elementorFrontend.hooks.addAction(\"frontend/element_ready/container\", SmoothAnimationHandler);\n  // elementorFrontend.hooks.addAction( \"frontend/element_ready/section\", SmoothAnimationHandler );\n  // elementorFrontend.hooks.addAction( \"frontend/element_ready/column\", SmoothAnimationHandler );\n});\n\n//# sourceURL=webpack:///./src/js/view/smooth-animation.js?");

/***/ })

/******/ });