!function(e){var a={};function n(t){if(a[t])return a[t].exports;var o=a[t]={i:t,l:!1,exports:{}};return e[t].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=a,n.d=function(e,a,t){n.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:t})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,a){if(1&a&&(e=n(e)),8&a)return e;if(4&a&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(n.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&a&&"string"!=typeof e)for(var o in e)n.d(t,o,function(a){return e[a]}.bind(null,o));return t},n.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(a,"a",a),a},n.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)},n.p="",n(n.s=18)}({18:function(e,a){eael.hooks.addAction("init","ea",(function(){if(eael.elementStatusCheck("eaelLoginRegisterPro"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-login-register.default",(function(e,a){var n,t=e.find(".eael-login-registration-wrapper"),o=t.data("is-ajax"),i=t.data("widget-id"),r=t.data("page-id"),s=t.data("redirect-to"),l=t.find("#eael-login-form"),d=t.find("#eael-lostpassword-form"),c=t.find("#eael-resetpassword-form"),f="undefined"!=typeof grecaptcha&&null!==grecaptcha,p=t.data("login-recaptcha-version"),u=t.data("register-recaptcha-version"),g=t.data("lostpassword-recaptcha-version"),m=t.data("recaptcha-sitekey-v3");n="v3"===p||"v3"===u||"v3"===g,window.isLoggedInByFB=!1,window.isUsingGoogleLogin=!1;var h="eael-google-login-btn-"+i,v=l.find("#"+h),b="eael-fb-login-btn-"+i,w=l.find("#"+b),_=t.find("#eael-register-form-wrapper"),y=t.find("#eael-register-form"),k="eael-google-register-btn-"+i,B=y.find("#"+k),x="eael-fb-register-btn-"+i,z=y.find("#"+x),j={name:"action",value:"eael-login-register-form"},L=["facebook","google","login"],S=y.find("#form-field-password"),C=y.find(".pass-meta-info").data("strength-options"),F=y.find(".eael-pass-notice"),T=y.find(".eael-pass-meter"),A=y.find(".eael-pass-hint"),I=_.attr("data-use-weak-password"),E=_.data("password-min-length"),O=_.attr("data-password-one-uppercase"),P=_.data("password-one-lowercase"),M=_.data("password-one-number"),N=_.data("password-one-special"),R=S.length>0&&(F.length>0||T.length>0||A.length>0);eael.getToken();var U=function(t,o){t.push({name:"eael-".concat(o,"-submit"),value:!0}),(t=t.map((function(e){return"eael-login-nonce"!==e.name&&"eael-register-nonce"!==e.name&&"eael-lostpassword-nonce"!==e.name&&"eael-resetpassword-nonce"!==e.name||(e.value=localize.nonce),e}))).push(j),f&&n?grecaptcha.ready((function(){grecaptcha.execute(m,{action:"eael_login_register_form"}).then((function(n){0===a('form input[name="g-recaptcha-response"]',e).length?a("form",e).append('<input type="hidden" name="g-recaptcha-response" value="'+n+'">'):a('form input[name="g-recaptcha-response"]',e).val(n);var i={name:"g-recaptcha-response",value:n};t.push(i),D(t,o)}))})):D(t,o)};function D(e,o){a.ajax({url:localize.ajaxurl,type:"POST",dataType:"json",data:e,beforeSend:function(){t.find(".eael-lr-form-loader").show()},success:function(e){var a,t=e&&e.success,i=L.includes(o),r="lostpassword"===o,s="resetpassword"===o;if(t)a='<div class="eael-form-msg valid">'.concat(e.data.message,"</div>"),l.trigger("reset"),y.trigger("reset"),d.trigger("reset");else{if(f&&!n)try{grecaptcha.reset(0),grecaptcha.reset(1)}catch(e){}a='<div class="eael-form-msg invalid">'.concat(e.data,"</div>")}i?(t||l.find("#eael-login-submit").prop("disabled",!1),l.find(".eael-form-validation-container").html(a)):r?(t||d.find("#eael-lostpassword-submit").prop("disabled",!1),d.find(".eael-form-validation-container").html(a)):s?(t?(c.find(".eael-lr-form-group").css("display","none"),c.find("#eael-resetpassword-submit").css("display","none")):c.find("#eael-resetpassword-submit").prop("disabled",!1),c.find(".eael-form-validation-container").html(a)):(y.find("#eael-register-submit").prop("disabled",!1),y.find(".eael-form-validation-container").html(a)),t&&(e.data.redirect_to?setTimeout((function(){return window.location=e.data.redirect_to}),500):i&&setTimeout((function(){return location.reload()}),1e3))},error:function(e,a){var n='\n                    <p class="eael-form-msg invalid">\n                    Error occurred: '.concat(a.toString()," \n                    </p>\n                    ");"login"===o?(l.find("#eael-login-submit").prop("disabled",!1),l.find(".eael-form-validation-container").html(n)):"lostpassword"===o?(d.find("#eael-lostpassword-submit").prop("disabled",!1),d.find(".eael-form-validation-container").html(n)):"resetpassword"===o?(c.find("#eael-resetpassword-submit").prop("disabled",!1),c.find(".eael-form-validation-container").html(n)):(y.find("#eael-register-submit").prop("disabled",!1),y.find(".eael-form-validation-container").html(n))},complete:function(){t.find(".eael-lr-form-loader").hide()}})}"yes"===o&&(l.unbind().on("submit",(function(e){l.find("#eael-login-submit").prop("disabled",!0);var n=a(this).serializeArray();return n.filter((function(e,a){"eael-login-nonce"!=n[a].name||(n[a].value=localize.eael_login_nonce)})),U(n,"login"),!1})),y.unbind().on("submit",(function(e){y.find("#eael-register-submit").prop("disabled",!0);var n=a(this).serializeArray();return n.filter((function(e,a){"eael-register-nonce"==n[a].name&&(n[a].value=localize.eael_register_nonce)})),U(n,"register"),!1})),d.on("submit",(function(e){d.find("#eael-lostpassword-submit").prop("disabled",!0);var n=a(this).serializeArray();return n.filter((function(e,a){"eael-lostpassword-nonce"==n[a].name&&(n[a].value=localize.eael_lostpassword_nonce)})),U(n,"lostpassword"),!1})),c.on("submit",(function(e){c.find("#eael-resetpassword-submit").prop("disabled",!0);var n=a(this).serializeArray();return n.filter((function(e,a){"eael-resetpassword-nonce"==n[a].name&&(n[a].value=localize.eael_resetpassword_nonce)})),U(n,"resetpassword"),!1})));if(v.length||B.length){var G=v.data("g-client-id"),V=a(".eael-login-form-wrapper",e).is(":visible")?"login":"register",Z=v.data("type"),$=v.data("theme"),q=v.data("size"),H=v.data("text"),J=v.data("shape"),K=v.data("logo_alignment"),Q=v.data("width"),W=v.data("locale"),X=!1,Y=!1,ee=function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"login";google.accounts.id.renderButton(document.getElementById(e),{type:Z,theme:$,size:q,text:"register"===a?"signup_with":H,shape:J,logo_alignment:K,width:Q,locale:W})};"undefined"!=typeof google&&null!==google?(google.accounts.id.initialize({client_id:G,callback:function(e){var a=e.credential,n=[{name:"widget_id",value:i},{name:"page_id",value:r},{name:"redirect_to",value:s},{name:"id_token",value:a},{name:"nonce",value:l.find("#eael-login-nonce").val()}];U(n,"google")}}),B.length&&"register"===V?(ee(k,"register"),a("#eael-lr-login-toggle").on("click",(function(){X||(ee(h),X=!0)}))):B.length&&"login"===V?(ee(h),a("#eael-lr-reg-toggle").on("click",(function(){Y||(ee(k,"register"),Y=!0)}))):ee(h)):console.log("google not defined or loaded")}var ae,ne,te,oe,ie;if(w.length&&!isEditMode||z.length&&!isEditMode){var re=function(){FB.api("/me",{fields:"id, name, email"},(function(e){window.isLoggedInByFB=!0;var a=[{name:"widget_id",value:i},{name:"redirect_to",value:s},{name:"email",value:e.email},{name:"full_name",value:e.name},{name:"user_id",value:e.id},{name:"access_token",value:FB.getAuthResponse().accessToken},{name:"nonce",value:l.find("#eael-login-nonce").val()}];U(a,"facebook")}))},se=w.data("fb-appid");window.fbAsyncInit=function(){FB.init({appId:se,cookie:!0,xfbml:!0,version:"v8.0"}),FB.AppEvents.logPageView()},ae=document,ne="script",te="facebook-jssdk",ie=ae.getElementsByTagName(ne)[0],ae.getElementById(te)||((oe=ae.createElement(ne)).id=te,oe.src="https://connect.facebook.net/en_US/sdk.js",ie.parentNode.insertBefore(oe,ie)),w.on("click",(function(){isLoggedInByFB||FB.login((function(e){"connected"===e.status?re():console.log("The person is not logged into our webpage or facebook is unable to tell.")}),{scope:"public_profile,email"})})),z.on("click",(function(){isLoggedInByFB||FB.login((function(e){"connected"===e.status?re():console.log("The person is not logged into our webpage or facebook is unable to tell.")}),{scope:"public_profile,email"})}))}if(R){var le=function(){var e,a=S.val();if(a&&(e=wp.passwordStrength.meter(a,wp.passwordStrength.userInputDisallowedList(),a)),void 0!==I&&"0"===I){e=2;var n=!E||a.length>=E,t=!O||a.match(/[A-Z]/),o=!P||a.match(/[a-z]/),i=!M||a.match(/\d/),r=!N||a.match(/[!@#$%^&*-]/);n&&t&&o&&i&&r?e=4:n&&(e=3)}!function(e,a){if("yes"===C.show_ps_meter)if(a){T.show(400);var n=0===e?1:e;T.val(n)}else T.hide(300)}(e,a),function(e,a){if("yes"===C.show_pass_strength)if(a){F.show(400);var n="",t="custom"===C.ps_text_type,o="short bad mismatch good strong";switch(e){case-1:break;case 2:n=t?C.ps_text_bad:pwsL10n.bad,F.html(n).removeClass(o).addClass("bad");break;case 3:n=t?C.ps_text_good:pwsL10n.good,F.html(n).removeClass(o).addClass("good");break;case 4:n=t?C.ps_text_strong:pwsL10n.strong,F.html(n).removeClass(o).addClass("strong");break;case 5:F.html(pwsL10n.mismatch).removeClass(o).addClass("mismatch");break;default:n=t?C.ps_text_short:pwsL10n.short,F.html(n).removeClass(o).addClass("short")}}else F.hide(300)}(e,a),function(e){e>=3?A.hide(300):A.show(400)}(e)};S.on("keyup",(function(e){le()}))}}))}))}});