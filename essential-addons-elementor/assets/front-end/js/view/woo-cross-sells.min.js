!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=38)}({38:function(e,t){jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("eaelWooCrossSells"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-woo-cross-sells.default",(function(e,t){if(e.find(".eael-cs-products-container.style-1").length)t(document).ajaxComplete((function(n,r,o){if("/?wc-ajax=add_to_cart"===o.url){var a=e.find(".ajax_add_to_cart.added");a.length&&a.each((function(){t(this).next().length<1&&t(this).closest(".eael-cs-purchasable").removeClass("eael-cs-purchasable")}))}}));else if(e.find(".eael-cs-products-container.style-2.eael-custom-image-area").length){var n=0,r=0;t(".eael-cs-product-info",e).each((function(){var e=parseInt(t(this).css("height"));n=n<e?e:n})),t(".eael-cs-single-product",e).each((function(){var e=parseInt(t(this).css("height"));r=r<e?e:r})),t(".eael-cs-products-container.style-2 .eael-cs-product-image",e).css("max-height","calc(100% - ".concat(n,"px)")),t(".eael-cs-products-container.style-2 .eael-cs-single-product",e).css("height","".concat(r,"px"))}}))}))}});