!function(e){var t={};function a(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,a),o.l=!0,o.exports}a.m=e,a.c=t,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(n,o,function(t){return e[t]}.bind(null,o));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=40)}({40:function(e,t){function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=a(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),eael.hooks.addAction("advancedDataTable.afterInitPanel","ea",this.afterPanelInit),eael.hooks.addAction("advancedDataTable.panelAction","ea",this.panelAction)},(t=[{key:"afterPanelInit",value:function(e,t,a){setTimeout((function(){var a=e.el.querySelector('[data-setting="ea_adv_data_table_source_remote_table"]');null!=a&&0==a.length&&t.attributes.settings.attributes.ea_adv_data_table_source_remote_tables.forEach((function(e,n){a[n]=new Option(e,e,!1,e==t.attributes.settings.attributes.ea_adv_data_table_source_remote_table)}))}),50),e.el.addEventListener("mousedown",(function(a){(a.target.classList.contains("elementor-section-title")||a.target.parentNode.classList.contains("elementor-panel-navigation-tab"))&&setTimeout((function(){var a=e.el.querySelector('[data-setting="ea_adv_data_table_source_remote_table"]');null!=a&&0==a.length&&t.attributes.settings.attributes.ea_adv_data_table_source_remote_tables.forEach((function(e,n){a[n]=new Option(e,e,!1,e==t.attributes.settings.attributes.ea_adv_data_table_source_remote_table)}))}),50)}))}},{key:"panelAction",value:function(e,t,a,n){if("ea:advTable:connect"==n.target.dataset.event){var o=n.target;o.innerHTML="Connecting",jQuery.ajax({url:localize.ajaxurl,type:"post",data:{action:"connect_remote_db",security:localize.nonce,host:t.attributes.settings.attributes.ea_adv_data_table_source_remote_host,username:t.attributes.settings.attributes.ea_adv_data_table_source_remote_username,password:t.attributes.settings.attributes.ea_adv_data_table_source_remote_password,database:t.attributes.settings.attributes.ea_adv_data_table_source_remote_database},success:function(t){if(1==t.connected){o.innerHTML="Connected",eael.hooks.doAction("advancedDataTable.updateFromView",a,{ea_adv_data_table_source_remote_connected:!0,ea_adv_data_table_source_remote_tables:t.tables},!0),e.content.el.querySelector(".elementor-section-title").click(),e.content.el.querySelector(".elementor-section-title").click();var n=e.el.querySelector('[data-setting="ea_adv_data_table_source_remote_table"]');n.length=0,t.tables.forEach((function(e,t){n[t]=new Option(e,e)}))}else o.innerHTML="Failed"},error:function(){o.innerHTML="Failed"}}),setTimeout((function(){o.innerHTML="Connect"}),2e3)}else"ea:advTable:disconnect"==n.target.dataset.event&&(eael.hooks.doAction("advancedDataTable.updateFromView",a,{ea_adv_data_table_source_remote_connected:!1,ea_adv_data_table_source_remote_tables:[]},!0),e.content.el.querySelector(".elementor-section-title").click(),e.content.el.querySelector(".elementor-section-title").click())}}])&&n(e.prototype,t),a&&n(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,a}();eael.hooks.addAction("editMode.init","ea",(function(){new r}))}});