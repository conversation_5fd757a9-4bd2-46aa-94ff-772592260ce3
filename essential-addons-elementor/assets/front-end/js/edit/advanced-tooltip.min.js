!function(t){var e={};function o(i){if(e[i])return e[i].exports;var n=e[i]={i:i,l:!1,exports:{}};return t[i].call(n.exports,n,n.exports,o),n.l=!0,n.exports}o.m=t,o.c=e,o.d=function(t,e,i){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(o.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)o.d(i,n,function(e){return t[e]}.bind(null,n));return i},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=41)}({41:function(t,e){var o=function(t,e){var o=t,i=o.data("id"),n=elementorFrontend.isEditMode();if(n){var a,l={},r={};if(!window.elementor.hasOwnProperty("elements"))return!1;if(!(a=window.elementor.elements).models)return!1;var s=function(t){return r.switch=t.eael_tooltip_section_enable,r.content=t.eael_tooltip_section_content,r.position=t.eael_tooltip_section_position,r.animation=t.eael_tooltip_section_animation,r.arrow=t.eael_tooltip_section_arrow,r.arrowType=t.eael_tooltip_section_arrow_type,r.duration=t.eael_tooltip_section_duration,r.delay=t.eael_tooltip_section_delay,r.size=t.eael_tooltip_section_size,r.trigger=t.eael_tooltip_section_trigger,r.flip=t.eael_tooltip_auto_flip,r.distance=t.eael_tooltip_section_distance,r.maxWidth=t.eael_tooltip_section_width,r};e.each(a.models,(function(t,a){function c(){o.attr("id","eael-section-tooltip-"+i);var t="#"+o.attr("id");tippy(t,{content:r.content,placement:r.position,animation:r.animation,arrow:r.arrow,arrowType:r.arrowType,duration:r.duration,distance:r.distance,delay:r.content,size:r.size,trigger:r.trigger,flip:"yes"===r.flip,flipBehavior:"yes"===r.flip?"flip":[],animateFill:!1,flipOnUpdate:!0,interactive:!0,maxWidth:r.maxWidth,zIndex:99999,onShow:function(t){var o;if(r.content=(o=l.eael_tooltip_section_content).search(/(<script>|<script type="text\/javascript">).*(<\/script>)/g)>0?o.replace(/[&<>"']/g,(function(t){return"&#"+t.charCodeAt(0)+";"})):o,r.position=l.eael_tooltip_section_position,r.animation=l.eael_tooltip_section_animation,r.arrow=l.eael_tooltip_section_arrow,r.arrowType=l.eael_tooltip_section_arrow_type,r.duration=l.eael_tooltip_section_duration,r.delay=l.eael_tooltip_section_delay,r.size=l.eael_tooltip_section_size,r.trigger=l.eael_tooltip_section_trigger,r.flip=l.eael_tooltip_auto_flip,r.distance=l.eael_tooltip_section_distance,r.maxWidth=l.eael_tooltip_section_width,r.switch=l.eael_tooltip_section_enable,"yes"!==r.switch)t.destroy();else{t.set({content:r.content,placement:r.position,animation:r.animation,arrow:r.arrow,arrowType:r.arrowType,duration:r.duration,distance:r.distance,delay:r.delay,size:r.size,trigger:r.trigger,flip:"yes"===r.flip,flipBehavior:"yes"===r.flip?"flip":[],maxWidth:r.maxWidth});var n=t.popper;e(n).attr("data-tippy-popper-id",i)}}})}a.id===o.closest(".elementor-top-section").data("id")&&e.each(a.attributes.elements.models,(function(t,a){e.each(a.attributes.elements.models,(function(t,a){e.each(a.attributes.elements.models,(function(t,a){e.each(a.attributes.elements.models,(function(t,e){return i===e.id&&(l=e.attributes.settings.attributes,"yes"===(r=s(l)).switch?(o.addClass("eael-section-tooltip"),c()):o.removeClass("eael-section-tooltip"),0!==r.length)?r:!(!n||!r)&&void 0}))}))}))})),a.id!==o.closest(".e-container").data("id")&&i!==(null==o?void 0:o[0].getAttribute("data-id"))||e.each(a.attributes.elements.models,(function(t,e){return i===e.id&&(l=e.attributes.settings.attributes,"yes"===(r=s(l)).switch?(o.addClass("eael-section-tooltip"),c()):o.removeClass("eael-section-tooltip"),0!==r.length)?r:!(!n||!r)&&void 0})),e.each(a.attributes.elements.models,(function(t,a){e.each(a.attributes.elements.models,(function(t,e){return i==e.id&&(l=e.attributes.settings.attributes,r.switch=l.eael_tooltip_section_enable,r.content=l.eael_tooltip_section_content,r.position=l.eael_tooltip_section_position,r.animation=l.eael_tooltip_section_animation,r.arrow=l.eael_tooltip_section_arrow,r.arrowType=l.eael_tooltip_section_arrow_type,r.duration=l.eael_tooltip_section_duration,r.delay=l.eael_tooltip_section_delay,r.size=l.eael_tooltip_section_size,r.trigger=l.eael_tooltip_section_trigger,r.flip=l.eael_tooltip_auto_flip,r.distance=l.eael_tooltip_section_distance,r.maxWidth=l.eael_tooltip_section_width,"yes"==r.switch?(o.addClass("eael-section-tooltip"),c()):o.removeClass("eael-section-tooltip"),0!==r.length)?r:!(!n||!r)&&void 0}))}))}))}};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/widget",o)}))}});