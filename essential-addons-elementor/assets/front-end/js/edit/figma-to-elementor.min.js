!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=42)}({42:function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function c(n,r,a,i){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return o(s,"_invoke",function(n,r,o){var a,i,c,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return a=t,i=0,c=e,p.n=n,u}};function d(n,r){for(i=n,c=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,a=s[t],d=p.p,m=a[2];n>3?(o=m===r)&&(c=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=e):a[0]<=d&&((o=n<2&&d<a[1])?(i=0,p.v=r,p.n=a[1]):d<m&&(o=n<3||a[0]>r||r>m)&&(a[4]=n,a[5]=r,p.n=m,i=0))}if(o||n>1)return u;throw f=!0,r}return function(o,s,m){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,m),i=s,c=m;(t=i<2?e:c)||!f;){a||(i?i<3?(i>1&&(p.n=-1),d(i,c)):p.n=c:p.v=c);try{if(l=2,a){if(i||(o="next"),t=a[o]){if(!(t=t.call(a,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,i<2&&(i=0)}else 1===i&&(t=a.return)&&t.call(a),i<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=e}else if((t=(f=p.n<0)?c:n.call(r,p))!==u)break}catch(t){a=e,i=1,c=t}finally{l=1}}return{value:t,done:f}}}(n,a,i),!0),s}var u={};function l(){}function s(){}function f(){}t=Object.getPrototypeOf;var p=[][a]?t(t([][a]())):(o(t={},a,(function(){return this})),t),d=f.prototype=l.prototype=Object.create(p);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,o(e,i,"GeneratorFunction")),e.prototype=Object.create(d),e}return s.prototype=f,o(d,"constructor",f),o(f,"constructor",s),s.displayName="GeneratorFunction",o(f,i,"GeneratorFunction"),o(d),o(d,i,"Generator"),o(d,a,(function(){return this})),o(d,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:c,m:m}})()}function o(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}(o=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){o(e,t,(function(e){return this._invoke(t,n,e)}))};i("next",0),i("throw",1),i("return",2)}})(e,t,n,r)}function a(e,t,n,r,o,a,i){try{var c=e[a](i),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function i(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function c(e){a(i,r,o,c,u,"next",e)}function u(e){a(i,r,o,c,u,"throw",e)}c(void 0)}))}}function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,l(r.key),r)}}function u(e,t,n){return(t=l(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}var s=function(){return e=function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),u(this,"uploadFigmaImages",function(){var e=i(r().m((function e(t){var n,o,a;return r().w((function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,fetch(localize.ajaxurl,{method:"POST",body:new URLSearchParams({action:"eael_upload_figma_images",nonce:localize.nonce,figma_urls:JSON.stringify(t)})});case 1:if((n=e.v).ok){e.n=2;break}throw new Error("Network response was not ok");case 2:return e.n=3,n.json();case 3:if((o=e.v).success){e.n=4;break}throw new Error(o.data||"Failed to upload images");case 4:return e.a(2,o.data.images);case 5:return e.p=5,a=e.v,console.error("Error uploading images:",a),e.a(2,null)}}),e,null,[[0,5]])})));return function(t){return e.apply(this,arguments)}}()),u(this,"insertElementorTemplate",function(){var e=i(r().m((function e(o,a){var i,c,u,l,s,f,p,d,m,v,y;return r().w((function(e){for(;;)switch(e.n){case 0:if(0!==a.content.length){e.n=1;break}return e.a(2,!1);case 1:if(!((i=t.findUrlsInContent(a.content)).length>0)){e.n=3;break}return e.n=2,t.uploadFigmaImages(i);case 2:(c=e.v)&&(Object.keys(c),u=function(e){if(e&&"object"===n(e)){for(var t in e)if(t.toLowerCase().includes("url")&&"string"==typeof e[t]&&e[t].startsWith("http")){var r=c[e[t]];r&&(e[t]=r.url)}for(var o in e)Array.isArray(e[o])?e[o].forEach((function(e){return u(e)})):"object"===n(e[o])&&u(e[o])}},a.content.forEach((function(e){return u(e)})));case 3:if(void 0===(l=window.elementor)){e.n=4;break}if(s=window.TemplatelyIndex,void 0!==(f=parent.window.$e)){for(p=0;p<a.content.length;p++)f.run("document/elements/create",{container:l.getPreviewContainer(),model:a.content[p],options:s>=0?{at:s++}:{}});try{m=l.getPreviewView().getContainer(),v=m.findChildrenRecursive((function(e){return e.id===o})),y=null!=v&&null!==(d=v.parent)&&void 0!==d&&d.children&&1===v.parent.children.length?v.parent:v,v&&f.run("document/elements/delete",{container:y})}catch(e){console.log("catch"),console.error("Error deleting element:",e)}}return e.a(2,!0);case 4:return e.a(2,!1)}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),u(this,"findUrlsInContent",(function(e){var t=[],r=function(e){if(e&&"object"===n(e)){for(var o in e)o.toLowerCase().includes("url")&&"string"==typeof e[o]&&e[o].startsWith("http")&&t.push(e[o]);for(var a in e)Array.isArray(e[a])?e[a].forEach((function(e){return r(e)})):"object"===n(e[a])&&r(e[a])}};return Array.isArray(e)?e.forEach((function(e){return r(e)})):r(e),t})),elementor.hooks.addAction("panel/open_editor/widget/eael-figma-to-elementor",this.initPanel.bind(this))},(t=[{key:"initPanel",value:(a=i(r().m((function e(t,n,o){var a,c=this;return r().w((function(e){for(;;)switch(e.n){case 0:a=o.container.args.id,t.content.el.onclick=function(){var e=i(r().m((function e(o){var u,l,s,f,p,d,m,v;return r().w((function(e){for(;;)switch(e.p=e.n){case 0:if(null!==(u=o.target.dataset.event)&&void 0!==u&&u.startsWith("eael:figmajson")){e.n=1;break}return e.a(2);case 1:if(o.target.innerHTML="Importing...",o.target.disabled=!0,l=null,e.p=2,"eael:figmajson:import"!==o.target.dataset.event){e.n=3;break}s=t.content.el.querySelector(".eael_figma_to_elementor_json").value,l=JSON.parse(s),e.n=9;break;case 3:if("eael:figmajsonfile:import"!==o.target.dataset.event){e.n=9;break}if(p=null==n||null===(f=n.attributes)||void 0===f||null===(f=f.settings)||void 0===f||null===(f=f.attributes)||void 0===f||null===(f=f.eael_figma_to_elementor_file)||void 0===f?void 0:f.id){e.n=4;break}throw new Error("Please upload a JSON file first.");case 4:return e.n=5,fetch(localize.ajaxurl,{method:"POST",body:new URLSearchParams({action:"eael_get_figma_file_content",nonce:localize.nonce,file_id:p})});case 5:if((d=e.v).ok){e.n=6;break}throw new Error("Network response was not ok");case 6:return e.n=7,d.json();case 7:if((m=e.v).success){e.n=8;break}throw new Error(m.data||"Failed to load file content");case 8:l=JSON.parse(m.data);case 9:if(l&&a){e.n=10;break}throw new Error("Something went wrong! Please reload the page and try again.");case 10:setTimeout(i(r().m((function e(){return r().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,c.insertElementorTemplate(a,l);case 1:if(!e.v){e.n=2;break}elementor.notifications.showToast({message:"Figma content imported successfully!",type:"success"}),e.n=3;break;case 2:throw new Error("Failed to import Figma content. Please try again.");case 3:return e.a(2)}}),e)}))),10),e.n=12;break;case 11:e.p=11,v=e.v,elementor.notifications.showToast({message:v.message,type:"error",sticky:v.message.includes("reload the page")}),o.target.innerHTML="Import",o.target.disabled=!1;case 12:return e.a(2)}}),e,null,[[2,11]])})));return function(t){return e.apply(this,arguments)}}();case 1:return e.a(2)}}),e)}))),function(e,t,n){return a.apply(this,arguments)})}])&&c(e.prototype,t),o&&c(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,o,a}();eael.hooks.addAction("editMode.init","ea",(function(){new s}))}});