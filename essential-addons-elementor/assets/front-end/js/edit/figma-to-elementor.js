/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./src/js/edit/figma-to-elementor.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./src/js/edit/figma-to-elementor.js":
/*!*******************************************!*\
  !*** ./src/js/edit/figma-to-elementor.js ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i[\"return\"]) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2); } }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar figmaToElementor = /*#__PURE__*/function () {\n  function figmaToElementor() {\n    var _this = this;\n    _classCallCheck(this, figmaToElementor);\n    _defineProperty(this, \"uploadFigmaImages\", /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(figmaUrls) {\n        var response, data, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.p = _context.n) {\n            case 0:\n              _context.p = 0;\n              _context.n = 1;\n              return fetch(localize.ajaxurl, {\n                method: 'POST',\n                body: new URLSearchParams({\n                  action: 'eael_upload_figma_images',\n                  nonce: localize.nonce,\n                  figma_urls: JSON.stringify(figmaUrls)\n                })\n              });\n            case 1:\n              response = _context.v;\n              if (response.ok) {\n                _context.n = 2;\n                break;\n              }\n              throw new Error('Network response was not ok');\n            case 2:\n              _context.n = 3;\n              return response.json();\n            case 3:\n              data = _context.v;\n              if (data.success) {\n                _context.n = 4;\n                break;\n              }\n              throw new Error(data.data || 'Failed to upload images');\n            case 4:\n              return _context.a(2, data.data.images);\n            case 5:\n              _context.p = 5;\n              _t = _context.v;\n              console.error('Error uploading images:', _t);\n              return _context.a(2, null);\n          }\n        }, _callee, null, [[0, 5]]);\n      }));\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    _defineProperty(this, \"insertElementorTemplate\", /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(elementId, response) {\n        var figmaUrls, uploadedImages, uploadedImageKeys, _replaceUrls, templatelyElementor, insertIndex, $e, i, _targetElement$parent, container, targetElement, deleteElement;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              if (!(response.content.length === 0)) {\n                _context2.n = 1;\n                break;\n              }\n              return _context2.a(2, false);\n            case 1:\n              figmaUrls = _this.findUrlsInContent(response.content);\n              if (!(figmaUrls.length > 0)) {\n                _context2.n = 3;\n                break;\n              }\n              _context2.n = 2;\n              return _this.uploadFigmaImages(figmaUrls);\n            case 2:\n              uploadedImages = _context2.v;\n              // Replace Figma URLs with uploaded URLs in the content and add attachment IDs\n              if (uploadedImages) {\n                uploadedImageKeys = Object.keys(uploadedImages);\n                _replaceUrls = function replaceUrls(obj) {\n                  if (!obj || _typeof(obj) !== 'object') return;\n                  for (var key in obj) {\n                    if (key.toLowerCase().includes('url') && typeof obj[key] === 'string' && obj[key].startsWith('http')) {\n                      var uploadedImage = uploadedImages[obj[key]];\n                      if (uploadedImage) {\n                        // Replace the URL\n                        obj[key] = uploadedImage.url;\n\n                        // Add attachment ID if the key contains 'image'\n                        // if (key.toLowerCase().includes('image')) {\n                        // \tconst idKey = key.replace('url', 'id');\n                        // \tobj[idKey] = uploadedImage.id;\n                        // }\n                      }\n                    }\n                  }\n                  for (var _key in obj) {\n                    if (Array.isArray(obj[_key])) {\n                      obj[_key].forEach(function (item) {\n                        return _replaceUrls(item);\n                      });\n                    } else if (_typeof(obj[_key]) === 'object') {\n                      _replaceUrls(obj[_key]);\n                    }\n                  }\n                };\n                response.content.forEach(function (item) {\n                  return _replaceUrls(item);\n                });\n              }\n            case 3:\n              templatelyElementor = window.elementor;\n              if (!(typeof templatelyElementor !== 'undefined')) {\n                _context2.n = 4;\n                break;\n              }\n              insertIndex = window.TemplatelyIndex;\n              $e = parent.window.$e;\n              if (typeof $e !== 'undefined') {\n                for (i = 0; i < response.content.length; i++) {\n                  $e.run('document/elements/create', {\n                    container: templatelyElementor.getPreviewContainer(),\n                    model: response.content[i],\n                    options: insertIndex >= 0 ? {\n                      at: insertIndex++\n                    } : {}\n                  });\n                }\n\n                // Delete the figma widget after import\n                try {\n                  container = templatelyElementor.getPreviewView().getContainer();\n                  targetElement = container.findChildrenRecursive(function (child) {\n                    return child.id === elementId;\n                  });\n                  deleteElement = targetElement !== null && targetElement !== void 0 && (_targetElement$parent = targetElement.parent) !== null && _targetElement$parent !== void 0 && _targetElement$parent.children && targetElement.parent.children.length === 1 ? targetElement.parent : targetElement;\n                  if (targetElement) {\n                    $e.run('document/elements/delete', {\n                      container: deleteElement\n                    });\n                  }\n                } catch (error) {\n                  console.log('catch');\n                  console.error('Error deleting element:', error);\n                }\n\n                // $e.run('document/elements/settings', {\n                // \tcontainer: templatelyElementor.getPreviewContainer(),\n                // \tsettings: {\n                // \t\ttemplate: response.page_settings.template,\n                // \t},\n                // });\n              }\n              return _context2.a(2, true);\n            case 4:\n              return _context2.a(2, false);\n          }\n        }, _callee2);\n      }));\n      return function (_x2, _x3) {\n        return _ref2.apply(this, arguments);\n      };\n    }());\n    _defineProperty(this, \"findUrlsInContent\", function (content) {\n      var urls = [];\n      var _traverse = function traverse(obj) {\n        if (!obj || _typeof(obj) !== 'object') return;\n\n        // Check all properties for keys containing 'url'\n        for (var key in obj) {\n          if (key.toLowerCase().includes('url') && typeof obj[key] === 'string' && obj[key].startsWith('http')) {\n            urls.push(obj[key]);\n          }\n        }\n\n        // Recursively traverse through all properties\n        for (var _key2 in obj) {\n          if (Array.isArray(obj[_key2])) {\n            obj[_key2].forEach(function (item) {\n              return _traverse(item);\n            });\n          } else if (_typeof(obj[_key2]) === 'object') {\n            _traverse(obj[_key2]);\n          }\n        }\n      };\n\n      // Handle array of objects\n      if (Array.isArray(content)) {\n        content.forEach(function (item) {\n          return _traverse(item);\n        });\n      } else {\n        _traverse(content);\n      }\n      return urls;\n    });\n    elementor.hooks.addAction(\"panel/open_editor/widget/eael-figma-to-elementor\", this.initPanel.bind(this));\n  }\n  return _createClass(figmaToElementor, [{\n    key: \"initPanel\",\n    value: function () {\n      var _initPanel = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5(panel, model, view) {\n        var _this2 = this;\n        var elementId;\n        return _regenerator().w(function (_context5) {\n          while (1) switch (_context5.n) {\n            case 0:\n              elementId = view.container.args.id;\n              panel.content.el.onclick = /*#__PURE__*/function () {\n                var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(event) {\n                  var _event$target$dataset;\n                  var jsonData, jsonString, _model$attributes, fileId, response, data, _t2;\n                  return _regenerator().w(function (_context4) {\n                    while (1) switch (_context4.p = _context4.n) {\n                      case 0:\n                        if ((_event$target$dataset = event.target.dataset.event) !== null && _event$target$dataset !== void 0 && _event$target$dataset.startsWith('eael:figmajson')) {\n                          _context4.n = 1;\n                          break;\n                        }\n                        return _context4.a(2);\n                      case 1:\n                        event.target.innerHTML = 'Importing...';\n                        event.target.disabled = true;\n                        jsonData = null;\n                        _context4.p = 2;\n                        if (!(event.target.dataset.event === \"eael:figmajson:import\")) {\n                          _context4.n = 3;\n                          break;\n                        }\n                        jsonString = panel.content.el.querySelector(\".eael_figma_to_elementor_json\").value;\n                        jsonData = JSON.parse(jsonString);\n                        _context4.n = 9;\n                        break;\n                      case 3:\n                        if (!(event.target.dataset.event === \"eael:figmajsonfile:import\")) {\n                          _context4.n = 9;\n                          break;\n                        }\n                        fileId = model === null || model === void 0 || (_model$attributes = model.attributes) === null || _model$attributes === void 0 || (_model$attributes = _model$attributes.settings) === null || _model$attributes === void 0 || (_model$attributes = _model$attributes.attributes) === null || _model$attributes === void 0 || (_model$attributes = _model$attributes.eael_figma_to_elementor_file) === null || _model$attributes === void 0 ? void 0 : _model$attributes.id;\n                        if (fileId) {\n                          _context4.n = 4;\n                          break;\n                        }\n                        throw new Error('Please upload a JSON file first.');\n                      case 4:\n                        _context4.n = 5;\n                        return fetch(localize.ajaxurl, {\n                          method: 'POST',\n                          body: new URLSearchParams({\n                            action: 'eael_get_figma_file_content',\n                            nonce: localize.nonce,\n                            file_id: fileId\n                          })\n                        });\n                      case 5:\n                        response = _context4.v;\n                        if (response.ok) {\n                          _context4.n = 6;\n                          break;\n                        }\n                        throw new Error('Network response was not ok');\n                      case 6:\n                        _context4.n = 7;\n                        return response.json();\n                      case 7:\n                        data = _context4.v;\n                        if (data.success) {\n                          _context4.n = 8;\n                          break;\n                        }\n                        throw new Error(data.data || 'Failed to load file content');\n                      case 8:\n                        jsonData = JSON.parse(data.data);\n                      case 9:\n                        if (!(!jsonData || !elementId)) {\n                          _context4.n = 10;\n                          break;\n                        }\n                        throw new Error('Something went wrong! Please reload the page and try again.');\n                      case 10:\n                        setTimeout(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n                          var result;\n                          return _regenerator().w(function (_context3) {\n                            while (1) switch (_context3.n) {\n                              case 0:\n                                _context3.n = 1;\n                                return _this2.insertElementorTemplate(elementId, jsonData);\n                              case 1:\n                                result = _context3.v;\n                                if (!result) {\n                                  _context3.n = 2;\n                                  break;\n                                }\n                                elementor.notifications.showToast({\n                                  message: 'Figma content imported successfully!',\n                                  type: 'success'\n                                });\n                                _context3.n = 3;\n                                break;\n                              case 2:\n                                throw new Error('Failed to import Figma content. Please try again.');\n                              case 3:\n                                return _context3.a(2);\n                            }\n                          }, _callee3);\n                        })), 10);\n                        _context4.n = 12;\n                        break;\n                      case 11:\n                        _context4.p = 11;\n                        _t2 = _context4.v;\n                        elementor.notifications.showToast({\n                          message: _t2.message,\n                          type: 'error',\n                          sticky: _t2.message.includes('reload the page')\n                        });\n                        event.target.innerHTML = 'Import';\n                        event.target.disabled = false;\n                      case 12:\n                        return _context4.a(2);\n                    }\n                  }, _callee4, null, [[2, 11]]);\n                }));\n                return function (_x7) {\n                  return _ref3.apply(this, arguments);\n                };\n              }();\n            case 1:\n              return _context5.a(2);\n          }\n        }, _callee5);\n      }));\n      function initPanel(_x4, _x5, _x6) {\n        return _initPanel.apply(this, arguments);\n      }\n      return initPanel;\n    }()\n  }]);\n}();\neael.hooks.addAction(\"editMode.init\", \"ea\", function () {\n  new figmaToElementor();\n});\n\n//# sourceURL=webpack:///./src/js/edit/figma-to-elementor.js?");

/***/ })

/******/ });