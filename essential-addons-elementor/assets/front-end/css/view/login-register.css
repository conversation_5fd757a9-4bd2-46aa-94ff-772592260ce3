/*------------------------------*/
/* Login Register Pro Style
/*------------------------------*/
/*-----Form Specific---*/
.eael-lr-form-wrapper {
  /* Password Strength Meter */
  /* Webkit based browsers */
  /* Gecko based browsers */
}
.eael-lr-form-wrapper.lr-icon-showing .eael-lr-form-group {
  position: relative;
}
.eael-lr-form-wrapper.lr-icon-showing .eael-lr-form-control {
  padding: 15px 15px 15px 30px;
}
.eael-lr-form-wrapper.lr-icon-showing i {
  position: absolute;
  bottom: 20px;
  left: 10px;
  color: inherit;
}
.eael-lr-form-wrapper.lr-icon-showing svg {
  position: absolute;
  bottom: 20px;
  left: 10px;
  color: inherit;
  height: 1rem;
  width: 1rem;
  line-height: 1rem;
}
.eael-lr-form-wrapper.lr-icon-showing .lr-social-login-container svg {
  position: relative;
  bottom: 0;
  left: 0;
  right: 5px;
}
.eael-lr-form-wrapper .eael-lr-form-loader-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
}
.eael-lr-form-wrapper .eael-lr-form-loader-wrapper .eael-lr-form-loader {
  position: absolute;
  right: 10px;
}
.eael-lr-form-wrapper .eael-lr-form-loader-wrapper .eael-lr-form-loader svg {
  -webkit-animation: eael-lr-spin 2s linear infinite;
          animation: eael-lr-spin 2s linear infinite;
}
@-webkit-keyframes eael-lr-spin {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes eael-lr-spin {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.eael-lr-form-wrapper meter {
  /* Reset the default appearance */
  display: block;
  margin: 0 auto;
  width: 100%;
  height: 1em;
}
@supports (-moz-appearance: none) {
  .eael-lr-form-wrapper {
    /* Applicable only to Firefox */
  }
  .eael-lr-form-wrapper meter {
    -moz-appearance: none;
    height: 0.5em;
    background: none;
    background-color: rgba(0, 0, 0, 0.1);
  }
}
.eael-lr-form-wrapper meter::-webkit-meter-bar {
  background: none;
  background-color: rgba(0, 0, 0, 0.1);
}
.eael-lr-form-wrapper meter[value="0"]::-webkit-meter-optimum-value,
.eael-lr-form-wrapper meter[value="1"]::-webkit-meter-optimum-value,
.eael-lr-form-wrapper meter[value="2"]::-webkit-meter-optimum-value {
  background: #ff2828;
}
.eael-lr-form-wrapper meter[value="3"]::-webkit-meter-optimum-value {
  background: #ffa500;
}
.eael-lr-form-wrapper meter[value="4"]::-webkit-meter-optimum-value {
  background: #008000;
}
.eael-lr-form-wrapper meter[value="0"]::-moz-meter-bar,
.eael-lr-form-wrapper meter[value="1"]::-moz-meter-bar,
.eael-lr-form-wrapper meter[value="2"]::-moz-meter-bar {
  background: #ff2828;
}
.eael-lr-form-wrapper meter[value="3"]::-moz-meter-bar {
  background: #ffa500;
}
.eael-lr-form-wrapper meter[value="4"]::-moz-meter-bar {
  background: #008000;
}
.eael-lr-form-wrapper meter::-webkit-meter-optimum-value {
  -webkit-transition: width 0.4s linear;
  transition: width 0.4s linear;
}
.eael-lr-form-wrapper meter::-moz-meter-bar {
  -moz-transition: width 0.4s linear;
  transition: width 0.4s linear;
}
.eael-pass-notice.mismatch {
  color: #ff2828;
}
.eael-pass-notice.short {
  color: #ff2828;
}
.eael-pass-notice.bad {
  color: #ff2828;
}
.eael-pass-notice.good {
  color: #FFA500;
}
.eael-pass-notice.strong {
  color: #5cb85c;
}
.lr-social-login-container {
  margin-top: 1rem;
}
.lr-social-login-container .lr-separator {
  width: 100%;
  text-align: center;
}
.lr-social-login-container .lr-separator p {
  padding: 0;
  margin: 0;
}
.lr-social-login-container .lr-separator hr {
  width: 100%;
  height: 1px;
  background: gray;
}
.lr-social-login-container .lr-social-buttons-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.lr-social-login-container .eael-social-button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  max-width: 100%;
  -webkit-box-pack: space-evenly;
      -ms-flex-pack: space-evenly;
          justify-content: space-evenly;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-top: 10px;
  margin-left: auto;
  margin-right: auto;
  cursor: pointer;
}
.lr-social-login-container .eael-social-button.eael-gis iframe {
  margin: 0 !important;
}
.lr-social-login-container .eael-social-button.eael-facebook {
  width: 190px;
}
