body {
  overflow-x: hidden;
}

.eael-stacked-cards__item_hr {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}

.eael-stacked-cards {
  position: relative;
}

.eael-stacked-cards__container {
  position: relative;
  width: 100%;
  height: 100vh;
  will-change: position;
}

.eael-stacked-cards__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  -webkit-box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
}

.eael-stacked-cards__media {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 60%;
          flex: 0 0 60%;
  margin-right: 30px;
}

.eael-stacked-cards__image {
  width: 100%;
  height: auto;
  border-radius: 10px;
  -o-object-fit: cover;
     object-fit: cover;
}

.eael-stacked-cards__content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.eael-stacked-cards__body {
  padding: 10px 0;
}

.eael-stacked-cards__title {
  margin: 0 0 15px;
}

.eael-stacked-cards__link {
  font-size: 15px;
  margin-top: 20px;
  display: inline-block;
  overflow: hidden;
  position: relative;
  z-index: 1;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.eael-stacked-cards__link:hover {
  text-decoration: none;
}

@media only screen and (max-width: 767px) {
  .eael-stacked-cards__item,
  .eael-stacked-cards__item_hr {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
            flex-direction: column !important;
  }
  .eael-stacked-cards__media {
    margin-right: 0;
  }
}
