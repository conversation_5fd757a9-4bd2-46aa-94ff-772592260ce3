.eael-static-product {
  position: relative;
  overflow: hidden;
  display: block;
  margin: 0 auto;
}
.eael-static-product .eael-static-product-media {
  position: relative;
}
.eael-static-product .eael-static-product-media img {
  width: 100%;
  display: block;
}
.eael-static-product .eael-static-product-media a.eael-static-product-live-demo-btn {
  color: #fff;
}
.eael-static-product .eael-static-product-price-and-reviews {
  margin-bottom: 20px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.eael-static-product .eael-static-product-price-and-reviews .eael-static-product-price {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}
.eael-static-product .eael-static-product-price-and-reviews .eael-static-product-reviews {
  font-size: 13px;
  font-weight: 400;
  color: #485771;
}
.eael-static-product .eael-static-product-thumb-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in-out, -webkit-transform 0.25s cubic-bezier(0.19, 1, 0.22, 1);
  transition: opacity 0.2s ease-in-out, -webkit-transform 0.25s cubic-bezier(0.19, 1, 0.22, 1);
  transition: opacity 0.2s ease-in-out, transform 0.25s cubic-bezier(0.19, 1, 0.22, 1);
  transition: opacity 0.2s ease-in-out, transform 0.25s cubic-bezier(0.19, 1, 0.22, 1), -webkit-transform 0.25s cubic-bezier(0.19, 1, 0.22, 1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: rgba(0, 0, 0, 0.75);
}
.eael-static-product .eael-static-product-thumb-overlay a.eael-static-product-add-to-cart {
  padding: 5px 10px;
  line-height: 18px;
  margin: 0 0 0 5px;
  font-size: 14px;
  font-weight: 500;
}
.eael-static-product .eael-static-product-thumb-overlay:hover {
  opacity: 1;
}
.eael-static-product .eael-static-product-details {
  padding: 10px 20px 30px;
  margin: 0;
}
.eael-static-product .eael-static-product-details > h2 {
  font-size: 1.2em;
  margin: 0;
  padding: 0.5em 0;
}
.eael-static-product .eael-static-product-details > p {
  margin: 0 0 1.5em;
  font-size: 14px;
}
.eael-static-product .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-add-to-cart,
.eael-static-product .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-btn {
  padding: 10px 20px;
  font-size: 14px;
  transition: color 0.5s ease;
  -webkit-transition: color 0.5s ease;
  -moz-transition: color 0.5s ease;
}
.eael-static-product .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-add-to-cart,
.eael-static-product .eael-static-product-details .eael-static-product-btn-wrap .eael-static-product-btn {
  display: inline-block;
}
.eael-static-product .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-add-to-cart .eael-static-product-btn-inner,
.eael-static-product .eael-static-product-details .eael-static-product-btn-wrap .eael-static-product-btn .eael-static-product-btn-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.eael-static-product--style-three .eael-static-product-media .eael-static-product-thumb-overlay {
  width: auto;
  height: auto;
  background-color: rgba(255, 255, 255, 0.75);
}
.eael-static-product--style-three .eael-static-product-media .eael-static-product-thumb-overlay .eael-static-product-details {
  background: none !important;
}
.eael-static-product--style-three .eael-static-product-media .eael-static-product-thumb-overlay .eael-static-product-details .eael-static-product-btn-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.eael-static-product--style-three .eael-static-product-media .eael-static-product-thumb-overlay .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-live-demo-btn,
.eael-static-product--style-three .eael-static-product-media .eael-static-product-thumb-overlay .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-btn,
.eael-static-product--style-three .eael-static-product-media .eael-static-product-thumb-overlay .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-add-to-cart {
  padding: 5px 7px;
  margin: 3px;
  font-size: 14px;
  line-height: 21px;
  font-weight: 500;
  background-color: #646464;
  color: #fff;
}
.eael-static-product--style-three .eael-static-product-media .eael-static-product-thumb-overlay .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-live-demo-btn span,
.eael-static-product--style-three .eael-static-product-media .eael-static-product-thumb-overlay .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-btn span,
.eael-static-product--style-three .eael-static-product-media .eael-static-product-thumb-overlay .eael-static-product-details .eael-static-product-btn-wrap a.eael-static-product-add-to-cart span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
