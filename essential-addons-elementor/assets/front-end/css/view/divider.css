.eael-divider-wrap {
  font-size: 0;
  line-height: 0;
}

.eael-divider {
  text-align: center;
}

.eael-divider-left .divider-border-left {
  display: none;
}

.eael-divider-right .divider-border-right {
  display: none;
}

/*--- Horizontal ---*/

.eael-divider.horizontal {
  border: 0;
  border-color: #000;
  border-bottom-width: 4px;
  border-top-width: 0px;
  display: inline-block;
  width: 80px;
  height: 0;
  border-style: dashed;
}

/*--- Vertical ---*/

.eael-divider.vertical {
  border: 0;
  display: inline-block;
  border-left: 2px solid #000;
  height: 50px;
}

/*--- divider with Text ---*/

.divider-text-container {
  display: inline-block;
  max-width: 100%;
}

.divider-text-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 auto;
}

.eael-divider-wrap.divider-direction-vertical .divider-text-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.eael-divider-wrap.divider-direction-vertical .divider-border {
  border: 1px solid;
}

.eael-divider-wrap.divider-direction-vertical .divider-border-left {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
}

.eael-divider-wrap.divider-direction-vertical .eael-divider-content {
  -webkit-box-ordinal-group: 3;
      -ms-flex-order: 2;
          order: 2;
}

.eael-divider-wrap.divider-direction-vertical .divider-border-right {
  -webkit-box-ordinal-group: 4;
      -ms-flex-order: 3;
          order: 3;
}

.eael-divider-wrap.divider-direction-vertical .divider-text-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.eael-divider-text {
  font-size: 16px;
  line-height: 1.4;
  white-space: nowrap;
}

.divider-border-wrap {
  -webkit-box-flex: 1;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
}

.divider-border {
  border: 0;
  height: 1px;
  border-top: 1px solid #000;
  display: block;
  width: 100%;
}

.eael-divider-content {
  display: inherit;
  -webkit-box-flex: 0;
      -ms-flex: 0 1 auto;
          flex: 0 1 auto;
  margin: 0 20px;
}
