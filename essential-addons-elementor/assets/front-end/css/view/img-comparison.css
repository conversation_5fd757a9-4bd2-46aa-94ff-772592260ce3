.eael-img-comp-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  line-height: 0;
  margin: 0 auto;
  overflow: hidden;
  padding: 0;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.eael-img-comp-container * {
  -webkit-box-sizing: inherit;
          box-sizing: inherit;
}

.eael-img-comp-container::after,
.eael-img-comp-container::before {
  -webkit-box-sizing: inherit;
          box-sizing: inherit;
}

.eael-img-comp-container img, .eael-img-comp-container picture > img {
  max-width: none !important;
}

.eael-img-comp-container > img,
.eael-img-comp-container > picture > img {
  display: block;
  width: 100%;
}

.eael-img-comp-container > div:first-child,
picture .eael-img-comp-container > div {
  height: 100%;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 50%;
}

.cocoen-drag {
  background: #fff;
  bottom: 0;
  cursor: ew-resize;
  left: 50%;
  margin-left: -1px;
  position: absolute;
  top: 0;
  width: 2px;
}

.cocoen-drag::before {
  border: 3px solid #fff;
  content: "";
  height: 30px;
  left: 50%;
  margin-left: -7px;
  margin-top: -18px;
  position: absolute;
  top: 50%;
  width: 14px;
}
