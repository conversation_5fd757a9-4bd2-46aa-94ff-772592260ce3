html {
  scroll-behavior: smooth;
}

.ea-woo-checkout.layout-split .steps-buttons, .ea-woo-checkout.layout-multi-steps .steps-buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.ea-woo-checkout.layout-split .steps-buttons button:focus, .ea-woo-checkout.layout-multi-steps .steps-buttons button:focus {
  outline: none;
}

.ea-woo-checkout.layout-split .steps-buttons button.ea-woo-checkout-btn-prev, .ea-woo-checkout.layout-multi-steps .steps-buttons button.ea-woo-checkout-btn-prev {
  display: none;
}

.ea-woo-checkout.layout-split .layout-split-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-split .layout-split-container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
  }
}

.ea-woo-checkout.layout-split .layout-split-container .info-area {
  width: 65%;
  margin-right: 5%;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-split .layout-split-container .info-area {
    width: 100%;
  }
}

.ea-woo-checkout.layout-split .layout-split-container .info-area .split-tabs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  padding-left: 0;
  margin: 0 0 30px 0;
  border-radius: 5px;
  list-style: none;
  text-align: center;
}

.ea-woo-checkout.layout-split .layout-split-container .info-area .split-tabs li {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  line-height: 1.2em;
  -webkit-font-smoothing: subpixel-antialiased;
  padding: 12px;
}

.ea-woo-checkout.layout-split .layout-split-container .info-area .split-tabs li.active {
  background-color: #7866ff;
  border-radius: 4px;
  color: #FFFFFF;
  cursor: pointer;
}

.ea-woo-checkout.layout-split .layout-split-container .info-area .split-tabs li.completed {
  cursor: pointer;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-split .layout-split-container .info-area .woo-checkout-login .form-row-first {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-split .layout-split-container .info-area .woo-checkout-login .form-row-last {
    width: 100%;
  }
}

.ea-woo-checkout.layout-split .layout-split-container .info-area .woo-checkout-coupon .checkout_coupon .form-row-first {
  width: 70%;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-split .layout-split-container .info-area .woo-checkout-coupon .checkout_coupon .form-row-first {
    width: 100%;
  }
}

.ea-woo-checkout.layout-split .layout-split-container .info-area .woo-checkout-coupon .checkout_coupon .form-row-last {
  width: 28%;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-split .layout-split-container .info-area .woo-checkout-coupon .checkout_coupon .form-row-last {
    width: 100%;
  }
}

.ea-woo-checkout.layout-split .layout-split-container .table-area {
  width: 30%;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-split .layout-split-container .table-area {
    width: 100%;
  }
}

.ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table .product-quantity {
  padding: 0;
}

.ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row {
  background-color: transparent;
  border-bottom: 1px solid;
  padding-bottom: 11px;
  border-radius: 0;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row {
    width: 100%;
  }
}

.ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row .table-col-1 {
  -ms-flex-preferred-size: 80%;
      flex-basis: 80%;
  max-width: 80%;
}

.ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row .table-col-1 .product-thumbnail {
  width: 20%;
}

.ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row .table-col-1 .product-name {
  width: 80%;
}

.ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row .table-col-3.product-total {
  -ms-flex-preferred-size: 20%;
      flex-basis: 20%;
  max-width: 20%;
  padding-right: 0 !important;
}

.ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table-footer {
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
      -ms-flex-direction: column-reverse;
          flex-direction: column-reverse;
}

.ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table-footer .footer-content {
  width: 100%;
  padding: 0;
  background-color: transparent;
}

.ea-woo-checkout.layout-split .layout-split-container .table-area .ea-woo-checkout-order-review .ea-order-review-table-footer .footer-content .order-total {
  border-top: 1px solid;
  margin-top: 10px;
  padding-top: 10px;
  font-size: 1.2em;
}

.ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .col-1, .ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .col-2 {
  float: none !important;
  width: 100% !important;
  padding-left: 0;
  padding-right: 0;
}

.ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .woocommerce-billing-fields, .ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .woo-checkout-payment {
  margin-bottom: 30px;
}

.ea-woo-checkout.layout-split .woocommerce .woo-checkout-payment {
  margin-bottom: 30px;
}

.ea-woo-checkout.layout-split .woocommerce .woo-checkout-payment .woocommerce-privacy-policy-text p:last-child {
  margin-bottom: 0;
}

.ea-woo-checkout.layout-split .woocommerce .woo-checkout-payment #place_order {
  display: none !important;
}

.ea-woo-checkout.layout-split .woocommerce .woo-checkout-login, .ea-woo-checkout.layout-split .woocommerce .woo-checkout-coupon, .ea-woo-checkout.layout-split .woocommerce #customer_details, .ea-woo-checkout.layout-split .woocommerce .woo-checkout-payment {
  display: none;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container {
  /*marking active/completed steps green*/
  /*The number of the step and the connector before it = green*/
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs {
  padding: 0;
  margin: 0 0 30px 0;
  overflow: hidden;
  counter-reset: step;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs li {
  list-style-type: none;
  color: #7866ff;
  font-size: 1rem;
  line-height: 1.2em;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  position: relative;
  text-align: center;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs li:first-child:after {
  /*connector not needed before the first step*/
  content: none;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs li:before {
  content: counter(step);
  counter-increment: step;
  width: 24px;
  height: 24px;
  line-height: 26px;
  display: block;
  font-size: 12px;
  color: #fff;
  background: #7866ff;
  border-radius: 25px;
  margin: 0 auto 10px auto;
  position: relative;
  z-index: 1;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs li:after {
  content: "";
  width: 100%;
  height: 2px;
  background: black;
  position: absolute;
  left: -50%;
  top: 9px;
  z-index: 0;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs li.completed:before, .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs li.completed:after {
  background: #7866ff;
  color: white;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
  }
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .ms-tabs-content {
  width: 65%;
  margin-right: 5%;
  -webkit-box-shadow: 2px 0px 15px 5px rgba(41, 41, 93, 0.07);
          box-shadow: 2px 0px 15px 5px rgba(41, 41, 93, 0.07);
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .ms-tabs-content {
    width: 100%;
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .ms-tabs-content .woo-checkout-login .form-row-first {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .ms-tabs-content .woo-checkout-login .form-row-last {
    width: 100%;
  }
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .ms-tabs-content .woo-checkout-coupon .checkout_coupon .form-row-first {
  width: 70%;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .ms-tabs-content .woo-checkout-coupon .checkout_coupon .form-row-first {
    width: 100%;
  }
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .ms-tabs-content .woo-checkout-coupon .checkout_coupon .form-row-last {
  width: 28%;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .ms-tabs-content .woo-checkout-coupon .checkout_coupon .form-row-last {
    width: 100%;
  }
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area {
  width: 30%;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area {
    width: 100%;
  }
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review {
    margin-bottom: 30px;
  }
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table .product-quantity {
  padding: 0;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row {
  background-color: transparent;
  border-bottom: 1px solid;
  padding-bottom: 11px;
  border-radius: 0;
}

@media (max-width: 768px) {
  .ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row {
    width: 100%;
  }
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row .table-col-1 {
  -ms-flex-preferred-size: 80%;
      flex-basis: 80%;
  max-width: 80%;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row .table-col-1 .product-thumbnail {
  width: 20%;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row .table-col-1 .product-name {
  width: 80%;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table .table-row .table-col-3.product-total {
  -ms-flex-preferred-size: 20%;
      flex-basis: 20%;
  max-width: 20%;
  padding-right: 0 !important;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table-footer {
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
      -ms-flex-direction: column-reverse;
          flex-direction: column-reverse;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table-footer .footer-content {
  width: 100%;
  padding: 0;
  background-color: transparent;
}

.ea-woo-checkout.layout-multi-steps .layout-multi-steps-container .ms-tabs-content-wrap .table-area .ea-woo-checkout-order-review .ea-order-review-table-footer .footer-content .order-total {
  border-top: 1px solid;
  margin-top: 10px;
  padding-top: 10px;
  font-size: 1.2em;
}

.ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .col-1, .ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .col-2 {
  float: none !important;
  width: 100% !important;
  padding-left: 0;
  padding-right: 0;
}

.ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .woocommerce-billing-fields, .ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .woo-checkout-payment {
  margin-bottom: 30px;
}

.ea-woo-checkout.layout-multi-steps .woocommerce .woo-checkout-payment {
  margin-bottom: 30px;
}

.ea-woo-checkout.layout-multi-steps .woocommerce .woo-checkout-payment .woocommerce-privacy-policy-text p:last-child {
  margin-bottom: 0;
}

.ea-woo-checkout.layout-multi-steps .woocommerce .woo-checkout-payment #place_order {
  display: none !important;
}

.ea-woo-checkout.layout-multi-steps .woocommerce .woo-checkout-login, .ea-woo-checkout.layout-multi-steps .woocommerce .woo-checkout-coupon, .ea-woo-checkout.layout-multi-steps .woocommerce #customer_details, .ea-woo-checkout.layout-multi-steps .woocommerce .woo-checkout-payment {
  display: none;
}

.eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .col-1, .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .col-1 {
  width: 100% !important;
  float: none !important;
}

@media (min-width: 1024px) {
  .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .col-1 .woocommerce-billing-fields, .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .col-1 .woocommerce-account-fields, .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .col-1 .woocommerce-shipping-fields, .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .col-1 .woocommerce-additional-fields, .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .col-1 .woocommerce-billing-fields, .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .col-1 .woocommerce-account-fields, .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .col-1 .woocommerce-shipping-fields, .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .col-1 .woocommerce-additional-fields {
    float: none !important;
    width: 100% !important;
    padding-left: 0;
  }
}

.eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-split .woocommerce #customer_details.col2-set .col-2, .eael-woo-checkout.theme-astra:not(.elementor-editor-active) .ea-woo-checkout.layout-multi-steps .woocommerce #customer_details.col2-set .col-2 {
  clear: both;
}
