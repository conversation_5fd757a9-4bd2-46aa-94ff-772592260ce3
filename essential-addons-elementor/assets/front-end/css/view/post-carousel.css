.eael-post-carousel-wrap .eael-post-carousel.grayscale-normal img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.eael-post-carousel-wrap .eael-post-carousel.grayscale-normal .swiper-slide:hover img {
  -webkit-filter: none;
          filter: none;
}
.eael-post-carousel-wrap .eael-post-carousel.grayscale-hover .swiper-slide:hover img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.eael-post-carousel-wrap .eael-post-carousel.swiper-container .swiper-slide {
  text-align: center;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-grid-post-holder {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-entry-thumbnail > img,
.eael-post-carousel-wrap .eael-post-carousel .swiper-slide img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-entry-medianone {
  position: relative;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-entry-content {
  padding: 0 15px;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-entry-thumbnail {
  position: relative;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-entry-thumbnail.eael-image-ratio img {
  position: absolute;
  top: calc(50% + 1px);
  left: calc(50% + 1px);
  -webkit-transform: scale(1.01) translate(-50%, -50%);
      -ms-transform: scale(1.01) translate(-50%, -50%);
          transform: scale(1.01) translate(-50%, -50%);
}
.eael-post-carousel-wrap .eael-post-carousel .eael-entry-thumbnail a {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-post-carousel-title a {
  color: inherit;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-author-avatar > a {
  display: block;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-entry-footer {
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.eael-post-carousel-wrap .eael-post-carousel .eael-entry-footer > div {
  display: inline-block;
  float: left;
}
.eael-post-carousel-wrap .eael-post-carousel .post_carousel_meta_alignment-left .eael-entry-meta {
  text-align: left;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}
.eael-post-carousel-wrap .eael-post-carousel .post_carousel_meta_alignment-right .eael-entry-meta {
  text-align: right;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.eael-post-carousel-wrap .eael-post-carousel .post_carousel_meta_alignment-right .eael-entry-meta .eael-entry-footer {
  display: block;
}
.eael-post-carousel-wrap .eael-post-carousel .post_carousel_meta_alignment-right .eael-entry-meta .eael-entry-footer > div {
  float: right;
}
.eael-post-carousel-wrap .eael-post-carousel .post_carousel_meta_alignment-right .eael-entry-meta .eael-entry-footer .eael-entry-meta {
  text-align: right;
  padding-left: 0;
  padding-right: 15px;
}
.eael-post-carousel-wrap .eael-post-carousel .post_carousel_meta_alignment-center .eael-entry-meta {
  text-align: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.eael-post-carousel-wrap .eael-post-carousel .post_carousel_meta_alignment-center .eael-entry-meta .eael-entry-footer {
  margin: 0 auto 15px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.eael-post-carousel-wrap .eael-post-carousel .post_carousel_meta_alignment-center .eael-entry-meta .eael-entry-footer > div {
  float: none;
  display: block;
}
.eael-post-carousel-wrap .eael-post-carousel .show-read-more-button .eael-post-elements-readmore-btn {
  display: inline-block;
}
.eael-post-carousel-wrap .swiper-button-prev i {
  -webkit-transform: rotate(0deg);
      -ms-transform: rotate(0deg);
          transform: rotate(0deg);
}
.eael-post-carousel-wrap .eael-entry-media:hover .eael-entry-overlay.zoom-in {
  -webkit-transform: scale(1);
      -ms-transform: scale(1);
          transform: scale(1);
  visibility: visible;
  opacity: 1;
}
.eael-post-carousel-wrap .eael-entry-media:hover .eael-entry-overlay.fade-in {
  visibility: visible;
  opacity: 1;
}
.eael-post-carousel-wrap .eael-entry-media:hover .eael-entry-overlay.slide-up {
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
  visibility: visible;
  opacity: 1;
}
.eael-post-carousel-wrap .eael-entry-overlay.none {
  background: none !important;
}
.eael-post-carousel-wrap .eael-entry-overlay i, .eael-post-carousel-wrap .eael-entry-overlay svg {
  font-size: 20px;
  height: 20px;
  width: 20px;
}
.eael-post-carousel-wrap .eael-post-block-item-holder .eael-entry-media {
  overflow: hidden;
}
.eael-post-carousel-wrap .swiper-button-prev i {
  -webkit-transform: rotate(0deg);
      -ms-transform: rotate(0deg);
          transform: rotate(0deg);
}
.eael-post-carousel-wrap.swiper-container-wrap .swiper-pagination {
  bottom: 10px;
  left: 0;
  width: 100%;
}
.eael-post-carousel-wrap.swiper-container-wrap-dots-outside .swiper-pagination {
  position: static;
}
.eael-post-carousel-wrap.swiper-container-wrap .swiper-pagination-bullet {
  background: #ccc;
  margin: 0 4px;
  opacity: 1;
  height: 8px;
  width: 8px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.eael-post-carousel-wrap.swiper-container-wrap .swiper-pagination-bullet-active {
  background: #000;
}
.eael-post-carousel-wrap.swiper-container-wrap .swiper-button-next,
.eael-post-carousel-wrap.swiper-container-wrap .swiper-button-prev {
  background-image: none;
  outline: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.eael-post-carousel-wrap .eael-post-grid.eael-post-carousel {
  margin: 0;
}
.eael-post-carousel-wrap .eael-post-grid.eael-post-carousel .eael-grid-post {
  width: 100%;
}
.eael-post-carousel-wrap .post-carousel-categories {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 11;
  width: 100%;
  margin: 0;
  padding: 15px;
  text-align: left;
}
.eael-post-carousel-wrap .post-carousel-categories li {
  display: inline-block;
  text-transform: capitalize;
  margin-right: 5px;
  position: relative;
}
.eael-post-carousel-wrap .post-carousel-categories li:after {
  content: ",";
  color: #ffffff;
}
.eael-post-carousel-wrap .post-carousel-categories li:last-child:after {
  display: none;
}
.eael-post-carousel-wrap .post-carousel-categories li a {
  color: #fff;
}
.eael-post-carousel-wrap .eael-entry-content-btn {
  margin-top: 15px;
}
.eael-post-carousel-wrap.eael-post-carousel-style-three .eael-entry-content {
  padding: 0 15px 15px 15px;
}
.eael-post-carousel-wrap.eael-post-carousel-style-three .eael-meta-posted-on {
  min-width: 60px;
  height: 50px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 4px;
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);
          box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);
  text-align: center;
  font-size: 16px;
  line-height: 18px;
  padding: 5px;
  margin-top: 5px;
}
.eael-post-carousel-wrap.eael-post-carousel-style-three .eael-meta-posted-on span {
  display: block;
}
.eael-post-carousel-wrap.eael-post-carousel-style-two .eael-entry-content {
  padding: 0 15px;
}
.eael-post-carousel-wrap.eael-post-carousel-style-two .eael-entry-footer-two {
  padding: 15px;
}
.eael-post-carousel-wrap.eael-post-carousel-style-two .eael-entry-meta {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.eael-post-carousel-wrap.eael-post-carousel-style-two .eael-entry-meta .eael-meta-posted-on {
  padding: 0;
  font-size: 12px;
  margin-right: 15px;
  color: #929292;
}
.eael-post-carousel-wrap.eael-post-carousel-style-two .eael-entry-meta .eael-meta-posted-on i {
  margin-right: 7px;
}
.eael-post-carousel-wrap.eael-post-carousel-style-two .eael-entry-meta .post-meta-categories {
  list-style: none;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin: 0;
}
.eael-post-carousel-wrap.eael-post-carousel-style-two .eael-entry-meta .post-meta-categories li {
  font-size: 12px;
  margin-right: 7px;
  color: #929292;
}
.eael-post-carousel-wrap.eael-post-carousel-style-two .eael-entry-meta .post-meta-categories li a {
  color: #929292;
}
.eael-post-carousel-wrap.eael-post-carousel-style-two .eael-entry-meta .post-meta-categories li:last-child {
  margin-right: 0;
}
.eael-post-carousel-wrap .rtl .eael-logo-carousel-wrap .eael-entry-footer {
  direction: ltr;
}
.eael-post-carousel-wrap .swiper-container {
  width: 100%;
}
.eael-post-carousel-wrap .swiper-container ~ .swiper-button-prev:after,
.eael-post-carousel-wrap .swiper-container ~ .swiper-button-next:after {
  content: none;
}
.eael-post-carousel-wrap .swiper-button-next:after, .eael-post-carousel-wrap .swiper-rtl .swiper-button-prev:after,
.eael-post-carousel-wrap .swiper-button-prev:after, .eael-post-carousel-wrap .swiper-rtl .swiper-button-next:after {
  content: "";
}
.eael-post-carousel-wrap .eael-marquee-carousel .swiper-wrapper {
  -webkit-transition-timing-function: linear !important;
          transition-timing-function: linear !important;
}
