.eael-restaurant-menu .eael-restaurant-menu-item-wrap {
  margin-bottom: 10px;
  position: relative;
}

.eael-restaurant-menu .eael-restaurant-menu-item {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.eael-restaurant-menu .eael-restaurant-menu-image {
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0;
  margin-right: 10px;
}

.eael-restaurant-menu .eael-restaurant-menu-content {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.eael-restaurant-menu .eael-restaurant-menu-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 5px;
}

.eael-restaurant-menu .eael-restaurant-menu-title {
  display: inline-block;
  margin: 0;
}

.eael-restaurant-menu .eael-restaurant-menu-title a {
  color: inherit;
}

.eael-restaurant-menu-style-1 .eael-restaurant-menu-price {
  float: right;
}

.eael-restaurant-menu-style-3 .eael-restaurant-menu-image {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
}

.eael-restaurant-menu-style-4 .eael-restaurant-menu-item,
.eael-restaurant-menu-style-4 .eael-restaurant-menu-header {
  display: block;
}

.eael-restaurant-menu-style-4 .eael-restaurant-menu-image {
  display: inline-block;
}

.eael-restaurant-menu-style-1 .eael-price-title-connector {
  border-bottom: 1px dashed #000;
  height: 1px;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  -ms-flex-item-align: center;
      align-self: center;
  margin: 0 20px;
}

.eael-restaurant-menu-style-eael .eael-restaurant-menu-item {
  min-height: 150px;
  position: relative;
}

.eael-restaurant-menu-style-eael .eael-restaurant-menu-price {
  bottom: 0;
  padding: 10px;
  position: absolute;
  right: 0;
  z-index: 1;
}

.eael-restaurant-menu-style-eael .eael-restaurant-menu-price:after {
  border-color: transparent #B83D11;
  border-style: solid;
  border-width: 110px 130px 0 0;
  bottom: 0;
  content: "";
  position: absolute;
  right: 0;
  z-index: -1;
}

.eael-restaurant-menu-divider-wrap {
  font-size: 0;
  line-height: 0;
}

.eael-restaurant-menu-divider {
  display: inline-block;
}

.rtl .eael-restaurant-menu-style-eael .eael-restaurant-menu-price:after {
  border-width: 110px 0 0 130px;
  left: 0;
  right: unset;
}

.rtl .eael-restaurant-menu-style-eael .eael-restaurant-menu-price {
  left: 0;
  right: unset;
}
