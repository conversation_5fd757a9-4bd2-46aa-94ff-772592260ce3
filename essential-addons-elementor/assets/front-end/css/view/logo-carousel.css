.eael-logo-carousel-wrap .eael-logo-carousel.grayscale-normal img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.eael-logo-carousel-wrap .eael-logo-carousel.grayscale-normal .swiper-slide:hover img {
  -webkit-filter: none;
          filter: none;
}
.eael-logo-carousel-wrap .eael-logo-carousel.grayscale-hover .swiper-slide:hover img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.eael-logo-carousel-wrap .eael-logo-carousel.swiper-container .swiper-slide {
  text-align: center;
}
.eael-logo-carousel-wrap .eael-logo-carousel.swiper-container .swiper-slide img {
  width: auto;
}
.eael-logo-carousel-wrap .eael-logo-carousel.eael-marquee-carousel .swiper-wrapper {
  -webkit-transition-timing-function: linear !important;
          transition-timing-function: linear !important;
}
.eael-logo-carousel-wrap .eael-logo-carousel .eael-logo-carousel-title a {
  color: inherit;
}
.eael-logo-carousel-wrap.swiper-container-wrap .swiper-pagination {
  bottom: 10px;
  left: 0;
  width: 100%;
}
.eael-logo-carousel-wrap.swiper-container-wrap .swiper-pagination-bullet {
  background: #ccc;
  margin: 0 4px;
  opacity: 1;
  height: 8px;
  width: 8px;
}
.eael-logo-carousel-wrap.swiper-container-wrap .swiper-pagination-bullet-active {
  background: #000;
}
.eael-logo-carousel-wrap.swiper-container-wrap-dots-outside .swiper-pagination {
  position: static;
}
.eael-logo-carousel-wrap .swiper-button-next:after, .eael-logo-carousel-wrap .swiper-rtl .swiper-button-prev:after,
.eael-logo-carousel-wrap .swiper-button-prev:after, .eael-logo-carousel-wrap .swiper-rtl .swiper-button-next:after {
  content: "";
}
.eael-logo-carousel-wrap .eael-lc-logo {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.eael-logo-carousel-wrap .swiper-button-next, .eael-logo-carousel-wrap .swiper-button-prev {
  background-image: none;
  outline: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.eael-logo-carousel-wrap .swiper-container {
  width: 100%;
}
.eael-logo-carousel-wrap .swiper-container ~ .swiper-button-prev:after,
.eael-logo-carousel-wrap .swiper-container ~ .swiper-button-next:after {
  content: none;
}
