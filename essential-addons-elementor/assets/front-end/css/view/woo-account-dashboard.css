.eael-account-dashboard-wrapper .eael-account-dashboard-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 60px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation {
  float: none;
  width: 100%;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0;
  padding: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0;
  padding: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 2px;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.21;
  text-decoration: none;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .eael-account-profile {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .eael-account-profile .eael-account-profile-image {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  border-radius: 100%;
  overflow: hidden;
  -o-object-fit: cover;
     object-fit: cover;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .eael-account-profile .eael-account-profile-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .eael-account-profile .eael-account-profile-details {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 2px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .eael-account-profile .eael-account-profile-details p {
  font-size: 14px;
  line-height: 1.21;
  font-weight: 400;
  color: #787c8a;
  margin: 0;
  padding: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-navbar .eael-account-profile .eael-account-profile-details h5 {
  font-size: 16px;
  line-height: 1.21;
  font-weight: 500;
  margin: 0;
  padding: 0;
  text-transform: none;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content {
  min-height: 400px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce .woocommerce-MyAccount-navigation {
  display: none;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .tab-content {
  display: none;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .tab-content.active {
  display: block;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content {
  float: none;
  width: 100%;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content p {
  color: #0E0808;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.5;
  position: relative;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content p strong {
  color: #0E0808;
  font-weight: 500;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content p a {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
  -webkit-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .order-again {
  margin-top: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .order-again a {
  margin-left: auto;
  margin-top: 0;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0px 16px;
  min-height: 30px;
  gap: 6px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  line-height: 2.14;
  text-decoration: none;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .order-again a:hover {
  color: #ffffff;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table {
  border: none;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table thead tr {
  border: none;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table thead tr th {
  border: none;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.7;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td {
  padding: 5px;
  height: 56px;
  color: #61636d;
  font-size: 16px;
  font-weight: 400;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td {
  padding: 5px;
  height: 56px;
  color: #61636d;
  font-size: 16px;
  font-weight: 400;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th a,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td a {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-Price-amount {
  font-weight: 500;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-orders-table__cell-order-actions .woocommerce-button {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-height: 30px;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 2.14;
  text-decoration: none;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-details thead tr th:first-child,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-details tbody tr td:first-child,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-details tfoot tr th:first-child,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-details tfoot tr td:first-child {
  width: 75%;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-height: 30px;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 2.14;
  text-decoration: none;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:before {
  font-family: "eaicon";
  content: "\e977";
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  font-size: 16px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:hover {
  color: #ffffff;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:hover::before {
  color: #ffffff;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads tr th {
  padding: 0 5px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-notices-wrapper .woocommerce-error,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-notices-wrapper .woocommerce-message {
  min-height: 64px;
  border-radius: 8px;
  border: 1px solid #ece9f4;
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 2px 12px 2px 50px;
  color: #1a1a21;
  font-size: 18px;
  line-height: 1.7;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-notices-wrapper .woocommerce-error {
  background: #fff7f4 !important;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-notices-wrapper .woocommerce-error:before,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-notices-wrapper .woocommerce-message:before {
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
  left: 14px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-notices-wrapper .woocommerce-error:after,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-notices-wrapper .woocommerce-message:after {
  display: none;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-info {
  min-height: 64px;
  border-radius: 8px;
  border: 1px solid #ece9f4;
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 2px 12px 2px 20px;
  gap: 8px;
  color: #1a1a21;
  font-size: 18px;
  line-height: 1.7;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-info:before {
  font-family: "WooCommerce";
  content: "\e015";
  color: #787c8a;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-info .button {
  margin-left: auto;
  min-height: 46px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 2px 24px;
  gap: 6px;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.875;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-info:last-child {
  margin-bottom: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-order-downloads:not(:last-child) {
  margin-bottom: 32px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-order-downloads__title,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-order-details__title {
  font-weight: 500;
  line-height: 1.7;
  margin-bottom: 8px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  margin-top: 32px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details > .woocommerce-column__title,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details > address {
  width: 50%;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details .woocommerce-column__title {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-left: 1px solid #ece9f4;
  border-right: 1px solid #ece9f4;
  border-top: 1px solid #ece9f4;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.7;
  margin: 0;
  padding: 10px 30px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details address {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid #ece9f4;
  background: #fff;
  color: #787c8a;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.7;
  margin: 0;
  padding: 10px 30px 30px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details address p {
  line-height: 2;
  margin: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details address p:before {
  line-height: 2;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-left: 1px solid #ece9f4;
  border-right: 1px solid #ece9f4;
  border-top: 1px solid #ece9f4;
  margin: 0;
  padding: 10px 30px;
  gap: 6px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title h3 {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.7;
  margin: 0;
  width: 100%;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a {
  margin: 0;
  color: #787c8a;
  font-size: 14px;
  font-weight: 500;
  line-height: 2.14;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 6px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:before {
  font-family: "eaicon";
  content: "\e978";
  color: #bebfc2;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address address {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid #ece9f4;
  background: #fff;
  color: #787c8a;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.7;
  margin: 0;
  padding: 10px 30px 30px;
  font-style: normal;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address address p {
  line-height: 2;
  margin: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row {
  margin-top: 0;
  margin-bottom: 32px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row:last-child,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row:last-child {
  margin-bottom: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row label,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row label {
  color: #344054;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.25;
  margin-bottom: 8px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row label .required,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .form-row label .required {
  color: #ff1d1d;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.25;
  text-decoration: none;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row input,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row input {
  min-height: 44px;
  padding: 2px 15px;
  border: 1px solid #e2edf0;
  -webkit-box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
          box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  color: #667085;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row .select2-selection,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row .select2-selection {
  min-height: 44px;
  height: 44px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 2px 15px;
  border-radius: 8px;
  border: 1px solid #e2edf0;
  -webkit-box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
          box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  color: #667085;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row .select2-selection .select2-selection__arrow,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row .select2-selection .select2-selection__arrow {
  min-height: 44px;
  height: 44px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row .password-input,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row .password-input {
  position: relative;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row .password-input .show-password-input,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row .password-input .show-password-input {
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 14px;
  position: absolute;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row .password-input .show-password-input::after,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row .password-input .show-password-input::after {
  font-family: "eaicon";
  content: "\e979";
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row span,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row span em,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row span,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row span em {
  margin-top: 0px;
  color: #667085;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.42;
  font-style: normal;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields fieldset,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm fieldset {
  border: none;
  margin: 0;
  padding: 0;
  margin-top: 60px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields fieldset legend,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm fieldset legend {
  color: #1a1a21;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.7;
  padding: 0;
  margin-bottom: 20px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields > p,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm > p {
  margin-top: 32px;
  margin-bottom: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields > p .button,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm > p .woocommerce-Button {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 2px 32px;
  min-height: 46px;
  gap: 6px;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.8;
  margin: 0;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-table--order-details thead th {
  text-transform: none;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-table--order-details .woocommerce-Price-amount .woocommerce-Price-currencySymbol {
  margin-right: 5px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-order-details__title,
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-order-downloads__title {
  font-size: 22px;
  margin-top: 50px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Addresses.addresses {
  margin-top: 30px;
}
.eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content form > h3 {
  font-size: 22px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .eael-account-profile .eael-account-profile-details h5, .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .eael-account-profile .eael-account-profile-details h5 {
  color: #1a1a21;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table thead tr th, .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table thead tr th {
  padding-top: 0px;
  padding-bottom: 0px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table thead tr th:first-child, .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table thead tr th:first-child {
  padding-left: 0px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td a, .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td a {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details .woocommerce-column__title, .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details .woocommerce-column__title {
  background: #fff;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title, .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title {
  background: #fff;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a, .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-table--order-details tfoot tr:last-child th, .eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-table--order-details tfoot tr:last-child td, .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-table--order-details tfoot tr:last-child th, .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-table--order-details tfoot tr:last-child td {
  color: #fff;
  border-radius: 0;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.eael-wcd-icon a::before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.eael-wcd-icon a::before {
  content: "\e974";
  color: transparent !important;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a {
  gap: 16px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--dashboard a:before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--dashboard a:before {
  content: "\e974";
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--orders a:before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--orders a:before {
  content: "\e97e";
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--downloads a:before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--downloads a:before {
  content: "\e977";
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--edit-address a:before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--edit-address a:before {
  content: "\e97c";
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--edit-account a:before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--edit-account a:before {
  content: "\e973";
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-logout a:before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-logout a:before {
  content: "\e97b";
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-orders-table__row--status-completed .woocommerce-orders-table__cell-order-status::before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-orders-table__row--status-completed .woocommerce-orders-table__cell-order-status::before {
  font-family: "eaicon";
  content: "\e975";
  color: #00B05C;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-container, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-container {
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
}
@media only screen and (max-width: 768px) {
  .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-container, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-container {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 22.5%;
          flex: 0 0 22.5%;
  max-width: 22.5%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media only screen and (max-width: 768px) {
  .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 10px;
  }
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-ordinal-group: 3;
      -ms-flex-order: 2;
          order: 2;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li {
  width: 100%;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a {
  position: relative;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .eael-account-profile, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .eael-account-profile {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table {
  border-collapse: collapse;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr {
  border-top: 0;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td {
  border: none;
  line-height: 2.14;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td {
  border: none;
  line-height: 2.14;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file {
  padding: 0px 13px;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-order-downloads__title,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-order-details__title, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-order-downloads__title,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-order-details__title {
  font-size: 18px;
  margin-top: 0;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:before {
  -webkit-transform: all 0.3s ease-in-out;
      -ms-transform: all 0.3s ease-in-out;
          transform: all 0.3s ease-in-out;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields > p .button,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm > p .woocommerce-Button, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields > p .button,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm > p .woocommerce-Button {
  border-radius: 4px;
  -webkit-box-shadow: 0px 1px 2px 0px rgba(0, 1, 35, 0.1);
          box-shadow: 0px 1px 2px 0px rgba(0, 1, 35, 0.1);
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-container {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar {
  width: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 10px;
  background-color: #fff;
  gap: 10px;
  border-radius: 4px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul {
  gap: 10px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 12px 16px;
  color: #1a1a21;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active {
  background: #fff7f4;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a {
  color: #BE451A;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a:before, .eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a i {
  font-family: "eaicon";
  line-height: 1;
  color: #BE451A;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a svg {
  fill: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-navbar .eael-account-profile {
  margin-left: auto;
  min-width: 180px;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content {
  margin-top: 10px;
  padding: 50px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content p a:hover {
  color: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .order-again a {
  border-radius: 20px;
  border: 1px solid #f88258;
  background: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .order-again a:hover {
  border: 1px solid #f88258;
  background: #f88258;
  -webkit-box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
          box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  color: #fff;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table {
  border-collapse: separate;
  border-spacing: 0px 12px;
  margin-top: -12px;
  margin-bottom: -12px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td:first-child {
  border-left: 1px solid #ece9f4;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  padding-left: 20px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td:last-child {
  border-right: 1px solid #ece9f4;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  padding-right: 14px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td {
  border-top: 1px solid #ece9f4;
  border-bottom: 1px solid #ece9f4;
  line-height: 1.7;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td a {
  color: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th:first-child,
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td:first-child {
  border-left: 1px solid #ece9f4;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  padding-left: 20px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th:last-child,
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td:last-child {
  border-right: 1px solid #ece9f4;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  padding-right: 14px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th,
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td {
  border-top: 1px solid #ece9f4;
  border-bottom: 1px solid #ece9f4;
  line-height: 1.7;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th a,
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td a {
  color: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-orders-table__cell-order-actions .woocommerce-button {
  padding: 0px 16px;
  border-radius: 20px;
  border: 1px solid #ffe0d6;
  background: #fff7f4;
  color: #61636d;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-orders-table__cell-order-actions .woocommerce-button.view:before {
  font-family: "eaicon";
  content: "\e979";
  color: #f88258;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-orders-table__cell-order-actions .woocommerce-button:hover, .eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-orders-table__cell-order-actions .woocommerce-button.view:hover::before {
  color: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file {
  padding: 0px 16px;
  border-radius: 20px;
  border: 1px solid #ffe0d6;
  background: #fff7f4;
  color: #61636d;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:before {
  color: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:hover {
  background: #f88258;
  color: #ffffff;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:hover::before {
  color: #ffffff;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-info .button {
  border-radius: 24px;
  background: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:hover {
  color: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:before {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:hover::before {
  color: #f88258;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row input,
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row input {
  border-radius: 8px;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields > p .button,
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm > p .woocommerce-Button {
  border-radius: 8px;
  background: #f88258;
  font-family: Inter;
}
.eael-account-dashboard-wrapper.preset-1 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-table--order-details tfoot tr:last-child {
  background: #F88258;
}
@media only screen and (max-width: 768px) {
  .eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar {
    border-right: 1px solid #eef1f3;
  }
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation {
  padding: 0;
  background-color: #fff;
  border-left: 1px solid #eef1f3;
  border-bottom: 1px solid #eef1f3;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li {
  border-top: 1px solid #eef1f3;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a {
  padding: 16px 24px;
  color: #787c8a;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a {
  color: #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  border-left: 2px solid #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-navbar .eael-account-profile {
  padding: 20px 10px 20px 24px;
  background-color: #fff;
  border-top: 1px solid #eef1f3;
  border-left: 1px solid #eef1f3;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content {
  padding: 35px;
  border: 1px solid #eef1f3;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content p a:hover {
  color: #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .order-again a {
  border-radius: 4px;
  border: 1px solid #181818;
  background: #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .order-again a:hover {
  color: #fff;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table thead tr th {
  border-bottom: 2px solid #eef1f3;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td:first-child {
  padding-left: 0px;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td:last-child {
  padding-right: 0;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr {
  border-bottom: 1px dashed #e4e5e7;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td a {
  color: #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th:first-child,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td:first-child {
  padding-left: 0;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr:last-child {
  border: none;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr:last-child th:first-child {
  padding-left: 15px;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th:last-child,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td:last-child {
  padding-right: 0;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th a,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td a {
  color: #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-orders-table__cell-order-actions .woocommerce-button {
  border-radius: 4px;
  border: 1px solid #e1e2e3;
  background: #e7e9eb;
  color: #1a1a21;
  padding: 0px 13px;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-orders-table__cell-order-actions .woocommerce-button:hover {
  border-color: #181818;
  background: #181818;
  color: #fff;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file {
  border-radius: 4px;
  border: 1px solid #e1e2e3;
  background: #e7e9eb;
  color: #1a1a21;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:before {
  color: #1a1a21;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:hover {
  border-color: #181818;
  background: #181818;
  color: #fff;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:hover::before {
  color: #fff;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-info .button {
  border-radius: 4px;
  background: #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:hover {
  color: #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:hover::before {
  color: #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields .form-row input,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row input {
  border-radius: 4px;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields > p .button,
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm > p .woocommerce-Button {
  background: #181818;
}
.eael-account-dashboard-wrapper.preset-2 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-table--order-details tfoot tr:last-child {
  background: #181818;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar {
  background: -webkit-gradient(linear, left top, left bottom, from(#01094d), to(#01094d));
  background: linear-gradient(180deg, #01094d 0%, #01094d 100%);
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation {
  padding: 0 0 32px 0;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a {
  padding: 12px 12px 12px 32px;
  color: #9ca2d4;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a:before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a i {
  font-family: "eaicon";
  line-height: 1;
  color: #9ca2d4;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li a svg {
  fill: #9ca2d4;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a {
  color: #fff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a:before, .eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a i {
  color: #fff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a svg {
  fill: #fff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .woocommerce-MyAccount-navigation ul li.is-active a:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  border-left: 4px solid #4356ff;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .eael-account-profile {
  padding: 32px 10px 32px 20px;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-navbar .eael-account-profile .eael-account-profile-details h5 {
  color: #ffffff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content {
  padding: 30px;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content p a:hover {
  color: #4356ff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .order-again a {
  border-radius: 22px;
  border: 1px solid #4356ff;
  background: #4356ff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .order-again a:hover {
  color: #fff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table thead tr th {
  padding-top: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f3fa;
  background-color: #f9f9fd;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table thead tr th:first-child {
  padding-left: 18px;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td:first-child {
  padding-left: 18px;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td:last-child {
  padding-right: 18px;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr {
  border-bottom: 1px solid #f3f3fa;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tbody tr td a {
  color: #4356ff;
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th:first-child,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td:first-child {
  padding-left: 18px;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th:last-child,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td:last-child {
  padding-right: 18px;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr th a,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table tfoot tr td a {
  color: #4356ff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-orders-table__cell-order-actions .woocommerce-button {
  border-radius: 22px;
  border: 1px solid #d5d8ef;
  background: #fff;
  color: #4356ff;
  padding: 0 13px;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-MyAccount-orders .woocommerce-orders-table__cell-order-actions .woocommerce-button:hover {
  border-color: #4356ff;
  background: #4356ff;
  color: #fff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-details tbody tr,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-details tfoot tr {
  border-top: 1px dashed #e4e5e7;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file {
  border-radius: 22px;
  border: 1px solid #4356ff;
  background: #4356ff;
  color: #ffffff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:before {
  color: #ffffff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content table.woocommerce-table--order-downloads .woocommerce-MyAccount-downloads-file:hover {
  border-color: #4356ff;
  background: #4356ff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-info .button {
  border-radius: 22px;
  background: #4356ff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-customer-details .woocommerce-column__title {
  background: #f9f9fd;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title {
  background: #f9f9fd;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a {
  -webkit-transform: all 0.3s ease-in-out;
      -ms-transform: all 0.3s ease-in-out;
          transform: all 0.3s ease-in-out;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:hover {
  color: #4356ff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-Address-title a:hover::before {
  color: #4356ff;
}
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-address-fields > p .button,
.eael-account-dashboard-wrapper.preset-3 .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-EditAccountForm > p .woocommerce-Button {
  background: #4356ff;
}
body .eael-account-dashboard-wrapper .woocommerce-MyAccount-navigation-link {
  border: unset;
}
body .eael-account-dashboard-wrapper .woocommerce-MyAccount-navigation-link a {
  background-color: unset;
}
body .eael-account-dashboard-wrapper a:focus {
  outline: 0;
}
body .eael-account-dashboard-wrapper table, body .eael-account-dashboard-wrapper td, body .eael-account-dashboard-wrapper th {
  border: unset;
}
body .eael-account-dashboard-wrapper .woocommerce-Address h3 {
  padding: unset;
}
body.woocommerce-js table.shop_table thead, body.woocommerce-page table.shop_table thead {
  background: unset;
}
body.woocommerce-account .woocommerce-MyAccount-content fieldset legend {
  border-bottom: unset;
}
body .woocommerce-error::before, body .woocommerce-info::before {
  position: unset;
}
body table tbody tr:hover > td,
body table tbody tr:hover > th {
  background: unset;
}
body .woocommerce-MyAccount-content mark {
  background: unset;
}
@font-face {
  font-family: WooCommerce;
  src: url("../../plugins/woocommerce/assets/fonts/WooCommerce.eot");
  src: url("../../plugins/woocommerce/assets/fonts/WooCommerce.eot?#iefix") format("embedded-opentype"), url("../../plugins/woocommerce/assets/fonts/WooCommerce.woff") format("woff"), url("../../plugins/woocommerce/assets/fonts/WooCommerce.ttf") format("truetype"), url("../../plugins/woocommerce/assets/fonts/WooCommerce.svg#WooCommerce") format("svg");
  font-weight: 400;
  font-style: normal;
}
body.theme-oceanwp .eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content .woocommerce-notices-wrapper .woocommerce-message {
  display: block;
  line-height: 3;
  padding-left: 20px;
}
body.theme-oceanwp .eael-account-dashboard-wrapper.preset-1 .oceanwp-user-profile, body.theme-oceanwp .eael-account-dashboard-wrapper.preset-2 .oceanwp-user-profile, body.theme-oceanwp .eael-account-dashboard-wrapper.preset-3 .oceanwp-user-profile {
  display: none;
}
@media only screen and (min-width: 768px) {
  body.theme-oceanwp .eael-account-dashboard-wrapper.preset-1 .woocommerce-MyAccount-tabs, body.theme-oceanwp .eael-account-dashboard-wrapper.preset-2 .woocommerce-MyAccount-tabs, body.theme-oceanwp .eael-account-dashboard-wrapper.preset-3 .woocommerce-MyAccount-tabs {
    width: 100%;
    margin: 0;
  }
}
body.theme-oceanwp .eael-account-dashboard-wrapper.preset-1 .woocommerce-MyAccount-navigation ul, body.theme-oceanwp .eael-account-dashboard-wrapper.preset-3 .woocommerce-MyAccount-navigation ul {
  border-top: 0;
}
body.theme-oceanwp .eael-account-dashboard-wrapper.preset-2 .woocommerce-MyAccount-navigation ul li a:before {
  margin: 0;
}
body.theme-oceanwp .eael-account-dashboard-wrapper.preset-2 .woocommerce-MyAccount-navigation ul li a:focus {
  outline: none !important;
}
body.theme-oceanwp .eael-account-dashboard-wrapper.preset-2 .woocommerce-MyAccount-navigation ul li:not(.is-active) a:before {
  display: none;
}
body.theme-astra:not(.cartflows-canvas):not(.cartflows-default) .woocommerce form .form-row label:not(.checkbox):not(.radio):not(.woocommerce-form__label-for-checkbox) {
  position: relative;
  font-size: initial;
  opacity: 1;
  padding: initial;
}
body.theme-astra:not(.cartflows-canvas):not(.cartflows-default) .woocommerce form .form-row.ast-animate-input input[type=text] {
  padding: initial;
  margin-bottom: 5px;
}
body.theme-astra .eael-account-dashboard-wrapper .eael-account-dashboard-content .woocommerce-MyAccount-content p#billing_address_2_field .screen-reader-text {
  opacity: 0;
}
