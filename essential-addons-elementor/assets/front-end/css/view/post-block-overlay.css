.post-block-style-overlay .eael-entry-wrapper {
  -webkit-transition: 300ms;
  transition: 300ms;
}

/* SlideUp */

.post-block-style-overlay .eael-entry-wrapper.slide-up {
  -webkit-transform: translateY(100%);
      -ms-transform: translateY(100%);
          transform: translateY(100%);
}

.post-block-style-overlay .eael-entry-media {
  overflow: hidden;
}

.post-block-style-overlay .eael-post-block-item:hover .eael-entry-wrapper.slide-up {
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
}

/* Fade In */

.post-block-style-overlay .eael-entry-wrapper.fade-in {
  visibility: hidden;
  opacity: 0;
}

.post-block-style-overlay .eael-post-block-item:hover .eael-entry-wrapper.fade-in {
  visibility: visible;
  opacity: 1;
}

/* Zoom In */

.post-block-style-overlay .eael-entry-wrapper.zoom-in {
  -webkit-transform: scale(0.8);
      -ms-transform: scale(0.8);
          transform: scale(0.8);
  visibility: hidden;
  opacity: 0;
}

.post-block-style-overlay .eael-post-block-item:hover .eael-entry-wrapper.zoom-in {
  visibility: visible;
  opacity: 1;
  -webkit-transform: scale(1);
      -ms-transform: scale(1);
          transform: scale(1);
}

.post-block-style-overlay .eael-entry-wrapper.none {
  display: none;
}
