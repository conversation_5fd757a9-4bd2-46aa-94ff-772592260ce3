.eael-cs-products-container {
  display: grid;
  gap: 20px;
}
.eael-cs-products-container img {
  display: block;
}
.eael-cs-products-container .eael-cs-product-image {
  overflow: hidden;
}
.eael-cs-products-container .eael-cs-product-buttons a, .eael-cs-products-container .eael-cs-product-buttons i {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.eael-cs-products-container .eael-cs-product-buttons i {
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
}
.eael-cs-products-container .eael-cs-product-price ins {
  background: initial;
}
.eael-cs-products-container.style-1 .eael-cs-single-product {
  position: relative;
  overflow: hidden;
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info {
  background: rgba(10, 22, 61, 0.9);
  position: absolute;
  bottom: 0;
  left: 0;
  -webkit-transform: translateY(100%);
      -ms-transform: translateY(100%);
          transform: translateY(100%);
  width: 100%;
  z-index: 2;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-title-price {
  color: #FFFFFF;
  text-align: center;
  padding: 13px 0;
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-title-price .eael-cs-product-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 18px;
  padding: 0 10px;
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-title-price .eael-cs-product-price {
  font-size: 18px;
  font-weight: 600;
  line-height: 22px;
  margin-top: 10px;
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons {
  display: grid;
  grid-template-columns: auto;
  position: relative;
  border-top: 1px solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons.eael-cs-purchasable {
  grid-template-columns: repeat(2, 1fr);
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons.eael-cs-purchasable::after {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  height: 100%;
  width: 1px;
  background: rgba(255, 255, 255, 0.5);
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons a {
  border: none;
  text-align: center;
  background: transparent;
  line-height: 60px;
  color: #FFFFFF;
  padding: 0;
  font-size: 12px;
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons a i {
  margin-right: 6px;
}
.eael-cs-products-container.style-1 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons a.added {
  display: none;
}
.eael-cs-products-container.style-1 .eael-cs-single-product:hover .eael-cs-product-info {
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
}
.eael-cs-products-container.style-2 .eael-cs-single-product {
  position: relative;
  border: 1px solid #CCD5EC;
  border-radius: 16px;
  overflow: hidden;
  background: white;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-image {
  position: relative;
  background: #F0F4FF;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-image .eael-cs-product-buttons {
  position: absolute;
  text-align: center;
  bottom: 0;
  left: 0;
  width: 100%;
  -webkit-transform: translateY(100%);
      -ms-transform: translateY(100%);
          transform: translateY(100%);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-image .eael-cs-product-buttons a {
  font-size: 16px;
  display: inline-block;
  background: #FFFFFF;
  width: 38px;
  line-height: 38px;
  text-align: center;
  border-radius: 4px;
  color: #8C8B9A;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-image .eael-cs-product-buttons a + a {
  margin-left: 8px;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-image .eael-cs-product-buttons a.add_to_cart_button.added {
  display: none;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-image .eael-cs-product-buttons a.add_to_cart_button::before {
  content: "\f07a";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-image .eael-cs-product-buttons a.added_to_cart {
  font-size: 0;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-image .eael-cs-product-buttons a.added_to_cart::before {
  font-size: 16px;
  content: "\f217";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-image .eael-cs-product-buttons a:hover {
  color: #6345EA;
  -webkit-box-shadow: 0 8px 16px -2px rgba(23, 28, 58, 0.45);
          box-shadow: 0 8px 16px -2px rgba(23, 28, 58, 0.45);
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-info {
  text-align: center;
  padding: 13px 0;
  background: #FFFFFF;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-title {
  font-size: 18px;
  font-weight: 500;
  line-height: 18px;
  color: #5E6374;
  padding: 0 10px;
}
.eael-cs-products-container.style-2 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-price {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  margin-top: 10px;
  color: #1C1F29;
}
.eael-cs-products-container.style-2 .eael-cs-single-product:hover .eael-cs-product-buttons {
  -webkit-transform: translateY(-20px);
      -ms-transform: translateY(-20px);
          transform: translateY(-20px);
}
.eael-cs-products-container.style-3 {
  grid-template-columns: 100%;
  gap: 30px;
}
.eael-cs-products-container.style-3 .eael-cs-single-product {
  display: grid;
  gap: 50px;
  grid-template-columns: 25% auto;
  border: 1px solid #CDD0DA;
  border-radius: 4px;
  padding: 15px;
  overflow: hidden;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-image {
  border-radius: 4px;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-info {
  padding: 10px 0;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-title {
  font-size: 20px;
  font-weight: 500;
  line-height: 22px;
  color: #5E6374;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-title a {
  color: #5E6374;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-price {
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  margin-top: 15px;
  color: #1C1F29;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-excerpt {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-top: 15px;
  color: #5E6374;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons {
  margin-top: 20px;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons a {
  display: inline-block;
  background: #6345EA;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 38px;
  border-radius: 3px;
  padding: 0 25px;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons a i {
  margin-right: 10px;
}
.eael-cs-products-container.style-3 .eael-cs-single-product .eael-cs-product-info .eael-cs-product-buttons a.added {
  display: none;
}
@media only screen and (max-width: 767px) {
  .eael-cs-products-container .eael-cs-product-image img {
    width: 100%;
  }
  .eael-cs-products-container.style-3 .eael-cs-single-product {
    grid-template-columns: 100% !important;
  }
}
