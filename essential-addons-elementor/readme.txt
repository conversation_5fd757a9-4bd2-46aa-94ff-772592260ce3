=== Essential Addons for Elementor - Pro ===
Contributors: wpdevteam, re_enter_rupok, Asif2BD, priyomukul
Tags: elementor, elements, widgets, page builder, builder, visual editor, wordpress page builder
Tested up to: 6.7
Stable tag: 6.4.2
License: GPLv3
License URI: https://opensource.org/licenses/GPL-3.0

Supercharge your Elementor page building experience with Essential Addons PRO. Get your hands on exclusive elements such as Instagram Feed, Protected Content, Smart Post List, and many more.

== Description ==

Ultimate elements  library for Elementor WordPress Page Builder. 70+ useful and premium elements to complete your website quickly. Stunning design and neat and clean code. Option to enable/disable certain elements to improve page loading. No extra resources to slow down your website.


List of Elements :

* Post Grid
* Post Block
* Post Timeline
* Fancy Text
* Ligthbox & Modal
* Creative Buttons
* Instagram Feed
* Image Comparison
* Countdown
* Interactive Promo
* Team Members
* Testimonials
* Testimonial Slider
* WooCommerce Product Grid
* Contact Form 7
* weForms
* Info Box
* Flip Box
* Dual Color Headline
* Call to Action
* Static Product
* Pricing Table
* Flip Carousel
* Interactive Cards
* Ninja Forms
* Gravity Forms
* Caldera Forms 
* Content Timeline
* Data Table
* Facebook Feed
* Twitter Feed
* Filterable Gallery
* Dynamic FIlterable Gallery
* Image Accordion
* Post List
* Content Ticker
* Advanced Accordion
* Advanced Tabs
* Tooltip
* Advanced Google Map
* Content Toggle
* Mailchimp
* Counter
* Fancy Divider
* Logo Carousel
* Team Member Carousel
* Post Carousel
* Image Hotspot
* Price Menu
* One Page Navigation
* WPForms
* Protected Content
* Progress Bar
* Offcanvas Content
* Woo Product Collections
* Image Scroller
* Advanced Menu
* LearnDash Course List
* Formstack
* Typeform
* Woo Product Slider

List of Extensions :

* Particles (Section)
* Parallax (Section)
* Advanced Tooltip
* Content Protection
* Reading Progress Bar
* Custom JS
* Conditional Display

== Changelog ==

= 6.4.2 - 11/08/2025 =
- Improved: Image upload mechanism in Figma to Elementor Converter
- Few minor bug fixes & improvements

= 6.4.1 - 21/07/2025 =
- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 6.4.0 - 20/07/2025 =
- Added: New Widget | EA Figma to Elementor Converter (Beta)
- Improved: EA Login/Register Form | Added reCAPTCHA support in Password Reset Form
- Few minor bug fixes & improvements

= 6.3.1 - 02/06/2025 =
- Improved: EA Filterable Gallery | Harmonic Layout and Grid Flow Layout
- Improved; Removed Unused Class “use” Declaration
- Improved: EA Testimonial Slider, Post Block, Post Carousel | Controls Reorganized in Elementor Panel
- Fixed: EA Content Protection | Issues with Handling of Containers and Inner Sections
- Few minor bug fixes & improvements

= 6.3.0 - 25/05/2025 =
- Added: EA Filterable Gallery | New Presets: Grid Flow & Harmonic Layouts
- Improved: EA Lightbox & Modal | Button Width not Stretching to Fill Parent Container
- Improved: EA Interactive Cards | Improved Smooth Scrolling for Scrollable Content on Rear Panel
- Improved: EA Dynamic tags | Added option to insert Link to dynamic tags
- Improved: EA Counter | Added Controller for Thousand Separator
- Fixed: EA Advanced Google Maps | Not showing on iPhone properly
- Fixed: EA Smart Post List | Author Meta doesn't show properly
- Few minor bug fixes & improvements

= 6.2.3 - 19/05/2025 =
- Improved: Security Enhancements
- Few minor bug fixes & improvements

= 6.2.2 - 22/04/2025 =
- Fixed: EA Woo Thank You | Fatal error on last order refund
- Fixed: EA Multicolumn Pricing Table | Template export issue
- Fixed: EA LearnDash Course List | "Free" string not translatable
- Fixed: Compatibility issue with WordPress 6.8 | _load_textdomain_just_in_time()
- Few minor bug fixes & improvements

= 6.2.1 - 24/03/2025 =
- Improved: EA Advanced Search | Added SKU search support
- Improved: EA Post Carousel | Controller Mechanism
- Improved: EA Advanced Menu | Controller Mechanism
- Fixed: Elementor editor Overlapping issue
- Fixed: EA Lightbox & Modal | Lightbox visibility issue when using iframe
- Fixed: EA Creative Button | Controller Mechanism
- Few minor bug fixes & improvements

= 6.2.0 - 19/03/2025 =
- Added: New 360 Degree Photo Viewer Widget
- Added: New Multi Column Pricing Table Widget
- Improved: Security Enhancement
- Improved: EA Woo Account Dashboard | Added additional controls inside tabs
- Fixed: EA Parallax | Resolved functionality issue with multiple parallax effects on the same page
- Few minor bug fixes & improvements

= 6.1.1 - 13/03/2025 =
- Fixed: EA Counter | Suffix alignment issue
- Fixed: EA Advanced Google Map | Map title visibility issue on Multi-marker
- Few minor bug fixes & improvements

= 6.1.0 - 17/02/2025 =
- Added: New Widget | EA Stacked Cards
- Fixed: EA Content Timeline | Timeline highlight color missing on scroll with long content
- Fixed: EA Creative Icon | SVG icon color not applying from settings
- Fixed: EA Offcanvas | Conflict with Elementor Motion Effects
- Few minor bug fixes & improvements

= 6.0.13 - 21/01/2025 =
- Improved: Added Edit Template support right inside the page itself for the widgets which got Saved Template option
- Fixed: EA Lightbox & Modal | Doesn't work when Custom JavaScript is used inside the modal
- Fixed: EA Lightbox & Modal | Custom HTML not working properly
- Fixed: EA Event Calendar | Event start time, end time not visible in the event details
- Fixed: EA Dynamic Gallery | ACF Gallery doesn't show inside the popup
- Fixed: EA Content Timeline | - a hyperlink is added in the heading
- Few minor bug fixes & improvements

= 6.0.12 - 29/12/2024 =
- Improved: EA Image Hotspots | Add option for custom attribute to work
- Improved: Accessibility
- Few minor bug fixes & improvements

= 6.0.11 - 24/12/2024 =
- Improved: Added Optimized Markup Support
- Few minor bug fixes & improvements

= 6.0.10 - 11/12/2024 =
- Fixed: EA Tooltip | Undefined array key warning
- Fixed: EA Particles | Custom JS speed movement setup doesn't work
- Fixed: EA Instagram Feed | Updated API
- Few minor bug fixes & improvements

= 6.0.9 - 14/11/2024 =
- Fixed: EA Content Timeline | Scroll Bar Background color & height not functioning properly 
- Fixed: Security Enhancement
- Few minor bug fixes & improvements

= 6.0.8 - 06/11/2024 =
- Improved: EA Fancy Chart | Added responsive controls for mobile and tablet
- Fixed: EA Advanced Search | Search Result issue for First Post on Blog Archive
- Fixed: EA Toggle | Shortcode Functionality 
- Fixed: EA Logo Carousel | Slider UI issue in iOS Devices
- Few minor bug fixes & improvements

= 6.0.7 - 27/10/2024 =
- Improved: EA Image Hotspot | Add typography option for "Text" hotspot
- Improved: EA Content Timeline | Added support for ACF Repeater Fields
- Improved: EA MailChimp | Add redirection option after subscribing
- Fixed: MemberPress restriction is not working with the latest version of EA Pro
- Few minor bug fixes & improvements

= 6.0.6 - 09/10/2024 =
- Improved: EA Advanced Google Map | Add search option to search between markers
- Improved: EA Fancy Chart | WPML Support
- Fixed: EA Advanced Tabs | Style is not working when template is used inside the advanced tab
- Fixed: EA Dynamic Gallery | Undefined array key warning
- Fixed: EA Content Timeline | Timeline bar active color issue
- Few minor bug fixes & improvements

= 6.0.5 - 29/09/2024 =

- Improved: Added Marquee Support | EA Team Member Carousel, EA Post Carousel, EA Logo Carousel, EA Flip Carousel, EA Twitter Feed Carousel
- Improved: EA Lightbox & Modal | Set body class when popup is open
- Improved: EA Advanced Search | Added support to show Product price in search results
- Improved: EA Content Protection | Added Content Protection support for Elementor's Flexbox Container
- Improved: EA Dynamic Gallery | Added support for Image URL & Image ID as a Return Format of ACF(Gallery) Field Type
- Improved: EA Post Carousel | Image & Title link enable/disable control option
- Fixed: EA Protected Content | Not Working with Element Caching (Beta) Activated in Elementor
- Fixed: EA Content Protection | Added support Elementor's Flexbox Container
- Fixed: EA Conditional Display | Recurring day feature is not working
- Fixed: EA Advanced Tooltip | Follow Cursor not working on the frontend for all options except False
- Few minor bug fixes & improvements

= 6.0.4 - 18/09/2024 =
- Improved: EA Dynamic Gallery | Added Support for ACF Gallery
- Fixed: Animation conflicting issue with Elementor
- Fixed: EA Offcanvas | Offcanvas menu delay issue while closing
- Few minor bug fixes & improvements

= 6.0.3 - 09/09/2024 =

- Improved: Added Security Enhancement
- Added: Compatibility with Element Caching
- Improved: EA Woo Product widgets | Added manual product selection option
- Fixed: EA Fancy Chart | Pie Chart Display Issue with Float Value
- Improved: EA Conditional Display | Added Conditional Logic dynamic value compare
- Few minor bug fixes & improvements

= 6.0.2 - 04/09/2024 =

- Improved: XSS Security Enhancement
- Fixed: EA Content Timeline breakpoint scrolling issue
- Fixed: EA Advanced Tab compatibility issue with Ad inserter plugin
- Fixed: EA Advanced Menu | Global colour styling issue
- Fixed: EA Login Register Form | Social login issue for Multiple widgets on same page
- Few minor bug fixes & improvements

= 6.0.1 - 29/08/2024 =

- Improved: EA Conditional Display | ACF checkbox more than one value show to set condition
- Fixed: EA Logo Carousel | Auto play animation stops on window resize or mobile view
- Fixed: EA Smart Post List | Editor panel not loading for the blog post on the WPDeveloper site
- Fixed: A PHP Error related to automatic updater
- Fixed: EA Post Carousel | Post title "target blank" doesn't open the posts on new tab
- Fixed: EA Advanced Search | Styling is not working for Category list
- Improved: Optimized Control Loading | EA Lightbox & Modal, EA Advanced Tooltip
- Improved: Accessibility Issues | EA Advanced Tooltip, EA Image Hotspots, EA Toggle
- Few minor bug fixes & improvements

= 6.0.0 - 11/08/2024 =

- Revamped: New EA Dashboard for better UI/UX
- Revamped: New EA Quick Setup for better UI/UX
- Added: New Extension- EA Dynamic Tags
- Added: New Extension- EA Interactive Animations
- Updated: EA Conditional Display with more logics focusing on WooCommerce
- Added: New Skin Presets for different Post, WooCommerce & Other widgets
- Fixed: EA Twitter Feed Carousel | Added API V2 Support
- Few minor bug fixes & improvements

= 5.8.17 - 02/07/2024 =

- Fixed: Uncaught TypeError: Cannot read properties of undefined (reading 'size')
- Fixed: EA Advanced Search | not considering the "Show initial results" option
- Fixed: EA Lightbox/Modal | Throwing the Warning: Undefined array key "eael_lightbox_closebtn_color"
- Few minor bug fixes & improvements

= 5.8.16 - 05/06/2024 =

- Improved: Security Enhancement
- Improved: EA Advanced Search | Added Custom URL/Data support for search results
- Improved: EA Post Carousel & Post Block | Added Double Fallback Image Controller
- Fixed: EA Advanced Menu | not compatible with the WP Event plugin
- Fixed: EA Advanced Menu | Dropdown icon showing twice in some cases
- Few minor bug fixes & improvements

= 5.8.15 - 22/05/2024 =

- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 5.8.14 - 05/05/2024 =

- Fixed: EA Flip Carousel | Showing pink hover background color on the navigation icon
- Fixed: EA Interactive Card | Styling for Rear panel Color & Typography is not working
- Fixed: EA Content Protection | After disabling Content Protection, a few options still visible
- Fixed: EA Smart Post list | Smart Post List not working - JS error
- Fixed: EA Sliders widgets aren't working properly while using as a saved template
- Fixed: EA Pricing Table | Icons are not rendering
- Fixed: EA Woo Product Slider | 'Add to cart' button disappears in Elementor Product widget if EA Product Slider is active on same page
- Improved: Updated the new Twitter logo (X) for widgets with the Twitter icon
- Improved: EA Image Comparison | Added image size controller
- Improved: EA Post Carousel | Overlay option fallback image
- Improved: EA Instagram Feed | Filtering feed by hashtag(#)
- Improved: EA Offcanvas | Added an option to set close(X) icon position
- Improved: EA Smart Post List | Added showing all the posts in All tabs
- Improved: Conditional Display support For EA Woo Products widgets.
- Improved: EA Advanced Search | Hiding 'All Categories' dropdown without disabling the include/ exclude option
- Few minor bug fixes & improvements

= 5.8.13 - 23/04/2024 =

- Fixed: EA Woo Product Collection | Throwing Fatal error when Show Badge option is enabled
- Fixed: EA Dynamic Gallery | HTML title on the post not rendering
- Fixed: EA Smart Post List | Navigation is not working properly
- Fixed: EA Woo Account Dashboard | Not loading properly in frontend in certain themes
- Few minor bug fixes & improvements

= 5.8.12 - 18/04/2024 =

- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 5.8.11 - 25/03/2024 =

- Fixed: EA Fancy Chart | Chart stylings aren't showing except for the bar
- Fixed: EA Price Menu | WooCommerce product price is showing raw HTML tags
- Fixed: EA LearnDash Course List | PHP and Array offset issue with Course List
- Fixed: EA Counter | Animation is not working with scroll snap
- Improved: EA Conditional Display | Add rules on show count
- Few minor bug fixes & improvements

= 5.8.10 - 13/03/2024 =

- Fixed: EA Post Carousel | Thumbnail icon is not showing for card style
- Fixed: EA Filp Carousel | Unordered list or ordered list in the content area bugs
- Fixed: EA Team Member | "Social on bottom" layout shows the HTML tag
- Fixed: EA Woo Thank You | Added download option for virtual products
- Improved: EA Fancy Chart | Symbol display option in the fancy chart
- Improved: EA Fancy Chart | Option to style legends
- Few minor bug fixes & improvements


= 5.8.9 - 11/02/2024 =

- Fixed: EA Instagram Feed | Feed is getting broken on the front-end view
- Fixed: EA Smart Post List | post excerpt doesn't show
- Fixed: Saved Template Broken issues
- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 5.8.8 - 01/02/2024 =

- Fixed: EA Smart Post List | Image is not clickable issue
- Few minor bug fixes & improvements

= 5.8.7 - 17/01/2024 =

- Fixed: EA Advanced Menu | Throwing Fatal error when Hamburger option is enabled for Desktop device
- Few minor bug fixes & improvements

= 5.8.6 - 14/01/2024 =
- Fixed: EA Advanced Menu | SVG icon loading issue
- Fixed: EA Content Timeline | Typography Issue
- Fixed: EA Interactive Cards | Vimeo video visibility issue
- Fixed: EA Smart Post List | No Context Empty link creating issue
- Fixed: EA Instagram Feed | Border radius issue
- Improved: EA Particles Extension | Added support to control speed and opacity
- Few minor bug fixes & improvements

= 5.8.5 - 21/12/2023 =

- Improved: License Mechanism
- Few minor bug fixes & improvements

= 5.8.4 - 14/12/2023 =

- Fixed: EA Event Calendar | Event Details link is showing wrong for EventON source
- Fixed: EA Testimonial Slider | Read more button is not expanding fully
- Fixed: EA Post Block | Unable to full-width post block section
- Fixed: EA Login/Register Form | Using Elementor Flex container, when enabling "Show Field Icons" the facebook icon shifts
- Fixed: EA Image Comparison | Image Comparison Widget space issue
- Fixed: EA Woo Checkout | Woo Commerce 'don't allow PO box feature' isn't working with Split/Multi-step layouts
- Fixed: EA Flip Carousel | Arrows aren't showing the same on all pages
- Fixed: EA Advanced Tooltip | Inside Elementor Popup tooltip not working
- Fixed: EA Content Timeline | Horizontal timeline does not scroll properly on mobile view.
- Fixed: EA Smart Post List | Post Columns default value not present in responsive
- Improved: EA Advanced Search | Add option to hide the "Search" Button
- Improved: EA Advanced Data Table | colspan for Header rows are not implemented
- Improved: EA Smart Post List | Most Visited posts option
- Improved: EA Dynamic Gallery | Prevent multiple clicks for Load More
- Few minor bug fixes & improvements

= 5.8.3 - 27/11/2023 =

- Fixed: EA Login/Register Form | Facebook Login not working
- Fixed: EA Login/Register Form | Google sign-in creates an account, even with registration off
- Improved: EA Conditional Display | Dynamic tag => Post terms and Product terms
- Improved: EA Login/Register Form | On Google 'Create An Account', user's First Name and Last Name does not get populated
- Few minor bug fixes & improvements

= 5.8.2 - 07/11/2023 =

- Fixed: EA Smart Post List | Custom post terms not showing
- Fixed: EA Smart Post List | Topbar Background colour controller is missing
- Fixed: EA Woo Thank you | Woo Thank you widget - instruction does not display
- Fixed: EA Toggle | Toggle is showing the box shadow while focusing on iPhone 12 or later versions
- Improved: EA Smart Post List | Columns responsive unavailable at Tablet and Mobile Devices
- Improved: EA Post Carousel | Post Carousel Overlay option
- Improved: EA Advanced Search| Added an option to change "eael-advanced-search-category" and "eael-advanced-search-popular-keyword" from h4 tag to span/p
- Added: All EA WooCommerce Related Products inside Elementor Theme Builder Archive & Single Product Templates
- Few minor bug fixes & improvements

= 5.8.1 - 26/10/2023 =

- Improved: EA Fancy Chart | Added more variants for Bar, Line & Area Chart
- Few minor bug fixes & improvements

= 5.8.0 - 22/10/2023 =

- Added: New Widget | EA Fancy Chart
- Few minor bug fixes & improvements

= 5.7.0 - 09/10/2023 =

- Added: New Widget | EA Woo Account Dashboard
- Few minor bug fixes & improvements

= 5.6.2 - 02/10/2023 =

- Fixed: EA LearnDash Course List | While filtering the courses the images/courses are overriding each other and some courses are disappearing
- Fixed: EA LearnDash Course List | Exclude feature not working properly
- Few minor bug fixes & improvements

= 5.6.1 - 14/09/2023 =

- Improved: Security Enhancement
- Added: WooCommerce HPOS Support
- Few minor bug fixes & improvements

= 5.6.0 - 07/09/2023 =

- Added: New Widget | EA Woo Cross Sells Widget
- Few minor bug fixes & improvements

= 5.5.4 - 24/08/2023 =

- Fixed: EA Advanced Search | Compatibility issue with WPML
- Fixed: EA Content Timeline | Throwing Error on PHP <= 7.2
- Fixed: EA Advanced Data Table | TablePress content line breaking issue
- Fixed: EA Woo Checkout | Zip/Post code showing invalid for different Shipping and Billing addresses
- Few minor bug fixes & improvements

= 5.5.3 - 10/08/2023 =

- Improved: EA Content Timeline | Added Scrollbar Styling support for Horizontal layout
- Improved: EA Testimonial Slider | Added Option for number of slider items change on click
- Improved: EA LearnDash | Add Load more and Paginations support for the courses
- Improved: EA Conditional Display | Added support to set Conditions based on URL variables
- Improved: EA Flip Carousel | Added support for inserting Text content
- Improved: EA Lightbox & Modal | Accessibility improvements.
- Improved: EA Protected Content | Added Dynamic field support for Password
- Fixed: EA Interactive Promo | Added WMPL support for Link URL
- Fixed: EA Testimonial Slider | Overlapping issue with Fade transition effect
- Fixed: EA Content Timeline | Responsive issue on horizontal layout
- Fixed: EA Advanced Menu | Breakpoint and Responsive issues for the Layouts
- Fixed: EA Off Canvas Menu | Added support to add only icon in the button
- Fixed: EA Content Timeline | Caret and Bullet alignment issue in Custom content source
- Few minor bug fixes & improvements

= 5.5.2 - 03/08/2023 =

- Fixed: EA Smart Post List | showing 0 on the search result
- Fixed: EA Offcanvas | Not saving changes when used it with Advanced Accordion with Flexbox container
- Fixed: EA Counter | Number font family not working
- Fixed: EA Tooltip | Starting to offset to the right of the icon when increasing tooltip width
- Fixed: EA Offcanvas | Country and State dropdown not working when adding it on checkout page
- Few minor bug fixes & improvements

= 5.5.1 - 27/07/2023 =

- Fixed: EA Protected Content | Showing the direct URL of the Youtube instead of the video itself
- Fixed: EA Offcanvas | H3 Tag visible on the page if added without the title
- Few minor bug fixes & improvements

= 5.5.0 - 16/07/2023 =

- Added: New Widget- EA Woo Thank You
- Few minor bug fixes & improvements

= 5.4.12 - 06/07/2023 =

- Improved: EA Content Protection | Added scrolling control for password field
- Improved: Added PHP 8.2 Compatibility Support
- Fixed: Swiper JS issue with All the Carousel Widgets
- Few minor bug fixes & improvements

= 5.4.11 - 01/06/2023 =

- Improved: EA Protected Content | Error message offset settings and Style support
- Fixed: EA EA Mailchimp | Form submission giving undefined error issue
- Few minor bug fixes & improvements

= 5.4.10 - 22/05/2023 =

- Improved: EA Content Timeline | Added Anchor link Support for Dynamic Post's Feature Image
- Improved: EA LearnDash Course List | Added Dynamic Tag support for Single Course Template
- Fixed: EA Post Grid | Undefined array key issue
- Fixed: EA Content Timeline | HTML tag issue in the Post Date field
- Fixed: EA Woo Product Carousel | Support for Upgraded Swiper Library
- Fixed: EA Lightbox & Modal | Added HTML tag support for Button text
- Fixed: EA Woo Checkout | Compatibility issue with "The7" theme
- Few minor bug fixes & improvements

= 5.4.9 - 11/05/2023 =

- Improved: EA Login/Register Form for Security Enhancement
- Few minor bug fixes & improvements

= 5.4.8 - 17/04/2023 =

- Fixed: EA LearnDash Course List | Fixed "No course found!" Transition issue
- Fixed: EA One Page Navigation | Active status not showing while Scrolling
- Improved: EA Smart Post List | Added 4 Columns support for List Posts
- Few minor bug fixes & improvements

= 5.4.7 - 05/04/2023 =

- Fixed: Throwing fatal error when using EA Advanced Data Table with Google Sheet on PHP 8.0 or higher
- Few minor bug fixes & improvements

= 5.4.6 - 22/03/2023 =

- Fixed: EA Advanced Google Map | Callback parameter issue
- Fixed: EA Woo Checkout | Validation issue for post code
- Fixed: EA Woo Product Slider | Added CartZilla theme compatibility
- Fixed: Critical Error when running Elementor Data Updater
- Few minor bug fixes & improvements

= 5.4.5 - 07/02/2023 =

- Fixed: EA Post Block | not showing "Read More" after enabling "Show Load More" button
- Fixed: EA Advanced Data Table | Throwing fatal error when using Google Spreadsheet without nameRange
- Few minor bug fixes & improvements

= 5.4.4 - 25/01/2023 =

- Fixed: EA Post Carousel | Avatar icon alignment not working
- Fixed: EA Advanced Search | Popular keyword not working with 3 letters
- Fixed: EA Woo Checkout | Translation not working perfectly in Multistep or Split layout
- Fixed: EA Post Carousel | Excluded the unusual CSS margin
- Fixed: EA Content Timeline | HTML syntax not working in title
- Fixed: EA Advanced Menu | Throwing Uncaught type error
- Improved: EA MailChimp | Addded Double Opt-in API
- Improved: EA Advanced Menu | Dropdown menu should remain opened or closed when clicking on the menu item area
- Few minor bug fixes & improvements

= 5.4.3 - 22/12/2022 =

- Fixed: EA Login/Register Form | Social login not working
- Improved: EA Image Hotspots | Delay time added to the tooltip
- Few minor bug fixes & improvements

= 5.4.2 - 22/11/2022 =

- Improved: EA Advanced Menu | Improved Compatibility with custom ID links on menu items
- Improved: EA Advanced Search | Responsive device view
- Few minor bug fixes & improvements

= 5.4.1 - 27/10/2022 =

- Fixed: EA Login / Register Form | Security token did not match error

= 5.4.0 - 26/10/2022 =

- Improved: EA Content Timeline | Added “Horizontal” layout
- Improved: EA Smart Post List | Scroll to the top after clicking on the pagination
- Improved: EA Instagram Feed| Added option to control the caption length
- Improved: EA Conditional Display | Multiple date and time picker
- Improved:  EA Dynamic Gallery | Added option to make Image clickable
- Improved: EA LearnDash Course List | Added Exclude options
- Fixed: EA Content Timeline | Excerpt color not changing
- Fixed: EA Advanced Search | Category dropdown icon being broken
- Fixed: EA One page navigation | Strings to being translatable with WPML
- Fixed: EA One Page Navigation | Custom SVG icons not getting the styles properly
- Fixed: EA Advanced Menu | Dropdown menu not closing automatically when another one is opened
- Fixed: EA LearnDash Course List | sorting option not working properly
- Fixed: EA Advanced Tooltip | Showing in the bottom section despite setting position at top
- Fixed: EA Smart Post List | Featured Post not showing on custom templates
- Fixed: EA Testimonial Slider | {<br>} tag is not working in the username and company field
- Fixed: EA Login/Register Form | Showing 'You did not pass reCAPTCHA challenge.' error even if it is passed
- Fixed: EA Team Member Carousel | Custom Break Point not working
- Fixed: EA Advanced Search | Media file search not working
- Few minor bug fixes & improvements

= 5.3.1 - 21/09/2022 =

- Fixed: Throwing fatal error due to conflict with custom breakpoints & older Elementor versions
- Few minor bug fixes & improvements

= 5.3.0 - 21/09/2022 =

- Improved: EA Cross-Domain Copy Paste | Added Full Page Copy/Paste option
- Improved: EA Login/Register Form | Added MailChimp Integration for registration
- Improved: EA Login/Register Form | Added ReCaptcha v3 option
- Improved: EA Testimonial Slider | Added "Read More" Button for long content
- Improved: EA Advanced Search | Added option to define categories in the Editor and many more
- Improved: EA Advanced Menu | Added Custom breakpoints for responsive devices
- Fixed: EA LearnDash Course List | Fit to screen layout not working properly
- Fixed: EA Advanced Data Table | Number 0 is showing blank for Google Sheets as source
- Fixed: EA Post Carousel | Custom Break Point not working properly
- Fixed: EA Advanced Google Map | Themes not working on Polygon type Map
- Fixed: EA Dynamic Gallery | Items not loading properly if multiple galleries are used in the same page
- Fixed: EA Smart Post List | Throwing Fatal error when custom post type is selected as source
- Few minor bug fixes & improvements

= 5.2.1 - 29/08/2022 =

- Fixed: Elementor Paste Style not working when EA Conditional Display is activated
- Fixed: EA Login | Register Form | Missing translatable strings
- Few minor bug fixes & improvements

= 5.2.0 - 11/08/2022 =

- Improved: Asset loading mechanism for better performance
- Few minor bug fixes & improvements

= 5.1.6 - 01/08/2022 =

- Fixed: EA Smart Post List | Filters not working on responsive devices
- Fixed: EA LearnDash Course List | Undefined index error on legacy mode
- Fixed EA LearnDash Course List | Throwing Invalid taxonomy error
- Few minor bug fixes & improvements

= 5.1.5 - 05/07/2022 =

- Fixed: EA Login/Register Form | Google Login not working
- Few minor bug fixes & improvements

= 5.1.4 - 27/06/2022 =

- Fixed: EA Post Block & EA Post Carousel | Image Sizes being Changed because of the image ratio option
- Few minor bug fixes & improvements

= 5.1.3 - 14/06/2022 =

- Improved: EA LearnDash Course List | Added option to display categories, tags and enrolled labels
- Improved: EA Content Timeline | Added option to display feature images for dynamic posts
- Improved: EA advanced Search | Added option to "include" a certain category
- Improved: EA MailChimp | Added tags option in the MailChimp form
- Improved: EA Post Block | Added option to display Post Terms
- Fixed: EA Advanced Search | Search functionality is not working on mobile devices
- Fixed: EA Content Timeline | Content not showing if different characters such as % is used
- Fixed: EA Post Block | Throwing PHP Notice
- Fixed: EA Testimonial Slider | Description not being translatable
- Fixed: EA Dynamic Gallery | Category filters not loading properly
- Fixed: EA Image Hotspots | Link not removing from Tooltip hover
- Fixed: EA Twitter Feed Carousel | Replies being displayed in tweets
- Few minor bug fixes & improvements

= 5.1.2 - 25/05/2022 =

- Added: WordPress 6.0 Compatibility
- Improved: EA Team Member Carousel | Added Custom URL option to make images clickable for each team members
- Fixed: EA Logo Carousel | Responsive Breakpoints not working
- Fixed: EA Advanced Search | The border radius not working for search field style 2 or 3
- Fixed: EA Lightbox & Modal | Responsive settings not working properly
- Fixed: EA Image Hotspots | Throwing error notice when hotspot type is text or blank
- Fixed: EA Lightbox & Modal | Customized Height not working properly
- Fixed: EA Post Block | Title Tag not changing properly
- Few minor bug fixes & improvements

= 5.1.1 - 10/05/2022 =

- Fixed: EA Dynamic Gallery | Posts under child categories only loading inside "All" tab
- Few minor bug fixes & improvements

= 5.1.0 - 19/04/2022 =

- Added: EA Conditional Display extension
- Few minor bug fixes & improvements

= 5.0.10 - 12/04/2022 =

- Fixed: EA Twitter Feed Carousel | Latest feed data not showing automatically
- Few minor bug fixes & improvements

= 5.0.9 - 23/03/2022 =

- Fixed: “_register_skins is depcrecated” error
- Fixed: EA Post Carousel | Responsive Breakpoints not working
- Fixed: EA Interactive Cards | Rear side video content continuously playing despite closing it
- Fixed: EA Image Hotspots | Popup dynamic field not showing in the content
- Few minor bug fixes & improvements


= 5.0.8 - 08/03/2022 =

- Fixed: EA Advanced Data table | When Remote database with different language is used as source
- Fixed: EA Advanced Data Table | TablePress integration not working properly
- Fixed: EA Image Hotspots | Tooltip background color not changing when global color is used
- Fixed: EA Post Widgets | Extra attributes showing after load more button is pressed
- Few minor bug fixes & improvements

= 5.0.7 - 16/02/2022 =

- Fixed: EA Login/Register Form | Social login error message not working properly
- Fixed: EA Offcanvas | Entrance Animation not working on Google Chrome Mac OS
- Fixed: EA Post Carousel | Conflict with Beehive Theme
- Fixed: EA Woo Product Slider | Some Style options not working properly
- Fixed: EA Image Comparison | Divider width and handle icon color not working in vertical mode
- Fixed: EA Image Hotspots | Popup controller not showing in Content tab Link option
- Fixed: EA Post Carousel | Post Terms not working for the Products
- Fixed: EA Testimonial Slider | Visible items not working properly for responsive devices
- Fixed: EA Tooltip & Image Hotspots | Viber link not working
- Fixed: EA Logo Carousel | Conflict with Lazy Loader
- Fixed: EA Advanced Menu | Hamburger menu not showing in mobile view
- Fixed: EA Smart Post List | Filtering option not working properly
- Few minor bug fixes & improvements

= 5.0.6 - 02/02/2022 =

- EA Advanced Google Map | "Add Item" button not working in Polygon type
- Few minor bug fixes & improvements

= 5.0.5 - 27/01/2022 =

- Few minor bug fixes & improvements

= 5.0.4 - 27/01/2022 =

- Fixed: EA Parallax | Throwing Console Error
- Fixed: EA Advanced Search | Throwing PHP Warning
- Few minor bug fixes & improvements

= 5.0.3 - 25/01/2022 =

- Fixed: EA Lightbox & Modal | Modal text adding span tag at the end after pagination
- Added: EA Advanced Search | Exclude taxonomy option
- Few minor bug fixes & improvements

= 5.0.2 - 19/01/2022 =

- Improved: EA Advanced Data Table | SQL Queries for Database source
- EA Testimonial Slider | Throwing error on PHP 8.0
- Few minor bug fixes & improvements

= 5.0.1 - 30/12/2021 =

- Few minor bug fix & improvements

= 5.0.0 - 26/12/2021 =

- Added: EA Advanced Search
- Added: EA Cross-Domain Copy Paste
- Few minor bug fix & improvements

= 4.4.12 - 14/12/2021 =

- Improved: EA LearnDash Course List | Added controller to change the “Free” and “See More” texts
- Fixed: EA Static Product | add to cart not working
- Fixed: EA Instagram Feed | Force square option not stretching the images
- Few minor bug fix & improvements

= 4.4.11 - 11/10/2021 =

- Fixed: EA Static Product | Throwing Fatal Error when translating with WPML
- Fixed: EA Static Product | Typography options not working properly
- Fixed: EA Protected Content | Not working properly when saved template is used as content
- Fixed: EA Advanced Menu | Sub-menu not expanding in vertical layout
- Fixed: EA LearnDash Course List | Video Thumbnail Not Working
- Few minor bug fix & improvements

= 4.4.10 - 26/09/2021 =

- Fixed: EA Advanced Google Map | Map Icon color not working
- Improved: EA Instagram Feed | Added 'alt' text in image tag
- Fixed: EA Offcanvas Menu | not working on "question marked" URL
- Fixed: EA Lightbox & Modal | not working if image is selected as trigger.
- Few minor bug fix & improvements

= 4.4.9 - 01/09/2021 =

- Fixed: EA Smart Post List | Date Format not getting from WordPress Setting
- Fixed: Clicking on the arrow stopping all the Sliders
- Fixed: EA Offcanvas Menu | not working on "question marked" URL
- Fixed: EA Lightbox & Modal | not working if image is selected as trigger.
- Few minor bug fix & improvements

= 4.4.8 - 19/08/2021 =

- Fixed: EA Toggle | Secondd toggle doesn't work if Nested template is used
- Fixed: EA Smart Post List | Manual ordering not working
- Fixed: EA Smart Post List | Styling issue on 9th post
- Fixed: EA Lightbox & Modal | Not working with icon/image type
- Few minor bug fix and improvements

= 4.4.7 - 08/08/2021 =

- Fixed: EA Learndash Course List | throwing PHP Warning
- Fixed: EA Smart Post List | Custom Template not loading from theme
- Added: EA Team Member Carousel | Options to adjust height and width
- Removed: Unused TweenMax library from EA Parallax
- Few minor bug fix and improvements

= 4.4.6 - 27/07/2021 =

- Fixed: EA Smart Post List | 'Show Category' option not functioning properly
- Fixed: EA Woo Product Slider | Draft products being displayed on Edit Mode
- Fixed: EA Woo Product Slider | 'Quick View' popup not showing properly when multiple images are used
- Fixed: EA Dynamic Gallery | "Load More" not working when custom template is used from Theme
- Fixed: EA Woo Check Out | Broken 'Multi-Steps' layout in Neve theme
- Fixed: Conflict with Jetpack with Slider elements
- Few minor bug fix and improvements

= 4.4.5 - 18/07/2021 =

- Fixed: EA Lightbox & Modal | linked to the same post
- Fixed: EA Image Hotspots | Safari browser crashing when links are set to "Open in New Window"
- Fixed : EA Creative Buttons | Style controls not working properly for "Winona" & "Rayen" effects
- Fixed: EA Woo Product Carousel | swiperslider conflict with Jetpack
- Few minor bug fix and improvements

= 4.4.4 - 07/07/2021 =

- Fixed: EA Interactive Cards | no option to change heading tag for Counter text
- Fixed: EA Image Comparison | On Hover setting not working on Frontend
- Few minor bug fix and improvements

= 4.4.3 - 29/06/2021 =

- Fixed: EA Parallax | Broken in Safari browser
- Fixed: EA Testimonial Slider | Image not being centered in some Styles
- Fixed: EA Image Hotspot | Showing black border when the tooltip is enabled
- Improved: EA Woo Product Slider | Added buttons shadow, image size change option and add to cart style options for preset 3
- Improved: EA Team Member Carousel | Added Custom Social Icon option
- Few minor bug fix and improvements

= 4.4.2 - 15/06/2021 =
- Improved: WPML Compatibility
- Fixed: EA Smart Post List | Filtering not working
- Fixed: EA Content Protection | Not working properly if a certain user has multiple roles
- Few minor bug fix and improvements

= 4.4.1 - 07/06/2021 =
- Removed: Elementor deprecation methods
- Added: Compatibility with PHP 8.0
- Improved: EA Team Member Carousel | Added Snapchat & XING Social link options
- Improved: Added Target_Blank options in post widgets
- Improved: EA Testimonial Slider | Move navigation arrows outside of the box
- Fixed: EA Post Block | Hover Card Style not working
- Fixed: EA Dual Color Heading |Font Gradient color not changing
- Fixed: EA Woo Product Slider | Quick View not showing properly
- Few minor bug fix and improvements

= 4.4.0 - 09/05/2021 =
- Added: EA Woo Product Carousel
- Few minor bug fix and improvements

= ******* - 03/05/2021 =
- Version issue fixed

= 4.3.9 - 03/05/2021 =

- Improved: Query Optimization in dynamic widgets
- Fixed: Conflict with swiperJS
- Fixed: EA Advanced Google Map | showing wrong info for some markers on mobile screens
- Fixed: EA Image Comparison | not loading properly initially
- Fixed: EA LearnDash Course list | Image quality issue
- Fixed: EA Smart Post List | Uploaded SVG Icon not appearing
- Fixed: EA Smart Post List | Search result under Featured Post Settings not working for CPT
- Few minor bug fix and improvements

= 4.3.8 - 25/04/2021 =

- Fixed: Elementor\Scheme_Typography is deprecated
- Few minor bug fix and improvements

= 4.3.7 - 13/04/2021 =

- Fixed: EA Creative Button | SVG color not changing
- Few minor bug fix and improvements

= 4.3.6 - 11/04/2021 =

- Added: Cache Settings for EA Twitter Feed Carousel & Instagram Feed
- Fixed: EA Content Timeline | Custom Icon not working properly
- Fixed: EA Lightbox & Modal | Multiple lightboxes redirecting to the same content
- Fixed: EA Instagram Feed | ‘Sort By’ setting not being functional
- Fixed: EA Instagram Feed | Same row repetition when "load more" is clicked
- Fixed: EA Login/Register Form | Google Login will not redirect to the assigned login redirect link
- Fixed: EA Protected Content | missing hover controls
- Few minor bug fix and improvements

= 4.3.5 - 23/03/2021 =

- Fixed: PHP error related to HTML tags validation
- Few minor bug fix and improvements

= 4.3.4 - 22/03/2021 =

- Added: Compatibility with Elementor latest versions
- Fixed: Sanitized options in the Elementor HTML Tags
- Few minor bug fix and improvements

= 4.3.3 - 03/03/2021 =

- Fixed: EA Content Timeline | Title Tag not working
- Fixed: EA Post Carousel | categories not showing
- Fixed: EA Post Carousel | titles typography not working properly
- Fixed: EA Advanced Data Table | Google sheet data not showing properly
- Fixed: EA Interactive Card | height not working if percentage value is used
- Fixed: EA Login/Register Form | reCaptcha not working when used inside Elementor popup
- Fixed: EA Toggle | Throws Console error if you keep Button Label blank
- Fixed: EA Dynamic Gallery | ‘Show content’ and Typography not working properly
- Few minor bug fix and improvements

= 4.3.2 - 23/02/2021 =

- Fixed: Redirect issue for Single Site Licenses
- Improved: EA Advanced Menu | Added menu alignment options, background colors & more
- Fixed: EA Advanced Google Map | Zoom not working for ‘Multiple Markers’
- Added: EA Image Comparison & EA Interactive Promo | added Dynamic tags
- Fixed: EA Smart Post List | Excerpt not showing for 9th post
- Fixed: EA Dynamic Gallery | not working for LearnDash courses
- Fixed: EA Content Timeline | Title Style controller not working when anchor tag is not used
- Fixed: EA Protected Content | Content not support links from video sources
- Fixed: EA MailChimp | Placeholder text not translatable
- Fixed: EA Dynamic Gallery | Excerpt not displaying when Filter control is turned off
- Few minor bug fix and improvements

= 4.3.1 - 28/01/2021 =

- Fixed: Fixed: EA Instagram Feed | Force square image option not working
- Few minor bug fix and improvements


= 4.3.0 - 26/01/2021 =

- Fixed: EA Protected Content | Removed redirection if cookie is set
- Fixed: EA Advanced Google Map | Lat/Long error when default value is null
- Fixed: EA Dynamic Gallery | Category filter not working for pages
- Fixed: EA Smart Post List | Preset 2 & 3 not working
- Few minor bug fix and improvements

= 4.2.4 - 10/12/2020 =

- Fixed: EA Toggle | not working on WordPress 5.6
- Fixed: EA Protected Content | 'URL Redirection' error
- EA Content Timeline | Throws PHP error if Read More is Disabled
- Fixed: EA Image Hotspots | 'Animation Out' effect not working
- Few minor bug fix and improvements

= 4.2.3 - 19/11/2020 =

- Fixed: Broken style if Object Cache is enabled
- Fixed: Pages showing blank after updating with Elementor
- Added: RTL Support
- Improved: EA Smart Post List | New Preset Layouts
- Improved: EA Post Block | New Preset Layouts
- Few minor bug fix and improvements

= 4.2.2 - 12/11/2020 =

- Added: Dynamic Tag/ACF Support for most widgets
- Tweaked: Updated Repeater Control with Elementor 3.0
- Fixed: PHP Error with EA Lightbox, Pricing Table & Woo Product Collections
- Few minor bug fix and improvements

= 4.2.1 - 22/10/2020 =

- Fixed: Critical Errors related to Template queries
- Fixed: EA Instagram Feed | Caching issue
- Fixed: EA Interactive Promo | missing icon
- Few minor bug fix and improvements

= 4.2.0 - 12/10/2020 =

- Revamped: Code Structure for better performance
- Improved: Asset Generator method
- Improved: Slow queries for Dynamic widgets
- Added: Dynamic support for Post Widgets
- Fixed: EA Advanced Tooltip throwing PHP error
- Fixed: EA Parallax | Multi-Layer effect not working
- Few minor bug fix and improvements

= 4.1.6 - 03/09/2020 =

- Added: Social Login for EA Login | Register Form
- Added: Password Strength option for EA Login | Register Form
- Few minor bugfix and improvements

= 4.1.5 - 31/08/2020 =

- Fixed: Fatal error with Elementor 3.0.x when EA Parallax is activated

= 4.1.4 - 30/08/2020 =

- Fixed: Console error while deleting section and columns

= 4.1.3 - 20/08/2020 =

- Fixed: EA Instagram Feed | Database Cache issue
- Added: Compatibility with WooCommerce 4.4.1
- Few minor bugfix and improvements

= 4.1.2 - 13/08/2020 =

- Fixed: EA Toggle not working with WordPress 5.5
- Few minor bugfix & improvements

= 4.1.1 - 30/07/2020 =

- Fixed: EA Instagram Feed not showing Videos
- Fixed: EA Instagram Feed post limit issue
- Fixed: EA Woo Checkout Coupons showing despite disabling from Woo Settings
- Fixed: EA Twitter Feed Carousel | Content length issue
- Removed: Duplicate separators from EA Advanced Menu
- Few minor bugfix and improvements

= 4.1.0 - 16/07/2020 =

- Added: New Asset Embed Method with External/Internal JS & CSS support
- Fixed: EA Instagram Feed API Issue
- Fixed: EA Team Member Carousel | PHP Error Notice
- Fixed: Free version not installing automatically
- Few minor bugfix and improvements

= 4.0.1 - 17/06/2020 =

- Improvement : EA Post Carousel | Added margin & padding controls for Thumbnail & Content, and 2 more preset layouts
- Improvement : EA Logo Carousel | Added hover background controls for Logos, and styling options for navigation arrows
- Improvement: EA Image Comparison | Added controls to change the Image Overlay color
- Improvement: EA Team Member Carousel | Added Gradient Background controls, and padding controls
- Fixed: EA Image Hotspots images getting stretched
- Fixed: EA Google Map not showing street view icon
- Fixed: EA Woo Product Collections typos
- Fixed: EA LearnDash Course List causing Fatal error when category is hidden
- Fixed: EA Protected Content issue with Input field placeholder color not changing
- Lots of minor bugfix and improvements

= 4.0.0 - 21/05/2020 =

- Added: New Layouts: Split and Multi Steps for EA Woo Checkout
- Added: Dynamic Link option for EA Interactive Promo
- Added: Hamburger Menu Styling options for EA Advanced Menu
- Improvement: Code Splitting for better performance
- Fixed: Image not taking the full width of container in EA Image Hotspots
- Fixed: EA Toggle not working for Twenty Twenty theme
- Fixed: EA Content Toggle showing PHP Errors
- Fixed: EA Lightbox & Modal bugs
- Fixed: EA Smart Post List- Read More & Navigation issues
- Fixed: Icon color not changing for EA Google Map
- Few minor bug fix and improvements

= 3.6.4 - 28/04/2020 =

- Improvements - Offcanvas element new feature and bugfix

= 3.6.3 - 12/04/2020 =

- Improvements - EA Post Block added individual control over author/categories/date
- Post Carousel > Added multiple Tag support for headings
- Image Hotspot > Responsive feature for mobile devices
- Improvements - EA Interactive Promo
- Instagram Gallery > Load More Button text changes bug fixed.
- Facebook Feed > Load More Button text changes bug fixed.
- Improvements - EA Particles > Need to add an option to disable the particles in Mobile devices.
- Event Calendar > Fixed Hide end date in eventon
- Event Calendar > Cross Event in eventon when event in completed

= 3.6.2 - 29/03/2020 =

- Fixed : Post Carousel issue

= 3.6.0 - 25/03/2020 =

- New Integration: EventOn with Event Calendar
- Updated: New widget icons in elementor panel
- Added: Helper link for each widget in elementor panel
- Added: FontAwesome supports for Offcanvas
- Added: Option for showing 'Categories & Tags' for Post Carousel
- Added: 'Visible Items' feature only for 'Slide' and 'Coverflow' effects in Carousels
- Added: Responsive control for EA POST GRID
- Added: Text display option in hover style for EA Dynamic Gallery
- Fixed: Off Canvas left and right transitions
- Fixed: LearnDash course list bugs
- Fixed: Mobile Responsiveness issue for EA Post Carousel
- Fixed: EA Post Carousel & EA Post Timeline excerpt bug
- Fixed: 1 column bug in Smart Post List
- Fixed: HTML tag not working bug in EA Content Timeline Custom Content
- Few minor bugfix and improvements

= 3.5.3 - 15/03/2020 =

- Fixed: Smart Post List | Bug with 1 column
- Fixed: EA Post Carousel | Visible Items only shows upto 2 posts
- Fixed: Post grid with conflict with post carousel
- Few minor bugfix and improvements

= 3.5.2 - 08/03/2020 =

- Fixed: Mailchimp list limit issue
- Fixed: Dynamic Gallery hover issue
- Fixed: Divider height width issue
- Fixed: Advanced Table error if no table selected
- Fixed: Lightbox compatibility with Elementor 2.9.x
- Improved: Image Hotspot and Tooltip select2 control

= 3.5.1 - 01/03/2020 =

- Fixed: Mailchimp button issue

= 3.5.0 - 24/02/2020 =

- Added: Google Sheets, and TablePress integration in EA Advanced Data Table
- Added: ul, ol support in EA Interactive Promo
- Fixed: EA Smart Post List pagination not working
- Fixed: Multiple Content Protection Widget & Extension conflict in a page
- Fixed: Mailchimp subscription button not working
- Fixed: EA Content Timeline excerpt indicator not working
- Fixed: EA Testimonial Slider centre align not working
- Fixed: PHP deprecated method notice
- Fixed: EA Testimonial Slider flip effect not showing correctly
- Fixed: EA Twitter Carousel pagination on Tablet & Mobile devices
- Fixed: EA Content Timeline headings are link by default

= 3.4.2 - 10/12/2019 =
- Bugfix: EA Divider vertical layout fix
- Bugfix: EA Dynamic Gallery >> Source - 'Page' wasn't fetching pages
- Few minor bugfix and improvements

= 3.4.1 - 27/11/2019 =
- Bugfix: Particle error fixed.
- Improvement: Height Control added on content elements.
- Bugfix: Offcanvas fontawesome icon issue fixed.
- Improvement: Team Member Carousel social link open in new tab.
- Improvement: Interactive Card's close button is visible now.
- Few minor bugfix and improvements

= 3.4.0 - 31/10/2019 =
- Revamped: Instagram Feed with new graph API
- Added : Creative button icon condition
- Fixed : Media Carousel Conflict
- Fixed : Advanced Tooltip issue
- Fixed : SVG Icon issues for Icon Manager
- Fixed: Image Comparision not loading sometime
- Improved : Post element CHOOSE controls are changed with the SWITCHER
- Few minor bugfix and improvements

= 3.3.1 - 18/09/2019 =
- Fixed: Post List possible error
- Few minor bugfix and improvements

= 3.3.0 - 15/09/2019 =
- Improved : All post queries optimized to improve the load time and memory usage
- Improved : Icon Manager optmized for better performance
- Added : Content Timeline left and right layout
- Fixed: Instagram gallery layout issue
- Fixed: Instagram gallery layout issue
- Fixed: Tooltip tag issue fixed
- Fixed: Google Map polyline, polygon, routes fix
- Fixed: LearnDash masonry layout breaking issue
- Lots of minor bugfix and improvements

= 3.2.0 - 19/08/2019 =
- New : Icon Manager updated with Font Awesome 5
- New : LearnDash Course List element added
- Fixed: Image Hotspots link issue
- Few Minor Bugfix and Improvements

= 3.2.0 - 19/08/2019 =
- New : Icon Manager updated with Font Awesome 5
- New : LearnDash Course List element added
- Fixed: Image Hotspots link issue
- Few Minor Bugfix and Improvements

= 3.1.1 - 10/07/2019 =
- Twitter Feeds and Twitter Carousel updated with new API for better performance
- Added : WPML Support for "Load More" in post elements
- Few Minor Bugfix and Improvements

= 3.1.0 - 03/07/2019 =
- New Extension : Content Protection
- Added : WPML Support for most of the elements (rest are coming in next update)
- Added : Post Block, Post Grid, Post Carousel read more button
- Fixed : Image Hotspot link not working
- Fixed : License deactivation issue
- Few Minor Bugfix and Improvements

= 3.0.5 - 16/06/2019 =
- Added : Manual post selection option to all Post Elements
- Fixed : Interactive card video issue
- Fixed : License unvalidation issue
- Enhanced : Instagram Feed with API instead of JS library dependency
- Few Minor Bugfix and Improvements

= 3.0.4 - 04/06/2019 =
- Fixed : Particle effects issue
- Fixed : Interactive Promo link issue
- Fixed : Smart Post List issue
- Fixed : Woo Collections error
- Few Minor Bugfix and Improvements

= 3.0.3 - 29/05/2019 =
- Added : ALT tag for all images throught the plugin
- Fixed : Post List excerpt issue
- Enhancement : Image Comparison element script improved
- Few Minor Bugfix and Improvements

= 3.0.2 - 29/05/2019 =
- Fixed : Plugin automatic updater
- Fixed : Parallax error
- Few Minor Bugfix and Improvements

= 3.0.1 - 27/05/2019 =
- Fixed : Ajax Post Search
- Few Minor Bugfix and Improvements

= 3.0.0 - 23/05/2019 =
- Global Modular Control
- Clear Cache Tool
- Code Structure Revamped
- Only one minified JS and one CSS file for all the elements
- No extra asset will be loaded if EA isn't used on any page
- Fixed: IE Compatibility
- Fixed: EA Dynamic Gallery Product Query
- Few Minor Bugfix and Improvements

= 2.15.0 =
- Added : Cookie Consent element
- Added : Dismissible Section element
- Added : Global Tooltip extension
- Added : Smart post list advanced layout
- Fixed : Filterable gallery layout issue
- Fixed : Menu breaking issue fixed
- Few minor bugfix and improvements

= 2.14.2 =
- Particles z-index issue fixed
- Fixed: Product grid broken on some theme
- Fixed: Product grid layout conflict with woo layout
- Added: Logo Carousel alt tag
- Added: Advanced menu skins
- Few minor bugfix and improvements

= 2.14.1 =
- License issue fixed
- Advanced Menu more features added
- Few minor bugfix and improvements

= 2.14.0 =
- Particles (Section) added
- Parallax (Section) added
- Advanced Menu element added
- Woo Product Collections element added
- Image Scroller element added
- Product Grid new layouts added
- Fancy text loop settings improved
- Filterable gallery layout and load more issue fixed
- Twitter feed masonry layout issue
- Tweet mode to show the whole tweet
- Cart button added for post elements to support product type properly
- Filterable gallery capability to remove 'All' label
- Advanced Map improved
- Lots of minor bugfix and improvements

= 2.13.2 =
- Advanced Map control buttons issue fixed
- Mailchimp submission issue fixed
- Progress bar improved
- Few minor bugfix and improvements

= 2.13.1 =
- WPForms appearing issue fixed
- Few minor bugfix and improvements

= 2.13.0 =
- Offcanvas Content element added
- Image COmparison element improved
- All carousels element improved
- Contact Form 7 widget alignment issue fixed
- Lightbox element improved
- Few minor bugfix and improvements

= 2.12.5 =
- "Exit Intent" added to Lightbox element
- Filterable Gallery and Dynamic Gallery revamped and optmized
- Google map api key missing notice feature added
- Post elements improved and optimized
- Few minor bugfix and improvements

= 2.12.4 =
- Filterable Gallery card hover issue fixed
- Flipbox back image radius issue fixed
- Few minor bugfix and improvements

= 2.12.3 =
- Post List issue fixed
- Google Map script loading improved (if you leave the API key blank, it won't load the map script)
- Few minor bugfix and improvements

= 2.12.2 =
- Video card support added to Filterable Gallery
- Multiple hover effects added to Post Grid
- Icon changing option added to Post Grid
- Progress Bar element improved
- Few minor bugfix and improvements

= 2.12.1 =
- Content Timeline animation improved
- Nested Accordion feature added (as saved template)
- Few minor bugfix and improvements

= 2.12.0 =
- Progress Bar element added
- Flip Box improved
- Table widget random layout breaking fixed
- Pricing Table tooltip option added
- Testimonial widget improved with more options
- Creative Button improved
- Post Queries improved
- Instagram load more improved
- Advanced Tab content width issue fixed
- Lots of minor bugfix and improvements

= 2.11.0 =
- Protected Content element added
- Countdown element improved
- Info Box element improved
- Interactive Cards element improved
- Image Hotspots tooltip issue fixed
- Testimonial Slider improved
- Data Table custom responsiveness option added
- Flipbox element improved
- Dynamic Gallery improved with new post query
- Few minor bugfix and improvements

= 2.10.2 =
- Manual selection option added to post elements
- All Post elements improved and query minimized
- Data Table mobile responsiveness improved
- Testimonial element improved with new layout options
- Team Members element improved with new layout options
- Creative Buttons element improved
- Few minor bugfix and improvements

= 2.10.1 =
- Content Toggle bug fixed
- Carousel elements navigation issue fixed
- Countdown element improved and expire action added
- All Post elements query optimized and load more functionality improved
- Post Grid Masonry improved
- Info Box layout improved and more options added
- Content Ticker scripts and layout improved
- Data Table responsiveness improved
- Image Accordion scripts improved
- Interactive Cards scripts improved
- Lightbox scripts improved
- Fancy Text line breaking issue fixed
- All form elements improved and optimized
- Few minor bugfix and improvements

= 2.10.0 =
- WPForms element added
- Counter element added
- Fancy Divider element added
- Logo Carousel element added
- Post Carousel element added
- Team Member Carousel element added
- Image Hotspot element added
- Price Menu element added
- One Page Navigation element added
- Instagram Feed improved with more options
- Author and Tags filtering added to all Post elements (Post Grid, Post Block, Post List, Post Carousel, Post Timeline, Content Timeline)
- Exclude posts option added to all Post elements
- Google Map element updated
- Theme options and other controls added to Google Map element
- Filterable Gallery link and z-index issue fixed and "All" text customization option added
- Social Feeds (Facebook & Twitter Feed) API updated
- Few minor bugfix and improvements

= 2.9.1 =
- Data Table cell colspan feature added
- Flip Carousel element improved
- "On Sale Price" option added to Pricing Table element
- Testimonial avatar display issue fixed
- Advanced Tabs improved with custom icon option
- Contact Form 7 updated
- Ninja Forms updated
- Gravity Forms updated
- Caldera Forms updated
- Post Grid load more button position fixed
- Filterable Gallery improved with less script dependency

= 2.9.0 =
- Mailchimp element added
- Content Toggle element added
- Dynamic field support added to all elements
- Saved Templates option added to content area (all possible elements)
- Advanced Tabs vertical layout added
- Lots of minor bugfix and improvements

= 2.8.1 =
- Advanced Google Map element added
- Instagram Gallery pagination (load more) feature added
- Instagram Gallery squared image option added
- Few minor bugfix and improvements

= 2.8.0 =
- Advanced Accordion element added
- Advanced Tabs element added
- Tooltip element added
- Few minor bugfix and improvements

= 2.7.0 =
- Image Accordion element added
- Smart Post List element added
- Content Ticker element added
- Pricing Table element improved
- Info Box element updated with flexbox layout
- Twitter Feed element improved
- Facebook and Twitter Feed elements improved
- Filterable Gallery improved
- Few minor bugfix and improvements

= 2.6.0 =
- Lightbox conflict fixed with Elementor gallery
- Licensing improved, you might need to revalidate.
- Automatic update process improved
- Few minor bug fixed and improvements

= 2.5.3 =
- Lightbox Gallery conflict fixed with Elementor
- Filterable Gallery control alignment fixed for mobile
- Data Table element improvements
- Interactive Cards improvements
- Accessibility improvements
- Few minor bug fixed and improvements

= 2.5.2 =
- Some glitch fixed from 2.5.1

= 2.5.1 =

- Filterable Gallery element added
- Dynamic FIlterable Gallery element added
- Facebook Feed Carousel element added
- Twitter Feed Carousel element added
- Few minor bug fix and improvements

= 2.5.0 =

- Data Table element added
- Facebook Feed element added
- Twitter Feed element added
- Caldera Forms element added
- Ninja forms element improved
- Load more improved for post elements
- Info box improved
- Flipbox HTML support added
- Few minor bug fix and improvements


= 2.4.0 =

- Content Timeline element added
- Gravity Forms element added
- New auto update feature added
- Instagram Gallery link fixed
- Post Grid, Post Block and Post Timeline load more functionality improved
- Custom CSS/JS options removed from options page. Use customizer instead.
- Few minor improvements

= 2.3.1 =

- Ninja Forms element added
- Load more functionality added to Post elements (Post Grid, Post Block & Post Timeline)
- Few presets added for Team Members, Countdown, Fancy Text
- Few minor improvements


= 2.3.0 =

- Interactive Cards element added
- Several improvements for CTA, Info Box, Flip box and dual heading elements
- Call to action width control and button control added
- Pricing table options improved
- Admin page improved and optimized

= 2.2.1 =

- Flip Carousel improved
- Pricing Table updated with more options
- Flipbox animation improved and more options added
- Info Box improved
- Few minor improvements

= 2.2 =

- Flip Carousel element added
- Animation improved for Flip Box
- Few minor bugfix

= 2.1 =

- Added Pricing Table element
- More options added to CTA, Info Box, Flip Box elements
- Options panel improved
- Few minor bug fixed

= 2.0 =

- Options Panel added
- Elements control added. Now you can enable/disable certain elements.
- Info Box element added.
- Flip Box element added.
- Dual color headline element added.
- Static Product element added.
- Call to Action element added.
- Few minor bug fixes and improvements.

= 1.1.2 =

* Star rating feature added to Testimonials and Testimonials Slider

= 1.1.1 =

* Lightbox bug fixed

= 1.1.0 =

* Post Grid element added (Masonry)
* Post Block element added
* Few minor issue fixed

= 1.0.6 =

* Element added for weForms

= 1.0.5 =

* WooCommerce Product Grid element improved
* Contact Form 7 element added

= 1.0.4 =

* WooCommerce Product Grid element added
* Few minor issue fixed

= 1.0.3 =

* Post Timeline image ratio improved
* Default typography improved

= 1.0.2 =

* Testimonial slider added
* Some minor issues fixed


= 1.0.1 =

* Team Members added
* Testimonials added
* Some color scheme adjusted

= 1.0.0 =

Initial stable release


== Upgrade Notice ==