# Copyright (C) 2025 Essential Addons Elementor
# This file is distributed under the same license as the Essential Addons Elementor package.
msgid ""
msgstr ""
"Project-Id-Version: Essential Addons Elementor\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: ../includes/Classes/Helper.php:124, ../includes/Extensions/Conditional_Display.php:375
msgid "Select"
msgstr ""

#: ../includes/Classes/Helper.php:131
msgid "No saved templates found!"
msgstr ""

#: ../includes/Classes/Helper.php:146, ../includes/Elements/Offcanvas.php:171
msgid "Choose Sidebar"
msgstr ""

#: ../includes/Classes/Helper.php:144
msgid "No sidebars were found"
msgstr ""

#: ../includes/Classes/Helper.php:185
msgid "Select One"
msgstr ""

#. translators: 1: Breakpoint label, 2: `>` character, 3: Breakpoint value
#: ../includes/Classes/Helper.php:269
msgid "%1$s (%2$s %3$dpx)"
msgstr ""

#: ../includes/Classes/Helper.php:276
msgid "Desktop (> 2400px)"
msgstr ""

#: ../includes/Classes/Helper.php:277, ../includes/Elements/Counter.php:89, ../includes/Elements/Dynamic_Filterable_Gallery.php:234, ../includes/Elements/Instagram_Feed.php:124, ../includes/Elements/Lightbox.php:1122, ../includes/Elements/Lightbox.php:1244, ../includes/Elements/Post_Block.php:1381, ../includes/Elements/Post_Carousel.php:1416, ../includes/Elements/Price_Menu.php:517, ../includes/Elements/Protected_Content.php:296, ../includes/Elements/Stacked_Cards.php:154, ../includes/Elements/Stacked_Cards.php:322, ../includes/Elements/Stacked_Cards.php:583, ../includes/Elements/Stacked_Cards.php:674, ../includes/Elements/Team_Member_Carousel.php:510, ../includes/Elements/Woo_Collections.php:463, ../includes/Elements/Woo_Cross_Sells.php:155, ../includes/Extensions/Content_Protection.php:204, ../includes/Extensions/Smooth_Animation.php:246, ../includes/Extensions/Smooth_Animation.php:302, ../includes/Extensions/Smooth_Animation.php:841, ../includes/Extensions/Smooth_Animation.php:884, ../includes/Extensions/Smooth_Animation.php:942, ../includes/Extensions/Smooth_Animation.php:984, ../includes/Extensions/Smooth_Animation.php:1149, ../includes/Extensions/Smooth_Animation.php:1172, ../includes/Extensions/Smooth_Animation.php:1195, ../includes/Extensions/Smooth_Animation.php:1218, ../includes/Traits/Extender.php:4652, ../includes/Extensions/DynamicTags/Custom_Post_Types.php:103, ../includes/Extensions/DynamicTags/Posts.php:100, ../includes/Extensions/DynamicTags/Woo_Products.php:126
msgid "None"
msgstr ""

#: ../includes/Classes/Helper.php:323, ../includes/Elements/Woo_Product_Slider.php:147
msgid "Manual Selection"
msgstr ""

#: ../includes/Classes/Helper.php:331
msgid "Source"
msgstr ""

#: ../includes/Classes/Helper.php:341, ../includes/Elements/Content_Timeline.php:148
msgid "Search & Select"
msgstr ""

#: ../includes/Classes/Helper.php:356, ../includes/Elements/LD_Course_List.php:1734, ../includes/Extensions/Conditional_Display.php:1691
msgid "Author"
msgstr ""

#: ../includes/Classes/Helper.php:396, ../includes/Extensions/Conditional_Display.php:157, ../includes/Extensions/Conditional_Display.php:1081, ../includes/Extensions/Conditional_Display.php:1936
msgid "Exclude"
msgstr ""

#: ../includes/Classes/Helper.php:411, ../includes/Extensions/Conditional_Display.php:2023
msgid "Count"
msgstr ""

#: ../includes/Classes/Helper.php:421, ../includes/Elements/Woo_Cross_Sells.php:187, ../includes/Elements/Woo_Product_Slider.php:836
msgid "Offset"
msgstr ""

#: ../includes/Classes/Helper.php:433, ../includes/Elements/Woo_Cross_Sells.php:152, ../includes/Elements/Woo_Product_Slider.php:808, ../includes/Extensions/DynamicTags/Terms.php:73, ../includes/Extensions/DynamicTags/Woo_Products.php:87
msgid "Order By"
msgstr ""

#: ../includes/Classes/Helper.php:444, ../includes/Elements/Woo_Cross_Sells.php:168, ../includes/Elements/Woo_Product_Slider.php:816, ../includes/Extensions/DynamicTags/Terms.php:81
msgid "Order"
msgstr ""

#: ../includes/Classes/Notice.php:38
msgid "<strong>Essential Addons for Elementor - Pro</strong> requires <strong>Essential Addons for Elementor</strong> plugin to be installed and activated. Please install Essential Addons for Elementor to continue."
msgstr ""

#: ../includes/Classes/Notice.php:39
msgid "Install Essential Addons for Elementor"
msgstr ""

#: ../includes/Classes/Notice.php:34
msgid "<strong>Essential Addons for Elementor - Pro</strong> requires <strong>Essential Addons for Elementor</strong> plugin to be active. Please activate Essential Addons for Elementor to continue."
msgstr ""

#: ../includes/Classes/Notice.php:35
msgid "Activate Essential Addons for Elementor"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:32
msgid "Advanced Menu"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:95, ../includes/Elements/Fancy_Chart.php:71, ../includes/Elements/Image_Scroller.php:74, ../includes/Elements/Multicolumn_Pricing_Table.php:968, ../includes/Elements/Post_Block.php:859, ../includes/Elements/Post_Carousel.php:1046, ../includes/Elements/Woo_Account_Dashboard.php:1206, ../includes/Elements/Woo_Account_Dashboard.php:1543, ../includes/Elements/Woo_Collections.php:74, ../includes/Elements/Woo_Collections.php:209, ../includes/Elements/Woo_Cross_Sells.php:100, ../includes/Elements/Woo_Cross_Sells.php:433, ../includes/Elements/Woo_Thank_You.php:130, ../includes/Traits/Extender.php:147
msgid "General"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:102
msgid "Select Menu"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:103
msgid "Go to the <a href=\"%s\" target=\"_blank\">Menu screen</a> to manage your menus."
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:116
msgid "Hamburger Options"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:123
msgid "Disable Selected Menu"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:125, ../includes/Elements/Advanced_Menu.php:163, ../includes/Elements/Advanced_Search.php:288, ../includes/Elements/Advanced_Search.php:302, ../includes/Elements/Content_Timeline.php:454, ../includes/Elements/Content_Timeline.php:596, ../includes/Elements/Content_Timeline.php:610, ../includes/Elements/Content_Timeline.php:636, ../includes/Elements/Content_Timeline.php:650, ../includes/Elements/Content_Timeline.php:677, ../includes/Elements/Content_Timeline.php:692, ../includes/Elements/Dynamic_Filterable_Gallery.php:194, ../includes/Elements/Dynamic_Filterable_Gallery.php:209, ../includes/Elements/Dynamic_Filterable_Gallery.php:221, ../includes/Elements/Dynamic_Filterable_Gallery.php:318, ../includes/Elements/Dynamic_Filterable_Gallery.php:361, ../includes/Elements/Dynamic_Filterable_Gallery.php:461, ../includes/Elements/Dynamic_Filterable_Gallery.php:669, ../includes/Elements/Dynamic_Filterable_Gallery.php:681, ../includes/Elements/Dynamic_Filterable_Gallery.php:706, ../includes/Elements/Dynamic_Filterable_Gallery.php:721, ../includes/Elements/Dynamic_Filterable_Gallery.php:748, ../includes/Elements/Dynamic_Filterable_Gallery.php:762, ../includes/Elements/Dynamic_Filterable_Gallery.php:788, ../includes/Elements/Dynamic_Filterable_Gallery.php:802, ../includes/Elements/Flip_Carousel.php:110, ../includes/Elements/Flip_Carousel.php:142, ../includes/Elements/Flip_Carousel.php:154, ../includes/Elements/Flip_Carousel.php:185, ../includes/Elements/Flip_Carousel.php:197, ../includes/Elements/Flip_Carousel.php:209, ../includes/Elements/Flip_Carousel.php:221, ../includes/Elements/Flip_Carousel.php:233, ../includes/Elements/Flip_Carousel.php:361, ../includes/Elements/Flip_Carousel.php:512, ../includes/Elements/Google_Map.php:369, ../includes/Elements/Google_Map.php:508, ../includes/Elements/Google_Map.php:570, ../includes/Elements/Image_Hot_Spots.php:216, ../includes/Elements/Image_Hot_Spots.php:341, ../includes/Elements/Image_Hot_Spots.php:365, ../includes/Elements/Lightbox.php:203, ../includes/Elements/Lightbox.php:261, ../includes/Elements/Lightbox.php:726, ../includes/Elements/Lightbox.php:739, ../includes/Elements/Lightbox.php:752, ../includes/Elements/Logo_Carousel.php:512, ../includes/Elements/Logo_Carousel.php:524, ../includes/Elements/Logo_Carousel.php:562, ../includes/Elements/Logo_Carousel.php:574, ../includes/Elements/Logo_Carousel.php:627, ../includes/Elements/Logo_Carousel.php:639, ../includes/Elements/Logo_Carousel.php:736, ../includes/Elements/Logo_Carousel.php:794, ../includes/Elements/Offcanvas.php:413, ../includes/Elements/Offcanvas.php:425, ../includes/Elements/Offcanvas.php:438, ../includes/Elements/Offcanvas.php:450, ../includes/Elements/One_Page_Navigation.php:198, ../includes/Elements/Post_Block.php:751, ../includes/Elements/Post_Block.php:765, ../includes/Elements/Post_Block.php:791, ../includes/Elements/Post_Block.php:805, ../includes/Elements/Post_Block.php:831, ../includes/Elements/Post_Block.php:845, ../includes/Elements/Post_Carousel.php:160, ../includes/Elements/Post_Carousel.php:244, ../includes/Elements/Post_Carousel.php:292, ../includes/Elements/Post_Carousel.php:330, ../includes/Elements/Post_Carousel.php:426, ../includes/Elements/Post_Carousel.php:540, ../includes/Elements/Post_Carousel.php:723, ../includes/Elements/Post_Carousel.php:734, ../includes/Elements/Post_Carousel.php:772, ../includes/Elements/Post_Carousel.php:788, ../includes/Elements/Post_Carousel.php:834, ../includes/Elements/Post_Carousel.php:846, ../includes/Elements/Post_Carousel.php:902, ../includes/Elements/Post_Carousel.php:917, ../includes/Elements/Post_Carousel.php:932, ../includes/Elements/Post_Carousel.php:960, ../includes/Elements/Post_Carousel.php:975, ../includes/Elements/Post_Carousel.php:990, ../includes/Elements/Post_Carousel.php:1017, ../includes/Elements/Post_Carousel.php:1031, ../includes/Elements/Post_Carousel.php:1185, ../includes/Elements/Post_Carousel.php:1429, ../includes/Elements/Post_List.php:137, ../includes/Elements/Post_List.php:154, ../includes/Elements/Post_List.php:200, ../includes/Elements/Post_List.php:215, ../includes/Elements/Post_List.php:259, ../includes/Elements/Post_List.php:275, ../includes/Elements/Post_List.php:365, ../includes/Elements/Post_List.php:453, ../includes/Elements/Post_List.php:464, ../includes/Elements/Post_List.php:475, ../includes/Elements/Post_List.php:924, ../includes/Elements/Post_List.php:939, ../includes/Elements/Post_List.php:966, ../includes/Elements/Post_List.php:980, ../includes/Elements/Post_List.php:1006, ../includes/Elements/Post_List.php:1020, ../includes/Elements/Price_Menu.php:297, ../includes/Elements/Price_Menu.php:312, ../includes/Elements/Protected_Content.php:238, ../includes/Elements/Sphere_Photo_Viewer.php:204, ../includes/Elements/Static_Product.php:314, ../includes/Elements/Static_Product.php:755, ../includes/Elements/Static_Product.php:1015, ../includes/Elements/Team_Member_Carousel.php:773, ../includes/Elements/Team_Member_Carousel.php:785, ../includes/Elements/Team_Member_Carousel.php:839, ../includes/Elements/Team_Member_Carousel.php:886, ../includes/Elements/Team_Member_Carousel.php:898, ../includes/Elements/Testimonial_Slider.php:211, ../includes/Elements/Testimonial_Slider.php:222, ../includes/Elements/Testimonial_Slider.php:259, ../includes/Elements/Testimonial_Slider.php:275, ../includes/Elements/Testimonial_Slider.php:322, ../includes/Elements/Testimonial_Slider.php:350, ../includes/Elements/Testimonial_Slider.php:362, ../includes/Elements/Testimonial_Slider.php:455, ../includes/Elements/Testimonial_Slider.php:648, ../includes/Elements/Twitter_Feed_Carousel.php:94, ../includes/Elements/Twitter_Feed_Carousel.php:186, ../includes/Elements/Twitter_Feed_Carousel.php:441, ../includes/Elements/Twitter_Feed_Carousel.php:473, ../includes/Elements/Twitter_Feed_Carousel.php:497, ../includes/Elements/Twitter_Feed_Carousel.php:509, ../includes/Elements/Woo_Product_Slider.php:469, ../includes/Elements/Woo_Product_Slider.php:578, ../includes/Elements/Woo_Product_Slider.php:633, ../includes/Elements/Woo_Product_Slider.php:665, ../includes/Elements/Woo_Product_Slider.php:680, ../includes/Elements/Woo_Product_Slider.php:715, ../includes/Elements/Woo_Product_Slider.php:727, ../includes/Elements/Woo_Product_Slider.php:738, ../includes/Elements/Woo_Product_Slider.php:1063, ../includes/Elements/Woo_Product_Slider.php:2460, ../includes/Extensions/Conditional_Display.php:59, ../includes/Extensions/Conditional_Display.php:1447, ../includes/Extensions/Content_Protection.php:44, ../includes/Extensions/Content_Protection.php:139, ../includes/Skins/Skin_Default.php:139, ../includes/Skins/Skin_Five.php:157, ../includes/Skins/Skin_Four.php:139, ../includes/Skins/Skin_One.php:139, ../includes/Skins/Skin_Seven.php:139, ../includes/Skins/Skin_Six.php:139, ../includes/Skins/Skin_Three.php:157, ../includes/Skins/Skin_Two.php:156, ../includes/Traits/Extender.php:58, ../includes/Traits/Extender.php:911, ../includes/Traits/Extender.php:3827, ../includes/Traits/Extender.php:5163, ../includes/Traits/Filterable_Gallery_Extender.php:70, ../includes/Traits/Filterable_Gallery_Extender.php:113, ../includes/Extensions/DynamicTags/Terms.php:107
msgid "Yes"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:126, ../includes/Elements/Advanced_Menu.php:164, ../includes/Elements/Advanced_Search.php:289, ../includes/Elements/Advanced_Search.php:303, ../includes/Elements/Content_Timeline.php:458, ../includes/Elements/Content_Timeline.php:597, ../includes/Elements/Content_Timeline.php:611, ../includes/Elements/Content_Timeline.php:637, ../includes/Elements/Content_Timeline.php:651, ../includes/Elements/Content_Timeline.php:678, ../includes/Elements/Content_Timeline.php:693, ../includes/Elements/Dynamic_Filterable_Gallery.php:195, ../includes/Elements/Dynamic_Filterable_Gallery.php:210, ../includes/Elements/Dynamic_Filterable_Gallery.php:222, ../includes/Elements/Dynamic_Filterable_Gallery.php:322, ../includes/Elements/Dynamic_Filterable_Gallery.php:362, ../includes/Elements/Dynamic_Filterable_Gallery.php:462, ../includes/Elements/Dynamic_Filterable_Gallery.php:670, ../includes/Elements/Dynamic_Filterable_Gallery.php:682, ../includes/Elements/Dynamic_Filterable_Gallery.php:707, ../includes/Elements/Dynamic_Filterable_Gallery.php:722, ../includes/Elements/Dynamic_Filterable_Gallery.php:749, ../includes/Elements/Dynamic_Filterable_Gallery.php:763, ../includes/Elements/Dynamic_Filterable_Gallery.php:789, ../includes/Elements/Dynamic_Filterable_Gallery.php:803, ../includes/Elements/Flip_Carousel.php:111, ../includes/Elements/Flip_Carousel.php:143, ../includes/Elements/Flip_Carousel.php:155, ../includes/Elements/Flip_Carousel.php:186, ../includes/Elements/Flip_Carousel.php:198, ../includes/Elements/Flip_Carousel.php:210, ../includes/Elements/Flip_Carousel.php:222, ../includes/Elements/Flip_Carousel.php:234, ../includes/Elements/Flip_Carousel.php:362, ../includes/Elements/Flip_Carousel.php:513, ../includes/Elements/Google_Map.php:370, ../includes/Elements/Google_Map.php:509, ../includes/Elements/Google_Map.php:571, ../includes/Elements/Image_Hot_Spots.php:217, ../includes/Elements/Image_Hot_Spots.php:342, ../includes/Elements/Image_Hot_Spots.php:366, ../includes/Elements/Lightbox.php:204, ../includes/Elements/Lightbox.php:262, ../includes/Elements/Lightbox.php:727, ../includes/Elements/Lightbox.php:740, ../includes/Elements/Lightbox.php:753, ../includes/Elements/Logo_Carousel.php:513, ../includes/Elements/Logo_Carousel.php:525, ../includes/Elements/Logo_Carousel.php:563, ../includes/Elements/Logo_Carousel.php:575, ../includes/Elements/Logo_Carousel.php:628, ../includes/Elements/Logo_Carousel.php:640, ../includes/Elements/Logo_Carousel.php:737, ../includes/Elements/Logo_Carousel.php:795, ../includes/Elements/Offcanvas.php:414, ../includes/Elements/Offcanvas.php:426, ../includes/Elements/Offcanvas.php:439, ../includes/Elements/Offcanvas.php:451, ../includes/Elements/One_Page_Navigation.php:199, ../includes/Elements/Post_Block.php:752, ../includes/Elements/Post_Block.php:766, ../includes/Elements/Post_Block.php:792, ../includes/Elements/Post_Block.php:806, ../includes/Elements/Post_Block.php:832, ../includes/Elements/Post_Block.php:846, ../includes/Elements/Post_Carousel.php:161, ../includes/Elements/Post_Carousel.php:245, ../includes/Elements/Post_Carousel.php:293, ../includes/Elements/Post_Carousel.php:331, ../includes/Elements/Post_Carousel.php:427, ../includes/Elements/Post_Carousel.php:541, ../includes/Elements/Post_Carousel.php:724, ../includes/Elements/Post_Carousel.php:735, ../includes/Elements/Post_Carousel.php:773, ../includes/Elements/Post_Carousel.php:789, ../includes/Elements/Post_Carousel.php:835, ../includes/Elements/Post_Carousel.php:847, ../includes/Elements/Post_Carousel.php:903, ../includes/Elements/Post_Carousel.php:918, ../includes/Elements/Post_Carousel.php:933, ../includes/Elements/Post_Carousel.php:961, ../includes/Elements/Post_Carousel.php:976, ../includes/Elements/Post_Carousel.php:991, ../includes/Elements/Post_Carousel.php:1018, ../includes/Elements/Post_Carousel.php:1032, ../includes/Elements/Post_Carousel.php:1186, ../includes/Elements/Post_Carousel.php:1430, ../includes/Elements/Post_List.php:138, ../includes/Elements/Post_List.php:155, ../includes/Elements/Post_List.php:201, ../includes/Elements/Post_List.php:216, ../includes/Elements/Post_List.php:260, ../includes/Elements/Post_List.php:276, ../includes/Elements/Post_List.php:366, ../includes/Elements/Post_List.php:454, ../includes/Elements/Post_List.php:465, ../includes/Elements/Post_List.php:476, ../includes/Elements/Post_List.php:925, ../includes/Elements/Post_List.php:940, ../includes/Elements/Post_List.php:967, ../includes/Elements/Post_List.php:981, ../includes/Elements/Post_List.php:1007, ../includes/Elements/Post_List.php:1021, ../includes/Elements/Price_Menu.php:298, ../includes/Elements/Price_Menu.php:313, ../includes/Elements/Protected_Content.php:239, ../includes/Elements/Sphere_Photo_Viewer.php:205, ../includes/Elements/Static_Product.php:315, ../includes/Elements/Static_Product.php:756, ../includes/Elements/Static_Product.php:1016, ../includes/Elements/Team_Member_Carousel.php:774, ../includes/Elements/Team_Member_Carousel.php:786, ../includes/Elements/Team_Member_Carousel.php:840, ../includes/Elements/Team_Member_Carousel.php:887, ../includes/Elements/Team_Member_Carousel.php:899, ../includes/Elements/Testimonial_Slider.php:212, ../includes/Elements/Testimonial_Slider.php:223, ../includes/Elements/Testimonial_Slider.php:260, ../includes/Elements/Testimonial_Slider.php:276, ../includes/Elements/Testimonial_Slider.php:323, ../includes/Elements/Testimonial_Slider.php:351, ../includes/Elements/Testimonial_Slider.php:363, ../includes/Elements/Testimonial_Slider.php:456, ../includes/Elements/Testimonial_Slider.php:649, ../includes/Elements/Twitter_Feed_Carousel.php:95, ../includes/Elements/Twitter_Feed_Carousel.php:187, ../includes/Elements/Twitter_Feed_Carousel.php:442, ../includes/Elements/Twitter_Feed_Carousel.php:474, ../includes/Elements/Twitter_Feed_Carousel.php:498, ../includes/Elements/Twitter_Feed_Carousel.php:510, ../includes/Elements/Woo_Product_Slider.php:470, ../includes/Elements/Woo_Product_Slider.php:579, ../includes/Elements/Woo_Product_Slider.php:634, ../includes/Elements/Woo_Product_Slider.php:666, ../includes/Elements/Woo_Product_Slider.php:681, ../includes/Elements/Woo_Product_Slider.php:716, ../includes/Elements/Woo_Product_Slider.php:728, ../includes/Elements/Woo_Product_Slider.php:739, ../includes/Elements/Woo_Product_Slider.php:1064, ../includes/Elements/Woo_Product_Slider.php:2461, ../includes/Extensions/Conditional_Display.php:60, ../includes/Extensions/Conditional_Display.php:1448, ../includes/Extensions/Content_Protection.php:45, ../includes/Extensions/Content_Protection.php:140, ../includes/Skins/Skin_Default.php:140, ../includes/Skins/Skin_Five.php:158, ../includes/Skins/Skin_Four.php:140, ../includes/Skins/Skin_One.php:140, ../includes/Skins/Skin_Seven.php:140, ../includes/Skins/Skin_Six.php:140, ../includes/Skins/Skin_Three.php:158, ../includes/Skins/Skin_Two.php:157, ../includes/Traits/Extender.php:59, ../includes/Traits/Extender.php:912, ../includes/Traits/Extender.php:3828, ../includes/Traits/Extender.php:5164, ../includes/Traits/Filterable_Gallery_Extender.php:71, ../includes/Traits/Filterable_Gallery_Extender.php:114, ../includes/Extensions/DynamicTags/Terms.php:108
msgid "No"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:136
msgid "Hamburger Alignment"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:140, ../includes/Elements/Advanced_Menu.php:291, ../includes/Elements/Advanced_Search.php:1617, ../includes/Elements/Advanced_Search.php:1928, ../includes/Elements/Content_Timeline.php:1388, ../includes/Elements/Content_Timeline.php:1468, ../includes/Elements/Content_Timeline.php:1574, ../includes/Elements/Content_Timeline.php:1642, ../includes/Elements/Counter.php:358, ../includes/Elements/Divider.php:325, ../includes/Elements/Divider.php:639, ../includes/Elements/Divider.php:742, ../includes/Elements/Divider.php:880, ../includes/Elements/Dynamic_Filterable_Gallery.php:588, ../includes/Elements/Dynamic_Filterable_Gallery.php:1381, ../includes/Elements/Dynamic_Filterable_Gallery.php:1412, ../includes/Elements/Dynamic_Filterable_Gallery.php:1844, ../includes/Elements/Fancy_Chart.php:337, ../includes/Elements/Fancy_Chart.php:911, ../includes/Elements/Fancy_Chart.php:979, ../includes/Elements/Fancy_Chart.php:1580, ../includes/Elements/Fancy_Chart.php:1664, ../includes/Elements/Flip_Carousel.php:694, ../includes/Elements/Flip_Carousel.php:798, ../includes/Elements/Image_Comparison.php:579, ../includes/Elements/Image_Hot_Spots.php:288, ../includes/Elements/Image_Hot_Spots.php:395, ../includes/Elements/Image_Hot_Spots.php:647, ../includes/Elements/Interactive_Card.php:320, ../includes/Elements/Interactive_Card.php:406, ../includes/Elements/Interactive_Card.php:571, ../includes/Elements/Interactive_Card.php:911, ../includes/Elements/Interactive_Card.php:1112, ../includes/Elements/LD_Course_List.php:910, ../includes/Elements/LD_Course_List.php:1089, ../includes/Elements/LD_Course_List.php:2160, ../includes/Elements/Lightbox.php:451, ../includes/Elements/Lightbox.php:485, ../includes/Elements/Lightbox.php:831, ../includes/Elements/Logo_Carousel.php:222, ../includes/Elements/Logo_Carousel.php:652, ../includes/Elements/Mailchimp.php:878, ../includes/Elements/Multicolumn_Pricing_Table.php:184, ../includes/Elements/Multicolumn_Pricing_Table.php:254, ../includes/Elements/Multicolumn_Pricing_Table.php:428, ../includes/Elements/Multicolumn_Pricing_Table.php:570, ../includes/Elements/Multicolumn_Pricing_Table.php:617, ../includes/Elements/Multicolumn_Pricing_Table.php:850, ../includes/Elements/Multicolumn_Pricing_Table.php:1047, ../includes/Elements/Multicolumn_Pricing_Table.php:1499, ../includes/Elements/Multicolumn_Pricing_Table.php:1596, ../includes/Elements/Multicolumn_Pricing_Table.php:1838, ../includes/Elements/Multicolumn_Pricing_Table.php:2172, ../includes/Elements/Multicolumn_Pricing_Table.php:2254, ../includes/Elements/Multicolumn_Pricing_Table.php:2474, ../includes/Elements/Offcanvas.php:378, ../includes/Elements/Offcanvas.php:581, ../includes/Elements/Offcanvas.php:963, ../includes/Elements/Offcanvas.php:1241, ../includes/Elements/One_Page_Navigation.php:319, ../includes/Elements/Post_Block.php:1079, ../includes/Elements/Post_Block.php:1149, ../includes/Elements/Post_Block.php:1239, ../includes/Elements/Post_Block.php:1267, ../includes/Elements/Post_Carousel.php:1288, ../includes/Elements/Post_Carousel.php:1562, ../includes/Elements/Post_Carousel.php:1632, ../includes/Elements/Post_Carousel.php:1852, ../includes/Elements/Post_List.php:1329, ../includes/Elements/Post_List.php:1547, ../includes/Elements/Post_List.php:1601, ../includes/Elements/Post_List.php:1659, ../includes/Elements/Post_List.php:1806, ../includes/Elements/Post_List.php:1860, ../includes/Elements/Post_List.php:1918, ../includes/Elements/Price_Menu.php:265, ../includes/Elements/Protected_Content.php:398, ../includes/Elements/Protected_Content.php:485, ../includes/Elements/Protected_Content.php:566, ../includes/Elements/Protected_Content.php:639, ../includes/Elements/Sphere_Photo_Viewer.php:614, ../includes/Elements/Sphere_Photo_Viewer.php:791, ../includes/Elements/Sphere_Photo_Viewer.php:942, ../includes/Elements/Stacked_Cards.php:184, ../includes/Elements/Stacked_Cards.php:1064, ../includes/Elements/Stacked_Cards.php:1290, ../includes/Elements/Stacked_Cards.php:1347, ../includes/Elements/Static_Product.php:517, ../includes/Elements/Static_Product.php:544, ../includes/Elements/Team_Member_Carousel.php:928, ../includes/Elements/Team_Member_Carousel.php:1126, ../includes/Elements/Testimonial_Slider.php:720, ../includes/Elements/Toggle.php:302, ../includes/Elements/Toggle.php:692, ../includes/Elements/Woo_Account_Dashboard.php:1891, ../includes/Elements/Woo_Account_Dashboard.php:2015, ../includes/Elements/Woo_Collections.php:399, ../includes/Elements/Woo_Collections.php:432, ../includes/Elements/Woo_Cross_Sells.php:524, ../includes/Elements/Woo_Product_Slider.php:771, ../includes/Elements/Woo_Product_Slider.php:932, ../includes/Elements/Woo_Product_Slider.php:1077, ../includes/Elements/Woo_Thank_You.php:375, ../includes/Elements/Woo_Thank_You.php:969, ../includes/Extensions/Content_Protection.php:320, ../includes/Extensions/Content_Protection.php:404, ../includes/Extensions/Content_Protection.php:483, ../includes/Extensions/EAEL_Tooltip_Section.php:82, ../includes/Extensions/Smooth_Animation.php:248, ../includes/Extensions/Smooth_Animation.php:304, ../includes/Skins/Skin_Default.php:219, ../includes/Skins/Skin_Default.php:637, ../includes/Skins/Skin_Five.php:268, ../includes/Skins/Skin_Five.php:711, ../includes/Skins/Skin_Four.php:219, ../includes/Skins/Skin_Four.php:659, ../includes/Skins/Skin_One.php:267, ../includes/Skins/Skin_One.php:707, ../includes/Skins/Skin_Seven.php:219, ../includes/Skins/Skin_Seven.php:747, ../includes/Skins/Skin_Six.php:220, ../includes/Skins/Skin_Six.php:663, ../includes/Skins/Skin_Three.php:238, ../includes/Skins/Skin_Three.php:681, ../includes/Skins/Skin_Two.php:237, ../includes/Skins/Skin_Two.php:680, ../includes/Traits/Extender.php:159, ../includes/Traits/Extender.php:585, ../includes/Traits/Extender.php:2936, ../includes/Traits/Extender.php:3933, ../includes/Traits/Extender.php:4976, ../includes/Traits/Extender.php:5416
msgid "Left"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:144, ../includes/Elements/Advanced_Menu.php:295, ../includes/Elements/Advanced_Search.php:1621, ../includes/Elements/Advanced_Search.php:1932, ../includes/Elements/Content_Timeline.php:1578, ../includes/Elements/Content_Timeline.php:1646, ../includes/Elements/Counter.php:362, ../includes/Elements/Divider.php:329, ../includes/Elements/Divider.php:374, ../includes/Elements/Divider.php:643, ../includes/Elements/Divider.php:746, ../includes/Elements/Divider.php:884, ../includes/Elements/Dynamic_Filterable_Gallery.php:592, ../includes/Elements/Dynamic_Filterable_Gallery.php:1385, ../includes/Elements/Dynamic_Filterable_Gallery.php:1416, ../includes/Elements/Dynamic_Filterable_Gallery.php:1848, ../includes/Elements/Fancy_Chart.php:309, ../includes/Elements/Fancy_Chart.php:915, ../includes/Elements/Fancy_Chart.php:983, ../includes/Elements/Fancy_Chart.php:1668, ../includes/Elements/Flip_Carousel.php:698, ../includes/Elements/Flip_Carousel.php:802, ../includes/Elements/Image_Comparison.php:583, ../includes/Elements/Image_Hot_Spots.php:651, ../includes/Elements/Interactive_Card.php:915, ../includes/Elements/Interactive_Card.php:1116, ../includes/Elements/LD_Course_List.php:914, ../includes/Elements/LD_Course_List.php:1093, ../includes/Elements/LD_Course_List.php:2164, ../includes/Elements/Lightbox.php:455, ../includes/Elements/Lightbox.php:489, ../includes/Elements/Lightbox.php:835, ../includes/Elements/Mailchimp.php:882, ../includes/Elements/Multicolumn_Pricing_Table.php:1051, ../includes/Elements/Multicolumn_Pricing_Table.php:1503, ../includes/Elements/Multicolumn_Pricing_Table.php:1530, ../includes/Elements/Multicolumn_Pricing_Table.php:1600, ../includes/Elements/Multicolumn_Pricing_Table.php:1842, ../includes/Elements/Multicolumn_Pricing_Table.php:2176, ../includes/Elements/Multicolumn_Pricing_Table.php:2258, ../includes/Elements/Multicolumn_Pricing_Table.php:2478, ../includes/Elements/Offcanvas.php:585, ../includes/Elements/Offcanvas.php:967, ../includes/Elements/Post_Block.php:1083, ../includes/Elements/Post_Block.php:1153, ../includes/Elements/Post_Block.php:1243, ../includes/Elements/Post_Block.php:1271, ../includes/Elements/Post_Carousel.php:1292, ../includes/Elements/Post_Carousel.php:1566, ../includes/Elements/Post_Carousel.php:1636, ../includes/Elements/Post_Carousel.php:1856, ../includes/Elements/Post_List.php:1333, ../includes/Elements/Post_List.php:1551, ../includes/Elements/Post_List.php:1605, ../includes/Elements/Post_List.php:1663, ../includes/Elements/Post_List.php:1810, ../includes/Elements/Post_List.php:1864, ../includes/Elements/Post_List.php:1922, ../includes/Elements/Price_Menu.php:269, ../includes/Elements/Price_Menu.php:962, ../includes/Elements/Protected_Content.php:402, ../includes/Elements/Protected_Content.php:489, ../includes/Elements/Protected_Content.php:570, ../includes/Elements/Protected_Content.php:643, ../includes/Elements/Sphere_Photo_Viewer.php:618, ../includes/Elements/Sphere_Photo_Viewer.php:795, ../includes/Elements/Sphere_Photo_Viewer.php:946, ../includes/Elements/Stacked_Cards.php:1068, ../includes/Elements/Stacked_Cards.php:1294, ../includes/Elements/Stacked_Cards.php:1351, ../includes/Elements/Static_Product.php:521, ../includes/Elements/Static_Product.php:548, ../includes/Elements/Team_Member_Carousel.php:932, ../includes/Elements/Team_Member_Carousel.php:1130, ../includes/Elements/Testimonial_Slider.php:724, ../includes/Elements/Toggle.php:306, ../includes/Elements/Toggle.php:696, ../includes/Elements/Woo_Account_Dashboard.php:1895, ../includes/Elements/Woo_Account_Dashboard.php:2019, ../includes/Elements/Woo_Collections.php:400, ../includes/Elements/Woo_Collections.php:436, ../includes/Elements/Woo_Cross_Sells.php:528, ../includes/Elements/Woo_Product_Slider.php:1081, ../includes/Elements/Woo_Thank_You.php:379, ../includes/Elements/Woo_Thank_You.php:973, ../includes/Extensions/Content_Protection.php:324, ../includes/Extensions/Content_Protection.php:408, ../includes/Extensions/Content_Protection.php:487, ../includes/Extensions/Smooth_Animation.php:844, ../includes/Extensions/Smooth_Animation.php:887, ../includes/Extensions/Smooth_Animation.php:945, ../includes/Extensions/Smooth_Animation.php:987, ../includes/Skins/Skin_Default.php:223, ../includes/Skins/Skin_Default.php:641, ../includes/Skins/Skin_Five.php:272, ../includes/Skins/Skin_Five.php:715, ../includes/Skins/Skin_Four.php:223, ../includes/Skins/Skin_Four.php:663, ../includes/Skins/Skin_One.php:271, ../includes/Skins/Skin_One.php:711, ../includes/Skins/Skin_Seven.php:223, ../includes/Skins/Skin_Seven.php:751, ../includes/Skins/Skin_Six.php:224, ../includes/Skins/Skin_Six.php:667, ../includes/Skins/Skin_Three.php:242, ../includes/Skins/Skin_Three.php:685, ../includes/Skins/Skin_Two.php:241, ../includes/Skins/Skin_Two.php:684, ../includes/Traits/Extender.php:163, ../includes/Traits/Extender.php:589, ../includes/Traits/Extender.php:2940, ../includes/Traits/Extender.php:3934, ../includes/Traits/Extender.php:4980, ../includes/Traits/Extender.php:5420
msgid "Center"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:148, ../includes/Elements/Advanced_Menu.php:299, ../includes/Elements/Advanced_Search.php:1625, ../includes/Elements/Advanced_Search.php:1936, ../includes/Elements/Content_Timeline.php:1389, ../includes/Elements/Content_Timeline.php:1469, ../includes/Elements/Content_Timeline.php:1582, ../includes/Elements/Content_Timeline.php:1650, ../includes/Elements/Counter.php:366, ../includes/Elements/Divider.php:333, ../includes/Elements/Divider.php:647, ../includes/Elements/Divider.php:750, ../includes/Elements/Divider.php:888, ../includes/Elements/Dynamic_Filterable_Gallery.php:596, ../includes/Elements/Dynamic_Filterable_Gallery.php:1389, ../includes/Elements/Dynamic_Filterable_Gallery.php:1420, ../includes/Elements/Dynamic_Filterable_Gallery.php:1852, ../includes/Elements/Fancy_Chart.php:335, ../includes/Elements/Fancy_Chart.php:919, ../includes/Elements/Fancy_Chart.php:987, ../includes/Elements/Fancy_Chart.php:1576, ../includes/Elements/Fancy_Chart.php:1672, ../includes/Elements/Flip_Carousel.php:702, ../includes/Elements/Flip_Carousel.php:806, ../includes/Elements/Image_Comparison.php:587, ../includes/Elements/Image_Hot_Spots.php:289, ../includes/Elements/Image_Hot_Spots.php:396, ../includes/Elements/Image_Hot_Spots.php:655, ../includes/Elements/Interactive_Card.php:324, ../includes/Elements/Interactive_Card.php:407, ../includes/Elements/Interactive_Card.php:575, ../includes/Elements/Interactive_Card.php:919, ../includes/Elements/Interactive_Card.php:1120, ../includes/Elements/LD_Course_List.php:918, ../includes/Elements/LD_Course_List.php:1097, ../includes/Elements/LD_Course_List.php:2168, ../includes/Elements/Lightbox.php:459, ../includes/Elements/Lightbox.php:493, ../includes/Elements/Lightbox.php:839, ../includes/Elements/Logo_Carousel.php:230, ../includes/Elements/Logo_Carousel.php:653, ../includes/Elements/Mailchimp.php:886, ../includes/Elements/Multicolumn_Pricing_Table.php:188, ../includes/Elements/Multicolumn_Pricing_Table.php:258, ../includes/Elements/Multicolumn_Pricing_Table.php:436, ../includes/Elements/Multicolumn_Pricing_Table.php:574, ../includes/Elements/Multicolumn_Pricing_Table.php:621, ../includes/Elements/Multicolumn_Pricing_Table.php:854, ../includes/Elements/Multicolumn_Pricing_Table.php:1055, ../includes/Elements/Multicolumn_Pricing_Table.php:1507, ../includes/Elements/Multicolumn_Pricing_Table.php:1604, ../includes/Elements/Multicolumn_Pricing_Table.php:1846, ../includes/Elements/Multicolumn_Pricing_Table.php:2180, ../includes/Elements/Multicolumn_Pricing_Table.php:2262, ../includes/Elements/Multicolumn_Pricing_Table.php:2482, ../includes/Elements/Offcanvas.php:382, ../includes/Elements/Offcanvas.php:589, ../includes/Elements/Offcanvas.php:971, ../includes/Elements/Offcanvas.php:1245, ../includes/Elements/One_Page_Navigation.php:323, ../includes/Elements/Post_Block.php:1087, ../includes/Elements/Post_Block.php:1157, ../includes/Elements/Post_Block.php:1247, ../includes/Elements/Post_Block.php:1275, ../includes/Elements/Post_Carousel.php:1296, ../includes/Elements/Post_Carousel.php:1570, ../includes/Elements/Post_Carousel.php:1640, ../includes/Elements/Post_Carousel.php:1860, ../includes/Elements/Post_List.php:1337, ../includes/Elements/Post_List.php:1555, ../includes/Elements/Post_List.php:1609, ../includes/Elements/Post_List.php:1667, ../includes/Elements/Post_List.php:1814, ../includes/Elements/Post_List.php:1868, ../includes/Elements/Post_List.php:1926, ../includes/Elements/Price_Menu.php:273, ../includes/Elements/Protected_Content.php:406, ../includes/Elements/Protected_Content.php:493, ../includes/Elements/Protected_Content.php:574, ../includes/Elements/Protected_Content.php:647, ../includes/Elements/Sphere_Photo_Viewer.php:622, ../includes/Elements/Sphere_Photo_Viewer.php:799, ../includes/Elements/Sphere_Photo_Viewer.php:950, ../includes/Elements/Stacked_Cards.php:188, ../includes/Elements/Stacked_Cards.php:1072, ../includes/Elements/Stacked_Cards.php:1298, ../includes/Elements/Stacked_Cards.php:1355, ../includes/Elements/Static_Product.php:525, ../includes/Elements/Static_Product.php:552, ../includes/Elements/Team_Member_Carousel.php:936, ../includes/Elements/Team_Member_Carousel.php:1134, ../includes/Elements/Testimonial_Slider.php:728, ../includes/Elements/Toggle.php:310, ../includes/Elements/Toggle.php:700, ../includes/Elements/Woo_Account_Dashboard.php:1899, ../includes/Elements/Woo_Account_Dashboard.php:2023, ../includes/Elements/Woo_Collections.php:401, ../includes/Elements/Woo_Collections.php:440, ../includes/Elements/Woo_Cross_Sells.php:532, ../includes/Elements/Woo_Product_Slider.php:772, ../includes/Elements/Woo_Product_Slider.php:936, ../includes/Elements/Woo_Product_Slider.php:1085, ../includes/Elements/Woo_Thank_You.php:383, ../includes/Elements/Woo_Thank_You.php:977, ../includes/Extensions/Content_Protection.php:328, ../includes/Extensions/Content_Protection.php:412, ../includes/Extensions/Content_Protection.php:491, ../includes/Extensions/EAEL_Tooltip_Section.php:83, ../includes/Extensions/Smooth_Animation.php:250, ../includes/Extensions/Smooth_Animation.php:306, ../includes/Skins/Skin_Default.php:227, ../includes/Skins/Skin_Default.php:645, ../includes/Skins/Skin_Five.php:276, ../includes/Skins/Skin_Five.php:719, ../includes/Skins/Skin_Four.php:227, ../includes/Skins/Skin_Four.php:667, ../includes/Skins/Skin_One.php:275, ../includes/Skins/Skin_One.php:715, ../includes/Skins/Skin_Seven.php:227, ../includes/Skins/Skin_Seven.php:755, ../includes/Skins/Skin_Six.php:228, ../includes/Skins/Skin_Six.php:671, ../includes/Skins/Skin_Three.php:246, ../includes/Skins/Skin_Three.php:689, ../includes/Skins/Skin_Two.php:245, ../includes/Skins/Skin_Two.php:688, ../includes/Traits/Extender.php:167, ../includes/Traits/Extender.php:593, ../includes/Traits/Extender.php:2944, ../includes/Traits/Extender.php:4984, ../includes/Traits/Extender.php:5424
msgid "Right"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:160
msgid "Full Width"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:162
msgid "Stretch the dropdown of the menu to full width."
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:174, ../includes/Elements/Advanced_Search.php:685, ../includes/Elements/Content_Timeline.php:389, ../includes/Elements/Content_Timeline.php:434, ../includes/Elements/Counter.php:93, ../includes/Elements/Counter.php:108, ../includes/Elements/Counter.php:413, ../includes/Elements/Divider.php:120, ../includes/Elements/Divider.php:264, ../includes/Elements/Divider.php:727, ../includes/Elements/Image_Hot_Spots.php:155, ../includes/Elements/Image_Hot_Spots.php:165, ../includes/Elements/Interactive_Card.php:2007, ../includes/Elements/Lightbox.php:428, ../includes/Elements/Lightbox.php:549, ../includes/Elements/Lightbox.php:1021, ../includes/Elements/Multicolumn_Pricing_Table.php:359, ../includes/Elements/Multicolumn_Pricing_Table.php:467, ../includes/Elements/Multicolumn_Pricing_Table.php:551, ../includes/Elements/Multicolumn_Pricing_Table.php:802, ../includes/Elements/Offcanvas.php:866, ../includes/Elements/Stacked_Cards.php:166, ../includes/Elements/Stacked_Cards.php:330, ../includes/Elements/Stacked_Cards.php:991, ../includes/Elements/Stacked_Cards.php:1186, ../includes/Elements/Static_Product.php:397, ../includes/Elements/Static_Product.php:451, ../includes/Elements/Twitter_Feed_Carousel.php:1464, ../includes/Elements/Woo_Account_Dashboard.php:254, ../includes/Elements/Woo_Thank_You.php:249, ../includes/Elements/Woo_Thank_You.php:1064, ../includes/Elements/Woo_Thank_You.php:1094, ../includes/Elements/Woo_Thank_You.php:1131, ../includes/Elements/Woo_Thank_You.php:1161, ../includes/Elements/Woo_Thank_You.php:1233, ../includes/Skins/Skin_Default.php:369, ../includes/Skins/Skin_Default.php:690, ../includes/Skins/Skin_Five.php:442, ../includes/Skins/Skin_Five.php:787, ../includes/Skins/Skin_Four.php:390, ../includes/Skins/Skin_Four.php:732, ../includes/Skins/Skin_One.php:438, ../includes/Skins/Skin_One.php:767, ../includes/Skins/Skin_Seven.php:390, ../includes/Skins/Skin_Seven.php:677, ../includes/Skins/Skin_Six.php:391, ../includes/Skins/Skin_Six.php:736, ../includes/Skins/Skin_Three.php:410, ../includes/Skins/Skin_Three.php:754, ../includes/Skins/Skin_Two.php:409, ../includes/Skins/Skin_Two.php:753, ../includes/Traits/Extender.php:374, ../includes/Traits/Extender.php:3878, ../includes/Traits/Extender.php:4688, ../includes/Traits/Filterable_Gallery_Extender.php:415
msgid "Icon"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:186
msgid "Mobile Dropdown"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:198
msgid "Breakpoint"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:214
msgid "Main Menu"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:227
msgid "Hamburger Menu"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:235, ../includes/Elements/Advanced_Search.php:574, ../includes/Elements/Advanced_Search.php:736, ../includes/Elements/Advanced_Search.php:953, ../includes/Elements/Advanced_Search.php:1092, ../includes/Elements/Advanced_Search.php:1126, ../includes/Elements/Advanced_Search.php:1398, ../includes/Elements/Advanced_Search.php:1414, ../includes/Elements/Content_Timeline.php:830, ../includes/Elements/Content_Timeline.php:1252, ../includes/Elements/Content_Timeline.php:1353, ../includes/Elements/Content_Timeline.php:1792, ../includes/Elements/Content_Timeline.php:1846, ../includes/Elements/Content_Timeline.php:1998, ../includes/Elements/Content_Timeline.php:2055, ../includes/Elements/Counter.php:1064, ../includes/Elements/Dynamic_Filterable_Gallery.php:829, ../includes/Elements/Dynamic_Filterable_Gallery.php:963, ../includes/Elements/Dynamic_Filterable_Gallery.php:1029, ../includes/Elements/Dynamic_Filterable_Gallery.php:1097, ../includes/Elements/Dynamic_Filterable_Gallery.php:1199, ../includes/Elements/Dynamic_Filterable_Gallery.php:1289, ../includes/Elements/Dynamic_Filterable_Gallery.php:1334, ../includes/Elements/Dynamic_Filterable_Gallery.php:1550, ../includes/Elements/Dynamic_Filterable_Gallery.php:1649, ../includes/Elements/Dynamic_Filterable_Gallery.php:1698, ../includes/Elements/Fancy_Chart.php:1043, ../includes/Elements/Fancy_Chart.php:1449, ../includes/Elements/Flip_Carousel.php:422, ../includes/Elements/Flip_Carousel.php:643, ../includes/Elements/Google_Map.php:1303, ../includes/Elements/Image_Comparison.php:651, ../includes/Elements/Image_Comparison.php:707, ../includes/Elements/Image_Hot_Spots.php:787, ../includes/Elements/Image_Hot_Spots.php:852, ../includes/Elements/Image_Hot_Spots.php:908, ../includes/Elements/Instagram_Feed.php:637, ../includes/Elements/Instagram_Feed.php:701, ../includes/Elements/Interactive_Card.php:832, ../includes/Elements/Interactive_Card.php:937, ../includes/Elements/Interactive_Card.php:1134, ../includes/Elements/Interactive_Card.php:1582, ../includes/Elements/Interactive_Card.php:1672, ../includes/Elements/Interactive_Card.php:1844, ../includes/Elements/Interactive_Card.php:1914, ../includes/Elements/Interactive_Card.php:1982, ../includes/Elements/LD_Course_List.php:2264, ../includes/Elements/LD_Course_List.php:2296, ../includes/Elements/LD_Course_List.php:2372, ../includes/Elements/LD_Course_List.php:2567, ../includes/Elements/Lightbox.php:854, ../includes/Elements/Lightbox.php:1138, ../includes/Elements/Lightbox.php:1222, ../includes/Elements/Lightbox.php:1486, ../includes/Elements/Lightbox.php:1539, ../includes/Elements/Logo_Carousel.php:1023, ../includes/Elements/Logo_Carousel.php:1080, ../includes/Elements/Mailchimp.php:741, ../includes/Elements/Mailchimp.php:802, ../includes/Elements/Multicolumn_Pricing_Table.php:1091, ../includes/Elements/Multicolumn_Pricing_Table.php:1327, ../includes/Elements/Multicolumn_Pricing_Table.php:1625, ../includes/Elements/Multicolumn_Pricing_Table.php:2107, ../includes/Elements/Multicolumn_Pricing_Table.php:2121, ../includes/Elements/Multicolumn_Pricing_Table.php:2314, ../includes/Elements/Multicolumn_Pricing_Table.php:2364, ../includes/Elements/Multicolumn_Pricing_Table.php:2518, ../includes/Elements/Multicolumn_Pricing_Table.php:2569, ../includes/Elements/Offcanvas.php:619, ../includes/Elements/Offcanvas.php:1057, ../includes/Elements/Offcanvas.php:1146, ../includes/Elements/One_Page_Navigation.php:506, ../includes/Elements/One_Page_Navigation.php:564, ../includes/Elements/One_Page_Navigation.php:612, ../includes/Elements/One_Page_Navigation.php:656, ../includes/Elements/Post_Block.php:1408, ../includes/Elements/Post_Carousel.php:1207, ../includes/Elements/Post_Carousel.php:2032, ../includes/Elements/Post_Carousel.php:2088, ../includes/Elements/Post_List.php:1041, ../includes/Elements/Post_List.php:1246, ../includes/Elements/Price_Menu.php:338, ../includes/Elements/Price_Menu.php:657, ../includes/Elements/Price_Menu.php:822, ../includes/Elements/Protected_Content.php:719, ../includes/Elements/Protected_Content.php:766, ../includes/Elements/Protected_Content.php:879, ../includes/Elements/Protected_Content.php:925, ../includes/Elements/Stacked_Cards.php:480, ../includes/Elements/Stacked_Cards.php:520, ../includes/Elements/Static_Product.php:1037, ../includes/Elements/Static_Product.php:1252, ../includes/Elements/Static_Product.php:1319, ../includes/Elements/Static_Product.php:1508, ../includes/Elements/Static_Product.php:1564, ../includes/Elements/Team_Member_Carousel.php:1049, ../includes/Elements/Team_Member_Carousel.php:1990, ../includes/Elements/Team_Member_Carousel.php:2060, ../includes/Elements/Team_Member_Carousel.php:2195, ../includes/Elements/Team_Member_Carousel.php:2251, ../includes/Elements/Testimonial_Slider.php:657, ../includes/Elements/Testimonial_Slider.php:1215, ../includes/Elements/Testimonial_Slider.php:1282, ../includes/Elements/Testimonial_Slider.php:1471, ../includes/Elements/Testimonial_Slider.php:1527, ../includes/Elements/Twitter_Feed_Carousel.php:576, ../includes/Elements/Twitter_Feed_Carousel.php:1077, ../includes/Elements/Twitter_Feed_Carousel.php:1134, ../includes/Elements/Woo_Account_Dashboard.php:559, ../includes/Elements/Woo_Account_Dashboard.php:655, ../includes/Elements/Woo_Account_Dashboard.php:669, ../includes/Elements/Woo_Account_Dashboard.php:813, ../includes/Elements/Woo_Account_Dashboard.php:858, ../includes/Elements/Woo_Account_Dashboard.php:903, ../includes/Elements/Woo_Account_Dashboard.php:1168, ../includes/Elements/Woo_Collections.php:603, ../includes/Elements/Woo_Product_Slider.php:220, ../includes/Elements/Woo_Product_Slider.php:249, ../includes/Elements/Woo_Product_Slider.php:1047, ../includes/Elements/Woo_Product_Slider.php:1625, ../includes/Elements/Woo_Product_Slider.php:1695, ../includes/Elements/Woo_Product_Slider.php:1770, ../includes/Elements/Woo_Product_Slider.php:1830, ../includes/Elements/Woo_Product_Slider.php:2025, ../includes/Elements/Woo_Product_Slider.php:2068, ../includes/Elements/Woo_Product_Slider.php:2130, ../includes/Elements/Woo_Product_Slider.php:2182, ../includes/Elements/Woo_Product_Slider.php:2986, ../includes/Elements/Woo_Product_Slider.php:3042, ../includes/Extensions/Content_Protection.php:572, ../includes/Extensions/Content_Protection.php:638, ../includes/Extensions/Content_Protection.php:704, ../includes/Extensions/Content_Protection.php:768, ../includes/Extensions/EAEL_Tooltip_Section.php:271, ../includes/Skins/Skin_Default.php:68, ../includes/Skins/Skin_Default.php:156, ../includes/Skins/Skin_Default.php:457, ../includes/Skins/Skin_Default.php:502, ../includes/Skins/Skin_Default.php:549, ../includes/Skins/Skin_Default.php:598, ../includes/Skins/Skin_Default.php:777, ../includes/Skins/Skin_Default.php:824, ../includes/Skins/Skin_Default.php:872, ../includes/Skins/Skin_Default.php:921, ../includes/Skins/Skin_Five.php:68, ../includes/Skins/Skin_Five.php:174, ../includes/Skins/Skin_Five.php:531, ../includes/Skins/Skin_Five.php:576, ../includes/Skins/Skin_Five.php:623, ../includes/Skins/Skin_Five.php:672, ../includes/Skins/Skin_Five.php:874, ../includes/Skins/Skin_Five.php:921, ../includes/Skins/Skin_Five.php:968, ../includes/Skins/Skin_Five.php:1017, ../includes/Skins/Skin_Four.php:68, ../includes/Skins/Skin_Four.php:156, ../includes/Skins/Skin_Four.php:479, ../includes/Skins/Skin_Four.php:524, ../includes/Skins/Skin_Four.php:571, ../includes/Skins/Skin_Four.php:620, ../includes/Skins/Skin_Four.php:819, ../includes/Skins/Skin_Four.php:866, ../includes/Skins/Skin_Four.php:913, ../includes/Skins/Skin_Four.php:962, ../includes/Skins/Skin_One.php:68, ../includes/Skins/Skin_One.php:156, ../includes/Skins/Skin_One.php:527, ../includes/Skins/Skin_One.php:572, ../includes/Skins/Skin_One.php:619, ../includes/Skins/Skin_One.php:668, ../includes/Skins/Skin_One.php:854, ../includes/Skins/Skin_One.php:902, ../includes/Skins/Skin_One.php:949, ../includes/Skins/Skin_One.php:998, ../includes/Skins/Skin_Seven.php:68, ../includes/Skins/Skin_Seven.php:156, ../includes/Skins/Skin_Seven.php:479, ../includes/Skins/Skin_Seven.php:524, ../includes/Skins/Skin_Seven.php:571, ../includes/Skins/Skin_Seven.php:620, ../includes/Skins/Skin_Seven.php:819, ../includes/Skins/Skin_Seven.php:866, ../includes/Skins/Skin_Seven.php:913, ../includes/Skins/Skin_Seven.php:962, ../includes/Skins/Skin_Six.php:68, ../includes/Skins/Skin_Six.php:157, ../includes/Skins/Skin_Six.php:481, ../includes/Skins/Skin_Six.php:526, ../includes/Skins/Skin_Six.php:574, ../includes/Skins/Skin_Six.php:623, ../includes/Skins/Skin_Six.php:823, ../includes/Skins/Skin_Six.php:871, ../includes/Skins/Skin_Six.php:919, ../includes/Skins/Skin_Six.php:968, ../includes/Skins/Skin_Three.php:68, ../includes/Skins/Skin_Three.php:174, ../includes/Skins/Skin_Three.php:500, ../includes/Skins/Skin_Three.php:545, ../includes/Skins/Skin_Three.php:593, ../includes/Skins/Skin_Three.php:642, ../includes/Skins/Skin_Three.php:841, ../includes/Skins/Skin_Three.php:889, ../includes/Skins/Skin_Three.php:937, ../includes/Skins/Skin_Three.php:986, ../includes/Skins/Skin_Two.php:68, ../includes/Skins/Skin_Two.php:173, ../includes/Skins/Skin_Two.php:499, ../includes/Skins/Skin_Two.php:544, ../includes/Skins/Skin_Two.php:592, ../includes/Skins/Skin_Two.php:641, ../includes/Skins/Skin_Two.php:840, ../includes/Skins/Skin_Two.php:888, ../includes/Skins/Skin_Two.php:936, ../includes/Skins/Skin_Two.php:985, ../includes/Traits/Extender.php:216, ../includes/Traits/Extender.php:706, ../includes/Traits/Extender.php:803, ../includes/Traits/Extender.php:2475, ../includes/Traits/Extender.php:2499, ../includes/Traits/Extender.php:2636, ../includes/Traits/Extender.php:2666, ../includes/Traits/Extender.php:2823, ../includes/Traits/Extender.php:2853, ../includes/Traits/Extender.php:3785, ../includes/Traits/Extender.php:4175, ../includes/Traits/Extender.php:4292, ../includes/Traits/Extender.php:4474, ../includes/Traits/Extender.php:4964, ../includes/Traits/Filterable_Gallery_Extender.php:505
msgid "Background Color"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:247, ../includes/Elements/Content_Timeline.php:1028, ../includes/Elements/Flip_Carousel.php:571, ../includes/Elements/Image_Hot_Spots.php:724, ../includes/Elements/Interactive_Card.php:1528, ../includes/Elements/Interactive_Card.php:1801, ../includes/Elements/Interactive_Card.php:2020, ../includes/Elements/LD_Course_List.php:2451, ../includes/Elements/Lightbox.php:1434, ../includes/Elements/Multicolumn_Pricing_Table.php:1103, ../includes/Elements/Multicolumn_Pricing_Table.php:1349, ../includes/Elements/Multicolumn_Pricing_Table.php:1674, ../includes/Elements/Multicolumn_Pricing_Table.php:2155, ../includes/Elements/Multicolumn_Pricing_Table.php:2457, ../includes/Elements/Offcanvas.php:1000, ../includes/Elements/Post_List.php:1351, ../includes/Elements/Stacked_Cards.php:1000, ../includes/Elements/Stacked_Cards.php:1194, ../includes/Elements/Static_Product.php:1113, ../includes/Elements/Static_Product.php:1395, ../includes/Elements/Team_Member_Carousel.php:1942, ../includes/Elements/Woo_Product_Slider.php:1409, ../includes/Elements/Woo_Product_Slider.php:2271, ../includes/Elements/Woo_Product_Slider.php:2919, ../includes/Elements/Woo_Thank_You.php:2938, ../includes/Skins/Skin_Default.php:396, ../includes/Skins/Skin_Default.php:718, ../includes/Skins/Skin_Five.php:470, ../includes/Skins/Skin_Five.php:815, ../includes/Skins/Skin_Four.php:418, ../includes/Skins/Skin_Four.php:760, ../includes/Skins/Skin_One.php:466, ../includes/Skins/Skin_One.php:795, ../includes/Skins/Skin_Seven.php:418, ../includes/Skins/Skin_Seven.php:705, ../includes/Skins/Skin_Six.php:419, ../includes/Skins/Skin_Six.php:764, ../includes/Skins/Skin_Three.php:438, ../includes/Skins/Skin_Three.php:782, ../includes/Skins/Skin_Two.php:437, ../includes/Skins/Skin_Two.php:781, ../includes/Traits/Extender.php:531
msgid "Icon Size"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:264, ../includes/Elements/Flip_Carousel.php:630, ../includes/Elements/Image_Comparison.php:375, ../includes/Elements/Image_Comparison.php:440, ../includes/Elements/Instagram_Feed.php:534, ../includes/Elements/Interactive_Card.php:1597, ../includes/Elements/Interactive_Card.php:1687, ../includes/Elements/Interactive_Card.php:1856, ../includes/Elements/Interactive_Card.php:1925, ../includes/Elements/Interactive_Card.php:1994, ../includes/Elements/Lightbox.php:1100, ../includes/Elements/Lightbox.php:1205, ../includes/Elements/Lightbox.php:1842, ../includes/Elements/Lightbox.php:1913, ../includes/Elements/Multicolumn_Pricing_Table.php:820, ../includes/Elements/Multicolumn_Pricing_Table.php:1659, ../includes/Elements/Post_Block.php:1422, ../includes/Elements/Post_List.php:1371, ../includes/Elements/Stacked_Cards.php:1024, ../includes/Elements/Stacked_Cards.php:1218, ../includes/Elements/Twitter_Feed_Carousel.php:789, ../includes/Traits/Extender.php:659, ../includes/Traits/Extender.php:757
msgid "Icon Color"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:278, ../includes/Elements/Sphere_Photo_Viewer.php:518, ../includes/Elements/Woo_Thank_You.php:1695
msgid "Items"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:287, ../includes/Elements/Advanced_Search.php:1613, ../includes/Elements/Advanced_Search.php:1924, ../includes/Elements/Counter.php:354, ../includes/Elements/Divider.php:320, ../includes/Elements/Dynamic_Filterable_Gallery.php:584, ../includes/Elements/Fancy_Chart.php:975, ../includes/Elements/Flip_Carousel.php:690, ../includes/Elements/Flip_Carousel.php:794, ../includes/Elements/Image_Hot_Spots.php:642, ../includes/Elements/LD_Course_List.php:906, ../includes/Elements/LD_Course_List.php:1085, ../includes/Elements/Lightbox.php:481, ../includes/Elements/Lightbox.php:514, ../includes/Elements/Lightbox.php:827, ../includes/Elements/Multicolumn_Pricing_Table.php:1043, ../includes/Elements/Multicolumn_Pricing_Table.php:1592, ../includes/Elements/Multicolumn_Pricing_Table.php:1834, ../includes/Elements/Multicolumn_Pricing_Table.php:2168, ../includes/Elements/Multicolumn_Pricing_Table.php:2250, ../includes/Elements/Multicolumn_Pricing_Table.php:2470, ../includes/Elements/Offcanvas.php:577, ../includes/Elements/Offcanvas.php:958, ../includes/Elements/Offcanvas.php:1236, ../includes/Elements/One_Page_Navigation.php:307, ../includes/Elements/Post_Block.php:1075, ../includes/Elements/Post_Block.php:1145, ../includes/Elements/Post_Block.php:1235, ../includes/Elements/Post_Block.php:1263, ../includes/Elements/Post_Carousel.php:1284, ../includes/Elements/Post_Carousel.php:1558, ../includes/Elements/Post_Carousel.php:1628, ../includes/Elements/Post_Carousel.php:1848, ../includes/Elements/Post_List.php:1325, ../includes/Elements/Price_Menu.php:261, ../includes/Elements/Sphere_Photo_Viewer.php:610, ../includes/Elements/Sphere_Photo_Viewer.php:787, ../includes/Elements/Sphere_Photo_Viewer.php:938, ../includes/Elements/Stacked_Cards.php:1286, ../includes/Elements/Stacked_Cards.php:1343, ../includes/Elements/Team_Member_Carousel.php:924, ../includes/Elements/Team_Member_Carousel.php:1122, ../includes/Elements/Testimonial_Slider.php:711, ../includes/Elements/Toggle.php:297, ../includes/Elements/Toggle.php:687, ../includes/Elements/Woo_Account_Dashboard.php:1886, ../includes/Elements/Woo_Account_Dashboard.php:2010, ../includes/Elements/Woo_Cross_Sells.php:520, ../includes/Elements/Woo_Product_Slider.php:928, ../includes/Elements/Woo_Product_Slider.php:1073, ../includes/Elements/Woo_Thank_You.php:371, ../includes/Elements/Woo_Thank_You.php:965, ../includes/Skins/Skin_Default.php:215, ../includes/Skins/Skin_Default.php:633, ../includes/Skins/Skin_Five.php:264, ../includes/Skins/Skin_Five.php:707, ../includes/Skins/Skin_Four.php:215, ../includes/Skins/Skin_Four.php:655, ../includes/Skins/Skin_One.php:263, ../includes/Skins/Skin_One.php:703, ../includes/Skins/Skin_Seven.php:215, ../includes/Skins/Skin_Seven.php:743, ../includes/Skins/Skin_Six.php:216, ../includes/Skins/Skin_Six.php:659, ../includes/Skins/Skin_Three.php:234, ../includes/Skins/Skin_Three.php:677, ../includes/Skins/Skin_Two.php:233, ../includes/Skins/Skin_Two.php:676, ../includes/Traits/Extender.php:155, ../includes/Traits/Extender.php:2932, ../includes/Traits/Extender.php:4972, ../includes/Traits/Extender.php:5412
msgid "Alignment"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:327
msgid "Dropdown Menu"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:340
msgid "Top Level Item"
msgstr ""

#: ../includes/Elements/Advanced_Menu.php:353
msgid "Dropdown Item"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:25
msgid "Advanced Search"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:86
msgid "Search Settings"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:93, ../includes/Extensions/Conditional_Display.php:1707
msgid "Select Post Type"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:104
msgid "Include Category"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:116
msgid "Exclude Category"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:128
msgid "Search Among Taxonomies"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:139
msgid "Search Among SKU"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:150
msgid "Show Initial Result "
msgstr ""

#: ../includes/Elements/Advanced_Search.php:160
msgid "Show Category List"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:162, ../includes/Elements/Advanced_Search.php:174, ../includes/Elements/Advanced_Search.php:186, ../includes/Elements/Advanced_Search.php:198, ../includes/Elements/Advanced_Search.php:252, ../includes/Elements/Advanced_Search.php:264, ../includes/Elements/Advanced_Search.php:276, ../includes/Elements/Counter.php:152, ../includes/Elements/Divider.php:177, ../includes/Elements/Divider.php:213, ../includes/Elements/Fancy_Chart.php:213, ../includes/Elements/Fancy_Chart.php:228, ../includes/Elements/Fancy_Chart.php:244, ../includes/Elements/Fancy_Chart.php:270, ../includes/Elements/Fancy_Chart.php:282, ../includes/Elements/Fancy_Chart.php:294, ../includes/Elements/Fancy_Chart.php:320, ../includes/Elements/Fancy_Chart.php:1246, ../includes/Elements/Fancy_Chart.php:1314, ../includes/Elements/Fancy_Chart.php:1347, ../includes/Elements/Fancy_Chart.php:1359, ../includes/Elements/Fancy_Chart.php:1385, ../includes/Elements/Fancy_Chart.php:1500, ../includes/Elements/Fancy_Chart.php:1562, ../includes/Elements/Image_Hot_Spots.php:272, ../includes/Elements/Interactive_Card.php:129, ../includes/Elements/Interactive_Card.php:288, ../includes/Elements/Interactive_Card.php:521, ../includes/Elements/LD_Course_List.php:272, ../includes/Elements/LD_Course_List.php:295, ../includes/Elements/LD_Course_List.php:318, ../includes/Elements/LD_Course_List.php:341, ../includes/Elements/LD_Course_List.php:364, ../includes/Elements/LD_Course_List.php:387, ../includes/Elements/LD_Course_List.php:419, ../includes/Elements/LD_Course_List.php:441, ../includes/Elements/LD_Course_List.php:480, ../includes/Elements/LD_Course_List.php:499, ../includes/Elements/LD_Course_List.php:534, ../includes/Elements/LD_Course_List.php:554, ../includes/Elements/LD_Course_List.php:606, ../includes/Elements/LD_Course_List.php:629, ../includes/Elements/LD_Course_List.php:651, ../includes/Elements/LD_Course_List.php:708, ../includes/Elements/LD_Course_List.php:1033, ../includes/Elements/LD_Course_List.php:2184, ../includes/Elements/Logo_Carousel.php:591, ../includes/Elements/One_Page_Navigation.php:210, ../includes/Elements/Post_Carousel.php:158, ../includes/Elements/Post_Carousel.php:242, ../includes/Elements/Post_Carousel.php:290, ../includes/Elements/Post_Carousel.php:328, ../includes/Elements/Post_Carousel.php:424, ../includes/Elements/Post_Carousel.php:454, ../includes/Elements/Post_Carousel.php:470, ../includes/Elements/Post_Carousel.php:485, ../includes/Elements/Post_Carousel.php:516, ../includes/Elements/Post_Carousel.php:582, ../includes/Elements/Post_Carousel.php:801, ../includes/Elements/Post_List.php:542, ../includes/Elements/Post_List.php:564, ../includes/Elements/Post_List.php:576, ../includes/Elements/Post_List.php:666, ../includes/Elements/Post_List.php:730, ../includes/Elements/Post_List.php:731, ../includes/Elements/Post_List.php:769, ../includes/Elements/Post_List.php:803, ../includes/Elements/Post_List.php:818, ../includes/Elements/Post_List.php:834, ../includes/Elements/Protected_Content.php:158, ../includes/Elements/Protected_Content.php:222, ../includes/Elements/Protected_Content.php:253, ../includes/Elements/Sphere_Photo_Viewer.php:95, ../includes/Elements/Sphere_Photo_Viewer.php:124, ../includes/Elements/Sphere_Photo_Viewer.php:356, ../includes/Elements/Sphere_Photo_Viewer.php:595, ../includes/Elements/Stacked_Cards.php:422, ../includes/Elements/Stacked_Cards.php:882, ../includes/Elements/Static_Product.php:189, ../includes/Elements/Static_Product.php:221, ../includes/Elements/Static_Product.php:1203, ../includes/Elements/Static_Product.php:1470, ../includes/Elements/Team_Member_Carousel.php:636, ../includes/Elements/Team_Member_Carousel.php:852, ../includes/Elements/Testimonial_Slider.php:288, ../includes/Elements/Testimonial_Slider.php:376, ../includes/Elements/Testimonial_Slider.php:454, ../includes/Elements/Testimonial_Slider.php:518, ../includes/Elements/Testimonial_Slider.php:582, ../includes/Elements/Woo_Account_Dashboard.php:400, ../includes/Elements/Woo_Account_Dashboard.php:415, ../includes/Elements/Woo_Account_Dashboard.php:430, ../includes/Elements/Woo_Account_Dashboard.php:445, ../includes/Elements/Woo_Account_Dashboard.php:460, ../includes/Elements/Woo_Account_Dashboard.php:475, ../includes/Elements/Woo_Collections.php:179, ../includes/Elements/Woo_Product_Slider.php:357, ../includes/Elements/Woo_Product_Slider.php:481, ../includes/Elements/Woo_Product_Slider.php:694, ../includes/Elements/Woo_Product_Slider.php:753, ../includes/Elements/Woo_Thank_You.php:170, ../includes/Elements/Woo_Thank_You.php:218, ../includes/Elements/Woo_Thank_You.php:280, ../includes/Elements/Woo_Thank_You.php:293, ../includes/Elements/Woo_Thank_You.php:307, ../includes/Elements/Woo_Thank_You.php:320, ../includes/Elements/Woo_Thank_You.php:333, ../includes/Elements/Woo_Thank_You.php:346, ../includes/Elements/Woo_Thank_You.php:402, ../includes/Elements/Woo_Thank_You.php:435, ../includes/Elements/Woo_Thank_You.php:463, ../includes/Elements/Woo_Thank_You.php:520, ../includes/Elements/Woo_Thank_You.php:548, ../includes/Elements/Woo_Thank_You.php:576, ../includes/Elements/Woo_Thank_You.php:618, ../includes/Elements/Woo_Thank_You.php:644, ../includes/Elements/Woo_Thank_You.php:670, ../includes/Elements/Woo_Thank_You.php:696, ../includes/Elements/Woo_Thank_You.php:722, ../includes/Elements/Woo_Thank_You.php:761, ../includes/Elements/Woo_Thank_You.php:793, ../includes/Elements/Woo_Thank_You.php:836, ../includes/Elements/Woo_Thank_You.php:848, ../includes/Elements/Woo_Thank_You.php:860, ../includes/Elements/Woo_Thank_You.php:890, ../includes/Elements/Woo_Thank_You.php:919, ../includes/Elements/Woo_Thank_You.php:934, ../includes/Elements/Woo_Thank_You.php:1017, ../includes/Elements/Woo_Thank_You.php:1045, ../includes/Elements/Woo_Thank_You.php:1112, ../includes/Elements/Woo_Thank_You.php:1193, ../includes/Extensions/Conditional_Display.php:73, ../includes/Extensions/Conditional_Display.php:1043, ../includes/Extensions/Content_Protection.php:155, ../includes/Extensions/EAEL_Tooltip_Section.php:131, ../includes/Extensions/Smooth_Animation.php:638, ../includes/Extensions/Smooth_Animation.php:812, ../includes/Extensions/Smooth_Animation.php:1027, ../includes/Traits/Extender.php:648, ../includes/Traits/Extender.php:1649, ../includes/Traits/Extender.php:3005, ../includes/Traits/Filterable_Gallery_Extender.php:67, ../includes/Traits/Filterable_Gallery_Extender.php:110
msgid "Show"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:163, ../includes/Elements/Advanced_Search.php:175, ../includes/Elements/Advanced_Search.php:187, ../includes/Elements/Advanced_Search.php:199, ../includes/Elements/Advanced_Search.php:253, ../includes/Elements/Advanced_Search.php:265, ../includes/Elements/Advanced_Search.php:277, ../includes/Elements/Counter.php:153, ../includes/Elements/Divider.php:178, ../includes/Elements/Divider.php:214, ../includes/Elements/Fancy_Chart.php:214, ../includes/Elements/Fancy_Chart.php:229, ../includes/Elements/Fancy_Chart.php:245, ../includes/Elements/Fancy_Chart.php:271, ../includes/Elements/Fancy_Chart.php:283, ../includes/Elements/Fancy_Chart.php:295, ../includes/Elements/Fancy_Chart.php:321, ../includes/Elements/Fancy_Chart.php:1247, ../includes/Elements/Fancy_Chart.php:1315, ../includes/Elements/Fancy_Chart.php:1348, ../includes/Elements/Fancy_Chart.php:1360, ../includes/Elements/Fancy_Chart.php:1386, ../includes/Elements/Fancy_Chart.php:1501, ../includes/Elements/Fancy_Chart.php:1563, ../includes/Elements/Image_Hot_Spots.php:273, ../includes/Elements/Interactive_Card.php:130, ../includes/Elements/Interactive_Card.php:289, ../includes/Elements/Interactive_Card.php:522, ../includes/Elements/LD_Course_List.php:276, ../includes/Elements/LD_Course_List.php:299, ../includes/Elements/LD_Course_List.php:322, ../includes/Elements/LD_Course_List.php:345, ../includes/Elements/LD_Course_List.php:368, ../includes/Elements/LD_Course_List.php:391, ../includes/Elements/LD_Course_List.php:423, ../includes/Elements/LD_Course_List.php:445, ../includes/Elements/LD_Course_List.php:484, ../includes/Elements/LD_Course_List.php:503, ../includes/Elements/LD_Course_List.php:538, ../includes/Elements/LD_Course_List.php:558, ../includes/Elements/LD_Course_List.php:610, ../includes/Elements/LD_Course_List.php:633, ../includes/Elements/LD_Course_List.php:655, ../includes/Elements/LD_Course_List.php:712, ../includes/Elements/LD_Course_List.php:1034, ../includes/Elements/LD_Course_List.php:2185, ../includes/Elements/Logo_Carousel.php:592, ../includes/Elements/One_Page_Navigation.php:211, ../includes/Elements/Post_Carousel.php:455, ../includes/Elements/Post_Carousel.php:471, ../includes/Elements/Post_Carousel.php:486, ../includes/Elements/Post_Carousel.php:517, ../includes/Elements/Post_Carousel.php:583, ../includes/Elements/Post_Carousel.php:802, ../includes/Elements/Post_List.php:543, ../includes/Elements/Post_List.php:565, ../includes/Elements/Post_List.php:577, ../includes/Elements/Post_List.php:667, ../includes/Elements/Post_List.php:770, ../includes/Elements/Post_List.php:804, ../includes/Elements/Post_List.php:819, ../includes/Elements/Post_List.php:835, ../includes/Elements/Protected_Content.php:159, ../includes/Elements/Protected_Content.php:223, ../includes/Elements/Protected_Content.php:254, ../includes/Elements/Sphere_Photo_Viewer.php:96, ../includes/Elements/Sphere_Photo_Viewer.php:125, ../includes/Elements/Sphere_Photo_Viewer.php:357, ../includes/Elements/Sphere_Photo_Viewer.php:591, ../includes/Elements/Stacked_Cards.php:423, ../includes/Elements/Stacked_Cards.php:883, ../includes/Elements/Static_Product.php:190, ../includes/Elements/Static_Product.php:222, ../includes/Elements/Static_Product.php:1204, ../includes/Elements/Static_Product.php:1471, ../includes/Elements/Team_Member_Carousel.php:853, ../includes/Elements/Testimonial_Slider.php:289, ../includes/Elements/Testimonial_Slider.php:377, ../includes/Elements/Testimonial_Slider.php:519, ../includes/Elements/Testimonial_Slider.php:583, ../includes/Elements/Woo_Account_Dashboard.php:401, ../includes/Elements/Woo_Account_Dashboard.php:416, ../includes/Elements/Woo_Account_Dashboard.php:431, ../includes/Elements/Woo_Account_Dashboard.php:446, ../includes/Elements/Woo_Account_Dashboard.php:461, ../includes/Elements/Woo_Account_Dashboard.php:476, ../includes/Elements/Woo_Collections.php:180, ../includes/Elements/Woo_Product_Slider.php:358, ../includes/Elements/Woo_Product_Slider.php:482, ../includes/Elements/Woo_Product_Slider.php:695, ../includes/Elements/Woo_Product_Slider.php:754, ../includes/Elements/Woo_Thank_You.php:171, ../includes/Elements/Woo_Thank_You.php:219, ../includes/Elements/Woo_Thank_You.php:281, ../includes/Elements/Woo_Thank_You.php:294, ../includes/Elements/Woo_Thank_You.php:308, ../includes/Elements/Woo_Thank_You.php:321, ../includes/Elements/Woo_Thank_You.php:334, ../includes/Elements/Woo_Thank_You.php:347, ../includes/Elements/Woo_Thank_You.php:403, ../includes/Elements/Woo_Thank_You.php:436, ../includes/Elements/Woo_Thank_You.php:464, ../includes/Elements/Woo_Thank_You.php:521, ../includes/Elements/Woo_Thank_You.php:549, ../includes/Elements/Woo_Thank_You.php:577, ../includes/Elements/Woo_Thank_You.php:619, ../includes/Elements/Woo_Thank_You.php:645, ../includes/Elements/Woo_Thank_You.php:671, ../includes/Elements/Woo_Thank_You.php:697, ../includes/Elements/Woo_Thank_You.php:723, ../includes/Elements/Woo_Thank_You.php:762, ../includes/Elements/Woo_Thank_You.php:794, ../includes/Elements/Woo_Thank_You.php:837, ../includes/Elements/Woo_Thank_You.php:849, ../includes/Elements/Woo_Thank_You.php:861, ../includes/Elements/Woo_Thank_You.php:891, ../includes/Elements/Woo_Thank_You.php:920, ../includes/Elements/Woo_Thank_You.php:935, ../includes/Elements/Woo_Thank_You.php:1018, ../includes/Elements/Woo_Thank_You.php:1046, ../includes/Elements/Woo_Thank_You.php:1113, ../includes/Elements/Woo_Thank_You.php:1194, ../includes/Extensions/Conditional_Display.php:77, ../includes/Extensions/Conditional_Display.php:1044, ../includes/Extensions/Content_Protection.php:156, ../includes/Extensions/EAEL_Tooltip_Section.php:132, ../includes/Extensions/Smooth_Animation.php:639, ../includes/Extensions/Smooth_Animation.php:813, ../includes/Extensions/Smooth_Animation.php:1028, ../includes/Traits/Extender.php:649, ../includes/Traits/Extender.php:1650, ../includes/Traits/Extender.php:3006
msgid "Hide"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:172
msgid "Use Include & Exclude for Result"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:184
msgid "Show Category Result"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:196
msgid "Show Popular Keywords"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:208, ../includes/Elements/Advanced_Search.php:374, ../includes/Elements/Advanced_Search.php:1042
msgid "Popular Keywords"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:212
msgid "Number of popular searches to display."
msgstr ""

#: ../includes/Elements/Advanced_Search.php:222
msgid "Keywords Search"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:224
msgid "Minimum number of searches for a keyword to be considered a popular search."
msgstr ""

#: ../includes/Elements/Advanced_Search.php:236
msgid "Keywords Length"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:238
msgid "Minimum number of characters for a keyword to be considered a popular search."
msgstr ""

#: ../includes/Elements/Advanced_Search.php:250
msgid "Show Content Image"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:262
msgid "Show Total Results"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:274
msgid "Show Search Button"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:286
msgid "Open Result Link on New Tab"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:292
msgid "Open search result's link on a new tab on click"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:300
msgid "Show Product Price"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:306
msgid "Show product price with product title."
msgstr ""

#: ../includes/Elements/Advanced_Search.php:318, ../includes/Elements/Advanced_Search.php:552
msgid "Search Field"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:325, ../includes/Elements/Google_Map.php:583
msgid "Placeholder Text"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:327
msgid "Enter Search Keyword"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:337
msgid "Category List Text"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:339
msgid "All Categories"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:349, ../includes/Elements/Interactive_Card.php:300, ../includes/Elements/Interactive_Card.php:533, ../includes/Elements/LD_Course_List.php:514, ../includes/Elements/LD_Course_List.php:832, ../includes/Elements/Mailchimp.php:307, ../includes/Elements/Offcanvas.php:290, ../includes/Elements/Stacked_Cards.php:435, ../includes/Elements/Static_Product.php:379, ../includes/Elements/Static_Product.php:436, ../includes/Traits/Extender.php:362
msgid "Button Text"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:351, ../includes/Elements/Post_List.php:2701, ../includes/Extensions/Conditional_Display.php:1693
msgid "Search"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:365
msgid "Search Result"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:372
msgid "Popular Search Text"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:384
msgid "Popular Search Tag"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:388, ../includes/Elements/Advanced_Search.php:420, ../includes/Elements/Divider.php:284, ../includes/Elements/Fancy_Chart.php:94, ../includes/Elements/Interactive_Card.php:184, ../includes/Elements/LD_Course_List.php:1302, ../includes/Elements/Multicolumn_Pricing_Table.php:122, ../includes/Elements/Post_Carousel.php:175, ../includes/Elements/Post_List.php:680, ../includes/Elements/Stacked_Cards.php:104, ../includes/Elements/Woo_Cross_Sells.php:501, ../includes/Elements/Woo_Product_Slider.php:371, ../includes/Elements/Woo_Product_Slider.php:534
msgid "H1"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:389, ../includes/Elements/Advanced_Search.php:421, ../includes/Elements/Divider.php:285, ../includes/Elements/Fancy_Chart.php:95, ../includes/Elements/Interactive_Card.php:185, ../includes/Elements/LD_Course_List.php:1303, ../includes/Elements/Multicolumn_Pricing_Table.php:126, ../includes/Elements/Post_Carousel.php:179, ../includes/Elements/Post_List.php:684, ../includes/Elements/Stacked_Cards.php:105, ../includes/Elements/Woo_Cross_Sells.php:502, ../includes/Elements/Woo_Product_Slider.php:372, ../includes/Elements/Woo_Product_Slider.php:535
msgid "H2"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:390, ../includes/Elements/Advanced_Search.php:422, ../includes/Elements/Divider.php:286, ../includes/Elements/Fancy_Chart.php:96, ../includes/Elements/Interactive_Card.php:186, ../includes/Elements/LD_Course_List.php:1304, ../includes/Elements/Multicolumn_Pricing_Table.php:130, ../includes/Elements/Post_Carousel.php:183, ../includes/Elements/Post_List.php:688, ../includes/Elements/Stacked_Cards.php:106, ../includes/Elements/Woo_Cross_Sells.php:503, ../includes/Elements/Woo_Product_Slider.php:373, ../includes/Elements/Woo_Product_Slider.php:536
msgid "H3"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:391, ../includes/Elements/Advanced_Search.php:423, ../includes/Elements/Divider.php:287, ../includes/Elements/Fancy_Chart.php:97, ../includes/Elements/Interactive_Card.php:187, ../includes/Elements/LD_Course_List.php:1305, ../includes/Elements/Multicolumn_Pricing_Table.php:134, ../includes/Elements/Post_Carousel.php:187, ../includes/Elements/Post_List.php:692, ../includes/Elements/Stacked_Cards.php:107, ../includes/Elements/Woo_Cross_Sells.php:504, ../includes/Elements/Woo_Product_Slider.php:374, ../includes/Elements/Woo_Product_Slider.php:537
msgid "H4"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:392, ../includes/Elements/Advanced_Search.php:424, ../includes/Elements/Divider.php:288, ../includes/Elements/Fancy_Chart.php:98, ../includes/Elements/Interactive_Card.php:188, ../includes/Elements/LD_Course_List.php:1306, ../includes/Elements/Multicolumn_Pricing_Table.php:138, ../includes/Elements/Post_Carousel.php:191, ../includes/Elements/Post_List.php:696, ../includes/Elements/Stacked_Cards.php:108, ../includes/Elements/Woo_Cross_Sells.php:505, ../includes/Elements/Woo_Product_Slider.php:375, ../includes/Elements/Woo_Product_Slider.php:538
msgid "H5"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:393, ../includes/Elements/Advanced_Search.php:425, ../includes/Elements/Divider.php:289, ../includes/Elements/Fancy_Chart.php:99, ../includes/Elements/Interactive_Card.php:189, ../includes/Elements/LD_Course_List.php:1307, ../includes/Elements/Multicolumn_Pricing_Table.php:142, ../includes/Elements/Post_Carousel.php:195, ../includes/Elements/Post_List.php:700, ../includes/Elements/Stacked_Cards.php:109, ../includes/Elements/Woo_Cross_Sells.php:506, ../includes/Elements/Woo_Product_Slider.php:376, ../includes/Elements/Woo_Product_Slider.php:539
msgid "H6"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:394, ../includes/Elements/Advanced_Search.php:426, ../includes/Elements/Fancy_Chart.php:100, ../includes/Elements/Multicolumn_Pricing_Table.php:150, ../includes/Elements/Post_Carousel.php:203, ../includes/Elements/Post_List.php:708, ../includes/Elements/Stacked_Cards.php:110, ../includes/Elements/Woo_Cross_Sells.php:507, ../includes/Elements/Woo_Product_Slider.php:377, ../includes/Elements/Woo_Product_Slider.php:540
msgid "Span"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:395, ../includes/Elements/Advanced_Search.php:427, ../includes/Elements/Fancy_Chart.php:101, ../includes/Elements/Multicolumn_Pricing_Table.php:154, ../includes/Elements/Post_Carousel.php:207, ../includes/Elements/Post_List.php:712, ../includes/Elements/Stacked_Cards.php:111, ../includes/Elements/Woo_Cross_Sells.php:508, ../includes/Elements/Woo_Product_Slider.php:378, ../includes/Elements/Woo_Product_Slider.php:541
msgid "P"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:396, ../includes/Elements/Advanced_Search.php:428, ../includes/Elements/Fancy_Chart.php:102, ../includes/Elements/Multicolumn_Pricing_Table.php:146, ../includes/Elements/Post_Carousel.php:199, ../includes/Elements/Post_List.php:704, ../includes/Elements/Stacked_Cards.php:112, ../includes/Elements/Woo_Cross_Sells.php:509, ../includes/Elements/Woo_Product_Slider.php:379, ../includes/Elements/Woo_Product_Slider.php:542
msgid "Div"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:404
msgid "Category Search Text"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:406, ../includes/Elements/Fancy_Chart.php:696
msgid "Categories"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:416
msgid "Category Search Tag"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:436
msgid "Load More Text"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:438
msgid "View All Results"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:448
msgid "Total Results Text"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:449
msgid "Total result count will be displayed on [post count]."
msgstr ""

#: ../includes/Elements/Advanced_Search.php:451, ../includes/Elements/Advanced_Search.php:2131
msgid "Total [post_count] Results"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:461
msgid "Not Found Text"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:463
msgid "No Record Found"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:478
msgid "Search Box"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:495, ../includes/Elements/Advanced_Search.php:984, ../includes/Elements/Advanced_Search.php:1160, ../includes/Elements/Advanced_Search.php:1314, ../includes/Elements/Advanced_Search.php:1428, ../includes/Elements/Advanced_Search.php:1706, ../includes/Elements/Advanced_Search.php:1839, ../includes/Elements/Content_Timeline.php:845, ../includes/Elements/Content_Timeline.php:1744, ../includes/Elements/Content_Timeline.php:1948, ../includes/Elements/Counter.php:384, ../includes/Elements/Counter.php:554, ../includes/Elements/Counter.php:1112, ../includes/Elements/Dynamic_Filterable_Gallery.php:841, ../includes/Elements/Dynamic_Filterable_Gallery.php:915, ../includes/Elements/Dynamic_Filterable_Gallery.php:1111, ../includes/Elements/Dynamic_Filterable_Gallery.php:1346, ../includes/Elements/Dynamic_Filterable_Gallery.php:1710, ../includes/Elements/Flip_Carousel.php:434, ../includes/Elements/Flip_Carousel.php:737, ../includes/Elements/Google_Map.php:1251, ../includes/Elements/Google_Map.php:1314, ../includes/Elements/Image_Comparison.php:759, ../includes/Elements/Image_Hot_Spots.php:752, ../includes/Elements/Instagram_Feed.php:588, ../includes/Elements/Interactive_Card.php:844, ../includes/Elements/Interactive_Card.php:1012, ../includes/Elements/Interactive_Card.php:1146, ../includes/Elements/Interactive_Card.php:1488, ../includes/Elements/Interactive_Card.php:1770, ../includes/Elements/LD_Course_List.php:1115, ../includes/Elements/LD_Course_List.php:1986, ../includes/Elements/LD_Course_List.php:2491, ../includes/Elements/Lightbox.php:891, ../includes/Elements/Lightbox.php:958, ../includes/Elements/Lightbox.php:1393, ../includes/Elements/Lightbox.php:1808, ../includes/Elements/Logo_Carousel.php:712, ../includes/Elements/Logo_Carousel.php:1121, ../includes/Elements/Logo_Carousel.php:1285, ../includes/Elements/Mailchimp.php:413, ../includes/Elements/Mailchimp.php:693, ../includes/Elements/Mailchimp.php:904, ../includes/Elements/Multicolumn_Pricing_Table.php:1015, ../includes/Elements/Multicolumn_Pricing_Table.php:1157, ../includes/Elements/Multicolumn_Pricing_Table.php:1571, ../includes/Elements/Multicolumn_Pricing_Table.php:1700, ../includes/Elements/Multicolumn_Pricing_Table.php:1919, ../includes/Elements/Multicolumn_Pricing_Table.php:1971, ../includes/Elements/Multicolumn_Pricing_Table.php:2011, ../includes/Elements/Multicolumn_Pricing_Table.php:2051, ../includes/Elements/Multicolumn_Pricing_Table.php:2215, ../includes/Elements/Multicolumn_Pricing_Table.php:2401, ../includes/Elements/Multicolumn_Pricing_Table.php:2606, ../includes/Elements/Offcanvas.php:539, ../includes/Elements/Offcanvas.php:689, ../includes/Elements/Offcanvas.php:1117, ../includes/Elements/One_Page_Navigation.php:383, ../includes/Elements/One_Page_Navigation.php:461, ../includes/Elements/One_Page_Navigation.php:702, ../includes/Elements/Post_Carousel.php:1083, ../includes/Elements/Post_Carousel.php:1147, ../includes/Elements/Post_Carousel.php:2128, ../includes/Elements/Post_Carousel.php:2270, ../includes/Elements/Post_List.php:1052, ../includes/Elements/Post_List.php:1290, ../includes/Elements/Post_List.php:1423, ../includes/Elements/Post_List.php:1971, ../includes/Elements/Post_List.php:2351, ../includes/Elements/Price_Menu.php:369, ../includes/Elements/Price_Menu.php:429, ../includes/Elements/Price_Menu.php:870, ../includes/Elements/Protected_Content.php:420, ../includes/Elements/Protected_Content.php:507, ../includes/Elements/Protected_Content.php:588, ../includes/Elements/Protected_Content.php:661, ../includes/Elements/Sphere_Photo_Viewer.php:694, ../includes/Elements/Sphere_Photo_Viewer.php:993, ../includes/Elements/Stacked_Cards.php:967, ../includes/Elements/Stacked_Cards.php:1036, ../includes/Elements/Stacked_Cards.php:1163, ../includes/Elements/Stacked_Cards.php:1390, ../includes/Elements/Static_Product.php:927, ../includes/Elements/Static_Product.php:1137, ../includes/Elements/Static_Product.php:1416, ../includes/Elements/Team_Member_Carousel.php:962, ../includes/Elements/Team_Member_Carousel.php:1085, ../includes/Elements/Team_Member_Carousel.php:2026, ../includes/Elements/Team_Member_Carousel.php:2291, ../includes/Elements/Testimonial_Slider.php:752, ../includes/Elements/Testimonial_Slider.php:896, ../includes/Elements/Testimonial_Slider.php:1165, ../includes/Elements/Testimonial_Slider.php:1567, ../includes/Elements/Testimonial_Slider.php:1707, ../includes/Elements/Twitter_Feed_Carousel.php:603, ../includes/Elements/Twitter_Feed_Carousel.php:662, ../includes/Elements/Twitter_Feed_Carousel.php:695, ../includes/Elements/Twitter_Feed_Carousel.php:1175, ../includes/Elements/Twitter_Feed_Carousel.php:1314, ../includes/Elements/Woo_Account_Dashboard.php:528, ../includes/Elements/Woo_Account_Dashboard.php:607, ../includes/Elements/Woo_Account_Dashboard.php:623, ../includes/Elements/Woo_Account_Dashboard.php:748, ../includes/Elements/Woo_Account_Dashboard.php:764, ../includes/Elements/Woo_Account_Dashboard.php:1139, ../includes/Elements/Woo_Account_Dashboard.php:1281, ../includes/Elements/Woo_Account_Dashboard.php:1314, ../includes/Elements/Woo_Account_Dashboard.php:1409, ../includes/Elements/Woo_Account_Dashboard.php:1633, ../includes/Elements/Woo_Account_Dashboard.php:1849, ../includes/Elements/Woo_Account_Dashboard.php:1973, ../includes/Elements/Woo_Account_Dashboard.php:2098, ../includes/Elements/Woo_Account_Dashboard.php:2192, ../includes/Elements/Woo_Account_Dashboard.php:2277, ../includes/Elements/Woo_Account_Dashboard.php:2364, ../includes/Elements/Woo_Account_Dashboard.php:2409, ../includes/Elements/Woo_Account_Dashboard.php:2650, ../includes/Elements/Woo_Collections.php:240, ../includes/Elements/Woo_Collections.php:581, ../includes/Elements/Woo_Cross_Sells.php:578, ../includes/Elements/Woo_Cross_Sells.php:643, ../includes/Elements/Woo_Cross_Sells.php:737, ../includes/Elements/Woo_Cross_Sells.php:920, ../includes/Elements/Woo_Cross_Sells.php:1084, ../includes/Elements/Woo_Product_Slider.php:285, ../includes/Elements/Woo_Product_Slider.php:1275, ../includes/Elements/Woo_Product_Slider.php:2599, ../includes/Elements/Woo_Thank_You.php:1569, ../includes/Elements/Woo_Thank_You.php:1683, ../includes/Elements/Woo_Thank_You.php:1747, ../includes/Elements/Woo_Thank_You.php:1969, ../includes/Elements/Woo_Thank_You.php:2000, ../includes/Elements/Woo_Thank_You.php:2049, ../includes/Elements/Woo_Thank_You.php:2361, ../includes/Elements/Woo_Thank_You.php:2402, ../includes/Elements/Woo_Thank_You.php:2827, ../includes/Elements/Woo_Thank_You.php:3006, ../includes/Elements/Woo_Thank_You.php:3188, ../includes/Extensions/Content_Protection.php:345, ../includes/Extensions/Content_Protection.php:429, ../includes/Extensions/Content_Protection.php:508, ../includes/Extensions/EAEL_Tooltip_Section.php:353, ../includes/Skins/Skin_Default.php:99, ../includes/Skins/Skin_Default.php:189, ../includes/Skins/Skin_Default.php:275, ../includes/Skins/Skin_Five.php:117, ../includes/Skins/Skin_Five.php:238, ../includes/Skins/Skin_Five.php:407, ../includes/Skins/Skin_Four.php:99, ../includes/Skins/Skin_Four.php:189, ../includes/Skins/Skin_Four.php:355, ../includes/Skins/Skin_One.php:99, ../includes/Skins/Skin_One.php:213, ../includes/Skins/Skin_One.php:403, ../includes/Skins/Skin_Seven.php:99, ../includes/Skins/Skin_Seven.php:189, ../includes/Skins/Skin_Seven.php:355, ../includes/Skins/Skin_Six.php:99, ../includes/Skins/Skin_Six.php:190, ../includes/Skins/Skin_Six.php:356, ../includes/Skins/Skin_Three.php:117, ../includes/Skins/Skin_Three.php:208, ../includes/Skins/Skin_Three.php:375, ../includes/Skins/Skin_Two.php:116, ../includes/Skins/Skin_Two.php:207, ../includes/Skins/Skin_Two.php:374, ../includes/Traits/Extender.php:943, ../includes/Traits/Extender.php:2563, ../includes/Traits/Extender.php:2773, ../includes/Traits/Extender.php:2912, ../includes/Traits/Extender.php:3736, ../includes/Traits/Extender.php:4126, ../includes/Traits/Extender.php:4276, ../includes/Traits/Extender.php:4421, ../includes/Traits/Filterable_Gallery_Extender.php:402, ../includes/Traits/Filterable_Gallery_Extender.php:685, ../includes/Traits/Filterable_Gallery_Extender.php:737, ../includes/Traits/Filterable_Gallery_Extender.php:777
msgid "Padding"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:507, ../includes/Elements/Advanced_Search.php:996, ../includes/Elements/Advanced_Search.php:1172, ../includes/Elements/Advanced_Search.php:1326, ../includes/Elements/Advanced_Search.php:1440, ../includes/Elements/Advanced_Search.php:1718, ../includes/Elements/Advanced_Search.php:1851, ../includes/Elements/Content_Timeline.php:858, ../includes/Elements/Content_Timeline.php:1693, ../includes/Elements/Content_Timeline.php:1756, ../includes/Elements/Content_Timeline.php:1961, ../includes/Elements/Counter.php:396, ../includes/Elements/Counter.php:575, ../includes/Elements/Counter.php:766, ../includes/Elements/Counter.php:957, ../includes/Elements/Counter.php:1018, ../includes/Elements/Counter.php:1091, ../includes/Elements/Dynamic_Filterable_Gallery.php:853, ../includes/Elements/Dynamic_Filterable_Gallery.php:927, ../includes/Elements/Dynamic_Filterable_Gallery.php:1123, ../includes/Elements/Fancy_Chart.php:953, ../includes/Elements/Fancy_Chart.php:1021, ../includes/Elements/Flip_Carousel.php:446, ../includes/Elements/Flip_Carousel.php:559, ../includes/Elements/Flip_Carousel.php:749, ../includes/Elements/Google_Map.php:1050, ../includes/Elements/Google_Map.php:1239, ../includes/Elements/Google_Map.php:1325, ../includes/Elements/Instagram_Feed.php:600, ../includes/Elements/Interactive_Card.php:1503, ../includes/Elements/Interactive_Card.php:1782, ../includes/Elements/LD_Course_List.php:1128, ../includes/Elements/LD_Course_List.php:1171, ../includes/Elements/LD_Course_List.php:1341, ../includes/Elements/LD_Course_List.php:1393, ../includes/Elements/LD_Course_List.php:1998, ../includes/Elements/LD_Course_List.php:2532, ../includes/Elements/Lightbox.php:1405, ../includes/Elements/Lightbox.php:1785, ../includes/Elements/Mailchimp.php:424, ../includes/Elements/Mailchimp.php:705, ../includes/Elements/Mailchimp.php:915, ../includes/Elements/Multicolumn_Pricing_Table.php:1931, ../includes/Elements/Multicolumn_Pricing_Table.php:2063, ../includes/Elements/Multicolumn_Pricing_Table.php:2421, ../includes/Elements/Multicolumn_Pricing_Table.php:2626, ../includes/Elements/One_Page_Navigation.php:371, ../includes/Elements/Post_Block.php:1343, ../includes/Elements/Post_Carousel.php:1095, ../includes/Elements/Post_Carousel.php:1159, ../includes/Elements/Post_Carousel.php:1494, ../includes/Elements/Post_Carousel.php:1595, ../includes/Elements/Post_Carousel.php:1670, ../includes/Elements/Post_Carousel.php:1721, ../includes/Elements/Post_Carousel.php:1783, ../includes/Elements/Post_Carousel.php:1886, ../includes/Elements/Post_List.php:1063, ../includes/Elements/Post_List.php:1301, ../includes/Elements/Post_List.php:1435, ../includes/Elements/Post_List.php:2136, ../includes/Elements/Post_List.php:2181, ../includes/Elements/Post_List.php:2337, ../includes/Elements/Price_Menu.php:858, ../includes/Elements/Protected_Content.php:673, ../includes/Elements/Stacked_Cards.php:979, ../includes/Elements/Stacked_Cards.php:1048, ../includes/Elements/Stacked_Cards.php:1230, ../includes/Elements/Stacked_Cards.php:1410, ../includes/Elements/Static_Product.php:938, ../includes/Elements/Static_Product.php:1152, ../includes/Elements/Static_Product.php:1428, ../includes/Elements/Team_Member_Carousel.php:732, ../includes/Elements/Team_Member_Carousel.php:950, ../includes/Elements/Team_Member_Carousel.php:2431, ../includes/Elements/Testimonial_Slider.php:739, ../includes/Elements/Testimonial_Slider.php:884, ../includes/Elements/Testimonial_Slider.php:1150, ../includes/Elements/Woo_Account_Dashboard.php:516, ../includes/Elements/Woo_Account_Dashboard.php:718, ../includes/Elements/Woo_Account_Dashboard.php:733, ../includes/Elements/Woo_Account_Dashboard.php:1127, ../includes/Elements/Woo_Account_Dashboard.php:1268, ../includes/Elements/Woo_Account_Dashboard.php:1302, ../includes/Elements/Woo_Account_Dashboard.php:1397, ../includes/Elements/Woo_Account_Dashboard.php:1620, ../includes/Elements/Woo_Account_Dashboard.php:2264, ../includes/Elements/Woo_Account_Dashboard.php:2350, ../includes/Elements/Woo_Account_Dashboard.php:2396, ../includes/Elements/Woo_Account_Dashboard.php:2739, ../includes/Elements/Woo_Cross_Sells.php:593, ../includes/Elements/Woo_Cross_Sells.php:659, ../includes/Elements/Woo_Cross_Sells.php:750, ../includes/Elements/Woo_Cross_Sells.php:933, ../includes/Elements/Woo_Cross_Sells.php:1098, ../includes/Elements/Woo_Product_Slider.php:301, ../includes/Elements/Woo_Thank_You.php:1557, ../includes/Elements/Woo_Thank_You.php:1671, ../includes/Elements/Woo_Thank_You.php:1735, ../includes/Elements/Woo_Thank_You.php:1867, ../includes/Elements/Woo_Thank_You.php:1911, ../includes/Elements/Woo_Thank_You.php:2234, ../includes/Elements/Woo_Thank_You.php:2284, ../includes/Elements/Woo_Thank_You.php:2839, ../includes/Elements/Woo_Thank_You.php:2888, ../includes/Elements/Woo_Thank_You.php:3018, ../includes/Elements/Woo_Thank_You.php:3067, ../includes/Elements/Woo_Thank_You.php:3200, ../includes/Extensions/Content_Protection.php:523, ../includes/Skins/Skin_Default.php:288, ../includes/Skins/Skin_Default.php:421, ../includes/Skins/Skin_Default.php:743, ../includes/Skins/Skin_Five.php:420, ../includes/Skins/Skin_Five.php:495, ../includes/Skins/Skin_Five.php:840, ../includes/Skins/Skin_Four.php:368, ../includes/Skins/Skin_Four.php:443, ../includes/Skins/Skin_Four.php:785, ../includes/Skins/Skin_One.php:416, ../includes/Skins/Skin_One.php:491, ../includes/Skins/Skin_One.php:820, ../includes/Skins/Skin_Seven.php:368, ../includes/Skins/Skin_Seven.php:443, ../includes/Skins/Skin_Seven.php:730, ../includes/Skins/Skin_Six.php:369, ../includes/Skins/Skin_Six.php:444, ../includes/Skins/Skin_Six.php:789, ../includes/Skins/Skin_Three.php:388, ../includes/Skins/Skin_Three.php:463, ../includes/Skins/Skin_Three.php:807, ../includes/Skins/Skin_Two.php:387, ../includes/Skins/Skin_Two.php:462, ../includes/Skins/Skin_Two.php:806, ../includes/Traits/Extender.php:3721, ../includes/Traits/Extender.php:4111, ../includes/Traits/Extender.php:4261, ../includes/Traits/Extender.php:4405, ../includes/Traits/Extender.php:5555, ../includes/Traits/Filterable_Gallery_Extender.php:673, ../includes/Traits/Filterable_Gallery_Extender.php:725
msgid "Margin"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:520, ../includes/Elements/Advanced_Search.php:676, ../includes/Elements/Advanced_Search.php:809, ../includes/Elements/Advanced_Search.php:921, ../includes/Elements/Advanced_Search.php:1021, ../includes/Elements/Advanced_Search.php:1197, ../includes/Elements/Advanced_Search.php:1351, ../includes/Elements/Advanced_Search.php:1465, ../includes/Elements/Advanced_Search.php:1743, ../includes/Elements/Advanced_Search.php:1876, ../includes/Elements/Content_Timeline.php:872, ../includes/Elements/Content_Timeline.php:1805, ../includes/Elements/Content_Timeline.php:2012, ../includes/Elements/Counter.php:526, ../includes/Elements/Dynamic_Filterable_Gallery.php:866, ../includes/Elements/Dynamic_Filterable_Gallery.php:976, ../includes/Elements/Dynamic_Filterable_Gallery.php:1042, ../includes/Elements/Dynamic_Filterable_Gallery.php:1136, ../includes/Elements/Dynamic_Filterable_Gallery.php:1359, ../includes/Elements/Dynamic_Filterable_Gallery.php:1723, ../includes/Elements/Flip_Carousel.php:459, ../includes/Elements/Flip_Carousel.php:656, ../includes/Elements/Google_Map.php:1337, ../includes/Elements/Image_Comparison.php:400, ../includes/Elements/Image_Comparison.php:664, ../includes/Elements/Image_Comparison.php:720, ../includes/Elements/Image_Hot_Spots.php:800, ../includes/Elements/Image_Hot_Spots.php:864, ../includes/Elements/Instagram_Feed.php:485, ../includes/Elements/Instagram_Feed.php:650, ../includes/Elements/Interactive_Card.php:1028, ../includes/Elements/Interactive_Card.php:1063, ../includes/Elements/Interactive_Card.php:1159, ../includes/Elements/Interactive_Card.php:1191, ../includes/Elements/Interactive_Card.php:1614, ../includes/Elements/Interactive_Card.php:1873, ../includes/Elements/LD_Course_List.php:2405, ../includes/Elements/Lightbox.php:881, ../includes/Elements/Lightbox.php:1156, ../includes/Elements/Lightbox.php:1261, ../includes/Elements/Lightbox.php:1872, ../includes/Elements/Lightbox.php:1943, ../includes/Elements/Logo_Carousel.php:690, ../includes/Elements/Logo_Carousel.php:1049, ../includes/Elements/Logo_Carousel.php:1257, ../includes/Elements/Mailchimp.php:436, ../includes/Elements/Mailchimp.php:754, ../includes/Elements/Mailchimp.php:938, ../includes/Elements/Offcanvas.php:517, ../includes/Elements/Offcanvas.php:635, ../includes/Elements/Offcanvas.php:1083, ../includes/Elements/One_Page_Navigation.php:349, ../includes/Elements/One_Page_Navigation.php:519, ../includes/Elements/Post_Block.php:934, ../includes/Elements/Post_Block.php:1021, ../includes/Elements/Post_Carousel.php:1224, ../includes/Elements/Post_Carousel.php:2057, ../includes/Elements/Post_Carousel.php:2249, ../includes/Elements/Post_List.php:1075, ../includes/Elements/Post_List.php:1994, ../includes/Elements/Post_List.php:2042, ../includes/Elements/Post_List.php:2408, ../includes/Elements/Price_Menu.php:382, ../includes/Elements/Price_Menu.php:883, ../includes/Elements/Protected_Content.php:732, ../includes/Elements/Protected_Content.php:779, ../includes/Elements/Stacked_Cards.php:1423, ../includes/Elements/Team_Member_Carousel.php:987, ../includes/Elements/Team_Member_Carousel.php:1062, ../includes/Elements/Team_Member_Carousel.php:1244, ../includes/Elements/Team_Member_Carousel.php:2003, ../includes/Elements/Team_Member_Carousel.php:2220, ../includes/Elements/Team_Member_Carousel.php:2409, ../includes/Elements/Testimonial_Slider.php:765, ../includes/Elements/Testimonial_Slider.php:910, ../includes/Elements/Testimonial_Slider.php:1496, ../includes/Elements/Testimonial_Slider.php:1685, ../includes/Elements/Toggle.php:436, ../includes/Elements/Toggle.php:477, ../includes/Elements/Twitter_Feed_Carousel.php:620, ../includes/Elements/Twitter_Feed_Carousel.php:710, ../includes/Elements/Twitter_Feed_Carousel.php:1103, ../includes/Elements/Twitter_Feed_Carousel.php:1292, ../includes/Elements/Twitter_Feed_Carousel.php:1441, ../includes/Elements/Woo_Collections.php:305, ../includes/Elements/Woo_Product_Slider.php:2577, ../includes/Elements/Woo_Product_Slider.php:3011, ../includes/Extensions/Content_Protection.php:588, ../includes/Extensions/Content_Protection.php:654, ../includes/Skins/Skin_Default.php:82, ../includes/Skins/Skin_Default.php:169, ../includes/Skins/Skin_Five.php:82, ../includes/Skins/Skin_Five.php:187, ../includes/Skins/Skin_Four.php:82, ../includes/Skins/Skin_Four.php:169, ../includes/Skins/Skin_One.php:82, ../includes/Skins/Skin_One.php:169, ../includes/Skins/Skin_Seven.php:82, ../includes/Skins/Skin_Seven.php:169, ../includes/Skins/Skin_Six.php:82, ../includes/Skins/Skin_Six.php:170, ../includes/Skins/Skin_Three.php:82, ../includes/Skins/Skin_Three.php:187, ../includes/Skins/Skin_Two.php:82, ../includes/Skins/Skin_Two.php:186
msgid "Border"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:527, ../includes/Elements/Advanced_Search.php:663, ../includes/Elements/Advanced_Search.php:796, ../includes/Elements/Advanced_Search.php:929, ../includes/Elements/Advanced_Search.php:1008, ../includes/Elements/Advanced_Search.php:1184, ../includes/Elements/Advanced_Search.php:1338, ../includes/Elements/Advanced_Search.php:1452, ../includes/Elements/Advanced_Search.php:1730, ../includes/Elements/Advanced_Search.php:1863, ../includes/Elements/Content_Timeline.php:880, ../includes/Elements/Content_Timeline.php:1265, ../includes/Elements/Content_Timeline.php:1306, ../includes/Elements/Content_Timeline.php:1813, ../includes/Elements/Content_Timeline.php:2020, ../includes/Elements/Counter.php:539, ../includes/Elements/Divider.php:930, ../includes/Elements/Dynamic_Filterable_Gallery.php:874, ../includes/Elements/Dynamic_Filterable_Gallery.php:984, ../includes/Elements/Dynamic_Filterable_Gallery.php:1050, ../includes/Elements/Dynamic_Filterable_Gallery.php:1144, ../includes/Elements/Dynamic_Filterable_Gallery.php:1261, ../includes/Elements/Dynamic_Filterable_Gallery.php:1620, ../includes/Elements/Flip_Carousel.php:467, ../includes/Elements/Flip_Carousel.php:611, ../includes/Elements/Google_Map.php:1227, ../includes/Elements/Google_Map.php:1344, ../includes/Elements/Image_Comparison.php:309, ../includes/Elements/Image_Comparison.php:411, ../includes/Elements/Image_Comparison.php:674, ../includes/Elements/Image_Comparison.php:730, ../includes/Elements/Image_Hot_Spots.php:810, ../includes/Elements/Image_Hot_Spots.php:872, ../includes/Elements/Instagram_Feed.php:493, ../includes/Elements/Instagram_Feed.php:658, ../includes/Elements/Interactive_Card.php:856, ../includes/Elements/Interactive_Card.php:1625, ../includes/Elements/Interactive_Card.php:1881, ../includes/Elements/Interactive_Promo.php:238, ../includes/Elements/LD_Course_List.php:979, ../includes/Elements/LD_Course_List.php:1061, ../includes/Elements/LD_Course_List.php:1226, ../includes/Elements/LD_Course_List.php:1465, ../includes/Elements/LD_Course_List.php:1591, ../includes/Elements/LD_Course_List.php:1974, ../includes/Elements/LD_Course_List.php:2077, ../includes/Elements/LD_Course_List.php:2419, ../includes/Elements/Lightbox.php:945, ../includes/Elements/Lightbox.php:1320, ../includes/Elements/Lightbox.php:1885, ../includes/Elements/Lightbox.php:1956, ../includes/Elements/Logo_Carousel.php:700, ../includes/Elements/Logo_Carousel.php:1059, ../includes/Elements/Logo_Carousel.php:1270, ../includes/Elements/Mailchimp.php:443, ../includes/Elements/Mailchimp.php:547, ../includes/Elements/Mailchimp.php:762, ../includes/Elements/Mailchimp.php:926, ../includes/Elements/Multicolumn_Pricing_Table.php:1138, ../includes/Elements/Multicolumn_Pricing_Table.php:1467, ../includes/Elements/Multicolumn_Pricing_Table.php:1559, ../includes/Elements/Multicolumn_Pricing_Table.php:1729, ../includes/Elements/Multicolumn_Pricing_Table.php:2203, ../includes/Elements/Multicolumn_Pricing_Table.php:2333, ../includes/Elements/Multicolumn_Pricing_Table.php:2386, ../includes/Elements/Multicolumn_Pricing_Table.php:2537, ../includes/Elements/Multicolumn_Pricing_Table.php:2591, ../includes/Elements/Offcanvas.php:527, ../includes/Elements/Offcanvas.php:648, ../includes/Elements/Offcanvas.php:1093, ../includes/Elements/One_Page_Navigation.php:359, ../includes/Elements/One_Page_Navigation.php:529, ../includes/Elements/Post_Block.php:942, ../includes/Elements/Post_Carousel.php:1071, ../includes/Elements/Post_Carousel.php:1135, ../includes/Elements/Post_Carousel.php:1232, ../includes/Elements/Post_Carousel.php:1483, ../includes/Elements/Post_Carousel.php:2067, ../includes/Elements/Post_Carousel.php:2258, ../includes/Elements/Post_Carousel.php:2414, ../includes/Elements/Post_List.php:1082, ../includes/Elements/Post_List.php:1447, ../includes/Elements/Post_List.php:1479, ../includes/Elements/Post_List.php:1700, ../includes/Elements/Post_List.php:2002, ../includes/Elements/Post_List.php:2050, ../includes/Elements/Post_List.php:2285, ../includes/Elements/Price_Menu.php:392, ../includes/Elements/Price_Menu.php:893, ../includes/Elements/Protected_Content.php:685, ../includes/Elements/Protected_Content.php:845, ../includes/Elements/Stacked_Cards.php:1135, ../includes/Elements/Stacked_Cards.php:1452, ../includes/Elements/Static_Product.php:589, ../includes/Elements/Static_Product.php:799, ../includes/Elements/Static_Product.php:950, ../includes/Elements/Team_Member_Carousel.php:1073, ../includes/Elements/Team_Member_Carousel.php:1254, ../includes/Elements/Team_Member_Carousel.php:2014, ../includes/Elements/Team_Member_Carousel.php:2230, ../includes/Elements/Team_Member_Carousel.php:2419, ../includes/Elements/Testimonial_Slider.php:773, ../includes/Elements/Testimonial_Slider.php:929, ../includes/Elements/Testimonial_Slider.php:1251, ../includes/Elements/Testimonial_Slider.php:1506, ../includes/Elements/Testimonial_Slider.php:1695, ../includes/Elements/Testimonial_Slider.php:1860, ../includes/Elements/Toggle.php:446, ../includes/Elements/Toggle.php:487, ../includes/Elements/Toggle.php:521, ../includes/Elements/Twitter_Feed_Carousel.php:628, ../includes/Elements/Twitter_Feed_Carousel.php:720, ../includes/Elements/Twitter_Feed_Carousel.php:1113, ../includes/Elements/Twitter_Feed_Carousel.php:1302, ../includes/Elements/Woo_Account_Dashboard.php:540, ../includes/Elements/Woo_Account_Dashboard.php:639, ../includes/Elements/Woo_Account_Dashboard.php:780, ../includes/Elements/Woo_Account_Dashboard.php:1152, ../includes/Elements/Woo_Account_Dashboard.php:1421, ../includes/Elements/Woo_Account_Dashboard.php:1646, ../includes/Elements/Woo_Account_Dashboard.php:1861, ../includes/Elements/Woo_Account_Dashboard.php:1985, ../includes/Elements/Woo_Account_Dashboard.php:2111, ../includes/Elements/Woo_Account_Dashboard.php:2205, ../includes/Elements/Woo_Account_Dashboard.php:2422, ../includes/Elements/Woo_Collections.php:282, ../includes/Elements/Woo_Collections.php:382, ../includes/Elements/Woo_Cross_Sells.php:626, ../includes/Elements/Woo_Cross_Sells.php:697, ../includes/Elements/Woo_Cross_Sells.php:786, ../includes/Elements/Woo_Cross_Sells.php:1003, ../includes/Elements/Woo_Cross_Sells.php:1058, ../includes/Elements/Woo_Product_Slider.php:266, ../includes/Elements/Woo_Product_Slider.php:1212, ../includes/Elements/Woo_Product_Slider.php:1243, ../includes/Elements/Woo_Product_Slider.php:1644, ../includes/Elements/Woo_Product_Slider.php:1788, ../includes/Elements/Woo_Product_Slider.php:2149, ../includes/Elements/Woo_Product_Slider.php:2345, ../includes/Elements/Woo_Product_Slider.php:2377, ../includes/Elements/Woo_Product_Slider.php:2587, ../includes/Elements/Woo_Product_Slider.php:2710, ../includes/Elements/Woo_Product_Slider.php:2834, ../includes/Elements/Woo_Product_Slider.php:3021, ../includes/Elements/Woo_Thank_You.php:1408, ../includes/Elements/Woo_Thank_You.php:1545, ../includes/Elements/Woo_Thank_You.php:1659, ../includes/Elements/Woo_Thank_You.php:1723, ../includes/Elements/Woo_Thank_You.php:1899, ../includes/Elements/Woo_Thank_You.php:2083, ../includes/Elements/Woo_Thank_You.php:2272, ../includes/Elements/Woo_Thank_You.php:2505, ../includes/Elements/Woo_Thank_You.php:2815, ../includes/Elements/Woo_Thank_You.php:2994, ../includes/Elements/Woo_Thank_You.php:3176, ../includes/Extensions/Content_Protection.php:538, ../includes/Extensions/EAEL_Tooltip_Section.php:322, ../includes/Skins/Skin_Default.php:177, ../includes/Skins/Skin_Default.php:344, ../includes/Skins/Skin_Five.php:226, ../includes/Skins/Skin_Five.php:365, ../includes/Skins/Skin_Four.php:177, ../includes/Skins/Skin_Four.php:313, ../includes/Skins/Skin_One.php:177, ../includes/Skins/Skin_One.php:361, ../includes/Skins/Skin_Seven.php:177, ../includes/Skins/Skin_Seven.php:313, ../includes/Skins/Skin_Six.php:178, ../includes/Skins/Skin_Six.php:314, ../includes/Skins/Skin_Three.php:195, ../includes/Skins/Skin_Three.php:333, ../includes/Skins/Skin_Two.php:194, ../includes/Skins/Skin_Two.php:332, ../includes/Traits/Extender.php:738, ../includes/Traits/Extender.php:2544, ../includes/Traits/Extender.php:2712, ../includes/Traits/Extender.php:2757, ../includes/Traits/Extender.php:2888, ../includes/Traits/Extender.php:3758, ../includes/Traits/Extender.php:4148, ../includes/Traits/Extender.php:4310, ../includes/Traits/Extender.php:4445, ../includes/Traits/Extender.php:4897, ../includes/Traits/Extender.php:5541, ../includes/Traits/Filterable_Gallery_Extender.php:364
msgid "Border Radius"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:560, ../includes/Elements/Dynamic_Filterable_Gallery.php:142, ../includes/Elements/Woo_Collections.php:228
msgid "Layout Style"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:564, ../includes/Elements/Advanced_Search.php:1384, ../includes/Elements/Price_Menu.php:236, ../includes/Template/Woo-Cross-Sells/style-1.php:4
msgid "Style 1"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:565, ../includes/Elements/Advanced_Search.php:1385, ../includes/Elements/Price_Menu.php:237, ../includes/Traits/Extender.php:39, ../includes/Template/Woo-Cross-Sells/style-2.php:4
msgid "Style 2"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:566, ../includes/Elements/Advanced_Search.php:1386, ../includes/Elements/Price_Menu.php:238, ../includes/Template/Woo-Cross-Sells/style-3.php:4
msgid "Style 3"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:585, ../includes/Elements/Advanced_Search.php:693, ../includes/Elements/Advanced_Search.php:747, ../includes/Elements/Advanced_Search.php:874, ../includes/Elements/Advanced_Search.php:898, ../includes/Elements/Advanced_Search.php:1061, ../includes/Elements/Advanced_Search.php:1103, ../includes/Elements/Advanced_Search.php:1137, ../includes/Elements/Advanced_Search.php:1237, ../includes/Elements/Advanced_Search.php:1269, ../includes/Elements/Advanced_Search.php:1292, ../includes/Elements/Advanced_Search.php:1495, ../includes/Elements/Advanced_Search.php:1518, ../includes/Elements/Advanced_Search.php:1553, ../includes/Elements/Advanced_Search.php:1576, ../includes/Elements/Advanced_Search.php:1652, ../includes/Elements/Advanced_Search.php:1684, ../includes/Elements/Advanced_Search.php:1785, ../includes/Elements/Advanced_Search.php:1817, ../includes/Elements/Advanced_Search.php:1905, ../includes/Elements/Content_Timeline.php:1293, ../includes/Elements/Content_Timeline.php:1364, ../includes/Elements/Counter.php:437, ../includes/Elements/Counter.php:688, ../includes/Elements/Counter.php:872, ../includes/Elements/Counter.php:930, ../includes/Elements/Counter.php:991, ../includes/Elements/Divider.php:659, ../includes/Elements/Divider.php:762, ../includes/Elements/Dynamic_Filterable_Gallery.php:1211, ../includes/Elements/Dynamic_Filterable_Gallery.php:1301, ../includes/Elements/Dynamic_Filterable_Gallery.php:1450, ../includes/Elements/Dynamic_Filterable_Gallery.php:1481, ../includes/Elements/Dynamic_Filterable_Gallery.php:1512, ../includes/Elements/Dynamic_Filterable_Gallery.php:1563, ../includes/Elements/Dynamic_Filterable_Gallery.php:1662, ../includes/Elements/Dynamic_Filterable_Gallery.php:1748, ../includes/Elements/Dynamic_Filterable_Gallery.php:1789, ../includes/Elements/Dynamic_Filterable_Gallery.php:1818, ../includes/Elements/Fancy_Chart.php:744, ../includes/Elements/Fancy_Chart.php:837, ../includes/Elements/Fancy_Chart.php:1165, ../includes/Elements/Fancy_Chart.php:1530, ../includes/Elements/Fancy_Chart.php:1592, ../includes/Elements/Fancy_Chart.php:1622, ../includes/Elements/Flip_Carousel.php:716, ../includes/Elements/Flip_Carousel.php:820, ../includes/Elements/Google_Map.php:1369, ../includes/Elements/Google_Map.php:1397, ../includes/Elements/Image_Comparison.php:493, ../includes/Elements/Image_Hot_Spots.php:774, ../includes/Elements/Image_Hot_Spots.php:840, ../includes/Elements/Interactive_Card.php:1247, ../includes/Elements/Interactive_Card.php:1276, ../includes/Elements/Interactive_Card.php:1305, ../includes/Elements/Interactive_Card.php:1349, ../includes/Elements/Interactive_Card.php:1386, ../includes/Elements/Interactive_Card.php:1433, ../includes/Elements/LD_Course_List.php:1183, ../includes/Elements/LD_Course_List.php:1328, ../includes/Elements/LD_Course_List.php:1378, ../includes/Elements/LD_Course_List.php:1435, ../includes/Elements/LD_Course_List.php:1579, ../includes/Elements/LD_Course_List.php:1742, ../includes/Elements/LD_Course_List.php:1786, ../includes/Elements/LD_Course_List.php:1839, ../includes/Elements/LD_Course_List.php:1940, ../includes/Elements/LD_Course_List.php:2019, ../includes/Elements/LD_Course_List.php:2205, ../includes/Elements/Lightbox.php:867, ../includes/Elements/Lightbox.php:1609, ../includes/Elements/Logo_Carousel.php:847, ../includes/Elements/Logo_Carousel.php:1035, ../includes/Elements/Logo_Carousel.php:1092, ../includes/Elements/Logo_Carousel.php:1226, ../includes/Elements/Logo_Carousel.php:1319, ../includes/Elements/Multicolumn_Pricing_Table.php:1379, ../includes/Elements/Multicolumn_Pricing_Table.php:1908, ../includes/Elements/Multicolumn_Pricing_Table.php:1960, ../includes/Elements/Multicolumn_Pricing_Table.php:2000, ../includes/Elements/Multicolumn_Pricing_Table.php:2040, ../includes/Elements/Multicolumn_Pricing_Table.php:2143, ../includes/Elements/Offcanvas.php:767, ../includes/Elements/Offcanvas.php:806, ../includes/Elements/Offcanvas.php:839, ../includes/Elements/Offcanvas.php:878, ../includes/Elements/Offcanvas.php:1258, ../includes/Elements/Offcanvas.php:1316, ../includes/Elements/One_Page_Navigation.php:491, ../includes/Elements/One_Page_Navigation.php:550, ../includes/Elements/One_Page_Navigation.php:597, ../includes/Elements/Post_Block.php:1053, ../includes/Elements/Post_Block.php:1133, ../includes/Elements/Post_Block.php:1223, ../includes/Elements/Post_Block.php:1319, ../includes/Elements/Post_Carousel.php:1531, ../includes/Elements/Post_Carousel.php:1616, ../includes/Elements/Post_Carousel.php:1836, ../includes/Elements/Post_Carousel.php:2044, ../includes/Elements/Post_Carousel.php:2100, ../includes/Elements/Post_Carousel.php:2298, ../includes/Elements/Post_Carousel.php:2331, ../includes/Elements/Post_Carousel.php:2364, ../includes/Elements/Post_List.php:1257, ../includes/Elements/Post_List.php:2169, ../includes/Elements/Post_List.php:2214, ../includes/Elements/Post_List.php:2262, ../includes/Elements/Post_List.php:2309, ../includes/Elements/Price_Menu.php:457, ../includes/Elements/Price_Menu.php:672, ../includes/Elements/Price_Menu.php:757, ../includes/Elements/Price_Menu.php:1010, ../includes/Elements/Protected_Content.php:707, ../includes/Elements/Protected_Content.php:754, ../includes/Elements/Sphere_Photo_Viewer.php:725, ../includes/Elements/Sphere_Photo_Viewer.php:817, ../includes/Elements/Sphere_Photo_Viewer.php:965, ../includes/Elements/Stacked_Cards.php:1261, ../includes/Elements/Stacked_Cards.php:1324, ../includes/Elements/Static_Product.php:847, ../includes/Elements/Static_Product.php:894, ../includes/Elements/Static_Product.php:970, ../includes/Elements/Team_Member_Carousel.php:2207, ../includes/Elements/Team_Member_Carousel.php:2263, ../includes/Elements/Team_Member_Carousel.php:2384, ../includes/Elements/Team_Member_Carousel.php:2459, ../includes/Elements/Testimonial_Slider.php:962, ../includes/Elements/Testimonial_Slider.php:1003, ../includes/Elements/Testimonial_Slider.php:1043, ../includes/Elements/Testimonial_Slider.php:1096, ../includes/Elements/Testimonial_Slider.php:1204, ../includes/Elements/Testimonial_Slider.php:1271, ../includes/Elements/Testimonial_Slider.php:1483, ../includes/Elements/Testimonial_Slider.php:1539, ../includes/Elements/Testimonial_Slider.php:1660, ../includes/Elements/Testimonial_Slider.php:1735, ../includes/Elements/Twitter_Feed_Carousel.php:853, ../includes/Elements/Twitter_Feed_Carousel.php:882, ../includes/Elements/Twitter_Feed_Carousel.php:912, ../includes/Elements/Twitter_Feed_Carousel.php:1089, ../includes/Elements/Twitter_Feed_Carousel.php:1146, ../includes/Elements/Twitter_Feed_Carousel.php:1267, ../includes/Elements/Twitter_Feed_Carousel.php:1342, ../includes/Elements/Twitter_Feed_Carousel.php:1498, ../includes/Elements/Woo_Account_Dashboard.php:802, ../includes/Elements/Woo_Account_Dashboard.php:846, ../includes/Elements/Woo_Account_Dashboard.php:892, ../includes/Elements/Woo_Account_Dashboard.php:955, ../includes/Elements/Woo_Account_Dashboard.php:1054, ../includes/Elements/Woo_Account_Dashboard.php:1088, ../includes/Elements/Woo_Account_Dashboard.php:1221, ../includes/Elements/Woo_Account_Dashboard.php:1255, ../includes/Elements/Woo_Account_Dashboard.php:1348, ../includes/Elements/Woo_Account_Dashboard.php:1366, ../includes/Elements/Woo_Account_Dashboard.php:1443, ../includes/Elements/Woo_Account_Dashboard.php:1487, ../includes/Elements/Woo_Account_Dashboard.php:1572, ../includes/Elements/Woo_Account_Dashboard.php:1589, ../includes/Elements/Woo_Account_Dashboard.php:1669, ../includes/Elements/Woo_Account_Dashboard.php:1717, ../includes/Elements/Woo_Account_Dashboard.php:1826, ../includes/Elements/Woo_Account_Dashboard.php:2168, ../includes/Elements/Woo_Account_Dashboard.php:2252, ../includes/Elements/Woo_Account_Dashboard.php:2307, ../includes/Elements/Woo_Account_Dashboard.php:2337, ../includes/Elements/Woo_Account_Dashboard.php:2445, ../includes/Elements/Woo_Account_Dashboard.php:2493, ../includes/Elements/Woo_Account_Dashboard.php:2606, ../includes/Elements/Woo_Account_Dashboard.php:2637, ../includes/Elements/Woo_Account_Dashboard.php:2670, ../includes/Elements/Woo_Account_Dashboard.php:2727, ../includes/Elements/Woo_Cross_Sells.php:553, ../includes/Elements/Woo_Cross_Sells.php:824, ../includes/Elements/Woo_Cross_Sells.php:895, ../includes/Elements/Woo_Cross_Sells.php:970, ../includes/Elements/Woo_Cross_Sells.php:1034, ../includes/Elements/Woo_Product_Slider.php:208, ../includes/Elements/Woo_Product_Slider.php:237, ../includes/Elements/Woo_Product_Slider.php:1437, ../includes/Elements/Woo_Product_Slider.php:1614, ../includes/Elements/Woo_Product_Slider.php:1684, ../includes/Elements/Woo_Product_Slider.php:1759, ../includes/Elements/Woo_Product_Slider.php:1818, ../includes/Elements/Woo_Product_Slider.php:2015, ../includes/Elements/Woo_Product_Slider.php:2056, ../includes/Elements/Woo_Product_Slider.php:2118, ../includes/Elements/Woo_Product_Slider.php:2170, ../includes/Elements/Woo_Product_Slider.php:2323, ../includes/Elements/Woo_Product_Slider.php:2564, ../includes/Elements/Woo_Product_Slider.php:2627, ../includes/Elements/Woo_Product_Slider.php:2660, ../includes/Elements/Woo_Product_Slider.php:2998, ../includes/Elements/Woo_Product_Slider.php:3054, ../includes/Elements/Woo_Thank_You.php:1367, ../includes/Elements/Woo_Thank_You.php:1445, ../includes/Elements/Woo_Thank_You.php:1484, ../includes/Elements/Woo_Thank_You.php:1516, ../includes/Elements/Woo_Thank_You.php:1617, ../includes/Elements/Woo_Thank_You.php:1776, ../includes/Elements/Woo_Thank_You.php:1805, ../includes/Elements/Woo_Thank_You.php:1853, ../includes/Elements/Woo_Thank_You.php:1940, ../includes/Elements/Woo_Thank_You.php:2218, ../includes/Elements/Woo_Thank_You.php:2321, ../includes/Elements/Woo_Thank_You.php:2546, ../includes/Elements/Woo_Thank_You.php:2588, ../includes/Elements/Woo_Thank_You.php:2633, ../includes/Elements/Woo_Thank_You.php:2676, ../includes/Elements/Woo_Thank_You.php:2723, ../includes/Elements/Woo_Thank_You.php:2768, ../includes/Elements/Woo_Thank_You.php:2874, ../includes/Elements/Woo_Thank_You.php:2922, ../includes/Elements/Woo_Thank_You.php:3053, ../includes/Elements/Woo_Thank_You.php:3101, ../includes/Elements/Woo_Thank_You.php:3229, ../includes/Elements/Woo_Thank_You.php:3258, ../includes/Extensions/Content_Protection.php:557, ../includes/Extensions/Content_Protection.php:623, ../includes/Extensions/EAEL_Tooltip_Section.php:291, ../includes/Skins/Skin_Default.php:310, ../includes/Skins/Skin_Default.php:488, ../includes/Skins/Skin_Default.php:583, ../includes/Skins/Skin_Default.php:808, ../includes/Skins/Skin_Default.php:906, ../includes/Skins/Skin_Five.php:331, ../includes/Skins/Skin_Five.php:562, ../includes/Skins/Skin_Five.php:657, ../includes/Skins/Skin_Five.php:905, ../includes/Skins/Skin_Five.php:1002, ../includes/Skins/Skin_Four.php:279, ../includes/Skins/Skin_Four.php:510, ../includes/Skins/Skin_Four.php:605, ../includes/Skins/Skin_Four.php:850, ../includes/Skins/Skin_Four.php:947, ../includes/Skins/Skin_One.php:327, ../includes/Skins/Skin_One.php:558, ../includes/Skins/Skin_One.php:653, ../includes/Skins/Skin_One.php:885, ../includes/Skins/Skin_One.php:983, ../includes/Skins/Skin_Seven.php:279, ../includes/Skins/Skin_Seven.php:510, ../includes/Skins/Skin_Seven.php:605, ../includes/Skins/Skin_Seven.php:850, ../includes/Skins/Skin_Seven.php:947, ../includes/Skins/Skin_Six.php:280, ../includes/Skins/Skin_Six.php:512, ../includes/Skins/Skin_Six.php:608, ../includes/Skins/Skin_Six.php:855, ../includes/Skins/Skin_Six.php:953, ../includes/Skins/Skin_Three.php:299, ../includes/Skins/Skin_Three.php:531, ../includes/Skins/Skin_Three.php:627, ../includes/Skins/Skin_Three.php:873, ../includes/Skins/Skin_Three.php:971, ../includes/Skins/Skin_Two.php:298, ../includes/Skins/Skin_Two.php:530, ../includes/Skins/Skin_Two.php:626, ../includes/Skins/Skin_Two.php:872, ../includes/Skins/Skin_Two.php:970, ../includes/Traits/Extender.php:2486, ../includes/Traits/Extender.php:2510, ../includes/Traits/Extender.php:2521, ../includes/Traits/Extender.php:2644, ../includes/Traits/Extender.php:2674, ../includes/Traits/Extender.php:2833, ../includes/Traits/Extender.php:2863, ../includes/Traits/Extender.php:3098, ../includes/Traits/Extender.php:4461, ../includes/Traits/Extender.php:5467, ../includes/Traits/Filterable_Gallery_Extender.php:296, ../includes/Traits/Filterable_Gallery_Extender.php:454, ../includes/Traits/Filterable_Gallery_Extender.php:555, ../includes/Traits/Filterable_Gallery_Extender.php:583, ../includes/Traits/Filterable_Gallery_Extender.php:654, ../includes/Traits/Filterable_Gallery_Extender.php:706, ../includes/Traits/Filterable_Gallery_Extender.php:758
msgid "Color"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:596
msgid "Placeholder Color"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:615, ../includes/Elements/Content_Timeline.php:1233, ../includes/Elements/Counter.php:631, ../includes/Elements/Counter.php:817, ../includes/Elements/Divider.php:418, ../includes/Elements/Divider.php:450, ../includes/Elements/Fancy_Chart.php:1066, ../includes/Elements/Fancy_Chart.php:1714, ../includes/Elements/Google_Map.php:1199, ../includes/Elements/Interactive_Card.php:804, ../includes/Elements/LD_Course_List.php:1477, ../includes/Elements/Lightbox.php:215, ../includes/Elements/Lightbox.php:1705, ../includes/Elements/Multicolumn_Pricing_Table.php:1409, ../includes/Elements/Multicolumn_Pricing_Table.php:1811, ../includes/Elements/Multicolumn_Pricing_Table.php:2088, ../includes/Elements/Post_Block.php:1198, ../includes/Elements/Post_Carousel.php:2206, ../includes/Elements/Post_Carousel.php:2395, ../includes/Elements/Post_List.php:2116, ../includes/Elements/Stacked_Cards.php:275, ../includes/Elements/Team_Member_Carousel.php:1027, ../includes/Elements/Testimonial_Slider.php:1066, ../includes/Elements/Testimonial_Slider.php:1384, ../includes/Elements/Testimonial_Slider.php:1805, ../includes/Elements/Twitter_Feed_Carousel.php:1416, ../includes/Elements/Woo_Cross_Sells.php:228, ../includes/Elements/Woo_Product_Slider.php:1027, ../includes/Elements/Woo_Product_Slider.php:2491, ../includes/Elements/Woo_Product_Slider.php:2691, ../includes/Elements/Woo_Product_Slider.php:2779, ../includes/Elements/Woo_Thank_You.php:2467, ../includes/Traits/Extender.php:196, ../includes/Traits/Extender.php:1233, ../includes/Traits/Extender.php:1287, ../includes/Traits/Extender.php:3695, ../includes/Traits/Extender.php:4791, ../includes/Traits/Extender.php:5369
msgid "Height"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:638, ../includes/Elements/Advanced_Search.php:767, ../includes/Elements/Advanced_Search.php:832, ../includes/Elements/Advanced_Search.php:963, ../includes/Elements/Counter.php:657, ../includes/Elements/Counter.php:842, ../includes/Elements/Divider.php:482, ../includes/Elements/Divider.php:514, ../includes/Elements/Divider.php:900, ../includes/Elements/Fancy_Chart.php:1090, ../includes/Elements/Fancy_Chart.php:1404, ../includes/Elements/Fancy_Chart.php:1696, ../includes/Elements/Google_Map.php:1171, ../includes/Elements/Google_Map.php:1281, ../includes/Elements/Image_Comparison.php:505, ../includes/Elements/Image_Hot_Spots.php:617, ../includes/Elements/Image_Hot_Spots.php:926, ../includes/Elements/LD_Course_List.php:1500, ../includes/Elements/Lightbox.php:169, ../includes/Elements/Lightbox.php:1293, ../includes/Elements/Lightbox.php:1682, ../includes/Elements/Multicolumn_Pricing_Table.php:1296, ../includes/Elements/Multicolumn_Pricing_Table.php:1438, ../includes/Elements/Multicolumn_Pricing_Table.php:1789, ../includes/Elements/Multicolumn_Pricing_Table.php:2277, ../includes/Elements/Offcanvas.php:477, ../includes/Elements/Offcanvas.php:921, ../includes/Elements/Post_Carousel.php:2183, ../includes/Elements/Post_Carousel.php:2376, ../includes/Elements/Post_List.php:2096, ../includes/Elements/Price_Menu.php:834, ../includes/Elements/Stacked_Cards.php:247, ../includes/Elements/Stacked_Cards.php:360, ../includes/Elements/Testimonial_Slider.php:857, ../includes/Elements/Testimonial_Slider.php:1365, ../includes/Elements/Testimonial_Slider.php:1778, ../includes/Elements/Twitter_Feed_Carousel.php:1389, ../includes/Elements/Woo_Product_Slider.php:1558, ../includes/Elements/Woo_Product_Slider.php:2469, ../includes/Elements/Woo_Product_Slider.php:2672, ../includes/Elements/Woo_Product_Slider.php:2752, ../includes/Elements/Woo_Thank_You.php:2441, ../includes/Elements/Woo_Thank_You.php:3142, ../includes/Skins/Skin_Default.php:327, ../includes/Skins/Skin_Five.php:348, ../includes/Skins/Skin_Four.php:296, ../includes/Skins/Skin_One.php:344, ../includes/Skins/Skin_Seven.php:296, ../includes/Skins/Skin_Six.php:297, ../includes/Skins/Skin_Three.php:316, ../includes/Skins/Skin_Two.php:315, ../includes/Traits/Extender.php:175, ../includes/Traits/Extender.php:604, ../includes/Traits/Extender.php:1258, ../includes/Traits/Extender.php:3665, ../includes/Traits/Extender.php:3941, ../includes/Traits/Extender.php:4765, ../includes/Traits/Extender.php:5345
msgid "Width"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:704, ../includes/Elements/Counter.php:453, ../includes/Elements/Divider.php:778, ../includes/Elements/Image_Hot_Spots.php:374, ../includes/Elements/Image_Hot_Spots.php:699, ../includes/Elements/LD_Course_List.php:858, ../includes/Elements/LD_Course_List.php:1603, ../includes/Elements/Lightbox.php:1033, ../includes/Elements/Lightbox.php:1360, ../includes/Elements/Lightbox.php:1635, ../includes/Elements/Logo_Carousel.php:1166, ../includes/Elements/Offcanvas.php:894, ../includes/Elements/Offcanvas.php:984, ../includes/Elements/Offcanvas.php:1274, ../includes/Elements/One_Page_Navigation.php:417, ../includes/Elements/Post_Carousel.php:1965, ../includes/Elements/Team_Member_Carousel.php:2333, ../includes/Elements/Testimonial_Slider.php:1609, ../includes/Elements/Twitter_Feed_Carousel.php:1220, ../includes/Elements/Woo_Account_Dashboard.php:971, ../includes/Elements/Woo_Product_Slider.php:2513, ../includes/Elements/Woo_Thank_You.php:1245, ../includes/Extensions/EAEL_Tooltip_Section.php:232, ../includes/Traits/Extender.php:2692, ../includes/Traits/Extender.php:3895, ../includes/Traits/Filterable_Gallery_Extender.php:424
msgid "Size"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:725
msgid "Category List"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:821
msgid "Search Button"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:860, ../includes/Elements/Advanced_Search.php:1087, ../includes/Elements/Advanced_Search.php:1264, ../includes/Elements/Advanced_Search.php:1393, ../includes/Elements/Advanced_Search.php:1490, ../includes/Elements/Advanced_Search.php:1548, ../includes/Elements/Advanced_Search.php:1638, ../includes/Elements/Advanced_Search.php:1771, ../includes/Elements/Content_Timeline.php:1775, ../includes/Elements/Content_Timeline.php:1981, ../includes/Elements/Dynamic_Filterable_Gallery.php:946, ../includes/Elements/Dynamic_Filterable_Gallery.php:1192, ../includes/Elements/Dynamic_Filterable_Gallery.php:1543, ../includes/Elements/Image_Comparison.php:368, ../includes/Elements/Image_Hot_Spots.php:767, ../includes/Elements/Instagram_Feed.php:620, ../includes/Elements/Interactive_Card.php:1557, ../includes/Elements/Interactive_Card.php:1827, ../includes/Elements/LD_Course_List.php:953, ../includes/Elements/LD_Course_List.php:1921, ../includes/Elements/LD_Course_List.php:2361, ../includes/Elements/Lightbox.php:1089, ../includes/Elements/Lightbox.php:1466, ../includes/Elements/Lightbox.php:1666, ../includes/Elements/Lightbox.php:1832, ../includes/Elements/Logo_Carousel.php:726, ../includes/Elements/Logo_Carousel.php:1016, ../includes/Elements/Logo_Carousel.php:1216, ../includes/Elements/Mailchimp.php:724, ../includes/Elements/Multicolumn_Pricing_Table.php:2296, ../includes/Elements/Multicolumn_Pricing_Table.php:2499, ../includes/Elements/Offcanvas.php:757, ../includes/Elements/Offcanvas.php:1050, ../includes/Elements/One_Page_Navigation.php:484, ../includes/Elements/Post_Carousel.php:2025, ../includes/Elements/Post_Carousel.php:2291, ../includes/Elements/Post_List.php:1964, ../includes/Elements/Post_List.php:2257, ../includes/Elements/Protected_Content.php:701, ../includes/Elements/Protected_Content.php:861, ../includes/Elements/Static_Product.php:840, ../includes/Elements/Static_Product.php:1214, ../includes/Elements/Static_Product.php:1478, ../includes/Elements/Team_Member_Carousel.php:1971, ../includes/Elements/Team_Member_Carousel.php:2188, ../includes/Elements/Team_Member_Carousel.php:2377, ../includes/Elements/Testimonial_Slider.php:1199, ../includes/Elements/Testimonial_Slider.php:1464, ../includes/Elements/Testimonial_Slider.php:1653, ../includes/Elements/Twitter_Feed_Carousel.php:1070, ../includes/Elements/Twitter_Feed_Carousel.php:1260, ../includes/Elements/Woo_Account_Dashboard.php:796, ../includes/Elements/Woo_Account_Dashboard.php:1342, ../includes/Elements/Woo_Account_Dashboard.php:1437, ../includes/Elements/Woo_Account_Dashboard.php:1566, ../includes/Elements/Woo_Account_Dashboard.php:1663, ../includes/Elements/Woo_Account_Dashboard.php:2439, ../includes/Elements/Woo_Cross_Sells.php:963, ../includes/Elements/Woo_Product_Slider.php:203, ../includes/Elements/Woo_Product_Slider.php:1139, ../includes/Elements/Woo_Product_Slider.php:1609, ../includes/Elements/Woo_Product_Slider.php:1754, ../includes/Elements/Woo_Product_Slider.php:2113, ../includes/Elements/Woo_Product_Slider.php:2557, ../includes/Elements/Woo_Product_Slider.php:2979, ../includes/Elements/Woo_Thank_You.php:2115, ../includes/Traits/Extender.php:656, ../includes/Traits/Extender.php:2472, ../includes/Traits/Extender.php:2634, ../includes/Traits/Extender.php:2820
msgid "Normal"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:884, ../includes/Elements/Advanced_Search.php:1121, ../includes/Elements/Advanced_Search.php:1287, ../includes/Elements/Advanced_Search.php:1409, ../includes/Elements/Advanced_Search.php:1513, ../includes/Elements/Advanced_Search.php:1571, ../includes/Elements/Advanced_Search.php:1670, ../includes/Elements/Advanced_Search.php:1803, ../includes/Elements/Content_Timeline.php:1829, ../includes/Elements/Content_Timeline.php:2037, ../includes/Elements/Dynamic_Filterable_Gallery.php:1282, ../includes/Elements/Dynamic_Filterable_Gallery.php:1642, ../includes/Elements/Image_Comparison.php:433, ../includes/Elements/Image_Hot_Spots.php:834, ../includes/Elements/Instagram_Feed.php:684, ../includes/Elements/Interactive_Card.php:1647, ../includes/Elements/Interactive_Card.php:1897, ../includes/Elements/LD_Course_List.php:1024, ../includes/Elements/LD_Course_List.php:2012, ../includes/Elements/LD_Course_List.php:2556, ../includes/Elements/Lightbox.php:1182, ../includes/Elements/Lightbox.php:1516, ../includes/Elements/Lightbox.php:1903, ../includes/Elements/Logo_Carousel.php:251, ../includes/Elements/Logo_Carousel.php:774, ../includes/Elements/Logo_Carousel.php:1073, ../includes/Elements/Logo_Carousel.php:1309, ../includes/Elements/Mailchimp.php:785, ../includes/Elements/Multicolumn_Pricing_Table.php:2346, ../includes/Elements/Multicolumn_Pricing_Table.php:2550, ../includes/Elements/Offcanvas.php:796, ../includes/Elements/Offcanvas.php:1139, ../includes/Elements/One_Page_Navigation.php:543, ../includes/Elements/Post_Carousel.php:2081, ../includes/Elements/Post_Carousel.php:2324, ../includes/Elements/Post_List.php:2024, ../includes/Elements/Post_List.php:2304, ../includes/Elements/Protected_Content.php:748, ../includes/Elements/Protected_Content.php:907, ../includes/Elements/Static_Product.php:888, ../includes/Elements/Static_Product.php:1281, ../includes/Elements/Static_Product.php:1533, ../includes/Elements/Team_Member_Carousel.php:2041, ../includes/Elements/Team_Member_Carousel.php:2244, ../includes/Elements/Team_Member_Carousel.php:2452, ../includes/Elements/Testimonial_Slider.php:1266, ../includes/Elements/Testimonial_Slider.php:1520, ../includes/Elements/Testimonial_Slider.php:1728, ../includes/Elements/Twitter_Feed_Carousel.php:1127, ../includes/Elements/Twitter_Feed_Carousel.php:1335, ../includes/Elements/Woo_Account_Dashboard.php:840, ../includes/Elements/Woo_Account_Dashboard.php:1360, ../includes/Elements/Woo_Account_Dashboard.php:1481, ../includes/Elements/Woo_Account_Dashboard.php:1583, ../includes/Elements/Woo_Account_Dashboard.php:1711, ../includes/Elements/Woo_Account_Dashboard.php:2487, ../includes/Elements/Woo_Cross_Sells.php:1027, ../includes/Elements/Woo_Product_Slider.php:232, ../includes/Elements/Woo_Product_Slider.php:1181, ../includes/Elements/Woo_Product_Slider.php:1679, ../includes/Elements/Woo_Product_Slider.php:1813, ../includes/Elements/Woo_Product_Slider.php:2165, ../includes/Elements/Woo_Product_Slider.php:2620, ../includes/Elements/Woo_Product_Slider.php:3035, ../includes/Elements/Woo_Thank_You.php:2145, ../includes/Extensions/EAEL_Tooltip_Section.php:188, ../includes/Skins/Skin_Default.php:528, ../includes/Skins/Skin_Default.php:851, ../includes/Skins/Skin_Five.php:602, ../includes/Skins/Skin_Five.php:947, ../includes/Skins/Skin_Four.php:550, ../includes/Skins/Skin_Four.php:892, ../includes/Skins/Skin_One.php:598, ../includes/Skins/Skin_One.php:928, ../includes/Skins/Skin_Seven.php:550, ../includes/Skins/Skin_Seven.php:892, ../includes/Skins/Skin_Six.php:552, ../includes/Skins/Skin_Six.php:897, ../includes/Skins/Skin_Three.php:571, ../includes/Skins/Skin_Three.php:915, ../includes/Skins/Skin_Two.php:570, ../includes/Skins/Skin_Two.php:914, ../includes/Traits/Extender.php:754, ../includes/Traits/Extender.php:2850
msgid "Hover"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:945
msgid "Search Result Box"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1030, ../includes/Elements/Advanced_Search.php:1206, ../includes/Elements/Advanced_Search.php:1360, ../includes/Elements/Advanced_Search.php:1474, ../includes/Elements/Advanced_Search.php:1752, ../includes/Elements/Advanced_Search.php:1885, ../includes/Elements/Team_Member_Carousel.php:996, ../includes/Elements/Twitter_Feed_Carousel.php:822, ../includes/Elements/Twitter_Feed_Carousel.php:1449, ../includes/Elements/Woo_Product_Slider.php:1170, ../includes/Elements/Woo_Product_Slider.php:1201, ../includes/Elements/Woo_Product_Slider.php:1255, ../includes/Elements/Woo_Product_Slider.php:1805, ../includes/Elements/Woo_Product_Slider.php:2369, ../includes/Elements/Woo_Product_Slider.php:2404, ../includes/Elements/Woo_Product_Slider.php:3083
msgid "Box Shadow"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1053, ../includes/Elements/Advanced_Search.php:1229, ../includes/Elements/Image_Comparison.php:538, ../includes/Elements/Instagram_Feed.php:393, ../includes/Elements/Toggle.php:132, ../includes/Elements/Toggle.php:214, ../includes/Elements/Toggle.php:538, ../includes/Elements/Woo_Account_Dashboard.php:2236, ../includes/Elements/Woo_Thank_You.php:416, ../includes/Elements/Woo_Thank_You.php:446, ../includes/Elements/Woo_Thank_You.php:474, ../includes/Elements/Woo_Thank_You.php:531, ../includes/Elements/Woo_Thank_You.php:559, ../includes/Elements/Woo_Thank_You.php:587, ../includes/Elements/Woo_Thank_You.php:628, ../includes/Elements/Woo_Thank_You.php:654, ../includes/Elements/Woo_Thank_You.php:680, ../includes/Elements/Woo_Thank_You.php:706, ../includes/Elements/Woo_Thank_You.php:732, ../includes/Elements/Woo_Thank_You.php:775, ../includes/Elements/Woo_Thank_You.php:1028, ../includes/Elements/Woo_Thank_You.php:1078, ../includes/Elements/Woo_Thank_You.php:1145, ../includes/Elements/Woo_Thank_You.php:1204, ../includes/Elements/Woo_Thank_You.php:1759, ../includes/Elements/Woo_Thank_You.php:3212
msgid "Label"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1080
msgid "Popular Tag"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1218
msgid "Category Result"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1256, ../includes/Elements/LD_Course_List.php:290, ../includes/Elements/Post_Carousel.php:382, ../includes/Elements/Woo_Collections.php:97, ../includes/Elements/Woo_Collections.php:107, ../includes/Elements/Woo_Product_Slider.php:494, ../includes/Extensions/Conditional_Display.php:2019
msgid "Category"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1372
msgid "Search Content"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1380
msgid "Content Layout"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1482, ../includes/Elements/Content_Timeline.php:344, ../includes/Elements/Content_Timeline.php:583, ../includes/Elements/Counter.php:190, ../includes/Elements/Counter.php:1038, ../includes/Elements/Dynamic_Filterable_Gallery.php:735, ../includes/Elements/Google_Map.php:339, ../includes/Elements/Google_Map.php:469, ../includes/Elements/Google_Map.php:617, ../includes/Elements/Interactive_Card.php:200, ../includes/Elements/Interactive_Card.php:451, ../includes/Elements/LD_Course_List.php:743, ../includes/Elements/LD_Course_List.php:1290, ../includes/Elements/Lightbox.php:273, ../includes/Elements/Lightbox.php:817, ../includes/Elements/Logo_Carousel.php:157, ../includes/Elements/Logo_Carousel.php:839, ../includes/Elements/Multicolumn_Pricing_Table.php:457, ../includes/Elements/Multicolumn_Pricing_Table.php:1870, ../includes/Elements/Offcanvas.php:137, ../includes/Elements/Offcanvas.php:226, ../includes/Elements/Offcanvas.php:231, ../includes/Elements/Post_Block.php:778, ../includes/Elements/Post_Block.php:1044, ../includes/Elements/Post_Carousel.php:149, ../includes/Elements/Post_Carousel.php:946, ../includes/Elements/Post_Carousel.php:1522, ../includes/Elements/Post_List.php:663, ../includes/Elements/Post_List.php:953, ../includes/Elements/Price_Menu.php:81, ../includes/Elements/Price_Menu.php:87, ../includes/Elements/Price_Menu.php:88, ../includes/Elements/Price_Menu.php:251, ../includes/Elements/Price_Menu.php:449, ../includes/Elements/Stacked_Cards.php:87, ../includes/Elements/Stacked_Cards.php:1253, ../includes/Elements/Woo_Account_Dashboard.php:2590, ../includes/Elements/Woo_Account_Dashboard.php:2712, ../includes/Elements/Woo_Collections.php:490, ../includes/Elements/Woo_Cross_Sells.php:156, ../includes/Elements/Woo_Cross_Sells.php:331, ../includes/Elements/Woo_Cross_Sells.php:816, ../includes/Elements/Woo_Product_Slider.php:1873, ../includes/Elements/Woo_Thank_You.php:400, ../includes/Elements/Woo_Thank_You.php:759, ../includes/Elements/Woo_Thank_You.php:1015, ../includes/Elements/Woo_Thank_You.php:1191, ../includes/Elements/Woo_Thank_You.php:1459, ../includes/Elements/Woo_Thank_You.php:1595, ../includes/Elements/Woo_Thank_You.php:1830, ../includes/Elements/Woo_Thank_You.php:2191, ../includes/Elements/Woo_Thank_You.php:2851, ../includes/Elements/Woo_Thank_You.php:3030, ../includes/Traits/Extender.php:5189, ../includes/Traits/Filterable_Gallery_Extender.php:546, ../includes/Traits/Filterable_Gallery_Extender.php:645, ../includes/Extensions/DynamicTags/Custom_Post_Types.php:91, ../includes/Extensions/DynamicTags/Posts.php:88
msgid "Title"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1540, ../includes/Elements/Content_Timeline.php:358, ../includes/Elements/Figma_To_Elementor.php:77, ../includes/Elements/Flip_Carousel.php:348, ../includes/Elements/Flip_Carousel.php:679, ../includes/Elements/Google_Map.php:354, ../includes/Elements/Google_Map.php:482, ../includes/Elements/Image_Hot_Spots.php:146, ../includes/Elements/Interactive_Card.php:238, ../includes/Elements/Interactive_Card.php:261, ../includes/Elements/Interactive_Card.php:470, ../includes/Elements/Interactive_Card.php:494, ../includes/Elements/LD_Course_List.php:1354, ../includes/Elements/Lightbox.php:251, ../includes/Elements/Lightbox.php:296, ../includes/Elements/Multicolumn_Pricing_Table.php:915, ../includes/Elements/Offcanvas.php:566, ../includes/Elements/Price_Menu.php:418, ../includes/Elements/Protected_Content.php:75, ../includes/Elements/Protected_Content.php:362, ../includes/Elements/Sphere_Photo_Viewer.php:71, ../includes/Elements/Sphere_Photo_Viewer.php:384, ../includes/Elements/Stacked_Cards.php:77, ../includes/Elements/Stacked_Cards.php:308, ../includes/Elements/Stacked_Cards.php:407, ../includes/Elements/Stacked_Cards.php:1316, ../includes/Elements/Static_Product.php:1001, ../includes/Elements/Team_Member_Carousel.php:140, ../includes/Elements/Team_Member_Carousel.php:1019, ../includes/Elements/Testimonial_Slider.php:508, ../includes/Elements/Testimonial_Slider.php:946, ../includes/Elements/Toggle.php:149, ../includes/Elements/Toggle.php:173, ../includes/Elements/Toggle.php:231, ../includes/Elements/Toggle.php:255, ../includes/Elements/Toggle.php:679, ../includes/Elements/Woo_Account_Dashboard.php:273, ../includes/Elements/Woo_Account_Dashboard.php:1111, ../includes/Elements/Woo_Account_Dashboard.php:1197, ../includes/Elements/Woo_Account_Dashboard.php:2620, ../includes/Elements/Woo_Product_Slider.php:1266, ../includes/Elements/Woo_Product_Slider.php:1944, ../includes/Elements/Woo_Thank_You.php:2903, ../includes/Elements/Woo_Thank_You.php:3082, ../includes/Extensions/Conditional_Display.php:314, ../includes/Extensions/Conditional_Display.php:337, ../includes/Extensions/EAEL_Tooltip_Section.php:59, ../includes/Traits/Extender.php:85, ../includes/Traits/Filterable_Gallery_Extender.php:574, ../includes/Traits/Filterable_Gallery_Extender.php:697
msgid "Content"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1602, ../includes/Extensions/DynamicTags/Terms.php:98
msgid "Total Results"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1764, ../includes/Elements/Dynamic_Filterable_Gallery.php:498, ../includes/Elements/Dynamic_Filterable_Gallery.php:505, ../includes/Elements/Dynamic_Filterable_Gallery.php:549, ../includes/Elements/Instagram_Feed.php:398, ../includes/Elements/LD_Course_List.php:835
msgid "Load More"
msgstr ""

#: ../includes/Elements/Advanced_Search.php:1897, ../includes/Elements/Woo_Product_Slider.php:507
msgid "Not Found Message"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:31
msgid "Content Timeline"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:78
msgid "Timeline Content"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:85
msgid "Content Source"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:90
msgid "Dynamic"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:94, ../includes/Elements/Google_Map.php:921, ../includes/Elements/Stacked_Cards.php:824, ../includes/Elements/Woo_Account_Dashboard.php:200, ../includes/Elements/Woo_Thank_You.php:1299, ../includes/Extensions/EAEL_Particle_Section.php:112, ../includes/Extensions/Smooth_Animation.php:251, ../includes/Extensions/Smooth_Animation.php:307, ../includes/Extensions/Smooth_Animation.php:845, ../includes/Extensions/Smooth_Animation.php:888, ../includes/Extensions/Smooth_Animation.php:946, ../includes/Extensions/Smooth_Animation.php:988, ../includes/Extensions/Smooth_Animation.php:1072, ../includes/Traits/Extender.php:47, ../includes/Traits/Extender.php:3660, ../includes/Traits/Extender.php:3859, ../includes/Traits/Extender.php:4041, ../includes/Traits/Extender.php:4198, ../includes/Traits/Extender.php:4338, ../includes/Traits/Extender.php:4540, ../includes/Traits/Extender.php:4580, ../includes/Traits/Extender.php:4654
msgid "Custom"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:98
msgid "ACF"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:316
msgid "<strong>Advanced Custom Fields (ACF)</strong> is not installed/activated on your site. Please install and activate <a href=\"plugin-install.php?s=advanced-custom-fields&tab=search&type=term\" target=\"_blank\">Advanced Custom Fields (ACF)</a> first."
msgstr ""

#: ../includes/Elements/Content_Timeline.php:110
msgid "No Gallery Field Found"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:124
msgid "Content From"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:129
msgid "Current Post"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:133
msgid "Mannual Selection"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:163
msgid "No Repeater Field Found"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:176
msgid "Repeater Field"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:224
msgid "No Text Type Field found for Title"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:225
msgid "No Image Type Field found for Image"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:226
msgid "No URL Type Field found for URL"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:227
msgid "No Text, Textare or wysiwyg Type Field found for URL"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:228
msgid "No Date or Time Type Field found for Date/Time"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:233
msgid "Title Field"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:246
msgid "Image Field"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:259
msgid "Content Fields"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:274
msgid "Date/Time Field"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:287
msgid "URL Field"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:302
msgid "This option will only affect in <strong>Archive page of Elementor Theme Builder</strong> dynamically."
msgstr ""

#: ../includes/Elements/Content_Timeline.php:332, ../includes/Traits/Extender.php:70
msgid "Custom Content Settings"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:347, ../includes/Elements/Content_Timeline.php:509
msgid "The Ultimate Addons For Elementor"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:361, ../includes/Elements/Content_Timeline.php:510
msgid "<p>A new concept of showing content in your web page with more interactive way.</p>"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:368
msgid "Post Date"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:371
msgid "Nov 09, 2017"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:381
msgid "Show Circle Image / Icon"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:385, ../includes/Elements/Counter.php:97, ../includes/Elements/Counter.php:120, ../includes/Elements/Divider.php:124, ../includes/Elements/Divider.php:303, ../includes/Elements/Divider.php:865, ../includes/Elements/Dynamic_Filterable_Gallery.php:692, ../includes/Elements/Image_Hot_Spots.php:103, ../includes/Elements/Image_Hot_Spots.php:110, ../includes/Elements/Image_Hot_Spots.php:609, ../includes/Elements/Lightbox.php:294, ../includes/Elements/Lightbox.php:432, ../includes/Elements/Multicolumn_Pricing_Table.php:363, ../includes/Elements/Post_Block.php:738, ../includes/Elements/Post_Carousel.php:507, ../includes/Elements/Post_Carousel.php:889, ../includes/Elements/Post_List.php:539, ../includes/Elements/Post_List.php:910, ../includes/Elements/Price_Menu.php:175, ../includes/Elements/Price_Menu.php:252, ../includes/Elements/Price_Menu.php:805, ../includes/Elements/Sphere_Photo_Viewer.php:78, ../includes/Elements/Sphere_Photo_Viewer.php:433, ../includes/Elements/Stacked_Cards.php:158, ../includes/Elements/Stacked_Cards.php:326, ../includes/Elements/Team_Member_Carousel.php:191, ../includes/Elements/Team_Member_Carousel.php:1189, ../includes/Elements/Testimonial_Slider.php:797, ../includes/Elements/Toggle.php:148, ../includes/Elements/Toggle.php:185, ../includes/Elements/Toggle.php:230, ../includes/Elements/Toggle.php:267, ../includes/Elements/Woo_Product_Slider.php:1226, ../includes/Elements/Woo_Thank_You.php:834
msgid "Image"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:393, ../includes/Elements/Content_Timeline.php:1000
msgid "Bullet"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:405, ../includes/Elements/Stacked_Cards.php:202, ../includes/Elements/Stacked_Cards.php:344, ../includes/Extensions/EAEL_Parallax_Section.php:145
msgid "Choose Image"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:422
msgid "Icon Image Size"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:450, ../includes/Elements/Twitter_Feed_Carousel.php:316
msgid "Show Read More"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:470, ../includes/Elements/Post_List.php:782
msgid "Label Text"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:474, ../includes/Elements/Content_Timeline.php:623, ../includes/Elements/Dynamic_Filterable_Gallery.php:436, ../includes/Elements/Dynamic_Filterable_Gallery.php:775, ../includes/Elements/Interactive_Card.php:536, ../includes/Elements/LD_Course_List.php:516, ../includes/Elements/Post_Block.php:818, ../includes/Elements/Post_Carousel.php:306, ../includes/Elements/Post_Carousel.php:1003, ../includes/Elements/Post_List.php:767, ../includes/Elements/Post_List.php:786, ../includes/Elements/Post_List.php:993, ../includes/Elements/Stacked_Cards.php:437, ../includes/Elements/Testimonial_Slider.php:469
msgid "Read More"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:487, ../includes/Elements/Interactive_Card.php:549, ../includes/Elements/Stacked_Cards.php:448, ../includes/Traits/Extender.php:95
msgid "Button Link"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:541, ../includes/Elements/Dynamic_Filterable_Gallery.php:620, ../includes/Elements/Offcanvas.php:743, ../includes/Elements/Post_Block.php:710, ../includes/Elements/Post_Carousel.php:861, ../includes/Elements/Post_List.php:882
msgid "Links"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:594, ../includes/Elements/Content_Timeline.php:634, ../includes/Elements/Content_Timeline.php:675, ../includes/Elements/Dynamic_Filterable_Gallery.php:667, ../includes/Elements/Dynamic_Filterable_Gallery.php:704, ../includes/Elements/Dynamic_Filterable_Gallery.php:746, ../includes/Elements/Dynamic_Filterable_Gallery.php:786, ../includes/Elements/Post_Block.php:749, ../includes/Elements/Post_Block.php:789, ../includes/Elements/Post_Block.php:829, ../includes/Elements/Post_Carousel.php:915, ../includes/Elements/Post_Carousel.php:973, ../includes/Elements/Post_Carousel.php:1015, ../includes/Elements/Post_List.php:922, ../includes/Elements/Post_List.php:964, ../includes/Elements/Post_List.php:1004
msgid "No Follow"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:608, ../includes/Elements/Content_Timeline.php:648, ../includes/Elements/Content_Timeline.php:690, ../includes/Elements/Dynamic_Filterable_Gallery.php:679, ../includes/Elements/Dynamic_Filterable_Gallery.php:719, ../includes/Elements/Dynamic_Filterable_Gallery.php:760, ../includes/Elements/Dynamic_Filterable_Gallery.php:800, ../includes/Elements/Post_Block.php:763, ../includes/Elements/Post_Block.php:803, ../includes/Elements/Post_Block.php:843, ../includes/Elements/Post_List.php:937, ../includes/Elements/Post_List.php:978, ../includes/Elements/Post_List.php:1018
msgid "Target Blank"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:662, ../includes/Elements/LD_Course_List.php:313, ../includes/Elements/Post_Carousel.php:1389, ../includes/Elements/Woo_Cross_Sells.php:677
msgid "Thumbnail"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:711
msgid "Timeline"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:719
msgid "Line Size"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:741, ../includes/Elements/Content_Timeline.php:1074
msgid "Position From Left"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:754, ../includes/Elements/Content_Timeline.php:777
msgid "Use half of the Line size for perfect centering"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:764, ../includes/Elements/Content_Timeline.php:1051
msgid "Position From Top"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:787
msgid "Inactive Line Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:801
msgid "Active Line Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:822, ../includes/Elements/Instagram_Feed.php:243
msgid "Card"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:909, ../includes/Elements/Post_Carousel.php:1952, ../includes/Elements/Team_Member_Carousel.php:2115, ../includes/Elements/Testimonial_Slider.php:1352, ../includes/Elements/Woo_Product_Slider.php:2886
msgid "Caret"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:926
msgid "Caret Size"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:949
msgid "Caret Position"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:979
msgid "Caret Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1008
msgid "Bullet Size"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1076
msgid "Use half of the Icon Cicle Size for perfect centering"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1092
msgid "Bullet Border Width"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1112, ../includes/Elements/Content_Timeline.php:1168
msgid "Bullet Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1127, ../includes/Elements/Content_Timeline.php:1183
msgid "Bullet Border Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1142, ../includes/Elements/Content_Timeline.php:1197
msgid "Bullet Font Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1159
msgid "Active State (Highlighted)"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1221
msgid "Scrollbar"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1284
msgid "Scrollbar Thumb"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1326
msgid "This may not work in all browsers. For a better experience you may use Chromium browser."
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1342, ../includes/Elements/Logo_Carousel.php:624, ../includes/Elements/Logo_Carousel.php:894, ../includes/Elements/Post_Carousel.php:831, ../includes/Elements/Post_Carousel.php:1930, ../includes/Elements/Team_Member_Carousel.php:883, ../includes/Elements/Team_Member_Carousel.php:2093, ../includes/Elements/Testimonial_Slider.php:319, ../includes/Elements/Testimonial_Slider.php:1331, ../includes/Elements/Twitter_Feed_Carousel.php:494, ../includes/Elements/Twitter_Feed_Carousel.php:949, ../includes/Elements/Woo_Product_Slider.php:712, ../includes/Elements/Woo_Product_Slider.php:2864
msgid "Arrows"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1375
msgid "Prev Arrow Position"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1384, ../includes/Elements/Content_Timeline.php:1464
msgid "Horizontal Position by"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1397, ../includes/Elements/Content_Timeline.php:1477
msgid "Left Indent"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1426, ../includes/Elements/Content_Timeline.php:1506
msgid "Right Indent"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1455
msgid "Next Arrow Position"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1537, ../includes/Elements/LD_Course_List.php:1282, ../includes/Elements/Mailchimp.php:578, ../includes/Elements/Post_Block.php:1036, ../includes/Elements/Post_Carousel.php:1514
msgid "Color & Typography"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1545, ../includes/Elements/Interactive_Card.php:1267, ../includes/Elements/Interactive_Card.php:1340, ../includes/Elements/Post_List.php:1513, ../includes/Elements/Post_List.php:1769, ../includes/Elements/Twitter_Feed_Carousel.php:845
msgid "Title Style"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1554, ../includes/Elements/Post_List.php:1521, ../includes/Elements/Post_List.php:1778, ../includes/Elements/Twitter_Feed_Carousel.php:749, ../includes/Elements/Woo_Collections.php:501, ../includes/Elements/Woo_Product_Slider.php:1890, ../includes/Elements/Woo_Product_Slider.php:2231
msgid "Title Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1570, ../includes/Elements/Fancy_Chart.php:907, ../includes/Elements/Post_List.php:1543, ../includes/Elements/Post_List.php:1802
msgid "Title Alignment"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1600, ../includes/Elements/Counter.php:758, ../includes/Elements/Counter.php:946, ../includes/Elements/Counter.php:1007, ../includes/Elements/Counter.php:1080, ../includes/Elements/Divider.php:675, ../includes/Elements/Image_Comparison.php:747, ../includes/Elements/Image_Hot_Spots.php:942, ../includes/Elements/LD_Course_List.php:1160, ../includes/Elements/LD_Course_List.php:1317, ../includes/Elements/LD_Course_List.php:1367, ../includes/Elements/LD_Course_List.php:1427, ../includes/Elements/LD_Course_List.php:1571, ../includes/Elements/LD_Course_List.php:1799, ../includes/Elements/LD_Course_List.php:1828, ../includes/Elements/LD_Course_List.php:1929, ../includes/Elements/LD_Course_List.php:2112, ../includes/Elements/LD_Course_List.php:2194, ../includes/Elements/LD_Course_List.php:2249, ../includes/Elements/LD_Course_List.php:2436, ../includes/Elements/Lightbox.php:905, ../includes/Elements/Logo_Carousel.php:878, ../includes/Elements/Offcanvas.php:732, ../includes/Elements/Offcanvas.php:783, ../includes/Elements/Offcanvas.php:852, ../includes/Elements/Offcanvas.php:1106, ../includes/Elements/One_Page_Navigation.php:688, ../includes/Elements/Post_Block.php:1101, ../includes/Elements/Post_Block.php:1175, ../includes/Elements/Post_Block.php:1296, ../includes/Elements/Post_Block.php:1332, ../includes/Elements/Post_Carousel.php:1584, ../includes/Elements/Post_Carousel.php:1659, ../includes/Elements/Post_Carousel.php:1875, ../includes/Elements/Post_List.php:1236, ../includes/Elements/Post_List.php:1568, ../includes/Elements/Post_List.php:1827, ../includes/Elements/Post_List.php:2240, ../includes/Elements/Price_Menu.php:470, ../includes/Elements/Price_Menu.php:688, ../includes/Elements/Price_Menu.php:770, ../includes/Elements/Team_Member_Carousel.php:1307, ../includes/Elements/Team_Member_Carousel.php:1511, ../includes/Elements/Team_Member_Carousel.php:1715, ../includes/Elements/Toggle.php:612, ../includes/Elements/Toggle.php:658, ../includes/Elements/Toggle.php:726, ../includes/Elements/Woo_Collections.php:481, ../includes/Elements/Woo_Product_Slider.php:1739, ../includes/Elements/Woo_Product_Slider.php:1882, ../includes/Elements/Woo_Product_Slider.php:1912, ../includes/Elements/Woo_Product_Slider.php:1954, ../includes/Elements/Woo_Product_Slider.php:2008, ../includes/Elements/Woo_Product_Slider.php:2048, ../includes/Elements/Woo_Product_Slider.php:2105, ../includes/Elements/Woo_Product_Slider.php:2224, ../includes/Skins/Skin_Default.php:239, ../includes/Skins/Skin_Default.php:657, ../includes/Skins/Skin_Five.php:288, ../includes/Skins/Skin_Five.php:731, ../includes/Skins/Skin_Four.php:239, ../includes/Skins/Skin_Four.php:679, ../includes/Skins/Skin_One.php:287, ../includes/Skins/Skin_One.php:727, ../includes/Skins/Skin_Seven.php:239, ../includes/Skins/Skin_Seven.php:767, ../includes/Skins/Skin_Six.php:240, ../includes/Skins/Skin_Six.php:683, ../includes/Skins/Skin_Three.php:259, ../includes/Skins/Skin_Three.php:701, ../includes/Skins/Skin_Two.php:258, ../includes/Skins/Skin_Two.php:700
msgid "Typography"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1614, ../includes/Elements/Post_List.php:1578, ../includes/Elements/Post_List.php:1837
msgid "Excerpt Style"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1623, ../includes/Elements/Post_List.php:1586, ../includes/Elements/Post_List.php:1845
msgid "Excerpt Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1638, ../includes/Elements/Post_List.php:1597, ../includes/Elements/Post_List.php:1856
msgid "Excerpt Alignment"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1654, ../includes/Elements/Counter.php:370, ../includes/Elements/Offcanvas.php:593, ../includes/Elements/Post_Block.php:1161, ../includes/Elements/Post_Block.php:1279, ../includes/Elements/Post_Carousel.php:1644, ../includes/Elements/Post_List.php:1613, ../includes/Elements/Post_List.php:1872, ../includes/Elements/Price_Menu.php:277, ../includes/Elements/Woo_Cross_Sells.php:536, ../includes/Traits/Extender.php:2948
msgid "Justified"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1670, ../includes/Elements/Post_List.php:1626, ../includes/Elements/Post_List.php:1885
msgid "Excerpt Typography"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1684
msgid "Date Style"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1706, ../includes/Elements/Twitter_Feed_Carousel.php:779
msgid "Date Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1720, ../includes/Elements/Post_List.php:1680, ../includes/Elements/Post_List.php:2203
msgid "Date Typography"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1733, ../includes/Elements/LD_Course_List.php:792, ../includes/Elements/LD_Course_List.php:2328
msgid "Load More Button"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1780, ../includes/Elements/Content_Timeline.php:1834, ../includes/Elements/Content_Timeline.php:1986, ../includes/Elements/Content_Timeline.php:2042, ../includes/Elements/Counter.php:1049, ../includes/Elements/Dynamic_Filterable_Gallery.php:951, ../includes/Elements/Dynamic_Filterable_Gallery.php:1017, ../includes/Elements/Fancy_Chart.php:934, ../includes/Elements/Fancy_Chart.php:1002, ../includes/Elements/Fancy_Chart.php:1461, ../includes/Elements/Google_Map.php:1090, ../includes/Elements/Image_Comparison.php:639, ../includes/Elements/Image_Comparison.php:695, ../includes/Elements/Image_Hot_Spots.php:917, ../includes/Elements/Instagram_Feed.php:625, ../includes/Elements/Instagram_Feed.php:689, ../includes/Elements/Interactive_Card.php:1567, ../includes/Elements/Interactive_Card.php:1657, ../includes/Elements/Interactive_Card.php:1832, ../includes/Elements/Interactive_Card.php:1902, ../includes/Elements/LD_Course_List.php:2277, ../includes/Elements/LD_Course_List.php:2309, ../includes/Elements/LD_Course_List.php:2388, ../includes/Elements/LD_Course_List.php:2583, ../includes/Elements/Lightbox.php:1473, ../includes/Elements/Lightbox.php:1526, ../includes/Elements/Mailchimp.php:729, ../includes/Elements/Mailchimp.php:790, ../includes/Elements/Multicolumn_Pricing_Table.php:1078, ../includes/Elements/Multicolumn_Pricing_Table.php:1647, ../includes/Elements/Multicolumn_Pricing_Table.php:2303, ../includes/Elements/Multicolumn_Pricing_Table.php:2353, ../includes/Elements/Multicolumn_Pricing_Table.php:2506, ../includes/Elements/Multicolumn_Pricing_Table.php:2557, ../includes/Elements/Offcanvas.php:716, ../includes/Elements/Offcanvas.php:1069, ../includes/Elements/Offcanvas.php:1158, ../includes/Elements/One_Page_Navigation.php:672, ../includes/Elements/Price_Menu.php:642, ../includes/Elements/Protected_Content.php:370, ../includes/Elements/Protected_Content.php:451, ../includes/Elements/Protected_Content.php:531, ../includes/Elements/Protected_Content.php:867, ../includes/Elements/Protected_Content.php:913, ../includes/Elements/Stacked_Cards.php:467, ../includes/Elements/Static_Product.php:1224, ../includes/Elements/Static_Product.php:1291, ../includes/Elements/Static_Product.php:1483, ../includes/Elements/Static_Product.php:1540, ../includes/Elements/Team_Member_Carousel.php:1318, ../includes/Elements/Team_Member_Carousel.php:1522, ../includes/Elements/Team_Member_Carousel.php:1726, ../includes/Elements/Toggle.php:587, ../includes/Elements/Toggle.php:633, ../includes/Elements/Toggle.php:713, ../includes/Elements/Woo_Collections.php:613, ../includes/Elements/Woo_Thank_You.php:2132, ../includes/Elements/Woo_Thank_You.php:2162, ../includes/Extensions/Content_Protection.php:285, ../includes/Extensions/Content_Protection.php:369, ../includes/Extensions/Content_Protection.php:689, ../includes/Extensions/Content_Protection.php:753, ../includes/Skins/Skin_Default.php:444, ../includes/Skins/Skin_Default.php:535, ../includes/Skins/Skin_Default.php:765, ../includes/Skins/Skin_Default.php:858, ../includes/Skins/Skin_Five.php:518, ../includes/Skins/Skin_Five.php:609, ../includes/Skins/Skin_Five.php:862, ../includes/Skins/Skin_Five.php:954, ../includes/Skins/Skin_Four.php:466, ../includes/Skins/Skin_Four.php:557, ../includes/Skins/Skin_Four.php:807, ../includes/Skins/Skin_Four.php:899, ../includes/Skins/Skin_One.php:514, ../includes/Skins/Skin_One.php:605, ../includes/Skins/Skin_One.php:842, ../includes/Skins/Skin_One.php:935, ../includes/Skins/Skin_Seven.php:466, ../includes/Skins/Skin_Seven.php:557, ../includes/Skins/Skin_Seven.php:807, ../includes/Skins/Skin_Seven.php:899, ../includes/Skins/Skin_Six.php:467, ../includes/Skins/Skin_Six.php:559, ../includes/Skins/Skin_Six.php:811, ../includes/Skins/Skin_Six.php:904, ../includes/Skins/Skin_Three.php:486, ../includes/Skins/Skin_Three.php:578, ../includes/Skins/Skin_Three.php:829, ../includes/Skins/Skin_Three.php:922, ../includes/Skins/Skin_Two.php:485, ../includes/Skins/Skin_Two.php:577, ../includes/Skins/Skin_Two.php:828, ../includes/Skins/Skin_Two.php:921, ../includes/Traits/Extender.php:669, ../includes/Traits/Extender.php:767, ../includes/Traits/Extender.php:3773, ../includes/Traits/Extender.php:4163
msgid "Text Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1858, ../includes/Elements/Content_Timeline.php:2068, ../includes/Elements/Fancy_Chart.php:1324, ../includes/Elements/Fancy_Chart.php:1472, ../includes/Elements/Image_Comparison.php:464, ../includes/Elements/Instagram_Feed.php:713, ../includes/Elements/Interactive_Card.php:1703, ../includes/Elements/Interactive_Card.php:1941, ../includes/Elements/LD_Course_List.php:2599, ../includes/Elements/Lightbox.php:1551, ../includes/Elements/Logo_Carousel.php:1105, ../includes/Elements/Logo_Carousel.php:1334, ../includes/Elements/Mailchimp.php:814, ../includes/Elements/Multicolumn_Pricing_Table.php:1775, ../includes/Elements/Multicolumn_Pricing_Table.php:2375, ../includes/Elements/Multicolumn_Pricing_Table.php:2580, ../includes/Elements/Offcanvas.php:1171, ../includes/Elements/One_Page_Navigation.php:576, ../includes/Elements/One_Page_Navigation.php:624, ../includes/Elements/Post_Carousel.php:2112, ../includes/Elements/Post_Carousel.php:2310, ../includes/Elements/Post_Carousel.php:2343, ../includes/Elements/Price_Menu.php:586, ../includes/Elements/Stacked_Cards.php:1440, ../includes/Elements/Static_Product.php:1335, ../includes/Elements/Static_Product.php:1579, ../includes/Elements/Team_Member_Carousel.php:2072, ../includes/Elements/Team_Member_Carousel.php:2275, ../includes/Elements/Team_Member_Carousel.php:2471, ../includes/Elements/Testimonial_Slider.php:1310, ../includes/Elements/Testimonial_Slider.php:1551, ../includes/Elements/Testimonial_Slider.php:1747, ../includes/Elements/Twitter_Feed_Carousel.php:800, ../includes/Elements/Twitter_Feed_Carousel.php:1159, ../includes/Elements/Twitter_Feed_Carousel.php:1354, ../includes/Elements/Woo_Product_Slider.php:1186, ../includes/Elements/Woo_Product_Slider.php:1706, ../includes/Elements/Woo_Product_Slider.php:1842, ../includes/Elements/Woo_Product_Slider.php:1985, ../includes/Elements/Woo_Product_Slider.php:2080, ../includes/Elements/Woo_Product_Slider.php:2194, ../includes/Elements/Woo_Product_Slider.php:2639, ../includes/Elements/Woo_Product_Slider.php:3066, ../includes/Extensions/EAEL_Tooltip_Section.php:306, ../includes/Skins/Skin_Default.php:514, ../includes/Skins/Skin_Default.php:611, ../includes/Skins/Skin_Default.php:837, ../includes/Skins/Skin_Default.php:934, ../includes/Skins/Skin_Five.php:588, ../includes/Skins/Skin_Five.php:685, ../includes/Skins/Skin_Five.php:933, ../includes/Skins/Skin_Five.php:1030, ../includes/Skins/Skin_Four.php:536, ../includes/Skins/Skin_Four.php:633, ../includes/Skins/Skin_Four.php:878, ../includes/Skins/Skin_Four.php:975, ../includes/Skins/Skin_One.php:584, ../includes/Skins/Skin_One.php:681, ../includes/Skins/Skin_One.php:914, ../includes/Skins/Skin_One.php:1011, ../includes/Skins/Skin_Seven.php:536, ../includes/Skins/Skin_Seven.php:633, ../includes/Skins/Skin_Seven.php:878, ../includes/Skins/Skin_Seven.php:975, ../includes/Skins/Skin_Six.php:538, ../includes/Skins/Skin_Six.php:636, ../includes/Skins/Skin_Six.php:883, ../includes/Skins/Skin_Six.php:981, ../includes/Skins/Skin_Three.php:557, ../includes/Skins/Skin_Three.php:655, ../includes/Skins/Skin_Three.php:901, ../includes/Skins/Skin_Three.php:999, ../includes/Skins/Skin_Two.php:556, ../includes/Skins/Skin_Two.php:654, ../includes/Skins/Skin_Two.php:900, ../includes/Skins/Skin_Two.php:998, ../includes/Traits/Extender.php:822, ../includes/Traits/Extender.php:2873
msgid "Border Color"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1884
msgid "Loader Position"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1893
msgid "From Left"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1913
msgid "From Top"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:1940, ../includes/Elements/Static_Product.php:368, ../includes/Elements/Static_Product.php:1066
msgid "Read More Button"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:2157, ../includes/Elements/Post_Block.php:1533, ../includes/Elements/Post_Carousel.php:2923, ../includes/Elements/Post_List.php:2810, ../includes/Elements/Woo_Account_Dashboard.php:3025, ../includes/Elements/Woo_Cross_Sells.php:1162, ../includes/Elements/Woo_Product_Slider.php:3248, ../includes/Elements/Woo_Thank_You.php:3327
msgid "No layout found!"
msgstr ""

#: ../includes/Elements/Content_Timeline.php:2153, ../includes/Elements/Dynamic_Filterable_Gallery.php:1999, ../includes/Elements/Post_Block.php:1529, ../includes/Elements/Post_Carousel.php:2919, ../includes/Elements/Post_List.php:2806
msgid "No posts found!"
msgstr ""

#: ../includes/Elements/Counter.php:24, ../includes/Elements/Counter.php:77, ../includes/Elements/Counter.php:346, ../includes/Elements/Interactive_Card.php:163
msgid "Counter"
msgstr ""

#: ../includes/Elements/Counter.php:84, ../includes/Elements/Multicolumn_Pricing_Table.php:350
msgid "Icon Type"
msgstr ""

#: ../includes/Elements/Counter.php:137, ../includes/Elements/Counter.php:737
msgid "Number"
msgstr ""

#: ../includes/Elements/Counter.php:150
msgid "Comma Separator"
msgstr ""

#: ../includes/Elements/Counter.php:162, ../includes/Elements/Counter.php:919
msgid "Prefix"
msgstr ""

#: ../includes/Elements/Counter.php:176, ../includes/Elements/Counter.php:980
msgid "Suffix"
msgstr ""

#: ../includes/Elements/Counter.php:193
msgid "Counter Title"
msgstr ""

#: ../includes/Elements/Counter.php:251, ../includes/Elements/Instagram_Feed.php:239, ../includes/Elements/Lightbox.php:156, ../includes/Elements/Mailchimp.php:391, ../includes/Elements/Post_Carousel.php:91, ../includes/Elements/Team_Member_Carousel.php:499, ../includes/Elements/Testimonial_Slider.php:396, ../includes/Elements/Woo_Account_Dashboard.php:161, ../includes/Elements/Woo_Account_Dashboard.php:168, ../includes/Elements/Woo_Cross_Sells.php:107, ../includes/Elements/Woo_Product_Slider.php:345, ../includes/Elements/Woo_Thank_You.php:155, ../includes/Skins/Skin_Default.php:48, ../includes/Skins/Skin_Five.php:48, ../includes/Skins/Skin_Four.php:48, ../includes/Skins/Skin_One.php:48, ../includes/Skins/Skin_Seven.php:48, ../includes/Skins/Skin_Six.php:48, ../includes/Skins/Skin_Three.php:48, ../includes/Skins/Skin_Two.php:48
msgid "Layout"
msgstr ""

#: ../includes/Elements/Counter.php:255, ../includes/Elements/LD_Course_List.php:130, ../includes/Traits/Extender.php:923
msgid "Layout 1"
msgstr ""

#: ../includes/Elements/Counter.php:256, ../includes/Elements/LD_Course_List.php:131, ../includes/Traits/Extender.php:924
msgid "Layout 2"
msgstr ""

#: ../includes/Elements/Counter.php:257, ../includes/Elements/LD_Course_List.php:132
msgid "Layout 3"
msgstr ""

#: ../includes/Elements/Counter.php:258
msgid "Layout 4"
msgstr ""

#: ../includes/Elements/Counter.php:259
msgid "Layout 5"
msgstr ""

#: ../includes/Elements/Counter.php:260
msgid "Layout 6"
msgstr ""

#: ../includes/Elements/Counter.php:274
msgid "Dividers"
msgstr ""

#: ../includes/Elements/Counter.php:281
msgid "For Icon"
msgstr ""

#: ../includes/Elements/Counter.php:284, ../includes/Elements/Counter.php:299, ../includes/Elements/Fancy_Chart.php:198, ../includes/Elements/Google_Map.php:797, ../includes/Elements/Google_Map.php:808, ../includes/Elements/Google_Map.php:819, ../includes/Elements/Google_Map.php:830, ../includes/Elements/Google_Map.php:841, ../includes/Elements/One_Page_Navigation.php:226, ../includes/Elements/One_Page_Navigation.php:239, ../includes/Elements/One_Page_Navigation.php:255, ../includes/Elements/Price_Menu.php:133, ../includes/Elements/Price_Menu.php:165, ../includes/Elements/Price_Menu.php:711, ../includes/Elements/Sphere_Photo_Viewer.php:216, ../includes/Elements/Sphere_Photo_Viewer.php:869, ../includes/Extensions/Smooth_Animation.php:373
msgid "On"
msgstr ""

#: ../includes/Elements/Counter.php:285, ../includes/Elements/Counter.php:300, ../includes/Elements/Fancy_Chart.php:199, ../includes/Elements/Google_Map.php:798, ../includes/Elements/Google_Map.php:809, ../includes/Elements/Google_Map.php:820, ../includes/Elements/Google_Map.php:831, ../includes/Elements/Google_Map.php:842, ../includes/Elements/One_Page_Navigation.php:227, ../includes/Elements/One_Page_Navigation.php:240, ../includes/Elements/One_Page_Navigation.php:256, ../includes/Elements/Price_Menu.php:134, ../includes/Elements/Price_Menu.php:166, ../includes/Elements/Price_Menu.php:712, ../includes/Elements/Sphere_Photo_Viewer.php:217, ../includes/Elements/Sphere_Photo_Viewer.php:870, ../includes/Extensions/Smooth_Animation.php:374
msgid "Off"
msgstr ""

#: ../includes/Elements/Counter.php:296
msgid "For Number"
msgstr ""

#: ../includes/Elements/Counter.php:313, ../includes/Elements/Fancy_Chart.php:121, ../includes/Elements/Image_Comparison.php:187, ../includes/Elements/Lightbox.php:386, ../includes/Elements/Offcanvas.php:364, ../includes/Elements/One_Page_Navigation.php:187, ../includes/Elements/Sphere_Photo_Viewer.php:150, ../includes/Elements/Stacked_Cards.php:558, ../includes/Extensions/EAEL_Tooltip_Section.php:50, ../includes/Traits/Core.php:105
msgid "Settings"
msgstr ""

#: ../includes/Elements/Counter.php:320
msgid "Counting Speed"
msgstr ""

#: ../includes/Elements/Counter.php:425, ../includes/Elements/Image_Comparison.php:344, ../includes/Elements/Interactive_Promo.php:312, ../includes/Elements/LD_Course_List.php:961, ../includes/Elements/LD_Course_List.php:1043, ../includes/Elements/LD_Course_List.php:1196, ../includes/Elements/LD_Course_List.php:1524, ../includes/Elements/LD_Course_List.php:1557, ../includes/Elements/LD_Course_List.php:1953, ../includes/Elements/LD_Course_List.php:2032, ../includes/Elements/Lightbox.php:924, ../includes/Elements/Lightbox.php:1859, ../includes/Elements/Lightbox.php:1930, ../includes/Elements/Mailchimp.php:405, ../includes/Elements/Mailchimp.php:849, ../includes/Elements/Offcanvas.php:507, ../includes/Elements/Post_Carousel.php:1195, ../includes/Elements/Post_Carousel.php:1259, ../includes/Elements/Post_Carousel.php:1439, ../includes/Elements/Post_Carousel.php:1750, ../includes/Elements/Post_List.php:1984, ../includes/Elements/Post_List.php:2032, ../includes/Elements/Post_List.php:2274, ../includes/Elements/Post_List.php:2322, ../includes/Elements/Static_Product.php:813, ../includes/Elements/Static_Product.php:870, ../includes/Elements/Static_Product.php:906, ../includes/Elements/Static_Product.php:1025, ../includes/Elements/Static_Product.php:1240, ../includes/Elements/Static_Product.php:1307, ../includes/Elements/Static_Product.php:1496, ../includes/Elements/Static_Product.php:1552, ../includes/Elements/Team_Member_Carousel.php:1005, ../includes/Elements/Testimonial_Slider.php:686, ../includes/Elements/Twitter_Feed_Carousel.php:812, ../includes/Elements/Woo_Account_Dashboard.php:1454, ../includes/Elements/Woo_Account_Dashboard.php:1498, ../includes/Elements/Woo_Account_Dashboard.php:1683, ../includes/Elements/Woo_Account_Dashboard.php:1731, ../includes/Elements/Woo_Account_Dashboard.php:1838, ../includes/Elements/Woo_Account_Dashboard.php:1962, ../includes/Elements/Woo_Account_Dashboard.php:2086, ../includes/Elements/Woo_Account_Dashboard.php:2180, ../includes/Elements/Woo_Account_Dashboard.php:2457, ../includes/Elements/Woo_Account_Dashboard.php:2505, ../includes/Elements/Woo_Account_Dashboard.php:2560, ../includes/Elements/Woo_Product_Slider.php:2334, ../includes/Elements/Woo_Product_Slider.php:2391, ../includes/Traits/Extender.php:959, ../includes/Traits/Extender.php:2748, ../includes/Traits/Extender.php:5516
msgid "Background"
msgstr ""

#: ../includes/Elements/Counter.php:481, ../includes/Elements/Team_Member_Carousel.php:1197
msgid "Image Width"
msgstr ""

#: ../includes/Elements/Counter.php:503, ../includes/Elements/Stacked_Cards.php:594, ../includes/Traits/Extender.php:385
msgid "Rotation"
msgstr ""

#: ../includes/Elements/Counter.php:596
msgid "Icon Divider"
msgstr ""

#: ../includes/Elements/Counter.php:609, ../includes/Elements/Counter.php:796
msgid "Divider Type"
msgstr ""

#: ../includes/Elements/Counter.php:613, ../includes/Elements/Counter.php:800, ../includes/Elements/Divider.php:404, ../includes/Elements/Fancy_Chart.php:187, ../includes/Elements/Price_Menu.php:518, ../includes/Elements/Price_Menu.php:992, ../includes/Elements/Team_Member_Carousel.php:1391, ../includes/Elements/Team_Member_Carousel.php:1595, ../includes/Elements/Team_Member_Carousel.php:1799
msgid "Solid"
msgstr ""

#: ../includes/Elements/Counter.php:614, ../includes/Elements/Counter.php:801, ../includes/Elements/Divider.php:407, ../includes/Elements/Price_Menu.php:519, ../includes/Elements/Price_Menu.php:995, ../includes/Elements/Team_Member_Carousel.php:1394, ../includes/Elements/Team_Member_Carousel.php:1598, ../includes/Elements/Team_Member_Carousel.php:1802
msgid "Double"
msgstr ""

#: ../includes/Elements/Counter.php:615, ../includes/Elements/Counter.php:802, ../includes/Elements/Divider.php:406, ../includes/Elements/Price_Menu.php:520, ../includes/Elements/Price_Menu.php:994, ../includes/Elements/Team_Member_Carousel.php:1392, ../includes/Elements/Team_Member_Carousel.php:1596, ../includes/Elements/Team_Member_Carousel.php:1800
msgid "Dotted"
msgstr ""

#: ../includes/Elements/Counter.php:616, ../includes/Elements/Counter.php:803, ../includes/Elements/Divider.php:405, ../includes/Elements/Price_Menu.php:521, ../includes/Elements/Price_Menu.php:993, ../includes/Elements/Team_Member_Carousel.php:1393, ../includes/Elements/Team_Member_Carousel.php:1597, ../includes/Elements/Team_Member_Carousel.php:1801
msgid "Dashed"
msgstr ""

#: ../includes/Elements/Counter.php:704, ../includes/Elements/Counter.php:887, ../includes/Elements/Divider.php:697, ../includes/Elements/Divider.php:835, ../includes/Elements/Divider.php:945, ../includes/Elements/Logo_Carousel.php:1192, ../includes/Elements/Multicolumn_Pricing_Table.php:869, ../includes/Elements/One_Page_Navigation.php:439, ../includes/Elements/Post_Block.php:1112, ../includes/Elements/Post_Block.php:1186, ../includes/Elements/Post_Carousel.php:2229, ../includes/Elements/Team_Member_Carousel.php:2356, ../includes/Elements/Testimonial_Slider.php:1632, ../includes/Elements/Twitter_Feed_Carousel.php:1239, ../includes/Elements/Woo_Product_Slider.php:2536
msgid "Spacing"
msgstr ""

#: ../includes/Elements/Counter.php:745
msgid "Number Color"
msgstr ""

#: ../includes/Elements/Counter.php:784
msgid "Number Divider"
msgstr ""

#: ../includes/Elements/Divider.php:37, ../includes/Elements/Divider.php:99, ../includes/Elements/Divider.php:355, ../includes/Elements/Image_Comparison.php:485, ../includes/Elements/Team_Member_Carousel.php:1357, ../includes/Elements/Team_Member_Carousel.php:1561, ../includes/Elements/Team_Member_Carousel.php:1765, ../includes/Skins/Skin_Default.php:301, ../includes/Skins/Skin_Five.php:322, ../includes/Skins/Skin_Four.php:270, ../includes/Skins/Skin_One.php:318, ../includes/Skins/Skin_Seven.php:270, ../includes/Skins/Skin_Six.php:271, ../includes/Skins/Skin_Three.php:290, ../includes/Skins/Skin_Two.php:289
msgid "Divider"
msgstr ""

#: ../includes/Elements/Divider.php:107, ../includes/Elements/Static_Product.php:91, ../includes/Elements/Woo_Collections.php:216
msgid "Choose Layout"
msgstr ""

#: ../includes/Elements/Divider.php:112
msgid "Plain"
msgstr ""

#: ../includes/Elements/Divider.php:116, ../includes/Elements/Divider.php:248, ../includes/Elements/Divider.php:624, ../includes/Elements/Image_Hot_Spots.php:156, ../includes/Elements/Image_Hot_Spots.php:181, ../includes/Elements/Lightbox.php:530, ../includes/Elements/Multicolumn_Pricing_Table.php:355, ../includes/Elements/Multicolumn_Pricing_Table.php:707, ../includes/Elements/Offcanvas.php:704, ../includes/Elements/Post_Carousel.php:302, ../includes/Elements/Testimonial_Slider.php:466, ../includes/Elements/Woo_Thank_You.php:1060, ../includes/Elements/Woo_Thank_You.php:1127, ../includes/Traits/Extender.php:3218, ../includes/Traits/Extender.php:3906, ../includes/Traits/Extender.php:5395
msgid "Text"
msgstr ""

#: ../includes/Elements/Divider.php:135, ../includes/Elements/Logo_Carousel.php:648, ../includes/Elements/Multicolumn_Pricing_Table.php:180, ../includes/Elements/Offcanvas.php:371, ../includes/Elements/Stacked_Cards.php:180, ../includes/Elements/Woo_Product_Slider.php:767
msgid "Direction"
msgstr ""

#: ../includes/Elements/Divider.php:139, ../includes/Elements/Fancy_Chart.php:156, ../includes/Elements/Image_Comparison.php:208, ../includes/Elements/Image_Scroller.php:124, ../includes/Elements/Interactive_Card.php:2100, ../includes/Elements/Multicolumn_Pricing_Table.php:1495, ../includes/Elements/Stacked_Cards.php:571, ../includes/Elements/Woo_Thank_You.php:1313, ../includes/Extensions/EAEL_Tooltip_Section.php:169, ../includes/Skins/Skin_Default.php:52, ../includes/Skins/Skin_Five.php:52, ../includes/Skins/Skin_Four.php:52, ../includes/Skins/Skin_One.php:52, ../includes/Skins/Skin_Seven.php:52, ../includes/Skins/Skin_Six.php:52, ../includes/Skins/Skin_Three.php:52, ../includes/Skins/Skin_Two.php:52, ../includes/Template/Content-Timeline/horizontal.php:3
msgid "Horizontal"
msgstr ""

#: ../includes/Elements/Divider.php:140, ../includes/Elements/Fancy_Chart.php:155, ../includes/Elements/Image_Comparison.php:209, ../includes/Elements/Image_Scroller.php:125, ../includes/Elements/Interactive_Card.php:2081, ../includes/Elements/Multicolumn_Pricing_Table.php:1522, ../includes/Elements/Stacked_Cards.php:570, ../includes/Elements/Woo_Thank_You.php:1339, ../includes/Extensions/EAEL_Tooltip_Section.php:168, ../includes/Skins/Skin_Default.php:53, ../includes/Skins/Skin_Five.php:53, ../includes/Skins/Skin_Four.php:53, ../includes/Skins/Skin_One.php:53, ../includes/Skins/Skin_Seven.php:53, ../includes/Skins/Skin_Six.php:53, ../includes/Skins/Skin_Three.php:53, ../includes/Skins/Skin_Two.php:53
msgid "Vertical"
msgstr ""

#: ../includes/Elements/Divider.php:148
msgid "Divider Position"
msgstr ""

#: ../includes/Elements/Divider.php:152, ../includes/Traits/Extender.php:3801
msgid "Inline"
msgstr ""

#: ../includes/Elements/Divider.php:156, ../includes/Traits/Extender.php:3802
msgid "Block"
msgstr ""

#: ../includes/Elements/Divider.php:175
msgid "Show Left Divider"
msgstr ""

#: ../includes/Elements/Divider.php:189
msgid "Left Divider Width"
msgstr ""

#: ../includes/Elements/Divider.php:211
msgid "Show Right Divider"
msgstr ""

#: ../includes/Elements/Divider.php:225
msgid "Right Divider Width"
msgstr ""

#: ../includes/Elements/Divider.php:251
msgid "Divider Text"
msgstr ""

#: ../includes/Elements/Divider.php:280
msgid "HTML Tag"
msgstr ""

#: ../includes/Elements/Divider.php:290, ../includes/Elements/Interactive_Card.php:190
msgid "div"
msgstr ""

#: ../includes/Elements/Divider.php:291, ../includes/Elements/Interactive_Card.php:191
msgid "span"
msgstr ""

#: ../includes/Elements/Divider.php:292, ../includes/Elements/Interactive_Card.php:192
msgid "p"
msgstr ""

#: ../includes/Elements/Divider.php:364, ../includes/Elements/Price_Menu.php:953
msgid "Vertical Alignment"
msgstr ""

#: ../includes/Elements/Divider.php:370, ../includes/Elements/Fancy_Chart.php:308, ../includes/Elements/Fancy_Chart.php:334, ../includes/Elements/Fancy_Chart.php:1518, ../includes/Elements/Image_Comparison.php:552, ../includes/Elements/Image_Hot_Spots.php:286, ../includes/Elements/Image_Hot_Spots.php:393, ../includes/Elements/Interactive_Card.php:408, ../includes/Elements/Logo_Carousel.php:226, ../includes/Elements/Multicolumn_Pricing_Table.php:432, ../includes/Elements/Multicolumn_Pricing_Table.php:1526, ../includes/Elements/One_Page_Navigation.php:311, ../includes/Elements/Price_Menu.php:910, ../includes/Elements/Price_Menu.php:958, ../includes/Elements/Toggle.php:552, ../includes/Elements/Twitter_Feed_Carousel.php:551, ../includes/Elements/Woo_Collections.php:415, ../includes/Elements/Woo_Product_Slider.php:1104, ../includes/Extensions/EAEL_Tooltip_Section.php:80, ../includes/Extensions/Smooth_Animation.php:247, ../includes/Extensions/Smooth_Animation.php:303, ../includes/Extensions/Smooth_Animation.php:842, ../includes/Extensions/Smooth_Animation.php:885, ../includes/Extensions/Smooth_Animation.php:943, ../includes/Extensions/Smooth_Animation.php:985
msgid "Top"
msgstr ""

#: ../includes/Elements/Divider.php:378, ../includes/Elements/Fancy_Chart.php:310, ../includes/Elements/Fancy_Chart.php:336, ../includes/Elements/Fancy_Chart.php:1514, ../includes/Elements/Image_Comparison.php:560, ../includes/Elements/Image_Hot_Spots.php:287, ../includes/Elements/Image_Hot_Spots.php:394, ../includes/Elements/Logo_Carousel.php:234, ../includes/Elements/Multicolumn_Pricing_Table.php:440, ../includes/Elements/Multicolumn_Pricing_Table.php:1534, ../includes/Elements/One_Page_Navigation.php:315, ../includes/Elements/Price_Menu.php:918, ../includes/Elements/Price_Menu.php:966, ../includes/Elements/Toggle.php:560, ../includes/Elements/Twitter_Feed_Carousel.php:559, ../includes/Elements/Woo_Collections.php:417, ../includes/Elements/Woo_Product_Slider.php:1112, ../includes/Extensions/EAEL_Tooltip_Section.php:81, ../includes/Extensions/Smooth_Animation.php:249, ../includes/Extensions/Smooth_Animation.php:305, ../includes/Extensions/Smooth_Animation.php:843, ../includes/Extensions/Smooth_Animation.php:886, ../includes/Extensions/Smooth_Animation.php:944, ../includes/Extensions/Smooth_Animation.php:986
msgid "Bottom"
msgstr ""

#: ../includes/Elements/Divider.php:400, ../includes/Elements/Price_Menu.php:988, ../includes/Extensions/Content_Protection.php:269
msgid "Style"
msgstr ""

#: ../includes/Elements/Divider.php:546, ../includes/Elements/Divider.php:573, ../includes/Elements/Divider.php:600, ../includes/Elements/Team_Member_Carousel.php:1369, ../includes/Elements/Team_Member_Carousel.php:1573, ../includes/Elements/Team_Member_Carousel.php:1777, ../includes/Elements/Woo_Cross_Sells.php:800, ../includes/Skins/Skin_Default.php:668, ../includes/Skins/Skin_Five.php:765, ../includes/Skins/Skin_Four.php:710, ../includes/Skins/Skin_Seven.php:655, ../includes/Skins/Skin_Six.php:714, ../includes/Skins/Skin_Three.php:732, ../includes/Skins/Skin_Two.php:731
msgid "Divider Color"
msgstr ""

#: ../includes/Elements/Divider.php:563, ../includes/Elements/Image_Comparison.php:632, ../includes/Elements/LD_Course_List.php:895, ../includes/Elements/Lightbox.php:566, ../includes/Elements/Multicolumn_Pricing_Table.php:656, ../includes/Elements/Offcanvas.php:318, ../includes/Elements/Static_Product.php:1081, ../includes/Elements/Static_Product.php:1369, ../includes/Traits/Extender.php:425
msgid "Before"
msgstr ""

#: ../includes/Elements/Divider.php:590, ../includes/Elements/Image_Comparison.php:688, ../includes/Elements/LD_Course_List.php:894, ../includes/Elements/Lightbox.php:567, ../includes/Elements/Multicolumn_Pricing_Table.php:660, ../includes/Elements/Offcanvas.php:319, ../includes/Elements/Static_Product.php:1082, ../includes/Elements/Static_Product.php:1370, ../includes/Traits/Extender.php:426
msgid "After"
msgstr ""

#: ../includes/Elements/Divider.php:635, ../includes/Elements/Divider.php:738, ../includes/Elements/Divider.php:876, ../includes/Elements/Fancy_Chart.php:1510, ../includes/Elements/Fancy_Chart.php:1572, ../includes/Elements/Image_Comparison.php:546, ../includes/Elements/Image_Comparison.php:574, ../includes/Elements/Image_Hot_Spots.php:224, ../includes/Elements/Interactive_Card.php:2072, ../includes/Elements/LD_Course_List.php:1447, ../includes/Elements/Lightbox.php:447, ../includes/Elements/Logo_Carousel.php:1150, ../includes/Elements/Multicolumn_Pricing_Table.php:1176, ../includes/Elements/Post_Carousel.php:436, ../includes/Elements/Post_Carousel.php:2157, ../includes/Elements/Team_Member_Carousel.php:164, ../includes/Elements/Team_Member_Carousel.php:646, ../includes/Elements/Team_Member_Carousel.php:681, ../includes/Elements/Team_Member_Carousel.php:1502, ../includes/Elements/Team_Member_Carousel.php:2320, ../includes/Elements/Testimonial_Slider.php:331, ../includes/Elements/Testimonial_Slider.php:1596, ../includes/Elements/Toggle.php:546, ../includes/Elements/Twitter_Feed_Carousel.php:1204, ../includes/Elements/Woo_Product_Slider.php:949, ../includes/Elements/Woo_Product_Slider.php:2445, ../includes/Elements/Woo_Thank_You.php:1296, ../includes/Extensions/EAEL_Tooltip_Section.php:76, ../includes/Traits/Filterable_Gallery_Extender.php:274, ../includes/Traits/Filterable_Gallery_Extender.php:377, ../includes/Traits/Filterable_Gallery_Extender.php:482
msgid "Position"
msgstr ""

#: ../includes/Elements/Divider.php:804
msgid "Icon Rotation"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:33
msgid "Dynamic Gallery"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:82
msgid "Dynamic Gallery Settings"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:89
msgid "Template Layout"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:99
msgid "Animation Duration (ms)"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:112, ../includes/Elements/LD_Course_List.php:242, ../includes/Elements/Woo_Cross_Sells.php:117
msgid "Columns"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:146, ../includes/Elements/LD_Course_List.php:145, ../includes/Traits/Filterable_Gallery_Extender.php:211
msgid "Grid"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:147, ../includes/Elements/LD_Course_List.php:146
msgid "Masonry"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:155
msgid "Grid item height"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:178, ../includes/Elements/Dynamic_Filterable_Gallery.php:1089, ../includes/Elements/Post_Carousel.php:136
msgid "Item Style"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:182
msgid "Hoverer"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:183, ../includes/Elements/Post_Carousel.php:141
msgid "Cards"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:191
msgid "Full Image Clickable"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:206, ../includes/Elements/Post_List.php:461, ../includes/Elements/Woo_Product_Slider.php:355, ../includes/Elements/Woo_Thank_You.php:616
msgid "Show Title"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:218, ../includes/Elements/Multicolumn_Pricing_Table.php:836, ../includes/Elements/Protected_Content.php:219
msgid "Show Content"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:230, ../includes/Elements/Post_Carousel.php:1411, ../includes/Elements/Static_Product.php:709
msgid "Hover Style"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:235
msgid "Slide In Up"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:236
msgid "Fade In"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:237
msgid "Zoom In "
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:270
msgid "Zoom Icon"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:283
msgid "Link Icon"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:307
msgid "Filter Controls"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:314
msgid "Show filter controls"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:333
msgid "Gallery All Label"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:348
msgid "Titles"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:359
msgid "Customize Filter Items"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:371
msgid "Filter Items From"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:376
msgid "Select options those are related to Content » Query » Source"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:387
msgid "Search & Select Item (e.x. Post)"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:424
msgid "Post Excerpt Length"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:433
msgid "Excerpt Read More"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:451
msgid "Popup Settings"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:458
msgid "Show Popup"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:470
msgid "Popup Styles"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:474, ../includes/Elements/Woo_Cross_Sells.php:355, ../includes/Elements/Woo_Cross_Sells.php:951
msgid "Buttons"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:478, ../includes/Elements/Stacked_Cards.php:140, ../includes/Elements/Stacked_Cards.php:913
msgid "Media"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:509, ../includes/Elements/Flip_Carousel.php:290, ../includes/Elements/Flip_Carousel.php:305, ../includes/Elements/LD_Course_List.php:202
msgid "Disable"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:513, ../includes/Elements/Interactive_Card.php:275, ../includes/Elements/Interactive_Card.php:508, ../includes/Elements/LD_Course_List.php:475, ../includes/Elements/LD_Course_List.php:1904, ../includes/Elements/Lightbox.php:424, ../includes/Elements/Lightbox.php:1347, ../includes/Elements/Protected_Content.php:802, ../includes/Elements/Stacked_Cards.php:1374, ../includes/Elements/Woo_Account_Dashboard.php:1382, ../includes/Elements/Woo_Account_Dashboard.php:1605, ../includes/Elements/Woo_Account_Dashboard.php:2379, ../includes/Elements/Woo_Product_Slider.php:1550, ../includes/Elements/Woo_Thank_You.php:2026
msgid "Button"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:517
msgid "Infinity Scroll"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:529
msgid "Scroll Offset (px)"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:534
msgid "Set the position of loading to the viewport before it ends from view"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:544
msgid "Button text"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:562
msgid "Button top space"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:658, ../includes/Elements/Image_Hot_Spots.php:197, ../includes/Elements/Logo_Carousel.php:333, ../includes/Elements/Multicolumn_Pricing_Table.php:720, ../includes/Elements/Price_Menu.php:193, ../includes/Elements/Team_Member_Carousel.php:208, ../includes/Elements/Woo_Account_Dashboard.php:1327, ../includes/Elements/Woo_Account_Dashboard.php:1551, ../includes/Traits/Filterable_Gallery_Extender.php:749
msgid "Link"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:821, ../includes/Elements/Google_Map.php:993, ../includes/Elements/Image_Scroller.php:177, ../includes/Elements/Interactive_Card.php:769, ../includes/Elements/Mailchimp.php:384
msgid "General Style"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:908
msgid "Control Style"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1012, ../includes/Elements/One_Page_Navigation.php:590, ../includes/Elements/Post_Carousel.php:2357, ../includes/Elements/Woo_Account_Dashboard.php:886, ../includes/Elements/Woo_Product_Slider.php:2653, ../includes/Traits/Extender.php:2496, ../includes/Traits/Extender.php:2664
msgid "Active"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1178
msgid "Buttons Style"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1223, ../includes/Elements/Dynamic_Filterable_Gallery.php:1577, ../includes/Elements/Post_Block.php:1437
msgid "Icon size"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1242, ../includes/Elements/Dynamic_Filterable_Gallery.php:1598
msgid "Icon font size"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1323
msgid "Item Caption Style"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1375
msgid "Caption Button Alignment"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1406
msgid "Caption Alignment"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1438
msgid "Caption Title"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1471
msgid "Caption Content"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1502
msgid "Caption Read More"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1533
msgid "Hover Icon"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1687
msgid "Item Content Style"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1739, ../includes/Elements/Post_List.php:2158
msgid "Title Typography"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1760, ../includes/Elements/Post_Block.php:1064, ../includes/Elements/Post_Carousel.php:1543, ../includes/Elements/Testimonial_Slider.php:974, ../includes/Elements/Testimonial_Slider.php:1015, ../includes/Elements/Testimonial_Slider.php:1055, ../includes/Elements/Testimonial_Slider.php:1108, ../includes/Elements/Twitter_Feed_Carousel.php:924, ../includes/Elements/Woo_Account_Dashboard.php:2682, ../includes/Elements/Woo_Product_Slider.php:2251, ../includes/Traits/Filterable_Gallery_Extender.php:309, ../includes/Traits/Filterable_Gallery_Extender.php:469
msgid "Hover Color"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1780
msgid "Content Typography"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1809
msgid "Read More Typography"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:1838, ../includes/Elements/Interactive_Card.php:907, ../includes/Elements/Interactive_Card.php:1108, ../includes/Elements/Static_Product.php:511, ../includes/Elements/Woo_Collections.php:428
msgid "Content Alignment"
msgstr ""

#: ../includes/Elements/Dynamic_Filterable_Gallery.php:2002
msgid "No Layout Found!"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:23
msgid "Fancy Chart"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:78
msgid "Chart Title"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:80
msgid "Type your chart title"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:81
msgid "Sample Chart Title"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:90, ../includes/Elements/LD_Course_List.php:1298, ../includes/Elements/Post_List.php:1186, ../includes/Elements/Stacked_Cards.php:100, ../includes/Elements/Woo_Product_Slider.php:367
msgid "Title Tag"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:110, ../includes/Elements/Fancy_Chart.php:966, ../includes/Elements/Offcanvas.php:242, ../includes/Elements/Price_Menu.php:99, ../includes/Elements/Price_Menu.php:105, ../includes/Elements/Price_Menu.php:106, ../includes/Elements/Price_Menu.php:749, ../includes/Elements/Sphere_Photo_Viewer.php:122, ../includes/Elements/Team_Member_Carousel.php:179, ../includes/Elements/Team_Member_Carousel.php:691, ../includes/Elements/Team_Member_Carousel.php:1706, ../includes/Elements/Testimonial_Slider.php:572, ../includes/Elements/Testimonial_Slider.php:1034, ../includes/Elements/Woo_Account_Dashboard.php:2291, ../includes/Elements/Woo_Cross_Sells.php:367, ../includes/Elements/Woo_Cross_Sells.php:884, ../includes/Traits/Extender.php:5208
msgid "Description"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:113
msgid "Sample chart description"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:130, ../includes/Elements/Fancy_Chart.php:1035
msgid "Chart Style"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:134
msgid "Bar"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:135, ../includes/Elements/Woo_Thank_You.php:1271
msgid "Area"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:136, ../includes/Traits/Extender.php:3217
msgid "Line"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:137
msgid "Radar"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:138
msgid "Pie"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:139
msgid "Donut"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:140
msgid "Polar Area"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:148
msgid "Chart Orientation"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:165, ../includes/Elements/Fancy_Chart.php:1372, ../includes/Elements/Google_Map.php:1387
msgid "Stroke Style"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:169
msgid "Smooth"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:170
msgid "Straight"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:171
msgid "Step line"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:182
msgid "Fill Type"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:186
msgid "Gradient"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:188
msgid "Pattern"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:196
msgid "Stacked"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:211
msgid "Show Central Labels"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:226
msgid "Show Total"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:242
msgid "Show Always Total"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:259
msgid "Advanced Settings"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:268
msgid "Toolbar"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:280
msgid "Tooltip Enable"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:292, ../includes/Elements/Fancy_Chart.php:802, ../includes/Elements/Fancy_Chart.php:804
msgid "Data Label"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:304
msgid "Data Position"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:318
msgid "Show Legend"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:330
msgid "Legend Position"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:345
msgid "Data Prefix"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:347
msgid "$"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:357
msgid "Data Suffix"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:359
msgid "thousands"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:374
msgid "Data"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:381
msgid "Data Source"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:385, ../includes/Elements/Google_Map.php:91
msgid "Static"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:386, ../includes/Elements/Figma_To_Elementor.php:89
msgid "JSON"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:387
msgid "CSV"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:388
msgid "Google Sheets"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:397
msgid "You will have to <strong>copy/paste</strong> the content from your <strong>.CSV</strong> file to show your data."
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:409
msgid "You will have to <strong>copy/paste</strong> the content from your <strong>.JSON</strong> file to show your data."
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:424
msgid "JSON Datasets"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:444, ../includes/Elements/Fancy_Chart.php:498
msgid "Insert JSON data here"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:486
msgid "JSON Dataset"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:546
msgid "CSV Datasets"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:555, ../includes/Elements/Fancy_Chart.php:596
msgid "Insert CSV data here"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:588
msgid "CSV Dataset"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:628, ../includes/Traits/Extender.php:1605
msgid "API Key"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:630
msgid "Insert API Key Here"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:641, ../includes/Traits/Extender.php:1619
msgid "Sheet ID"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:643
msgid "Insert Sheet ID"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:654, ../includes/Traits/Extender.php:1633
msgid "Table Range"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:656
msgid "Type Range Name"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:657
msgid "Sheet1"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:668, ../includes/Elements/Instagram_Feed.php:100, ../includes/Elements/Twitter_Feed_Carousel.php:196
msgid "Data Cache Time"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:672, ../includes/Elements/Instagram_Feed.php:104, ../includes/Elements/Twitter_Feed_Carousel.php:200
msgid "Cache expiration time (Minutes)"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:684, ../includes/Elements/Fancy_Chart.php:686
msgid "Category Title"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:708, ../includes/Elements/Fancy_Chart.php:765, ../includes/Elements/Fancy_Chart.php:858, ../includes/Elements/Flip_Carousel.php:338, ../includes/Elements/Post_List.php:863, ../includes/Elements/Stacked_Cards.php:529
msgid ""
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:712
msgid "Category 1"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:713
msgid "Category 2"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:714
msgid "Category 3"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:715
msgid "Category 4"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:732, ../includes/Elements/Fancy_Chart.php:734, ../includes/Elements/Fancy_Chart.php:814, ../includes/Elements/Fancy_Chart.php:816, ../includes/Elements/Woo_Thank_You.php:3241, ../includes/Extensions/Conditional_Display.php:1096, ../includes/Extensions/Conditional_Display.php:1611
msgid "Value"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:752, ../includes/Elements/Fancy_Chart.php:845
msgid "Datasets"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:770
msgid "44"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:774
msgid "76"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:778
msgid "35"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:782
msgid "13"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:827
msgid ""
"\n"
"Group data values by Comma ( , )\n"
"Example: 14, 25, 35, 9, 55"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:863
msgid "Dataset 1"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:864
msgid "44, 75, 35, 13"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:868
msgid "Dataset 2"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:869
msgid "55, 85, 41, 101"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:873
msgid "Dataset 3"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:874
msgid "57, 90, 36, 72"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:878
msgid "Dataset 4"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:879
msgid "45, 26, 12, 60"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:899
msgid "Header Style"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1051
msgid "Border Radious"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1114, ../includes/Elements/Fancy_Chart.php:1750
msgid "OffsetX"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1132, ../includes/Elements/Fancy_Chart.php:1768
msgid "OffsetY"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1154
msgid "Data Labels"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1174
msgid "Font Size (PX)"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1198
msgid "Bar Style"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1209
msgid "Bar Width"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1236, ../includes/Elements/Fancy_Chart.php:1244, ../includes/Elements/Lightbox.php:788, ../includes/Elements/Lightbox.php:795, ../includes/Elements/Lightbox.php:1193, ../includes/Elements/Lightbox.php:1562, ../includes/Elements/Offcanvas.php:1183, ../includes/Elements/Post_Block.php:1377, ../includes/Extensions/EAEL_Tooltip_Section.php:109, ../includes/Skins/Skin_Default.php:117, ../includes/Skins/Skin_Five.php:135, ../includes/Skins/Skin_Four.php:117, ../includes/Skins/Skin_One.php:117, ../includes/Skins/Skin_Seven.php:117, ../includes/Skins/Skin_Six.php:117, ../includes/Skins/Skin_Three.php:135, ../includes/Skins/Skin_Two.php:134
msgid "Animation"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1256, ../includes/Elements/Woo_Product_Slider.php:612
msgid "Speed"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1277, ../includes/Elements/Lightbox.php:626, ../includes/Extensions/Smooth_Animation.php:727
msgid "Delay"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1301
msgid "Grid Style"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1312
msgid "Show Grid"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1333, ../includes/Elements/Fancy_Chart.php:1422
msgid "Dash Stroke"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1345
msgid "Y-Axis Line"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1357
msgid "X-Axis Line"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1383
msgid "Stroke Show"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1395, ../includes/Elements/Fancy_Chart.php:1804, ../includes/Traits/Extender.php:257
msgid "Stroke Color"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1437
msgid "Tooltip Style"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1487
msgid "X-Axis Settings"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1498, ../includes/Elements/Fancy_Chart.php:1560, ../includes/Elements/Woo_Cross_Sells.php:384
msgid "Labels"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1549
msgid "Y-Axis Settings"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1611
msgid "Legend Settings"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1630, ../includes/Elements/Testimonial_Slider.php:1404, ../includes/Elements/Twitter_Feed_Carousel.php:1475
msgid "Font Size"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1660
msgid "Horizontal Alignment"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1687, ../includes/Elements/Sphere_Photo_Viewer.php:347, ../includes/Elements/Sphere_Photo_Viewer.php:856
msgid "Markers"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1732, ../includes/Elements/Image_Scroller.php:185, ../includes/Elements/Interactive_Card.php:1071, ../includes/Elements/Interactive_Card.php:1199, ../includes/Elements/Lightbox.php:1124, ../includes/Elements/Lightbox.php:1246, ../includes/Elements/Post_Block.php:1008, ../includes/Elements/Team_Member_Carousel.php:974, ../includes/Elements/Woo_Collections.php:592
msgid "Radius"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:1786, ../includes/Traits/Extender.php:236
msgid "Stroke Width"
msgstr ""

#: ../includes/Elements/Fancy_Chart.php:2763
msgid "Please insert correct API / Sheet ID"
msgstr ""

#: ../includes/Elements/Figma_To_Elementor.php:28
msgid "Figma to Elementor Converter"
msgstr ""

#: ../includes/Elements/Figma_To_Elementor.php:84
msgid "Import From"
msgstr ""

#: ../includes/Elements/Figma_To_Elementor.php:93
msgid "File"
msgstr ""

#: ../includes/Elements/Figma_To_Elementor.php:105
msgid "Paste your Figma JSON here"
msgstr ""

#: ../includes/Elements/Figma_To_Elementor.php:117, ../includes/Elements/Figma_To_Elementor.php:145
msgid "Import"
msgstr ""

#: ../includes/Elements/Figma_To_Elementor.php:128
msgid "Upload JSON File"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:23
msgid "Flip Carousel"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:71
msgid "Filp Carousel Settings"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:78
msgid "Carousel Type"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:83
msgid "Cover-Flow"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:84, ../includes/Elements/Post_Carousel.php:615, ../includes/Elements/Post_Carousel.php:1110, ../includes/Elements/Testimonial_Slider.php:98
msgid "Carousel"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:85
msgid "Flat"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:86
msgid "Wheel"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:94
msgid "Fade In (ms)"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:107
msgid "Item Starts From Center?"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:122
msgid "Enter Starts Number"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:139
msgid "Loop"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:151, ../includes/Elements/Logo_Carousel.php:509, ../includes/Elements/Post_Carousel.php:720, ../includes/Elements/Team_Member_Carousel.php:770, ../includes/Elements/Testimonial_Slider.php:208, ../includes/Elements/Twitter_Feed_Carousel.php:438, ../includes/Elements/Woo_Product_Slider.php:631
msgid "Autoplay"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:166
msgid "Autoplay Timeout (ms)"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:182, ../includes/Elements/Logo_Carousel.php:571, ../includes/Elements/Post_Carousel.php:769, ../includes/Elements/Team_Member_Carousel.php:819, ../includes/Elements/Testimonial_Slider.php:256, ../includes/Elements/Twitter_Feed_Carousel.php:470, ../includes/Elements/Woo_Product_Slider.php:662
msgid "Pause On Hover"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:194
msgid "On Click Play?"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:206
msgid "On Scroll Wheel Play?"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:218
msgid "On Touch Play?"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:230
msgid "Carousel Navigator"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:242
msgid "Slide Spacing"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:265
msgid "Flip Carousel Slides"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:272
msgid "Content Appearance"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:276
msgid "No Content"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:277
msgid "On Hover"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:278
msgid "Always Show"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:286
msgid "Enable Overlay"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:289, ../includes/Elements/Flip_Carousel.php:304, ../includes/Elements/LD_Course_List.php:198, ../includes/Elements/Post_Carousel.php:900, ../includes/Elements/Post_Carousel.php:958
msgid "Enable"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:301
msgid "Content on active only"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:318, ../includes/Elements/Logo_Carousel.php:270, ../includes/Elements/Logo_Carousel.php:434, ../includes/Elements/Offcanvas.php:397, ../includes/Elements/Post_Carousel.php:627, ../includes/Elements/Testimonial_Slider.php:110, ../includes/Elements/Twitter_Feed_Carousel.php:366
msgid "Slide"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:332
msgid "Slide Text"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:350
msgid "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Optio, neque qui velit. Magni dolorum quidem ipsam eligendi, totam, facilis laudantium cum accusamus ullam voluptatibus commodi numquam, error, est. Ea, consequatur."
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:351
msgid "Type your description here"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:358
msgid "Enable Slide Link"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:370
msgid "Slide Link"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:414
msgid "Flip Carousel Style"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:501
msgid "Navigator Style"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:509
msgid "Navigator"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:524
msgid "Previous Icon"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:543
msgid "Next Icon"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:592
msgid "Background Size"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:761, ../includes/Elements/Google_Map.php:1271, ../includes/Elements/Instagram_Feed.php:269
msgid "Overlay Style"
msgstr ""

#: ../includes/Elements/Flip_Carousel.php:786
msgid "Footer Content"
msgstr ""

#: ../includes/Elements/Google_Map.php:26
msgid "Advanced Google Map"
msgstr ""

#: ../includes/Elements/Google_Map.php:78, ../includes/Elements/Instagram_Feed.php:232
msgid "General Settings"
msgstr ""

#: ../includes/Elements/Google_Map.php:84
msgid "Google Map Type"
msgstr ""

#: ../includes/Elements/Google_Map.php:89, ../includes/Elements/Instagram_Feed.php:274
msgid "Basic"
msgstr ""

#: ../includes/Elements/Google_Map.php:90
msgid "Multiple Marker"
msgstr ""

#: ../includes/Elements/Google_Map.php:92
msgid "Polyline"
msgstr ""

#: ../includes/Elements/Google_Map.php:93
msgid "Polygon"
msgstr ""

#: ../includes/Elements/Google_Map.php:94, ../includes/Elements/Image_Comparison.php:332, ../includes/Elements/Instagram_Feed.php:244, ../includes/Elements/Lightbox.php:984, ../includes/Elements/Offcanvas.php:1308, ../includes/Elements/Post_Carousel.php:140, ../includes/Elements/Team_Member_Carousel.php:1111, ../includes/Traits/Filterable_Gallery_Extender.php:498, ../includes/Template/Post-Block/overlay.php:3
msgid "Overlay"
msgstr ""

#: ../includes/Elements/Google_Map.php:95
msgid "With Routes"
msgstr ""

#: ../includes/Elements/Google_Map.php:96
msgid "Panorama"
msgstr ""

#: ../includes/Elements/Google_Map.php:104
msgid "Address Type"
msgstr ""

#: ../includes/Elements/Google_Map.php:108
msgid "Address"
msgstr ""

#: ../includes/Elements/Google_Map.php:112
msgid "Coordinates"
msgstr ""

#: ../includes/Elements/Google_Map.php:125
msgid "Geo Address"
msgstr ""

#: ../includes/Elements/Google_Map.php:129
msgid "Marina Bay, Singapore"
msgstr ""

#: ../includes/Elements/Google_Map.php:142, ../includes/Elements/Google_Map.php:175, ../includes/Elements/Google_Map.php:253, ../includes/Elements/Google_Map.php:284, ../includes/Elements/Google_Map.php:443, ../includes/Elements/Google_Map.php:630, ../includes/Elements/Google_Map.php:707, ../includes/Elements/Google_Map.php:739, ../includes/Elements/Google_Map.php:865
msgid "Latitude"
msgstr ""

#: ../includes/Elements/Google_Map.php:145, ../includes/Elements/Google_Map.php:178, ../includes/Elements/Google_Map.php:256, ../includes/Elements/Google_Map.php:287, ../includes/Elements/Google_Map.php:446, ../includes/Elements/Google_Map.php:868
msgid "28.948790"
msgstr ""

#: ../includes/Elements/Google_Map.php:158, ../includes/Elements/Google_Map.php:190, ../includes/Elements/Google_Map.php:268, ../includes/Elements/Google_Map.php:299, ../includes/Elements/Google_Map.php:456, ../includes/Elements/Google_Map.php:642, ../includes/Elements/Google_Map.php:719, ../includes/Elements/Google_Map.php:751, ../includes/Elements/Google_Map.php:880
msgid "Longitude"
msgstr ""

#: ../includes/Elements/Google_Map.php:161, ../includes/Elements/Google_Map.php:193, ../includes/Elements/Google_Map.php:271, ../includes/Elements/Google_Map.php:302, ../includes/Elements/Google_Map.php:459
msgid "-81.298843"
msgstr ""

#: ../includes/Elements/Google_Map.php:205
msgid "Map Image Resolution"
msgstr ""

#: ../includes/Elements/Google_Map.php:216
msgid "Static Image Width"
msgstr ""

#: ../includes/Elements/Google_Map.php:234
msgid "Static Image Height"
msgstr ""

#: ../includes/Elements/Google_Map.php:314, ../includes/Elements/Team_Member_Carousel.php:506
msgid "Overlay Content"
msgstr ""

#: ../includes/Elements/Google_Map.php:317
msgid "Add your content here"
msgstr ""

#: ../includes/Elements/Google_Map.php:330, ../includes/Elements/Google_Map.php:431
msgid "Map Marker Settings"
msgstr ""

#: ../includes/Elements/Google_Map.php:345
msgid "Google Map Title"
msgstr ""

#: ../includes/Elements/Google_Map.php:360
msgid "Google map content"
msgstr ""

#: ../includes/Elements/Google_Map.php:366
msgid "Custom Marker Icon"
msgstr ""

#: ../includes/Elements/Google_Map.php:377
msgid "Marker Icon"
msgstr ""

#: ../includes/Elements/Google_Map.php:390
msgid "Marker Width"
msgstr ""

#: ../includes/Elements/Google_Map.php:408
msgid "Marker Height"
msgstr ""

#: ../includes/Elements/Google_Map.php:472
msgid "Marker Title"
msgstr ""

#: ../includes/Elements/Google_Map.php:485
msgid "Marker Content. You can put html here."
msgstr ""

#: ../includes/Elements/Google_Map.php:495
msgid "Default Icon Color"
msgstr ""

#: ../includes/Elements/Google_Map.php:496
msgid "(Works only on Static mode)"
msgstr ""

#: ../includes/Elements/Google_Map.php:505
msgid "Use Custom Icon"
msgstr ""

#: ../includes/Elements/Google_Map.php:517, ../includes/Elements/Team_Member_Carousel.php:446
msgid "Custom Icon"
msgstr ""

#: ../includes/Elements/Google_Map.php:531, ../includes/Traits/Extender.php:4203
msgid "Icon Width"
msgstr ""

#: ../includes/Elements/Google_Map.php:533, ../includes/Elements/Google_Map.php:545
msgid "32"
msgstr ""

#: ../includes/Elements/Google_Map.php:543, ../includes/Traits/Extender.php:4232
msgid "Icon Height"
msgstr ""

#: ../includes/Elements/Google_Map.php:558
msgid "Map Marker 1"
msgstr ""

#: ../includes/Elements/Google_Map.php:568
msgid "Enable Marker Search"
msgstr ""

#: ../includes/Elements/Google_Map.php:572
msgid "Search among listed markers"
msgstr ""

#: ../includes/Elements/Google_Map.php:586
msgid "Search Marker..."
msgstr ""

#: ../includes/Elements/Google_Map.php:605
msgid "Coordinate Settings"
msgstr ""

#: ../includes/Elements/Google_Map.php:620, ../includes/Elements/Stacked_Cards.php:452
msgid "#"
msgstr ""

#: ../includes/Elements/Google_Map.php:658
msgid "#1"
msgstr ""

#: ../includes/Elements/Google_Map.php:663
msgid "#2"
msgstr ""

#: ../includes/Elements/Google_Map.php:668
msgid "#3"
msgstr ""

#: ../includes/Elements/Google_Map.php:673
msgid "#4"
msgstr ""

#: ../includes/Elements/Google_Map.php:690
msgid "Routes Coordinate Settings"
msgstr ""

#: ../includes/Elements/Google_Map.php:699
msgid "Origin"
msgstr ""

#: ../includes/Elements/Google_Map.php:710
msgid "-12.044012922866312"
msgstr ""

#: ../includes/Elements/Google_Map.php:722
msgid "-77.02470665341184"
msgstr ""

#: ../includes/Elements/Google_Map.php:731
msgid "Destination"
msgstr ""

#: ../includes/Elements/Google_Map.php:742
msgid "-12.090814532191756"
msgstr ""

#: ../includes/Elements/Google_Map.php:754
msgid "-77.02271108990476"
msgstr ""

#: ../includes/Elements/Google_Map.php:763
msgid "Travel Mode"
msgstr ""

#: ../includes/Elements/Google_Map.php:768
msgid "Walking"
msgstr ""

#: ../includes/Elements/Google_Map.php:769
msgid "Bicycling"
msgstr ""

#: ../includes/Elements/Google_Map.php:770
msgid "Driving"
msgstr ""

#: ../includes/Elements/Google_Map.php:779
msgid "Map Controls"
msgstr ""

#: ../includes/Elements/Google_Map.php:785
msgid "Zoom Level"
msgstr ""

#: ../includes/Elements/Google_Map.php:788
msgid "14"
msgstr ""

#: ../includes/Elements/Google_Map.php:794
msgid "Street View Controls"
msgstr ""

#: ../includes/Elements/Google_Map.php:805
msgid "Map Type Control"
msgstr ""

#: ../includes/Elements/Google_Map.php:816
msgid "Zoom Control"
msgstr ""

#: ../includes/Elements/Google_Map.php:827
msgid "Fullscreen Control"
msgstr ""

#: ../includes/Elements/Google_Map.php:838
msgid "Scroll Wheel Zoom"
msgstr ""

#: ../includes/Elements/Google_Map.php:849
msgid "Center Point"
msgstr ""

#: ../includes/Elements/Google_Map.php:853, ../includes/Elements/Google_Map.php:956, ../includes/Elements/Image_Hot_Spots.php:378, ../includes/Elements/LD_Course_List.php:129, ../includes/Elements/LD_Course_List.php:776, ../includes/Elements/Logo_Carousel.php:314, ../includes/Elements/Mailchimp.php:874, ../includes/Elements/Post_Carousel.php:114, ../includes/Elements/Stacked_Cards.php:823, ../includes/Elements/Testimonial_Slider.php:335, ../includes/Elements/Testimonial_Slider.php:408, ../includes/Elements/Testimonial_Slider.php:716, ../includes/Elements/Woo_Account_Dashboard.php:196, ../includes/Elements/Woo_Thank_You.php:1298, ../includes/Extensions/EAEL_Particle_Section.php:130, ../includes/Extensions/Smooth_Animation.php:587, ../includes/Extensions/Smooth_Animation.php:1071, ../includes/Skins/Skin_Default.php:29, ../includes/Skins/Skin_Default.php:437, ../includes/Skins/Skin_Default.php:758, ../includes/Skins/Skin_Five.php:511, ../includes/Skins/Skin_Five.php:855, ../includes/Skins/Skin_Four.php:459, ../includes/Skins/Skin_Four.php:800, ../includes/Skins/Skin_One.php:507, ../includes/Skins/Skin_One.php:835, ../includes/Skins/Skin_Seven.php:459, ../includes/Skins/Skin_Seven.php:800, ../includes/Skins/Skin_Six.php:460, ../includes/Skins/Skin_Six.php:804, ../includes/Skins/Skin_Three.php:479, ../includes/Skins/Skin_Three.php:822, ../includes/Skins/Skin_Two.php:478, ../includes/Skins/Skin_Two.php:821, ../includes/Traits/Extender.php:497, ../includes/Traits/Extender.php:3659, ../includes/Traits/Extender.php:3858, ../includes/Traits/Extender.php:4040, ../includes/Traits/Extender.php:4197, ../includes/Traits/Extender.php:4337, ../includes/Traits/Extender.php:4539, ../includes/Traits/Extender.php:4579, ../includes/Template/Content-Timeline/default.php:3, ../includes/Template/Dynamic-Filterable-Gallery/default.php:4, ../includes/Template/Post-Block/default.php:3, ../includes/Template/Post-Carousel/default.php:3, ../includes/Template/Post-List/default.php:3
msgid "Default"
msgstr ""

#: ../includes/Elements/Google_Map.php:854
msgid "Auto Center"
msgstr ""

#: ../includes/Elements/Google_Map.php:855
msgid "Custom Point"
msgstr ""

#: ../includes/Elements/Google_Map.php:883
msgid "90.403947"
msgstr ""

#: ../includes/Elements/Google_Map.php:900
msgid "Map Theme"
msgstr ""

#: ../includes/Elements/Google_Map.php:909, ../includes/Extensions/EAEL_Particle_Section.php:104
msgid "Theme Source"
msgstr ""

#: ../includes/Elements/Google_Map.php:913
msgid "Google Standard"
msgstr ""

#: ../includes/Elements/Google_Map.php:917
msgid "Snazzy Maps"
msgstr ""

#: ../includes/Elements/Google_Map.php:931
msgid "Google Themes"
msgstr ""

#: ../includes/Elements/Google_Map.php:935, ../includes/Elements/Instagram_Feed.php:275, ../includes/Elements/Lightbox.php:159, ../includes/Traits/Extender.php:3877
msgid "Standard"
msgstr ""

#: ../includes/Elements/Google_Map.php:936
msgid "Silver"
msgstr ""

#: ../includes/Elements/Google_Map.php:937, ../includes/Elements/Multicolumn_Pricing_Table.php:99
msgid "Retro"
msgstr ""

#: ../includes/Elements/Google_Map.php:938, ../includes/Elements/Google_Map.php:960
msgid "Dark"
msgstr ""

#: ../includes/Elements/Google_Map.php:939
msgid "Night"
msgstr ""

#: ../includes/Elements/Google_Map.php:940
msgid "Aubergine"
msgstr ""

#: ../includes/Elements/Google_Map.php:942, ../includes/Elements/Google_Map.php:967, ../includes/Elements/Google_Map.php:977
msgid "Click here"
msgstr ""

#: ../includes/Elements/Google_Map.php:942
msgid "to generate your own theme and use JSON within Custom style field."
msgstr ""

#: ../includes/Elements/Google_Map.php:951
msgid "SnazzyMaps Themes"
msgstr ""

#: ../includes/Elements/Google_Map.php:957, ../includes/Elements/Instagram_Feed.php:273
msgid "Simple"
msgstr ""

#: ../includes/Elements/Google_Map.php:958
msgid "Colorful"
msgstr ""

#: ../includes/Elements/Google_Map.php:959
msgid "Complex"
msgstr ""

#: ../includes/Elements/Google_Map.php:961
msgid "Greyscale"
msgstr ""

#: ../includes/Elements/Google_Map.php:962, ../includes/Elements/Logo_Carousel.php:316
msgid "Light"
msgstr ""

#: ../includes/Elements/Google_Map.php:963
msgid "Monochrome"
msgstr ""

#: ../includes/Elements/Google_Map.php:964
msgid "No Labels"
msgstr ""

#: ../includes/Elements/Google_Map.php:965
msgid "Two Tone"
msgstr ""

#: ../includes/Elements/Google_Map.php:967
msgid "to explore more themes and use JSON within custom style field."
msgstr ""

#: ../includes/Elements/Google_Map.php:976, ../includes/Extensions/EAEL_Particle_Section.php:147
msgid "Custom Style"
msgstr ""

#: ../includes/Elements/Google_Map.php:977
msgid "to get JSON style code to style your map"
msgstr ""

#: ../includes/Elements/Google_Map.php:1000, ../includes/Elements/Interactive_Card.php:777, ../includes/Elements/Testimonial_Slider.php:831, ../includes/Extensions/EAEL_Tooltip_Section.php:380
msgid "Max Width"
msgstr ""

#: ../includes/Elements/Google_Map.php:1027
msgid "Max Height"
msgstr ""

#: ../includes/Elements/Google_Map.php:1070
msgid "Search Input"
msgstr ""

#: ../includes/Elements/Google_Map.php:1113, ../includes/Traits/Extender.php:3019
msgid "Position X"
msgstr ""

#: ../includes/Elements/Google_Map.php:1142, ../includes/Traits/Extender.php:3045
msgid "Position Y"
msgstr ""

#: ../includes/Elements/Google_Map.php:1405, ../includes/Elements/Logo_Carousel.php:745, ../includes/Elements/Logo_Carousel.php:803, ../includes/Elements/Offcanvas.php:1328, ../includes/Elements/Stacked_Cards.php:651, ../includes/Elements/Stacked_Cards.php:676, ../includes/Elements/Stacked_Cards.php:709, ../includes/Elements/Team_Member_Carousel.php:1163, ../includes/Extensions/EAEL_Particle_Section.php:60, ../includes/Extensions/Smooth_Animation.php:187
msgid "Opacity"
msgstr ""

#: ../includes/Elements/Google_Map.php:1423, ../includes/Elements/Lightbox.php:1662
msgid "Weight"
msgstr ""

#: ../includes/Elements/Google_Map.php:1441, ../includes/Elements/LD_Course_List.php:2132, ../includes/Traits/Extender.php:226, ../includes/Traits/Filterable_Gallery_Extender.php:227
msgid "Fill Color"
msgstr ""

#: ../includes/Elements/Google_Map.php:1452
msgid "Fill Opacity"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:26
msgid "Image Comparison"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:70
msgid "Images"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:77
msgid "Label Before"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:84
msgid "Input before image label"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:94
msgid "Choose Before Image"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:111
msgid "Before Image ALT Tag"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:116, ../includes/Elements/Image_Comparison.php:165, ../includes/Elements/Interactive_Promo.php:102
msgid "Enter alter tag for the image"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:117, ../includes/Elements/Image_Comparison.php:166, ../includes/Elements/Interactive_Promo.php:103
msgid "Input image alter tag here"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:127
msgid "Label After"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:134
msgid "Input after image label"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:143
msgid "Choose After Image"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:160
msgid "After Image ALT Tag"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:194
msgid "Original Image Visibility"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:205
msgid "Orientation"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:218
msgid "Wants Overlay ?"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:220, ../includes/Elements/Image_Comparison.php:231, ../includes/Elements/Image_Comparison.php:242, ../includes/Elements/Image_Comparison.php:263, ../includes/Elements/Interactive_Promo.php:191, ../includes/Elements/Lightbox.php:993, ../includes/Elements/Static_Product.php:130, ../includes/Elements/Static_Product.php:140, ../includes/Elements/Static_Product.php:472, ../includes/Elements/Twitter_Feed_Carousel.php:257, ../includes/Elements/Twitter_Feed_Carousel.php:278, ../includes/Elements/Twitter_Feed_Carousel.php:306, ../includes/Elements/Twitter_Feed_Carousel.php:318, ../includes/Elements/Twitter_Feed_Carousel.php:330, ../includes/Elements/Twitter_Feed_Carousel.php:342
msgid "yes"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:221, ../includes/Elements/Image_Comparison.php:232, ../includes/Elements/Image_Comparison.php:243, ../includes/Elements/Image_Comparison.php:264, ../includes/Elements/Interactive_Promo.php:192, ../includes/Elements/Lightbox.php:994, ../includes/Elements/Static_Product.php:131, ../includes/Elements/Static_Product.php:141, ../includes/Elements/Static_Product.php:473, ../includes/Elements/Twitter_Feed_Carousel.php:258, ../includes/Elements/Twitter_Feed_Carousel.php:279, ../includes/Elements/Twitter_Feed_Carousel.php:307, ../includes/Elements/Twitter_Feed_Carousel.php:319, ../includes/Elements/Twitter_Feed_Carousel.php:331, ../includes/Elements/Twitter_Feed_Carousel.php:343
msgid "no"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:229
msgid "Move Slider On Hover"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:240
msgid "Move Slider On Click"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:253
msgid "Image Container Styles"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:261, ../includes/Elements/Interactive_Promo.php:189, ../includes/Elements/Static_Product.php:470
msgid "Set max width for the container?"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:272
msgid "Container Max Width"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:358
msgid "Handle"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:556, ../includes/Elements/Price_Menu.php:914, ../includes/Elements/Toggle.php:556, ../includes/Elements/Twitter_Feed_Carousel.php:555, ../includes/Elements/Woo_Collections.php:416, ../includes/Elements/Woo_Product_Slider.php:1108
msgid "Middle"
msgstr ""

#: ../includes/Elements/Image_Comparison.php:602
msgid "Align"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:38
msgid "Image Hotspots"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:125, ../includes/Elements/Multicolumn_Pricing_Table.php:1395, ../includes/Elements/Price_Menu.php:814, ../includes/Elements/Testimonial_Slider.php:1833, ../includes/Elements/Woo_Product_Slider.php:2807, ../includes/Extensions/EAEL_Parallax_Section.php:201
msgid "Image Size"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:138
msgid "Hotspots"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:151, ../includes/Elements/Lightbox.php:290, ../includes/Elements/Lightbox.php:420, ../includes/Elements/Post_Carousel.php:1941, ../includes/Elements/Testimonial_Slider.php:1342, ../includes/Elements/Woo_Account_Dashboard.php:192, ../includes/Extensions/Conditional_Display.php:124, ../includes/Extensions/Conditional_Display.php:2015, ../includes/Extensions/EAEL_Parallax_Section.php:54, ../includes/Traits/Extender.php:3874, ../includes/Traits/Extender.php:5255, ../includes/Traits/Extender.php:5328
msgid "Type"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:157
msgid "Blank"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:203
msgid "https://your-link.com"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:213
msgid "Open Link in New Tab"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:229
msgid "Left Position"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:247
msgid "Top Position"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:264, ../includes/Elements/Image_Hot_Spots.php:269, ../includes/Elements/Image_Hot_Spots.php:900, ../includes/Elements/One_Page_Navigation.php:194, ../includes/Elements/One_Page_Navigation.php:645, ../includes/Elements/Sphere_Photo_Viewer.php:369
msgid "Tooltip"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:281
msgid "Tooltip Position"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:285
msgid "Global"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:290, ../includes/Elements/Image_Hot_Spots.php:397
msgid "Top Left"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:291, ../includes/Elements/Image_Hot_Spots.php:398
msgid "Top Right"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:292, ../includes/Elements/Image_Hot_Spots.php:399
msgid "Bottom Left"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:293, ../includes/Elements/Image_Hot_Spots.php:400
msgid "Bottom Right"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:304, ../includes/Elements/Image_Hot_Spots.php:306, ../includes/Elements/Logo_Carousel.php:205
msgid "Tooltip Content"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:324
msgid "Hotspot #1"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:338
msgid "Glow Effect"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:355
msgid "Tooltip Settings"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:362
msgid "Show Arrow"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:379
msgid "Tiny"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:380, ../includes/Elements/LD_Course_List.php:863, ../includes/Elements/Lightbox.php:1365, ../includes/Elements/Offcanvas.php:989, ../includes/Extensions/EAEL_Tooltip_Section.php:236, ../includes/Traits/Extender.php:3900
msgid "Small"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:381, ../includes/Elements/LD_Course_List.php:865, ../includes/Elements/Lightbox.php:1367, ../includes/Elements/Offcanvas.php:991, ../includes/Extensions/EAEL_Tooltip_Section.php:238, ../includes/Traits/Extender.php:3898
msgid "Large"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:389
msgid "Global Position"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:408
msgid "Animation In"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:413, ../includes/Elements/Image_Hot_Spots.php:493, ../includes/Extensions/Smooth_Animation.php:591
msgid "Bounce"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:414, ../includes/Elements/Image_Hot_Spots.php:494
msgid "Flash"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:415, ../includes/Elements/Image_Hot_Spots.php:495
msgid "Pulse"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:416, ../includes/Elements/Image_Hot_Spots.php:496
msgid "rubberBand"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:417, ../includes/Elements/Image_Hot_Spots.php:497
msgid "ShakeX"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:418, ../includes/Elements/Image_Hot_Spots.php:498
msgid "ShakeY"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:419, ../includes/Elements/Image_Hot_Spots.php:499, ../includes/Elements/Logo_Carousel.php:269
msgid "Swing"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:420, ../includes/Elements/Image_Hot_Spots.php:500
msgid "Tada"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:421, ../includes/Elements/Image_Hot_Spots.php:501
msgid "Wobble"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:422, ../includes/Elements/Image_Hot_Spots.php:502
msgid "bounceIn"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:423, ../includes/Elements/Image_Hot_Spots.php:503
msgid "bounceInDown"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:424, ../includes/Elements/Image_Hot_Spots.php:504
msgid "bounceInLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:425, ../includes/Elements/Image_Hot_Spots.php:505
msgid "bounceInRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:426, ../includes/Elements/Image_Hot_Spots.php:506
msgid "bounceInUp"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:427, ../includes/Elements/Image_Hot_Spots.php:507
msgid "bounceOut"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:428, ../includes/Elements/Image_Hot_Spots.php:508
msgid "bounceOutDown"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:429, ../includes/Elements/Image_Hot_Spots.php:509
msgid "bounceOutLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:430, ../includes/Elements/Image_Hot_Spots.php:510
msgid "bounceOutRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:431, ../includes/Elements/Image_Hot_Spots.php:511
msgid "bounceOutUp"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:432, ../includes/Elements/Image_Hot_Spots.php:512
msgid "fadeIn"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:433, ../includes/Elements/Image_Hot_Spots.php:513
msgid "fadeInDown"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:434, ../includes/Elements/Image_Hot_Spots.php:514
msgid "fadeInDownBig"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:435, ../includes/Elements/Image_Hot_Spots.php:515
msgid "fadeInLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:436, ../includes/Elements/Image_Hot_Spots.php:516
msgid "fadeInLeftBig"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:437, ../includes/Elements/Image_Hot_Spots.php:517
msgid "fadeInRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:438, ../includes/Elements/Image_Hot_Spots.php:518
msgid "fadeInRightBig"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:439, ../includes/Elements/Image_Hot_Spots.php:519
msgid "fadeInUp"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:440, ../includes/Elements/Image_Hot_Spots.php:520
msgid "fadeInUpBig"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:441, ../includes/Elements/Image_Hot_Spots.php:521
msgid "fadeOut"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:442, ../includes/Elements/Image_Hot_Spots.php:522
msgid "fadeOutDown"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:443, ../includes/Elements/Image_Hot_Spots.php:523
msgid "fadeOutDownBig"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:444, ../includes/Elements/Image_Hot_Spots.php:524
msgid "fadeOutLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:445, ../includes/Elements/Image_Hot_Spots.php:525
msgid "fadeOutLeftBig"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:446, ../includes/Elements/Image_Hot_Spots.php:526
msgid "fadeOutRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:447, ../includes/Elements/Image_Hot_Spots.php:527
msgid "fadeOutRightBig"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:448, ../includes/Elements/Image_Hot_Spots.php:528
msgid "fadeOutUp"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:449, ../includes/Elements/Image_Hot_Spots.php:529
msgid "fadeOutUpBig"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:450, ../includes/Elements/Image_Hot_Spots.php:530
msgid "flipInX"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:451, ../includes/Elements/Image_Hot_Spots.php:531
msgid "flipInY"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:452, ../includes/Elements/Image_Hot_Spots.php:532
msgid "flipOutX"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:453, ../includes/Elements/Image_Hot_Spots.php:533
msgid "flipOutY"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:454, ../includes/Elements/Image_Hot_Spots.php:534
msgid "lightSpeedInRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:455, ../includes/Elements/Image_Hot_Spots.php:535
msgid "lightSpeedInLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:456, ../includes/Elements/Image_Hot_Spots.php:536
msgid "lightSpeedOutRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:457, ../includes/Elements/Image_Hot_Spots.php:537
msgid "lightSpeedOutLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:458, ../includes/Elements/Image_Hot_Spots.php:538
msgid "rotateIn"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:459, ../includes/Elements/Image_Hot_Spots.php:539
msgid "rotateInDownLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:460, ../includes/Elements/Image_Hot_Spots.php:540
msgid "rotateInDownRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:461, ../includes/Elements/Image_Hot_Spots.php:541
msgid "rotateInUpLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:462, ../includes/Elements/Image_Hot_Spots.php:542
msgid "rotateInUpRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:463, ../includes/Elements/Image_Hot_Spots.php:543
msgid "rotateOut"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:464, ../includes/Elements/Image_Hot_Spots.php:544
msgid "rotateOutDownLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:465, ../includes/Elements/Image_Hot_Spots.php:545
msgid "rotateOutDownRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:466, ../includes/Elements/Image_Hot_Spots.php:546
msgid "rotateOutUpLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:467, ../includes/Elements/Image_Hot_Spots.php:547
msgid "rotateOutUpRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:468, ../includes/Elements/Image_Hot_Spots.php:548
msgid "Hinge"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:469, ../includes/Elements/Image_Hot_Spots.php:549
msgid "rollIn"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:470, ../includes/Elements/Image_Hot_Spots.php:550
msgid "rollOut"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:471, ../includes/Elements/Image_Hot_Spots.php:551, ../includes/Elements/Sphere_Photo_Viewer.php:498
msgid "zoomIn"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:472, ../includes/Elements/Image_Hot_Spots.php:552
msgid "zoomInDown"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:473, ../includes/Elements/Image_Hot_Spots.php:553
msgid "zoomInLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:474, ../includes/Elements/Image_Hot_Spots.php:554
msgid "zoomInRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:475, ../includes/Elements/Image_Hot_Spots.php:555
msgid "zoomInUp"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:476, ../includes/Elements/Image_Hot_Spots.php:556, ../includes/Elements/Sphere_Photo_Viewer.php:496
msgid "zoomOut"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:477, ../includes/Elements/Image_Hot_Spots.php:557
msgid "zoomOutDown"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:478, ../includes/Elements/Image_Hot_Spots.php:558
msgid "zoomOutLeft"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:479, ../includes/Elements/Image_Hot_Spots.php:559
msgid "zoomOutRight"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:480, ../includes/Elements/Image_Hot_Spots.php:560
msgid "zoomOutUp"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:488
msgid "Animation Out"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:568, ../includes/Elements/Multicolumn_Pricing_Table.php:206
msgid "Animation Speed"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:583
msgid "Animation Delay"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:672
msgid "Hotspot"
msgstr ""

#: ../includes/Elements/Image_Hot_Spots.php:689
msgid "Only Applicable for <br><strong>Content >> Hotspots >> Content >> Type:Text</strong>"
msgstr ""

#: ../includes/Elements/Image_Scroller.php:25
msgid "Image Scroller"
msgstr ""

#: ../includes/Elements/Image_Scroller.php:81, ../includes/Elements/Woo_Collections.php:146
msgid "Background Image"
msgstr ""

#: ../includes/Elements/Image_Scroller.php:95, ../includes/Elements/Stacked_Cards.php:1099
msgid "Container Height"
msgstr ""

#: ../includes/Elements/Image_Scroller.php:120
msgid "Scroll Direction"
msgstr ""

#: ../includes/Elements/Image_Scroller.php:134
msgid "Auto Scroll"
msgstr ""

#: ../includes/Elements/Image_Scroller.php:145
msgid "Scroll Duration"
msgstr ""

#: ../includes/Elements/Image_Scroller.php:197, ../includes/Elements/Interactive_Card.php:1084, ../includes/Elements/Interactive_Card.php:1212, ../includes/Elements/Logo_Carousel.php:318, ../includes/Elements/Logo_Carousel.php:764, ../includes/Elements/Logo_Carousel.php:822, ../includes/Elements/Post_Carousel.php:1796, ../includes/Elements/Post_Carousel.php:2427, ../includes/Elements/Static_Product.php:879, ../includes/Elements/Static_Product.php:915, ../includes/Elements/Testimonial_Slider.php:784, ../includes/Elements/Woo_Collections.php:317, ../includes/Elements/Woo_Product_Slider.php:2723, ../includes/Skins/Skin_Default.php:91, ../includes/Skins/Skin_Default.php:202, ../includes/Skins/Skin_Five.php:109, ../includes/Skins/Skin_Five.php:251, ../includes/Skins/Skin_Four.php:91, ../includes/Skins/Skin_Four.php:202, ../includes/Skins/Skin_One.php:91, ../includes/Skins/Skin_One.php:250, ../includes/Skins/Skin_Seven.php:91, ../includes/Skins/Skin_Seven.php:202, ../includes/Skins/Skin_Six.php:91, ../includes/Skins/Skin_Six.php:203, ../includes/Skins/Skin_Three.php:109, ../includes/Skins/Skin_Three.php:221, ../includes/Skins/Skin_Two.php:108, ../includes/Skins/Skin_Two.php:220
msgid "Shadow"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:27
msgid "Instagram Feed"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:79
msgid "Instagram Account Settings"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:86
msgid "Access Token"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:113
msgid "Feed Settings"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:120
msgid "Sort By"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:125
msgid "Most Recent"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:126
msgid "Least Recent"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:138
msgid "Max Visible Images"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:155
msgid "Max Caption Length"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:166
msgid "Force Square Image?"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:176, ../includes/Elements/Woo_Cross_Sells.php:208
msgid "Image Render Type"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:180, ../includes/Elements/Woo_Cross_Sells.php:213
msgid "Stretched"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:181, ../includes/Elements/Woo_Cross_Sells.php:214
msgid "Cropped"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:195
msgid "Image Dimension (px)"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:218
msgid "Filter By HashTags"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:253, ../includes/Elements/LD_Course_List.php:943, ../includes/Elements/Post_List.php:1951, ../includes/Elements/Stacked_Cards.php:566, ../includes/Elements/Twitter_Feed_Carousel.php:525
msgid "Card Style"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:257
msgid "Content Inner"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:258
msgid "Content Outer"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:286
msgid "Number of Columns"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:290, ../includes/Elements/LD_Course_List.php:247
msgid "1 Column"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:291, ../includes/Elements/LD_Course_List.php:248
msgid "2 Columns"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:292, ../includes/Elements/LD_Course_List.php:249
msgid "3 Columns"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:293, ../includes/Elements/LD_Course_List.php:250
msgid "4 Columns"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:294, ../includes/Elements/LD_Course_List.php:251
msgid "5 Columns"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:295, ../includes/Elements/LD_Course_List.php:252
msgid "6 Columns"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:304
msgid "User Info"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:315
msgid "Show Profile Image"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:327
msgid "Profile Image"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:346
msgid "Show Username"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:358, ../includes/Traits/Extender.php:1451
msgid "Username"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:361
msgid "Essential Addons"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:375
msgid "Pagination"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:383
msgid "Enable Load More?"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:411
msgid "Link & Content"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:419
msgid "Display Caption"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:429
msgid "Display Date"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:439, ../includes/Extensions/DynamicTags/Terms.php:105
msgid "Enable Link"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:449, ../includes/Elements/Static_Product.php:279, ../includes/Elements/Static_Product.php:354
msgid "Open in new window?"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:464
msgid "Instagram Feed Styles"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:472
msgid "Padding Between Images"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:506, ../includes/Elements/Twitter_Feed_Carousel.php:837, ../includes/Elements/Woo_Product_Slider.php:1303
msgid "Color &amp; Typography"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:514, ../includes/Elements/Post_Carousel.php:1451, ../includes/Elements/Post_Carousel.php:1467
msgid "Overlay Color"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:526
msgid "Icon Styles"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:546
msgid "Caption Styles"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:554
msgid "Caption Color"
msgstr ""

#: ../includes/Elements/Instagram_Feed.php:580
msgid "Load More Button Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:28, ../includes/Elements/Interactive_Card.php:86
msgid "Interactive Card"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:93
msgid "Front Panel Card Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:98
msgid "Text Card"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:99
msgid "Image Card"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:107
msgid "Rear Panel Card Type"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:112
msgid "Image Grid"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:113
msgid "Scrollable Content"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:114, ../includes/Elements/Stacked_Cards.php:162
msgid "Video"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:122
msgid "Front Panel"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:127
msgid "Show Cover Image"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:142, ../includes/Elements/Interactive_Card.php:216, ../includes/Elements/Interactive_Card.php:381
msgid "Cover Image"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:166, ../includes/Elements/Post_Carousel.php:400, ../includes/Elements/Post_List.php:509, ../includes/Elements/Post_List.php:846, ../includes/Elements/Woo_Cross_Sells.php:124
msgid "1"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:180
msgid "Counter HTML Tag"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:203
msgid "Interactive Cards"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:235, ../includes/Elements/Interactive_Card.php:467, ../includes/Elements/Offcanvas.php:153, ../includes/Elements/Protected_Content.php:72, ../includes/Elements/Stacked_Cards.php:74, ../includes/Elements/Toggle.php:145, ../includes/Elements/Toggle.php:227, ../includes/Elements/Woo_Account_Dashboard.php:269
msgid "Content Type"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:239, ../includes/Elements/Interactive_Card.php:471, ../includes/Elements/Lightbox.php:297, ../includes/Elements/Protected_Content.php:76, ../includes/Elements/Protected_Content.php:298, ../includes/Elements/Stacked_Cards.php:78, ../includes/Elements/Toggle.php:150, ../includes/Elements/Toggle.php:232, ../includes/Extensions/Content_Protection.php:206
msgid "Saved Templates"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:248, ../includes/Elements/Interactive_Card.php:480, ../includes/Elements/Lightbox.php:323, ../includes/Elements/Offcanvas.php:210, ../includes/Elements/Protected_Content.php:101, ../includes/Elements/Protected_Content.php:322, ../includes/Elements/Stacked_Cards.php:123, ../includes/Elements/Toggle.php:159, ../includes/Elements/Toggle.php:241, ../includes/Elements/Woo_Account_Dashboard.php:306, ../includes/Extensions/Conditional_Display.php:351, ../includes/Extensions/Content_Protection.php:230
msgid "Choose Template"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:264, ../includes/Elements/Interactive_Card.php:497
msgid "A new concept of showing content in your web page with more interactive way."
msgstr ""

#: ../includes/Elements/Interactive_Card.php:286, ../includes/Elements/Interactive_Card.php:519
msgid "Show Button Icon"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:304
msgid "More"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:316, ../includes/Elements/Interactive_Card.php:567
msgid "Button Icon Alignment"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:337, ../includes/Elements/Interactive_Card.php:588
msgid "Button Icon Spacing"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:363, ../includes/Elements/Interactive_Card.php:615, ../includes/Elements/LD_Course_List.php:878, ../includes/Elements/LD_Course_List.php:2519, ../includes/Elements/Offcanvas.php:305, ../includes/Elements/Offcanvas.php:1220
msgid "Button Icon"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:377
msgid "Rear Panel"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:401
msgid "Image Alignment"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:420, ../includes/Elements/Post_Carousel.php:682, ../includes/Elements/Team_Member_Carousel.php:1220
msgid "Image Height"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:454
msgid "Cool Headline"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:633, ../includes/Elements/Offcanvas.php:157, ../includes/Elements/Woo_Account_Dashboard.php:294
msgid "Custom Content"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:636
msgid "<h2>Custom Content</h2> <strong>A new concept of showing content in your web page with more interactive way</strong>. <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Voluptates assumenda recusandae a dolorum, nulla fugit reiciendis inventore explicabo cum autem placeat dignissimos doloremque quae magni sapiente eligendi hic ipsum quaerat mollitia, natus ullam. Repellat eligendi corporis cum suscipit totam molestiae ad, explicabo magnam libero, iusto sequi voluptatem nam culpa laboriosam officia consequatur eaque accusamus distinctio quas ipsa fuga consectetur iure asperiores! Ratione veniam magnam culpa temporibus nam quam cumque nesciunt debitis reprehenderit obcaecati eum tempore harum officiis autem facere, quos, ad officia sunt asperiores. Reprehenderit molestiae, vero omnis alias voluptatem recusandae dolores ab at. Nemo aliquam fuga vel necessitatibus voluptatum officiis ipsum, consequuntur id eum maiores debitis nostrum expedita libero saepe, doloribus mollitia minus quidem quo facere, consequatur! Veniam delectus doloribus blanditiis aliquid iure officiis modi sapiente unde. Ad, placeat suscipit. Perspiciatis dolores, expedita optio omnis reiciendis obcaecati quidem saepe praesentium autem unde suscipit nostrum natus vel tempore quas laudantium, excepturi! Ad, illo. Libero earum doloribus perspiciatis impedit, cum magni sint odio! Maxime sunt iste quibusdam nisi quia, voluptas, dolore tempora dolor neque error ducimus. Quas excepturi qui inventore quod at amet ipsa quasi blanditiis, voluptatem aliquam dolor beatae enim obcaecati alias voluptatibus vel molestias deleniti eius error nostrum, nesciunt adipisci quibusdam. Non mollitia rerum in commodi optio ipsam, neque quidem voluptatum velit quaerat suscipit consectetur nostrum odio, rem illo! Id placeat dignissimos tempora aliquam fugit veniam quam cum repudiandae fugiat nemo ad iure qui cupiditate natus aspernatur, dicta dolore ab corporis perferendis quaerat eaque assumenda libero explicabo beatae. Quas.</p>"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:650
msgid "Video Source"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:655
msgid "Youtube"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:656
msgid "Vimeo"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:670
msgid "Youtube URL"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:687
msgid "Vimeo URL"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:704
msgid "Allow Full Screen?"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:725, ../includes/Extensions/Smooth_Animation.php:570
msgid "Animation Settings"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:732
msgid "Content Animation"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:737
msgid "Appear"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:738, ../includes/Elements/Woo_Product_Slider.php:599
msgid "SlideInLeft"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:739, ../includes/Elements/Woo_Product_Slider.php:600
msgid "SlideInRight"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:740
msgid "SlideInSwingLeft"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:741
msgid "SlideInSwingRight"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:749
msgid "Timing (ms)"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:869
msgid "Small Overlay Circle"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:880
msgid "Large Overlay Circle"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:899
msgid "Front Panel Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:952
msgid "Front Content Width"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:982
msgid "Front Content Height"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1051
msgid "Front Panel Thumbnail Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1100
msgid "Rear Panel Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1182
msgid "Rear Panel Thumbnail Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1228
msgid "Front Panel Color &amp; Typography"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1239
msgid "Counter Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1296, ../includes/Elements/Interactive_Card.php:1424, ../includes/Elements/Twitter_Feed_Carousel.php:873
msgid "Content Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1332
msgid "Rear Panel Color &amp; Typography"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1375
msgid "All Heading Tags Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1462
msgid "Front Panel Button Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1476
msgid "Button Style ( Front Panel )"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1744
msgid "Rear Panel Button Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1758
msgid "Button Style ( Rear Panel )"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:1974
msgid "Close Button Style"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:2039
msgid "Icon Font Size"
msgstr ""

#: ../includes/Elements/Interactive_Card.php:2060
msgid "Icon Radius"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:29
msgid "Interactive Promo"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:74, ../includes/Elements/Interactive_Promo.php:130
msgid "Promo Content"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:81
msgid "Promo Image"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:98
msgid "Image ALT Tag"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:114
msgid "Promo Heading"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:118, ../includes/Elements/Interactive_Promo.php:119
msgid "Enter heading for the promo"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:132, ../includes/Elements/Static_Product.php:181
msgid "Click to inspect, then edit as needed."
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:139, ../includes/Traits/Extender.php:474
msgid "Link URL"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:155
msgid "Promo Effects &amp; Settings"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:163
msgid "Set Promo Effect"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:167
msgid "Lily"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:168
msgid "Sadie"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:169
msgid "Layla"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:170
msgid "Oscar"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:171
msgid "Marley"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:172
msgid "Ruby"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:173
msgid "Roxy"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:174
msgid "Bubba"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:175
msgid "Romeo"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:176
msgid "Sarah"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:177
msgid "Chico"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:178
msgid "Milo"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:179
msgid "Apolo"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:180
msgid "Jazz"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:181
msgid "Ming"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:200, ../includes/Elements/Static_Product.php:481
msgid "Container Max Width (% or px)"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:257, ../includes/Elements/Static_Product.php:737
msgid "Colors &amp; Typography"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:265
msgid "Promo Heading Color"
msgstr ""

#: ../includes/Elements/Interactive_Promo.php:288
msgid "Promo Content Color"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:31
msgid "LearnDash Course List"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:117, ../includes/Elements/Post_List.php:97, ../includes/Elements/Static_Product.php:84, ../includes/Elements/Twitter_Feed_Carousel.php:226, ../includes/Elements/Woo_Product_Slider.php:338
msgid "Layout Settings"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:124
msgid "Skins"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:127
msgid "Select skin for different layout design."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:141
msgid "Layout Mode"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:147
msgid "Fit To Screen"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:156
msgid "Number of Courses"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:158
msgid "How many courses will be displayed in your grid."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:169
msgid "Show by course category"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:170
msgid "Shows only courses in the specified LearnDash category. Use the category slug."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:181
msgid "Show by course tag"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:182
msgid "Shows only courses tagged with the specified LearnDash tag. Use the tag slug."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:193
msgid "Show by dynamic course tags"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:194
msgid "Enable this if you are using this list in single course page template. Course tags will be fetched dynamically."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:216
msgid "Exclude by course category"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:217
msgid "Excludes courses in the specified LearnDash category. Use the category slug."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:229
msgid "Exclude by course tag"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:230
msgid "Excludes courses tagged with the specified LearnDash tag. Use the tag slug."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:245
msgid "Number of columns your grid will have on differnt screens."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:267, ../includes/Elements/Post_Carousel.php:383, ../includes/Elements/Woo_Collections.php:98, ../includes/Elements/Woo_Product_Slider.php:495
msgid "Tags"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:268
msgid "Hide course tags."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:291
msgid "Hide course category."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:314
msgid "Hide the thumbnail image."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:336
msgid "Course Meta"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:337
msgid "Hide course meta."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:359, ../includes/Elements/LD_Course_List.php:745, ../includes/Elements/LD_Course_List.php:1778, ../includes/Elements/Post_Carousel.php:483, ../includes/Elements/Post_List.php:815, ../includes/Elements/Post_List.php:2193, ../includes/Elements/Woo_Cross_Sells.php:158, ../includes/Elements/Woo_Product_Slider.php:131, ../includes/Elements/Woo_Thank_You.php:461, ../includes/Extensions/Conditional_Display.php:1317, ../includes/Extensions/Conditional_Display.php:1692, ../includes/Extensions/DynamicTags/Woo_Products.php:251
msgid "Date"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:360
msgid "Hide show course date."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:382, ../includes/Elements/Post_Block.php:1124, ../includes/Elements/Post_Carousel.php:233, ../includes/Elements/Post_Carousel.php:1607, ../includes/Elements/Post_List.php:727
msgid "Excerpt"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:383
msgid "Hide course excerpt"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:402, ../includes/Elements/Post_List.php:484, ../includes/Elements/Post_List.php:738, ../includes/Elements/Woo_Cross_Sells.php:260, ../includes/Elements/Woo_Product_Slider.php:426
msgid "Excerpt Words"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:414, ../includes/Elements/LD_Course_List.php:1415, ../includes/Elements/LD_Course_List.php:1544, ../includes/Elements/Multicolumn_Pricing_Table.php:633, ../includes/Elements/Multicolumn_Pricing_Table.php:2023, ../includes/Elements/Price_Menu.php:114, ../includes/Elements/Price_Menu.php:622, ../includes/Elements/Static_Product.php:197, ../includes/Elements/Woo_Cross_Sells.php:161, ../includes/Elements/Woo_Cross_Sells.php:343, ../includes/Elements/Woo_Cross_Sells.php:845, ../includes/Elements/Woo_Product_Slider.php:129, ../includes/Elements/Woo_Product_Slider.php:1902, ../includes/Elements/Woo_Thank_You.php:950, ../includes/Traits/Extender.php:5193, ../includes/Extensions/DynamicTags/Woo_Products.php:249
msgid "Price"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:415
msgid "Hide course price"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:437
msgid "Change Free Price Text?"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:459
msgid "Free Price Text"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:461, ../includes/Elements/LD_Course_List.php:2854, ../includes/Elements/LD_Course_List.php:3104, ../includes/templates/ld-courses/default.php:37, ../includes/templates/ld-courses/layout__2.php:56, ../includes/templates/ld-courses/layout__3.php:37
msgid "Free"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:476
msgid "Hide course enroll button."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:495
msgid "Change Button Text?"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:529, ../includes/Elements/LD_Course_List.php:2100, ../includes/Traits/Extender.php:5532
msgid "Progress Bar"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:530
msgid "A visual indicator of a student’s current progress in each course."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:549
msgid "Enable Filter"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:550
msgid "It displays tab items with the selcted categories and tags."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:569
msgid "All Label"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:572, ../includes/Elements/LD_Course_List.php:2774, ../includes/Elements/Post_List.php:184, ../includes/Traits/Extender.php:1704
msgid "All"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:585
msgid "Not Found Label"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:588, ../includes/Elements/LD_Course_List.php:3040, ../includes/Elements/LD_Course_List.php:3297
msgid "No Courses Found!"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:601, ../includes/Elements/LD_Course_List.php:1633, ../includes/Elements/Post_List.php:800, ../includes/Elements/Post_List.php:2076
msgid "Author Meta"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:602
msgid "Hide show author meta from courses."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:624
msgid "Ribbon Text"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:625
msgid "Hide show ribbon text from courses."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:647
msgid "Change Ribbon Text?"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:669
msgid "Enrolled Label"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:672, ../includes/Elements/LD_Course_List.php:2874
msgid "Enrolled"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:686
msgid "Completed Label"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:689, ../includes/Elements/LD_Course_List.php:2875, ../includes/Elements/Woo_Account_Dashboard.php:1771
msgid "Completed"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:703
msgid "Course Duration"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:704
msgid "Hide show duration of courses."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:731
msgid "Sorting Options"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:738
msgid "Course Sorting"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:741
msgid "How to sort the courses in your grid."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:744, ../includes/Elements/Woo_Cross_Sells.php:157, ../includes/Extensions/DynamicTags/Custom_Post_Types.php:93, ../includes/Extensions/DynamicTags/Posts.php:90, ../includes/Extensions/DynamicTags/Terms.php:120
msgid "ID"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:746, ../includes/Elements/Woo_Cross_Sells.php:159
msgid "Modified"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:747, ../includes/Elements/Woo_Cross_Sells.php:160, ../includes/Elements/Woo_Product_Slider.php:135, ../includes/Extensions/DynamicTags/Woo_Products.php:255
msgid "Menu Order"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:748, ../includes/Elements/Woo_Product_Slider.php:134, ../includes/Extensions/DynamicTags/Woo_Products.php:254
msgid "Random"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:757
msgid "Order of Sorting"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:760
msgid "The sort order for the “orderby” parameter."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:762
msgid "ASC"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:763
msgid "DESC"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:772
msgid "My Courses"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:774
msgid "Shows only the courses in which the current user is enrolled."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:777
msgid "Enrolled Only"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:778
msgid "Not Enrolled Only"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:799
msgid "Show Load More"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:809
msgid "Posts Per Page"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:822, ../includes/Elements/Twitter_Feed_Carousel.php:245
msgid "Post Limit"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:845
msgid "No More Items Text"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:848
msgid "No more items!"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:862, ../includes/Elements/Lightbox.php:1364, ../includes/Elements/Offcanvas.php:988
msgid "Extra Small"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:864, ../includes/Elements/Lightbox.php:1366, ../includes/Elements/Offcanvas.php:990, ../includes/Traits/Extender.php:3899, ../includes/Traits/Extender.php:4549, ../includes/Traits/Extender.php:4916
msgid "Medium"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:866, ../includes/Elements/Lightbox.php:1368, ../includes/Elements/Offcanvas.php:992
msgid "Extra Large"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:890, ../includes/Elements/Lightbox.php:562, ../includes/Elements/Multicolumn_Pricing_Table.php:250, ../includes/Elements/Multicolumn_Pricing_Table.php:424, ../includes/Elements/Multicolumn_Pricing_Table.php:566, ../includes/Elements/Offcanvas.php:314, ../includes/Elements/Static_Product.php:1077, ../includes/Elements/Static_Product.php:1365, ../includes/Traits/Extender.php:421
msgid "Icon Position"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:999, ../includes/Elements/LD_Course_List.php:2053
msgid "Transition"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1031
msgid "Enable 3D Hover"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1147
msgid "Tags Style"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1215
msgid "Image Style"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1251, ../includes/Elements/Woo_Product_Slider.php:1574
msgid "Bottom Space"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1450
msgid "Select price ticker position."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1452
msgid "Left - Top"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1453
msgid "Left - Bottom"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1454
msgid "Right - Top"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1455
msgid "Right - Bottom"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1644
msgid "Space Around"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1659, ../includes/Elements/Post_Carousel.php:452, ../includes/Elements/Testimonial_Slider.php:517, ../includes/Elements/Twitter_Feed_Carousel.php:1378, ../includes/Elements/Woo_Account_Dashboard.php:398, ../includes/Elements/Woo_Account_Dashboard.php:443
msgid "Avatar"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1667, ../includes/Elements/Woo_Account_Dashboard.php:1008
msgid "Avatar Size"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1693
msgid "Avatar Border Radius"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1708
msgid "Avatar Space"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1754, ../includes/Elements/Twitter_Feed_Carousel.php:769
msgid "Link Color"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1766
msgid "Link Hover Color"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1816
msgid "Course Meta & Price"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1851, ../includes/Elements/Offcanvas.php:1025
msgid "Icon Space"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:1874
msgid "Meta Space"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2054
msgid "Hover transition in ms."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2120, ../includes/Elements/Mailchimp.php:585
msgid "Label Color"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2144, ../includes/Elements/LD_Course_List.php:2220
msgid "Space"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2156
msgid "Label Alignment"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2182
msgid "Enable Steps Label"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2237
msgid "Ribbon"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2257
msgid "Ribbon - Enrolled"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2289
msgid "Ribbon - Completed"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2340, ../includes/Elements/Woo_Product_Slider.php:1660
msgid "Top Spacing"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:2473, ../includes/Elements/Lightbox.php:1376, ../includes/Elements/Offcanvas.php:331, ../includes/Elements/Static_Product.php:1093, ../includes/Elements/Static_Product.php:1378, ../includes/Traits/Extender.php:434
msgid "Icon Spacing"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:94, ../includes/Elements/Woo_Account_Dashboard.php:143, ../includes/Elements/Woo_Cross_Sells.php:75, ../includes/Elements/Woo_Product_Slider.php:320, ../includes/Elements/Woo_Thank_You.php:109
msgid "Warning!"
msgstr ""

#: ../includes/Elements/LD_Course_List.php:102
msgid "<strong>LearnDash</strong> is not installed/activated on your site. Please install and activate <strong>LearnDash</strong> first."
msgstr ""

#: ../includes/Elements/LD_Course_List.php:3171
msgid ""
"Course layout file not found! It's must be removed \n"
""
msgstr ""

#: ../includes/Elements/Lightbox.php:29
msgid "Lightbox &amp; Modal"
msgstr ""

#: ../includes/Elements/Lightbox.php:149
msgid "Lightbox || Modal"
msgstr ""

#: ../includes/Elements/Lightbox.php:160
msgid "Fullscreen"
msgstr ""

#: ../includes/Elements/Lightbox.php:200
msgid "Auto Height"
msgstr ""

#: ../includes/Elements/Lightbox.php:258
msgid "Enable Title"
msgstr ""

#: ../includes/Elements/Lightbox.php:275
msgid "Lightbox Title"
msgstr ""

#: ../includes/Elements/Lightbox.php:295
msgid "Link (Page/Video/Map)"
msgstr ""

#: ../includes/Elements/Lightbox.php:298, ../includes/Elements/Lightbox.php:370
msgid "Custom HTML"
msgstr ""

#: ../includes/Elements/Lightbox.php:306
msgid "Choose Lightbox Image"
msgstr ""

#: ../includes/Elements/Lightbox.php:337
msgid "Add your content here (HTML/Shortcode)"
msgstr ""

#: ../includes/Elements/Lightbox.php:339
msgid "Add your popup content here"
msgstr ""

#: ../includes/Elements/Lightbox.php:350
msgid "Provide Page/Video/Map URL"
msgstr ""

#: ../includes/Elements/Lightbox.php:360
msgid "Place Page/Video/Map URL"
msgstr ""

#: ../includes/Elements/Lightbox.php:393
msgid "Action Type"
msgstr ""

#: ../includes/Elements/Lightbox.php:397
msgid "Button Click"
msgstr ""

#: ../includes/Elements/Lightbox.php:398
msgid "Page Load"
msgstr ""

#: ../includes/Elements/Lightbox.php:399
msgid "Exit Intent"
msgstr ""

#: ../includes/Elements/Lightbox.php:400
msgid "External Element"
msgstr ""

#: ../includes/Elements/Lightbox.php:409, ../includes/Elements/Mailchimp.php:301
msgid "Button Settings"
msgstr ""

#: ../includes/Elements/Lightbox.php:463
msgid "Stretch"
msgstr ""

#: ../includes/Elements/Lightbox.php:517
msgid "Only applicable for button type."
msgstr ""

#: ../includes/Elements/Lightbox.php:535
msgid "Open Popup"
msgstr ""

#: ../includes/Elements/Lightbox.php:580
msgid "Trigger Icon"
msgstr ""

#: ../includes/Elements/Lightbox.php:593
msgid "Trigger Image"
msgstr ""

#: ../includes/Elements/Lightbox.php:614
msgid "Page Load Settings"
msgstr ""

#: ../includes/Elements/Lightbox.php:627
msgid "seconds"
msgstr ""

#: ../includes/Elements/Lightbox.php:643, ../includes/Elements/Lightbox.php:673
msgid "Display After"
msgstr ""

#: ../includes/Elements/Lightbox.php:644, ../includes/Elements/Lightbox.php:674
msgid "day(s)"
msgstr ""

#: ../includes/Elements/Lightbox.php:645, ../includes/Elements/Lightbox.php:675
msgid "If a user closes the modal box, it will be displayed only after the defined day(s)"
msgstr ""

#: ../includes/Elements/Lightbox.php:662
msgid "Exit Intent Settings"
msgstr ""

#: ../includes/Elements/Lightbox.php:692
msgid "Element Identifier"
msgstr ""

#: ../includes/Elements/Lightbox.php:697, ../includes/Elements/Lightbox.php:698
msgid "#open-popup"
msgstr ""

#: ../includes/Elements/Lightbox.php:699
msgid "You can also use class identifier such as <strong>.open-popup</strong>"
msgstr ""

#: ../includes/Elements/Lightbox.php:714
msgid "Exit Settings"
msgstr ""

#: ../includes/Elements/Lightbox.php:723, ../includes/Elements/Offcanvas.php:422
msgid "Show Close Button"
msgstr ""

#: ../includes/Elements/Lightbox.php:735
msgid "Esc to Exit"
msgstr ""

#: ../includes/Elements/Lightbox.php:736
msgid "Close the modal box by pressing the Esc key"
msgstr ""

#: ../includes/Elements/Lightbox.php:748
msgid "Click to Exit"
msgstr ""

#: ../includes/Elements/Lightbox.php:749
msgid "Close the modal box by clicking anywhere outside the modal window"
msgstr ""

#: ../includes/Elements/Lightbox.php:763
msgid "Delay (Seconds)"
msgstr ""

#: ../includes/Elements/Lightbox.php:799
msgid "Zoom In"
msgstr ""

#: ../includes/Elements/Lightbox.php:800
msgid "Zoom Out"
msgstr ""

#: ../includes/Elements/Lightbox.php:801
msgid "3D Unfold"
msgstr ""

#: ../includes/Elements/Lightbox.php:802
msgid "Newspaper"
msgstr ""

#: ../includes/Elements/Lightbox.php:803
msgid "Move From Top"
msgstr ""

#: ../includes/Elements/Lightbox.php:804
msgid "Move Left"
msgstr ""

#: ../includes/Elements/Lightbox.php:805
msgid "Move Right"
msgstr ""

#: ../includes/Elements/Lightbox.php:916
msgid "Lightbox"
msgstr ""

#: ../includes/Elements/Lightbox.php:991
msgid "Enable dark overlay?"
msgstr ""

#: ../includes/Elements/Lightbox.php:1002
msgid "Overlay Background Color"
msgstr ""

#: ../includes/Elements/Lightbox.php:1061
msgid "Icon Background Size"
msgstr ""

#: ../includes/Elements/Lightbox.php:1117, ../includes/Elements/Lightbox.php:1239
msgid "Background Shape"
msgstr ""

#: ../includes/Elements/Lightbox.php:1123, ../includes/Elements/Lightbox.php:1245, ../includes/Traits/Extender.php:3923
msgid "Circle"
msgstr ""

#: ../includes/Elements/Lightbox.php:1125, ../includes/Elements/Lightbox.php:1247, ../includes/Traits/Extender.php:3924
msgid "Square"
msgstr ""

#: ../includes/Elements/Lightbox.php:1417, ../includes/Elements/Static_Product.php:1167, ../includes/Elements/Static_Product.php:1440
msgid "Button Border Radius"
msgstr ""

#: ../includes/Elements/Lightbox.php:1587
msgid "Content Styles"
msgstr ""

#: ../includes/Elements/Lightbox.php:1624, ../includes/Elements/Offcanvas.php:1209
msgid "Close Button"
msgstr ""

#: ../includes/Elements/Lightbox.php:1667
msgid "Bold"
msgstr ""

#: ../includes/Elements/Lightbox.php:1728
msgid "Close Button Position"
msgstr ""

#: ../includes/Elements/Lightbox.php:1740
msgid "Position Right"
msgstr ""

#: ../includes/Elements/Lightbox.php:1762
msgid "Position Top"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:44, ../includes/Elements/Logo_Carousel.php:131
msgid "Logo Carousel"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:140
msgid "Upload Logo Image"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:171
msgid "Hide Title ?"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:181
msgid "Alt Text"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:195
msgid "Enable Tooltip?"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:208
msgid "I'm a awesome tooltip!!"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:218
msgid "Tooltip Side"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:248
msgid "Tooltip Trigger"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:252, ../includes/Extensions/EAEL_Tooltip_Section.php:187
msgid "Click"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:264
msgid "Tooltip Animation"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:267, ../includes/Elements/Logo_Carousel.php:435, ../includes/Elements/Post_Carousel.php:628, ../includes/Elements/Testimonial_Slider.php:111, ../includes/Elements/Twitter_Feed_Carousel.php:367, ../includes/Extensions/EAEL_Parallax_Section.php:59, ../includes/Extensions/EAEL_Tooltip_Section.php:116, ../includes/Skins/Skin_Default.php:120, ../includes/Skins/Skin_Five.php:138, ../includes/Skins/Skin_Four.php:120, ../includes/Skins/Skin_One.php:120, ../includes/Skins/Skin_Seven.php:120, ../includes/Skins/Skin_Six.php:120, ../includes/Skins/Skin_Three.php:138, ../includes/Skins/Skin_Two.php:137
msgid "Fade"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:268
msgid "Grow"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:271
msgid "Fall"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:283
msgid "Animation Duration"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:298, ../includes/Elements/One_Page_Navigation.php:207
msgid "Tooltip Arrow"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:311
msgid "Tooltip Theme"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:315
msgid "Noir"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:317
msgid "Punk"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:319
msgid "Borderless"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:348
msgid "Logo Image "
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:368
msgid "Title HTML Tag"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:422, ../includes/Elements/Twitter_Feed_Carousel.php:354
msgid "Carousel Settings"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:429, ../includes/Elements/Post_Carousel.php:622, ../includes/Elements/Testimonial_Slider.php:105, ../includes/Elements/Twitter_Feed_Carousel.php:361
msgid "Effect"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:430, ../includes/Elements/Post_Carousel.php:623, ../includes/Elements/Testimonial_Slider.php:106, ../includes/Elements/Twitter_Feed_Carousel.php:362, ../includes/Elements/Woo_Product_Slider.php:588
msgid "Sets transition effect"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:436, ../includes/Elements/Post_Carousel.php:629, ../includes/Elements/Testimonial_Slider.php:112, ../includes/Elements/Twitter_Feed_Carousel.php:368
msgid "Cube"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:437, ../includes/Elements/Post_Carousel.php:630, ../includes/Elements/Testimonial_Slider.php:113, ../includes/Elements/Twitter_Feed_Carousel.php:369
msgid "Coverflow"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:438, ../includes/Elements/Post_Carousel.php:631, ../includes/Elements/Testimonial_Slider.php:114, ../includes/Elements/Twitter_Feed_Carousel.php:370
msgid "Flip"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:446, ../includes/Elements/Post_Carousel.php:639, ../includes/Elements/Team_Member_Carousel.php:713, ../includes/Elements/Testimonial_Slider.php:122, ../includes/Elements/Twitter_Feed_Carousel.php:378
msgid "Visible Items"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:470, ../includes/Elements/Post_Carousel.php:662, ../includes/Elements/Testimonial_Slider.php:170, ../includes/Elements/Twitter_Feed_Carousel.php:400
msgid "Items Gap"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:490, ../includes/Elements/Post_Carousel.php:702, ../includes/Elements/Team_Member_Carousel.php:751, ../includes/Elements/Twitter_Feed_Carousel.php:420
msgid "Slider Speed"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:491, ../includes/Elements/Post_Carousel.php:703, ../includes/Elements/Team_Member_Carousel.php:752, ../includes/Elements/Testimonial_Slider.php:191, ../includes/Elements/Twitter_Feed_Carousel.php:421, ../includes/Elements/Woo_Product_Slider.php:613
msgid "Duration of transition between slides (in ms)"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:522, ../includes/Elements/Post_Carousel.php:732, ../includes/Elements/Team_Member_Carousel.php:783, ../includes/Elements/Testimonial_Slider.php:220
msgid "Enable Marquee"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:538, ../includes/Elements/Post_Carousel.php:748, ../includes/Elements/Team_Member_Carousel.php:798, ../includes/Elements/Testimonial_Slider.php:235, ../includes/Elements/Twitter_Feed_Carousel.php:450, ../includes/Elements/Woo_Product_Slider.php:642
msgid "Autoplay Speed"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:559, ../includes/Elements/Post_Carousel.php:785, ../includes/Elements/Team_Member_Carousel.php:836, ../includes/Elements/Testimonial_Slider.php:272, ../includes/Elements/Woo_Product_Slider.php:677
msgid "Infinite Loop"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:587, ../includes/Elements/Post_Carousel.php:797, ../includes/Elements/Team_Member_Carousel.php:848, ../includes/Elements/Testimonial_Slider.php:284, ../includes/Elements/Woo_Product_Slider.php:689
msgid "Grab Cursor"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:588, ../includes/Elements/Post_Carousel.php:798, ../includes/Elements/Team_Member_Carousel.php:849, ../includes/Elements/Testimonial_Slider.php:285, ../includes/Elements/Woo_Product_Slider.php:690
msgid "Shows grab cursor when you hover over the slider"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:601, ../includes/Elements/Post_Carousel.php:810, ../includes/Elements/Team_Member_Carousel.php:861, ../includes/Elements/Testimonial_Slider.php:297, ../includes/Elements/Twitter_Feed_Carousel.php:485, ../includes/Elements/Woo_Product_Slider.php:703
msgid "Navigation"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:611, ../includes/Elements/Post_Carousel.php:819, ../includes/Elements/Team_Member_Carousel.php:871, ../includes/Elements/Testimonial_Slider.php:307
msgid "Arrows & Dots are not available on <strong>Marquee</stong> Mode."
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:636, ../includes/Elements/Post_Carousel.php:843, ../includes/Elements/Post_Carousel.php:2146, ../includes/Elements/Team_Member_Carousel.php:895, ../includes/Elements/Team_Member_Carousel.php:2309, ../includes/Elements/Testimonial_Slider.php:347, ../includes/Elements/Testimonial_Slider.php:1585, ../includes/Elements/Twitter_Feed_Carousel.php:506, ../includes/Elements/Twitter_Feed_Carousel.php:1193, ../includes/Elements/Woo_Product_Slider.php:724, ../includes/Elements/Woo_Product_Slider.php:2419
msgid "Dots"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:671
msgid "Logos"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:680
msgid "Button Background"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:733, ../includes/Elements/Logo_Carousel.php:791, ../includes/Elements/Stacked_Cards.php:677, ../includes/Elements/Stacked_Cards.php:731
msgid "Grayscale"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:782
msgid "Logo Background"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:859
msgid "Margin Top"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:905
msgid "Choose Previous Arrow"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:918
msgid "Choose Next Arrow"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:931, ../includes/Elements/Team_Member_Carousel.php:2128, ../includes/Elements/Twitter_Feed_Carousel.php:986, ../includes/Elements/Woo_Product_Slider.php:2899
msgid "Arrows Size"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:953
msgid "Arrow Position"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:976, ../includes/Elements/Team_Member_Carousel.php:2148, ../includes/Elements/Twitter_Feed_Carousel.php:1030, ../includes/Elements/Woo_Product_Slider.php:2939
msgid "Align Left Arrow"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:995, ../includes/Elements/Team_Member_Carousel.php:2167, ../includes/Elements/Twitter_Feed_Carousel.php:1049, ../includes/Elements/Woo_Product_Slider.php:2958
msgid "Align Right Arrow"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:1139
msgid "Pagination: Dots"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:1153, ../includes/Elements/Post_Carousel.php:2160, ../includes/Elements/Team_Member_Carousel.php:2323, ../includes/Elements/Testimonial_Slider.php:1599, ../includes/Elements/Twitter_Feed_Carousel.php:1207, ../includes/Elements/Woo_Product_Slider.php:2448
msgid "Inside"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:1154, ../includes/Elements/Post_Carousel.php:2161, ../includes/Elements/Team_Member_Carousel.php:2324, ../includes/Elements/Testimonial_Slider.php:1600, ../includes/Elements/Twitter_Feed_Carousel.php:1208, ../includes/Elements/Woo_Product_Slider.php:2449
msgid "Outside"
msgstr ""

#: ../includes/Elements/Logo_Carousel.php:1241, ../includes/Elements/Post_List.php:1279, ../includes/Elements/Team_Member_Carousel.php:2396, ../includes/Elements/Testimonial_Slider.php:1672, ../includes/Elements/Twitter_Feed_Carousel.php:1279
msgid "Active Color"
msgstr ""

#: ../includes/Elements/Mailchimp.php:28
msgid "Mailchimp"
msgstr ""

#: ../includes/Elements/Mailchimp.php:75
msgid "Mailchimp Account Settings"
msgstr ""

#: ../includes/Elements/Mailchimp.php:82, ../includes/Traits/Extender.php:3835
msgid "Mailchimp List"
msgstr ""

#: ../includes/Elements/Mailchimp.php:93
msgid "Redirect on Form Submission"
msgstr ""

#: ../includes/Elements/Mailchimp.php:123
msgid "Field Settings"
msgstr ""

#: ../includes/Elements/Mailchimp.php:129
msgid "Email Label"
msgstr ""

#: ../includes/Elements/Mailchimp.php:144
msgid "Email Placeholder"
msgstr ""

#: ../includes/Elements/Mailchimp.php:159
msgid "Enable First Name"
msgstr ""

#: ../includes/Elements/Mailchimp.php:168
msgid "First Name Label"
msgstr ""

#: ../includes/Elements/Mailchimp.php:186
msgid "First Name Placeholder"
msgstr ""

#: ../includes/Elements/Mailchimp.php:204
msgid "Enable Last Name"
msgstr ""

#: ../includes/Elements/Mailchimp.php:213
msgid "Last Name Label"
msgstr ""

#: ../includes/Elements/Mailchimp.php:231
msgid "Last Name Placeholder"
msgstr ""

#: ../includes/Elements/Mailchimp.php:249
msgid "Enable Tags"
msgstr ""

#: ../includes/Elements/Mailchimp.php:258
msgid "Tags Label"
msgstr ""

#: ../includes/Elements/Mailchimp.php:277
msgid "Tags Placeholder"
msgstr ""

#: ../includes/Elements/Mailchimp.php:313
msgid "Subscribe"
msgstr ""

#: ../includes/Elements/Mailchimp.php:322
msgid "Loading Text"
msgstr ""

#: ../includes/Elements/Mailchimp.php:328
msgid "Submitting..."
msgstr ""

#: ../includes/Elements/Mailchimp.php:342
msgid "Message Settings"
msgstr ""

#: ../includes/Elements/Mailchimp.php:348
msgid "Success Text"
msgstr ""

#: ../includes/Elements/Mailchimp.php:354
msgid "You have subscribed successfully!"
msgstr ""

#: ../includes/Elements/Mailchimp.php:363
msgid "Pending Text"
msgstr ""

#: ../includes/Elements/Mailchimp.php:369
msgid "Please check your email and confirm subscription!"
msgstr ""

#: ../includes/Elements/Mailchimp.php:466
msgid "Form Fields Styles"
msgstr ""

#: ../includes/Elements/Mailchimp.php:473
msgid "Input Field Background"
msgstr ""

#: ../includes/Elements/Mailchimp.php:483, ../includes/Elements/Protected_Content.php:618, ../includes/Extensions/Content_Protection.php:458
msgid "Input Width"
msgstr ""

#: ../includes/Elements/Mailchimp.php:504
msgid "Input Height"
msgstr ""

#: ../includes/Elements/Mailchimp.php:525
msgid "Fields Padding"
msgstr ""

#: ../includes/Elements/Mailchimp.php:536
msgid "Fields Margin"
msgstr ""

#: ../includes/Elements/Mailchimp.php:595
msgid "Field Font Color"
msgstr ""

#: ../includes/Elements/Mailchimp.php:605
msgid "Placeholder Font Color"
msgstr ""

#: ../includes/Elements/Mailchimp.php:618
msgid "Label Typography"
msgstr ""

#: ../includes/Elements/Mailchimp.php:633
msgid "Input Fields Typography"
msgstr ""

#: ../includes/Elements/Mailchimp.php:652
msgid "Subscribe Button Style"
msgstr ""

#: ../includes/Elements/Mailchimp.php:659
msgid "Button Display"
msgstr ""

#: ../includes/Elements/Mailchimp.php:672
msgid "Button Max Width"
msgstr ""

#: ../includes/Elements/Mailchimp.php:842
msgid "Message Style"
msgstr ""

#: ../includes/Elements/Mailchimp.php:859
msgid "Font Color"
msgstr ""

#: ../includes/Elements/Mailchimp.php:869, ../includes/Elements/Protected_Content.php:393, ../includes/Elements/Protected_Content.php:480, ../includes/Elements/Protected_Content.php:561, ../includes/Extensions/Content_Protection.php:315, ../includes/Extensions/Content_Protection.php:399
msgid "Text Alignment"
msgstr ""

#: ../includes/Elements/Mailchimp.php:1032
msgid "Whoops! It seems like you didn't set Mailchimp API key. You can set from <strong>WordPress Dashboard > Essential Addons > Elements > Form Styler Elements > Mailchimp (Settings)</strong>"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:32
msgid "Multicolumn Pricing Table"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:87
msgid "Layouts"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:103
msgid "Modern"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:117
msgid "Package Title Tag"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:166
msgid "Text Effect"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:170
msgid "No Effect"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:171
msgid "Marquee"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:172
msgid "Reflect"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:227
msgid "Collapse Feature Rows"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:237
msgid "Number of Rows"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:273
msgid "Collapsed Label"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:276, ../includes/templates/ld-courses/default.php:91, ../includes/templates/ld-courses/layout__1.php:88, ../includes/templates/ld-courses/layout__2.php:81, ../includes/templates/ld-courses/layout__3.php:84
msgid "See More"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:286
msgid "Collapsed Icon"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:301
msgid "Expanded Label"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:304
msgid "See Less"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:314
msgid "Expanded Icon"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:335, ../includes/Elements/Multicolumn_Pricing_Table.php:1288
msgid "Title Column"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:342, ../includes/Elements/Multicolumn_Pricing_Table.php:1318
msgid "Header Content"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:415, ../includes/Elements/Multicolumn_Pricing_Table.php:1255, ../includes/Elements/Multicolumn_Pricing_Table.php:2080
msgid "Features"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:459
msgid "Feature"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:469
msgid "This Icon will only available for Modern Layout."
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:489
msgid "Add Feature"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:499
msgid "Sync Features"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:500
msgid "After adding or removing features, you need to click on the button to sync all the features."
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:514
msgid "Pricing Packages"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:541
msgid "Make it Featured"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:589
msgid "Featured Text Label"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:593
msgid "Best Value"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:603
msgid "Currency"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:613
msgid "Currency Position"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:643
msgid "Sale Price"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:652
msgid "Sale Price Position"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:675
msgid "Period (Per)"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:677
msgid "month"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:685
msgid "Period Separator"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:695
msgid "Enable Button"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:708
msgid "Buy Now"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:737
msgid "Add Package"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:750
msgid "Sync Pricing Packages"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:751
msgid "After adding or removing pricing packages, you need to click on the button to sync all the packages."
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:810
msgid "Individual Color"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:846, ../includes/Elements/Multicolumn_Pricing_Table.php:1484
msgid "Content Position"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:895, ../includes/Elements/Twitter_Feed_Carousel.php:759, ../includes/Elements/Woo_Product_Slider.php:1962, ../includes/Elements/Woo_Product_Slider.php:2241
msgid "Content Color"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:976
msgid "Column Gap"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1035
msgid "Featured Badge"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1204
msgid "Featured Column"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1229
msgid "Featured Column Backgrounds"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1241
msgid "Package"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1269
msgid "Button Cell"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1583
msgid "Features Title"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1746
msgid "Packages"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1754, ../includes/Elements/Price_Menu.php:560, ../includes/Elements/Stacked_Cards.php:1430
msgid "Border Width"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1887
msgid "Reflection Gap"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1943
msgid "Period"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:1983
msgid "Previous Price"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:2234
msgid "Buy Button"
msgstr ""

#: ../includes/Elements/Multicolumn_Pricing_Table.php:2438
msgid "Collaps Button"
msgstr ""

#: ../includes/Elements/Offcanvas.php:48
msgid "Offcanvas"
msgstr ""

#: ../includes/Elements/Offcanvas.php:130
msgid "Offcanvas Content"
msgstr ""

#: ../includes/Elements/Offcanvas.php:156
msgid "Sidebar"
msgstr ""

#: ../includes/Elements/Offcanvas.php:158
msgid "Saved Section"
msgstr ""

#: ../includes/Elements/Offcanvas.php:159
msgid "Saved Widget"
msgstr ""

#: ../includes/Elements/Offcanvas.php:160
msgid "Saved Page Template"
msgstr ""

#: ../includes/Elements/Offcanvas.php:184
msgid "Choose Widget"
msgstr ""

#: ../includes/Elements/Offcanvas.php:197
msgid "Choose Section"
msgstr ""

#: ../includes/Elements/Offcanvas.php:258
msgid "Box 1"
msgstr ""

#: ../includes/Elements/Offcanvas.php:259, ../includes/Elements/Offcanvas.php:263
msgid "Text box description goes here"
msgstr ""

#: ../includes/Elements/Offcanvas.php:262
msgid "Box 2"
msgstr ""

#: ../includes/Elements/Offcanvas.php:283, ../includes/Elements/Offcanvas.php:950
msgid "Toggle Button"
msgstr ""

#: ../includes/Elements/Offcanvas.php:295
msgid "Click Here"
msgstr ""

#: ../includes/Elements/Offcanvas.php:393
msgid "Content Transition"
msgstr ""

#: ../includes/Elements/Offcanvas.php:398
msgid "Reveal"
msgstr ""

#: ../includes/Elements/Offcanvas.php:399
msgid "Push"
msgstr ""

#: ../includes/Elements/Offcanvas.php:400
msgid "Slide Along"
msgstr ""

#: ../includes/Elements/Offcanvas.php:410
msgid "Open OffCanvas by Default"
msgstr ""

#: ../includes/Elements/Offcanvas.php:435
msgid "Esc to Close"
msgstr ""

#: ../includes/Elements/Offcanvas.php:447
msgid "Click anywhere to Close"
msgstr ""

#: ../includes/Elements/Offcanvas.php:469
msgid "Offcanvas Bar"
msgstr ""

#: ../includes/Elements/Offcanvas.php:607, ../includes/Traits/Extender.php:31
msgid "Box"
msgstr ""

#: ../includes/Elements/Offcanvas.php:663, ../includes/Elements/Post_List.php:1167
msgid "Bottom Spacing"
msgstr ""

#: ../includes/Elements/Offcanvas.php:831
msgid "Offcanvas Title"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:38
msgid "One Page Navigation"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:103, ../includes/Elements/One_Page_Navigation.php:409
msgid "Navigation Dots"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:112, ../includes/Elements/One_Page_Navigation.php:117
msgid "Section Title"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:127
msgid "Section ID"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:142
msgid "Navigation Dot"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:159
msgid "Section #1"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:164
msgid "Section #2"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:169
msgid "Section #3"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:195
msgid "Show tooltip on hover"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:222
msgid "Scroll Wheel"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:223
msgid "Use mouse wheel to navigate from one row to another"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:235
msgid "Touch Swipe"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:236
msgid "Use touch swipe to navigate from one row to another in mobile devices"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:251
msgid "Scroll Keys"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:252
msgid "Use UP and DOWN arrow keys to navigate from one row to another"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:264
msgid "Row Top Offset"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:281
msgid "Scrolling Speed"
msgstr ""

#: ../includes/Elements/One_Page_Navigation.php:299
msgid "Navigation Box"
msgstr ""

#: ../includes/Elements/Post_Block.php:32
msgid "Post Block"
msgstr ""

#: ../includes/Elements/Post_Block.php:867
msgid "Post Background Color"
msgstr ""

#: ../includes/Elements/Post_Block.php:896
msgid "Spacing Between Items"
msgstr ""

#: ../includes/Elements/Post_Block.php:911, ../includes/Elements/Post_List.php:1109
msgid "Grid Gap"
msgstr ""

#: ../includes/Elements/Post_Block.php:961, ../includes/Elements/Post_Block.php:977
msgid "Content Box Padding"
msgstr ""

#: ../includes/Elements/Post_Block.php:998, ../includes/Elements/Post_List.php:1735, ../includes/Elements/Woo_Collections.php:329
msgid "Thumbnail Style"
msgstr ""

#: ../includes/Elements/Post_Block.php:1214, ../includes/Elements/Post_Carousel.php:415, ../includes/Elements/Post_List.php:561
msgid "Meta"
msgstr ""

#: ../includes/Elements/Post_Block.php:1312, ../includes/Elements/Woo_Product_Slider.php:188, ../includes/Extensions/Conditional_Display.php:1690, ../includes/Extensions/DynamicTags/Terms.php:34
msgid "Terms"
msgstr ""

#: ../includes/Elements/Post_Block.php:1369
msgid "Card Hover"
msgstr ""

#: ../includes/Elements/Post_Block.php:1382, ../includes/Elements/Post_Carousel.php:1417, ../includes/Elements/Woo_Product_Slider.php:592
msgid "FadeIn"
msgstr ""

#: ../includes/Elements/Post_Block.php:1383, ../includes/Elements/Post_Carousel.php:1418, ../includes/Elements/Woo_Collections.php:464, ../includes/Elements/Woo_Product_Slider.php:601, ../includes/Skins/Skin_Default.php:122, ../includes/Skins/Skin_Five.php:140, ../includes/Skins/Skin_Four.php:122, ../includes/Skins/Skin_One.php:122, ../includes/Skins/Skin_Seven.php:122, ../includes/Skins/Skin_Six.php:122, ../includes/Skins/Skin_Three.php:140, ../includes/Skins/Skin_Two.php:139
msgid "ZoomIn"
msgstr ""

#: ../includes/Elements/Post_Block.php:1384, ../includes/Elements/Post_Carousel.php:1419
msgid "SlideUp"
msgstr ""

#: ../includes/Elements/Post_Block.php:1392, ../includes/Elements/Post_Carousel.php:1396
msgid "Post Hover Icon"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:32
msgid "Post Carousel"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:98
msgid "View"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:110, ../includes/Elements/Testimonial_Slider.php:404
msgid "Skin"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:118
msgid "Two"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:122
msgid "Three"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:170
msgid "Html Tag"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:222, ../includes/Elements/Woo_Product_Slider.php:390
msgid "Title Length"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:254
msgid "Length"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:266, ../includes/Elements/Post_List.php:750, ../includes/Elements/Testimonial_Slider.php:491, ../includes/Elements/Woo_Cross_Sells.php:272, ../includes/Elements/Woo_Product_Slider.php:438
msgid "Expansion Indicator"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:271, ../includes/Elements/Post_List.php:754, ../includes/Elements/Testimonial_Slider.php:493, ../includes/Elements/Woo_Cross_Sells.php:275
msgid "..."
msgstr ""

#: ../includes/Elements/Post_Carousel.php:281, ../includes/Elements/Testimonial_Slider.php:1127
msgid "Read More Text"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:316
msgid "Post Terms"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:363, ../includes/Elements/Post_Carousel.php:379, ../includes/Elements/Woo_Product_Slider.php:491
msgid "Show Terms From"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:397
msgid "Max Terms to Show"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:401, ../includes/Elements/Post_List.php:513, ../includes/Elements/Post_List.php:847, ../includes/Elements/Woo_Cross_Sells.php:125
msgid "2"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:402, ../includes/Elements/Post_List.php:517, ../includes/Elements/Post_List.php:848, ../includes/Elements/Woo_Cross_Sells.php:126
msgid "3"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:440
msgid "Entry Header"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:441
msgid "Entry Footer"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:468, ../includes/Elements/Post_List.php:2148
msgid "Author Name"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:514, ../includes/Elements/Price_Menu.php:162
msgid "Show Image"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:538
msgid "Enable Image Ratio"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:553
msgid "Image Ratio"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:580
msgid "Fallback Image"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:930, ../includes/Elements/Post_Carousel.php:988, ../includes/Elements/Post_Carousel.php:1029
msgid "Open in New Tab"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1175
msgid "Post"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1183, ../includes/Elements/Post_Carousel.php:1427
msgid "Use Gradient Background?"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1250
msgid "Hover Effect"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1273
msgid "Carousel Title"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1687
msgid "Terms Style"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1697
msgid "Terms Color"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1710, ../includes/Elements/Post_List.php:1939
msgid "Meta Typography"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1737
msgid "Meta Date Style"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1759
msgid "Meta Date Color"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1772
msgid "Meta Date Typography"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1812
msgid "Meta Date Position"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1826, ../includes/Elements/Post_List.php:1636, ../includes/Elements/Post_List.php:1895
msgid "Meta Style"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1904, ../includes/Elements/Post_Carousel.php:1916
msgid "Meta Position"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1946, ../includes/Elements/Team_Member_Carousel.php:2109, ../includes/Elements/Testimonial_Slider.php:1346, ../includes/Elements/Woo_Product_Slider.php:2880
msgid "Angle"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1947, ../includes/Elements/Team_Member_Carousel.php:2110, ../includes/Elements/Testimonial_Slider.php:1347, ../includes/Elements/Woo_Product_Slider.php:2881
msgid "Double Angle"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1948, ../includes/Elements/Team_Member_Carousel.php:2111, ../includes/Elements/Testimonial_Slider.php:1348, ../includes/Elements/Woo_Product_Slider.php:2882
msgid "Chevron"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1949, ../includes/Elements/Team_Member_Carousel.php:2112, ../includes/Elements/Testimonial_Slider.php:1349, ../includes/Elements/Woo_Product_Slider.php:2883
msgid "Chevron Circle"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1950, ../includes/Elements/Team_Member_Carousel.php:2113, ../includes/Elements/Testimonial_Slider.php:1350, ../includes/Elements/Woo_Product_Slider.php:2884, ../includes/Extensions/EAEL_Tooltip_Section.php:129
msgid "Arrow"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1951, ../includes/Elements/Team_Member_Carousel.php:2114, ../includes/Elements/Testimonial_Slider.php:1351, ../includes/Elements/Woo_Product_Slider.php:2885
msgid "Long Arrow"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1953, ../includes/Elements/Team_Member_Carousel.php:2116, ../includes/Elements/Testimonial_Slider.php:1353, ../includes/Elements/Woo_Product_Slider.php:2887
msgid "Caret Square"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1954, ../includes/Elements/Team_Member_Carousel.php:2117, ../includes/Elements/Testimonial_Slider.php:1354, ../includes/Elements/Woo_Product_Slider.php:2888
msgid "Arrow Circle"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1955, ../includes/Elements/Team_Member_Carousel.php:2118, ../includes/Elements/Testimonial_Slider.php:1355, ../includes/Elements/Woo_Product_Slider.php:2889
msgid "Arrow Circle O"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1956, ../includes/Elements/Team_Member_Carousel.php:2119, ../includes/Elements/Testimonial_Slider.php:1356, ../includes/Elements/Toggle.php:43, ../includes/Elements/Woo_Product_Slider.php:2890
msgid "Toggle"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1957, ../includes/Elements/Team_Member_Carousel.php:2120, ../includes/Elements/Testimonial_Slider.php:1357, ../includes/Elements/Woo_Product_Slider.php:2891
msgid "Hand"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:1985
msgid "Align Left"
msgstr ""

#: ../includes/Elements/Post_Carousel.php:2004
msgid "Align Right"
msgstr ""

#: ../includes/Elements/Post_List.php:38
msgid "Smart Post List"
msgstr ""

#: ../includes/Elements/Post_List.php:121
msgid "Layout Type"
msgstr ""

#: ../includes/Elements/Post_List.php:134
msgid "Enable Ajax Post Search"
msgstr ""

#: ../includes/Elements/Post_List.php:151
msgid "Show Top Bar"
msgstr ""

#: ../includes/Elements/Post_List.php:163
msgid "Title Text"
msgstr ""

#: ../includes/Elements/Post_List.php:167
msgid "Recent Posts"
msgstr ""

#: ../includes/Elements/Post_List.php:180
msgid "Change All Text"
msgstr ""

#: ../includes/Elements/Post_List.php:197
msgid "Show Category Filter"
msgstr ""

#: ../includes/Elements/Post_List.php:212
msgid "Show Navigation"
msgstr ""

#: ../includes/Elements/Post_List.php:224
msgid "Prev Post Icon"
msgstr ""

#: ../includes/Elements/Post_List.php:240
msgid "Next Post Icon"
msgstr ""

#: ../includes/Elements/Post_List.php:256
msgid "Show Featured Post"
msgstr ""

#: ../includes/Elements/Post_List.php:271
msgid "Scroll to Top"
msgstr ""

#: ../includes/Elements/Post_List.php:272
msgid "Enabling it allows the widget to scroll to top when navigating to the next page through pagination."
msgstr ""

#: ../includes/Elements/Post_List.php:284
msgid "Scroll Top Offset"
msgstr ""

#: ../includes/Elements/Post_List.php:310
msgid "Featured Post Settings"
msgstr ""

#: ../includes/Elements/Post_List.php:321
msgid "Featured Post"
msgstr ""

#: ../includes/Elements/Post_List.php:344
msgid "Featured Post Min Height"
msgstr ""

#: ../includes/Elements/Post_List.php:362
msgid "Equal Post Height"
msgstr ""

#: ../includes/Elements/Post_List.php:374
msgid "Post Min Height"
msgstr ""

#: ../includes/Elements/Post_List.php:398
msgid "Featured Post Width"
msgstr ""

#: ../includes/Elements/Post_List.php:424
msgid "List Area Width"
msgstr ""

#: ../includes/Elements/Post_List.php:450
msgid "Show Meta"
msgstr ""

#: ../includes/Elements/Post_List.php:472
msgid "Show Excerpt"
msgstr ""

#: ../includes/Elements/Post_List.php:498
msgid "List Post Settings"
msgstr ""

#: ../includes/Elements/Post_List.php:505
msgid "Post List Column(s)"
msgstr ""

#: ../includes/Elements/Post_List.php:521, ../includes/Elements/Woo_Cross_Sells.php:127
msgid "4"
msgstr ""

#: ../includes/Elements/Post_List.php:573, ../includes/Elements/Woo_Product_Slider.php:132, ../includes/Extensions/DynamicTags/Woo_Products.php:252
msgid "Last Modified Date"
msgstr ""

#: ../includes/Elements/Post_List.php:589
msgid "Last Modified Position"
msgstr ""

#: ../includes/Elements/Post_List.php:593
msgid "Before Published Date"
msgstr ""

#: ../includes/Elements/Post_List.php:594
msgid "After Published Date"
msgstr ""

#: ../includes/Elements/Post_List.php:595
msgid "Separate Line"
msgstr ""

#: ../includes/Elements/Post_List.php:596
msgid "Replace Published Date"
msgstr ""

#: ../includes/Elements/Post_List.php:608
msgid "Last Modified Date Format"
msgstr ""

#: ../includes/Elements/Post_List.php:612
msgid "Default (WordPress Setting)"
msgstr ""

#: ../includes/Elements/Post_List.php:613
msgid "Relative (e.g., \"2 days ago\")"
msgstr ""

#: ../includes/Elements/Post_List.php:614
msgid "Custom Format"
msgstr ""

#: ../includes/Elements/Post_List.php:626
msgid "Custom Date Format"
msgstr ""

#: ../includes/Elements/Post_List.php:630
msgid "Enter date format (e.g., F j, Y for \"January 1, 2024\"). See <a href=\"https://wordpress.org/support/article/formatting-date-and-time/\" target=\"_blank\">WordPress date formatting</a>."
msgstr ""

#: ../includes/Elements/Post_List.php:645
msgid "Last Modified Prefix Text"
msgstr ""

#: ../includes/Elements/Post_List.php:647, ../includes/Elements/Post_List.php:648
msgid "Updated: "
msgstr ""

#: ../includes/Elements/Post_List.php:675, ../includes/Elements/Woo_Product_Slider.php:530
msgid "Quick view Title Tag"
msgstr ""

#: ../includes/Elements/Post_List.php:831
msgid "Show Category"
msgstr ""

#: ../includes/Elements/Post_List.php:843
msgid "Max Items to Show"
msgstr ""

#: ../includes/Elements/Post_List.php:860
msgid "Items Separator"
msgstr ""

#: ../includes/Elements/Post_List.php:1034
msgid "Post List Style"
msgstr ""

#: ../includes/Elements/Post_List.php:1133
msgid "Topbar Style"
msgstr ""

#: ../includes/Elements/Post_List.php:1158
msgid "Topbar Border"
msgstr ""

#: ../includes/Elements/Post_List.php:1194
msgid "Title Tag Background Color"
msgstr ""

#: ../includes/Elements/Post_List.php:1205
msgid "Title Tag Color"
msgstr ""

#: ../includes/Elements/Post_List.php:1217
msgid "Tag Typography"
msgstr ""

#: ../includes/Elements/Post_List.php:1227
msgid "Category Filter"
msgstr ""

#: ../includes/Elements/Post_List.php:1268
msgid "Active Background Color"
msgstr ""

#: ../includes/Elements/Post_List.php:1314
msgid "Navigation Style"
msgstr ""

#: ../includes/Elements/Post_List.php:1385
msgid "Icon Background Color"
msgstr ""

#: ../includes/Elements/Post_List.php:1397
msgid "Icon Hover Color"
msgstr ""

#: ../includes/Elements/Post_List.php:1411
msgid "Icon Background Hover Color"
msgstr ""

#: ../includes/Elements/Post_List.php:1468
msgid "Featured Post Style"
msgstr ""

#: ../includes/Elements/Post_List.php:1532, ../includes/Elements/Post_List.php:1790
msgid "Title Hover Color"
msgstr ""

#: ../includes/Elements/Post_List.php:1644, ../includes/Elements/Post_List.php:1903
msgid "Meta Color"
msgstr ""

#: ../includes/Elements/Post_List.php:1655, ../includes/Elements/Post_List.php:1914
msgid "Meta Alignment"
msgstr ""

#: ../includes/Elements/Post_List.php:1692
msgid "Post Style"
msgstr ""

#: ../includes/Elements/Post_List.php:1747
msgid "Thumbnail Space"
msgstr ""

#: ../includes/Elements/Post_List.php:2088
msgid "Author Image"
msgstr ""

#: ../includes/Elements/Post_List.php:2227
msgid "Category Style"
msgstr ""

#: ../includes/Elements/Post_List.php:2378
msgid "Search Form Style"
msgstr ""

#: ../includes/Elements/Post_List.php:2389
msgid "Form Width"
msgstr ""

#: ../includes/Elements/Post_List.php:2415
msgid "Search Icon Color"
msgstr ""

#: ../includes/Elements/Post_List.php:2456
msgid "%s ago"
msgstr ""

#: ../includes/Elements/Price_Menu.php:23, ../includes/Elements/Price_Menu.php:71
msgid "Price Menu"
msgstr ""

#: ../includes/Elements/Price_Menu.php:130
msgid "Discount"
msgstr ""

#: ../includes/Elements/Price_Menu.php:143, ../includes/Elements/Price_Menu.php:699
msgid "Original Price"
msgstr ""

#: ../includes/Elements/Price_Menu.php:211
msgid "Menu Item #1"
msgstr ""

#: ../includes/Elements/Price_Menu.php:215
msgid "Menu Item #2"
msgstr ""

#: ../includes/Elements/Price_Menu.php:219
msgid "Menu Item #3"
msgstr ""

#: ../includes/Elements/Price_Menu.php:231
msgid "Menu Style"
msgstr ""

#: ../includes/Elements/Price_Menu.php:235
msgid "EA Style"
msgstr ""

#: ../includes/Elements/Price_Menu.php:239
msgid "Style 4"
msgstr ""

#: ../includes/Elements/Price_Menu.php:247
msgid "Link apply on"
msgstr ""

#: ../includes/Elements/Price_Menu.php:253
msgid "Full Item"
msgstr ""

#: ../includes/Elements/Price_Menu.php:294, ../includes/Elements/Price_Menu.php:941
msgid "Title-Price Connector"
msgstr ""

#: ../includes/Elements/Price_Menu.php:309, ../includes/Elements/Price_Menu.php:502
msgid "Title Separator"
msgstr ""

#: ../includes/Elements/Price_Menu.php:330
msgid "Menu Items"
msgstr ""

#: ../includes/Elements/Price_Menu.php:350
msgid "Items Spacing"
msgstr ""

#: ../includes/Elements/Price_Menu.php:481, ../includes/Elements/Price_Menu.php:601, ../includes/Elements/Price_Menu.php:781, ../includes/Elements/Team_Member_Carousel.php:1266, ../includes/Elements/Team_Member_Carousel.php:1330, ../includes/Elements/Team_Member_Carousel.php:1467, ../includes/Elements/Team_Member_Carousel.php:1534, ../includes/Elements/Team_Member_Carousel.php:1671, ../includes/Elements/Team_Member_Carousel.php:1738, ../includes/Elements/Team_Member_Carousel.php:1875, ../includes/Elements/Toggle.php:390
msgid "Margin Bottom"
msgstr ""

#: ../includes/Elements/Price_Menu.php:513, ../includes/Elements/Stacked_Cards.php:1426
msgid "Border Type"
msgstr ""

#: ../includes/Elements/Price_Menu.php:535
msgid "Border Height"
msgstr ""

#: ../includes/Elements/Price_Menu.php:630
msgid "Price Badge"
msgstr ""

#: ../includes/Elements/Price_Menu.php:708
msgid "Strikethrough"
msgstr ""

#: ../includes/Elements/Price_Menu.php:723
msgid "Original Price Color"
msgstr ""

#: ../includes/Elements/Price_Menu.php:736
msgid "Original Price Typography"
msgstr ""

#: ../includes/Elements/Price_Menu.php:905, ../includes/Elements/Woo_Product_Slider.php:1099, ../includes/Extensions/EAEL_Parallax_Section.php:191
msgid "Vertical Position"
msgstr ""

#: ../includes/Elements/Price_Menu.php:1026
msgid "Divider Weight"
msgstr ""

#: ../includes/Elements/Protected_Content.php:26, ../includes/Elements/Protected_Content.php:65, ../includes/Elements/Protected_Content.php:85
msgid "Protected Content"
msgstr ""

#: ../includes/Elements/Protected_Content.php:91
msgid "This is the content that you want to be protected by either role or password."
msgstr ""

#: ../includes/Elements/Protected_Content.php:120, ../includes/Elements/Protected_Content.php:127, ../includes/Extensions/Content_Protection.php:53
msgid "Protection Type"
msgstr ""

#: ../includes/Elements/Protected_Content.php:131, ../includes/Extensions/Content_Protection.php:57
msgid "User role"
msgstr ""

#: ../includes/Elements/Protected_Content.php:132, ../includes/Extensions/Content_Protection.php:58
msgid "Password protected"
msgstr ""

#: ../includes/Elements/Protected_Content.php:141, ../includes/Extensions/Content_Protection.php:70
msgid "Select Roles"
msgstr ""

#: ../includes/Elements/Protected_Content.php:155
msgid "Show Preview of Error Message"
msgstr ""

#: ../includes/Elements/Protected_Content.php:171, ../includes/Extensions/Content_Protection.php:85
msgid "Set Password"
msgstr ""

#: ../includes/Elements/Protected_Content.php:187, ../includes/Extensions/Content_Protection.php:102
msgid "Input Placehlder"
msgstr ""

#: ../includes/Elements/Protected_Content.php:203, ../includes/Extensions/Content_Protection.php:119
msgid "Submit Button Text"
msgstr ""

#: ../includes/Elements/Protected_Content.php:235, ../includes/Extensions/Content_Protection.php:136
msgid "Scroll to Section"
msgstr ""

#: ../includes/Elements/Protected_Content.php:250, ../includes/Extensions/Content_Protection.php:152
msgid "Remember Cookie"
msgstr ""

#: ../includes/Elements/Protected_Content.php:265, ../includes/Extensions/Content_Protection.php:168
msgid "Expire Time"
msgstr ""

#: ../includes/Elements/Protected_Content.php:269, ../includes/Extensions/Content_Protection.php:172
msgid "Cookie expiration time (Minutes)"
msgstr ""

#: ../includes/Elements/Protected_Content.php:284, ../includes/Elements/Protected_Content.php:297, ../includes/Elements/Protected_Content.php:345, ../includes/Elements/Protected_Content.php:434, ../includes/Elements/Woo_Thank_You.php:265, ../includes/Elements/Woo_Thank_You.php:1499, ../includes/Extensions/Content_Protection.php:192, ../includes/Extensions/Content_Protection.php:205, ../includes/Extensions/Content_Protection.php:252
msgid "Message"
msgstr ""

#: ../includes/Elements/Protected_Content.php:291, ../includes/Extensions/Content_Protection.php:199
msgid "Message Type"
msgstr ""

#: ../includes/Elements/Protected_Content.php:294, ../includes/Extensions/Content_Protection.php:202
msgid "Set a message or a saved template when the content is protected."
msgstr ""

#: ../includes/Elements/Protected_Content.php:307, ../includes/Extensions/Content_Protection.php:215
msgid "Public Text"
msgstr ""

#: ../includes/Elements/Protected_Content.php:309, ../includes/Extensions/Content_Protection.php:217
msgid "You do not have permission to see this content."
msgstr ""

#: ../includes/Elements/Protected_Content.php:336, ../includes/Extensions/Content_Protection.php:243
msgid "Incorrect Password"
msgstr ""

#: ../includes/Elements/Protected_Content.php:347, ../includes/Extensions/Content_Protection.php:254
msgid "Password does not match."
msgstr ""

#: ../includes/Elements/Protected_Content.php:442, ../includes/Extensions/Content_Protection.php:277
msgid "Permission Message"
msgstr ""

#: ../includes/Elements/Protected_Content.php:522, ../includes/Extensions/Content_Protection.php:360
msgid "Error Message"
msgstr ""

#: ../includes/Elements/Protected_Content.php:606, ../includes/Extensions/Content_Protection.php:446
msgid "Password Field"
msgstr ""

#: ../includes/Elements/Protected_Content.php:634, ../includes/Extensions/Content_Protection.php:478
msgid "Input Alignment"
msgstr ""

#: ../includes/Elements/Protected_Content.php:821, ../includes/Traits/Extender.php:627
msgid "Button Padding"
msgstr ""

#: ../includes/Elements/Protected_Content.php:833
msgid "Button Margin"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:25
msgid "360 Degree Photo Viewer"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:93
msgid "Caption"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:108, ../includes/Elements/Sphere_Photo_Viewer.php:109
msgid "Enter Your Caption Here"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:136
msgid "Add a brief overview or interesting facts about the image here. This is a placeholder description. Replace with details about the location, key features, or any relevant information for viewers."
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:157
msgid "Photo Viewer Height"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:184
msgid "Initial Zoom Level"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:202
msgid "Fisheye Effect"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:214
msgid "Enable Auto-Rotation"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:227
msgid "Auto-Rotation Delay (ms)"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:248
msgid "Auto-Rotation Speed"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:269
msgid "Auto-Rotation Pitch"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:290
msgid "Pan Correction"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:309
msgid "Tilt Correction"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:327
msgid "Roll Axis Correction"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:354
msgid "Show Markers"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:372, ../includes/Elements/Sphere_Photo_Viewer.php:373
msgid "Enter Marker Title Here"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:386, ../includes/Elements/Sphere_Photo_Viewer.php:465
msgid "Detailed content goes here. Provide insights or interesting facts about this marker point."
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:387
msgid "Brief Description for Tooltip (e.g., Click for more details!)"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:395, ../includes/Elements/Sphere_Photo_Viewer.php:640
msgid "X Position"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:414, ../includes/Elements/Sphere_Photo_Viewer.php:661
msgid "Y Position"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:447
msgid "Image Dimension"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:449
msgid "Crop the original image size to any custom size. Set custom width or height to keep the original size ratio."
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:460
msgid "Marker Pointers"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:464
msgid "Marker 1"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:481, ../includes/Elements/Sphere_Photo_Viewer.php:579
msgid "Navigation Bar"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:490
msgid "Navigator type"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:494
msgid "Select Type"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:495
msgid "autorotate"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:497
msgid "zoomRange"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:499
msgid "zoom"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:500
msgid "moveLeft"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:501
msgid "moveRight"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:502
msgid "moveUp"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:503
msgid "moveDown"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:504
msgid "move"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:505
msgid "download"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:506
msgid "description"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:507
msgid "caption"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:508
msgid "fullscreen"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:509
msgid "markers"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:510
msgid "markersList"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:587, ../includes/Elements/Testimonial_Slider.php:374
msgid "Visibility"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:744
msgid "Icon Background"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:761
msgid "Active Icon Background"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:867
msgid "Hover Scale"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:879, ../includes/Elements/Stacked_Cards.php:586, ../includes/Elements/Stacked_Cards.php:632, ../includes/Extensions/EAEL_Tooltip_Section.php:115, ../includes/Extensions/Smooth_Animation.php:382
msgid "Scale"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:897, ../includes/Extensions/EAEL_Tooltip_Section.php:200, ../includes/Extensions/Smooth_Animation.php:706
msgid "Duration"
msgstr ""

#: ../includes/Elements/Sphere_Photo_Viewer.php:917
msgid "Panel Content"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:20
msgid "Stacked Cards"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:65
msgid "Card Items"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:89
msgid "Card Item"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:150, ../includes/Elements/Stacked_Cards.php:318
msgid "Choose Type"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:218
msgid "Choose Video"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:231, ../includes/Elements/Stacked_Cards.php:391
msgid "Choose Icon"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:409
msgid "Insert your own content to showcase or highlight the feature you want to represent. Add any blocks you want to customize it as per your preference."
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:420
msgid "Show Button"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:492
msgid "Hover Text Color"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:504
msgid "Hover Background Color"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:534
msgid "Card Item #1"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:538
msgid "Card Item #2"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:542
msgid "Card Item #3"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:579
msgid "Transform"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:584, ../includes/Extensions/Smooth_Animation.php:208
msgid "Rotate"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:585, ../includes/Elements/Stacked_Cards.php:613
msgid "Translate"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:669
msgid "Filter"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:675, ../includes/Elements/Stacked_Cards.php:687, ../includes/Elements/Woo_Collections.php:466
msgid "Blur"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:678, ../includes/Elements/Stacked_Cards.php:753
msgid "Sepia"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:775
msgid "Card Direction From (Bottom/Top)"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:777
msgid "Set the starting point of the card. When we select plus (+) value the card will go from bottom to top, When select minus (-) value it will go top to bottom"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:798
msgid "Card Start From"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:811
msgid "From where stacked card animation will start in pixels"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:818
msgid "Card End From"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:829
msgid "To where stacked card animation will end"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:836
msgid "Custom End Value"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:853
msgid "The ScrollTrigger's ending scroll position in pixels"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:860
msgid "End Value"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:880
msgid "Show Marker"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:886
msgid "Show marker during development to see where card animation start and end"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:921
msgid "Section Width"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:944, ../includes/Traits/Extender.php:2958
msgid "Gap"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:1060
msgid "Icon Alignment"
msgstr ""

#: ../includes/Elements/Stacked_Cards.php:1091, ../includes/Elements/Woo_Account_Dashboard.php:508, ../includes/Elements/Woo_Thank_You.php:1631
msgid "Container"
msgstr ""

#: ../includes/Elements/Static_Product.php:30
msgid "Static Product"
msgstr ""

#: ../includes/Elements/Static_Product.php:95, ../includes/Elements/Twitter_Feed_Carousel.php:537, ../includes/Elements/Woo_Collections.php:220
msgid "Default Style"
msgstr ""

#: ../includes/Elements/Static_Product.php:96
msgid "Cart Button On Hover Image"
msgstr ""

#: ../includes/Elements/Static_Product.php:97
msgid "All Content On Hover Image"
msgstr ""

#: ../includes/Elements/Static_Product.php:106
msgid "Goto <strong>product details</strong> panel and active add to cart button"
msgstr ""

#: ../includes/Elements/Static_Product.php:121, ../includes/Elements/Woo_Cross_Sells.php:765
msgid "Product Details"
msgstr ""

#: ../includes/Elements/Static_Product.php:128
msgid "Show Details Button?"
msgstr ""

#: ../includes/Elements/Static_Product.php:138
msgid "Show Add To Cart Button?"
msgstr ""

#: ../includes/Elements/Static_Product.php:149, ../includes/Elements/Woo_Thank_You.php:2428
msgid "Product Image"
msgstr ""

#: ../includes/Elements/Static_Product.php:163
msgid "Product Heading"
msgstr ""

#: ../includes/Elements/Static_Product.php:166, ../includes/Elements/Woo_Thank_You.php:2521
msgid "Product Name"
msgstr ""

#: ../includes/Elements/Static_Product.php:167, ../includes/Elements/Static_Product.php:168
msgid "Enter heading for the product"
msgstr ""

#: ../includes/Elements/Static_Product.php:179, ../includes/Elements/Woo_Product_Slider.php:1426
msgid "Product Description"
msgstr ""

#: ../includes/Elements/Static_Product.php:187
msgid "Show Price"
msgstr ""

#: ../includes/Elements/Static_Product.php:205
msgid "$77.5"
msgstr ""

#: ../includes/Elements/Static_Product.php:206
msgid "Type Your Price"
msgstr ""

#: ../includes/Elements/Static_Product.php:219
msgid "Show Rating"
msgstr ""

#: ../includes/Elements/Static_Product.php:229
msgid "Review"
msgstr ""

#: ../includes/Elements/Static_Product.php:234
msgid "(4.5 REVIEWS)"
msgstr ""

#: ../includes/Elements/Static_Product.php:235
msgid "Type Your Reviews"
msgstr ""

#: ../includes/Elements/Static_Product.php:248
msgid "Links & Buttons"
msgstr ""

#: ../includes/Elements/Static_Product.php:257
msgid "Product Link URL"
msgstr ""

#: ../includes/Elements/Static_Product.php:268
msgid "Enter link URL for the promo"
msgstr ""

#: ../includes/Elements/Static_Product.php:269
msgid "Enter URL for the product"
msgstr ""

#: ../includes/Elements/Static_Product.php:281, ../includes/Elements/Static_Product.php:356
msgid "_blank"
msgstr ""

#: ../includes/Elements/Static_Product.php:282, ../includes/Elements/Static_Product.php:357
msgid "_self"
msgstr ""

#: ../includes/Elements/Static_Product.php:290
msgid "Live Demo URL"
msgstr ""

#: ../includes/Elements/Static_Product.php:301
msgid "Enter link URL for live demo"
msgstr ""

#: ../includes/Elements/Static_Product.php:302
msgid "Enter URL for the promo"
msgstr ""

#: ../includes/Elements/Static_Product.php:312
msgid "Show Live Demo Icon?"
msgstr ""

#: ../includes/Elements/Static_Product.php:323
msgid "Live Demo Icon"
msgstr ""

#: ../includes/Elements/Static_Product.php:338
msgid "Live Demo Text"
msgstr ""

#: ../includes/Elements/Static_Product.php:341
msgid "Live Demo"
msgstr ""

#: ../includes/Elements/Static_Product.php:384
msgid "View Details"
msgstr ""

#: ../includes/Elements/Static_Product.php:412, ../includes/Elements/Static_Product.php:1354
msgid "Add To Cart Button"
msgstr ""

#: ../includes/Elements/Static_Product.php:423, ../includes/Elements/Woo_Product_Slider.php:127, ../includes/Extensions/DynamicTags/Woo_Products.php:247
msgid "Product ID"
msgstr ""

#: ../includes/Elements/Static_Product.php:424
msgid "add product id to generate add to cart url"
msgstr ""

#: ../includes/Elements/Static_Product.php:441
msgid "Add To Cart"
msgstr ""

#: ../includes/Elements/Static_Product.php:462
msgid "Product Style"
msgstr ""

#: ../includes/Elements/Static_Product.php:538, ../includes/Traits/Extender.php:580
msgid "Button Alignment"
msgstr ""

#: ../includes/Elements/Static_Product.php:569
msgid "Content Padding"
msgstr ""

#: ../includes/Elements/Static_Product.php:615
msgid "Price & Rating Style"
msgstr ""

#: ../includes/Elements/Static_Product.php:624
msgid "Price & Rating Box Margin"
msgstr ""

#: ../includes/Elements/Static_Product.php:635
msgid "Space Between Price And Rating"
msgstr ""

#: ../includes/Elements/Static_Product.php:663
msgid "Price Typography"
msgstr ""

#: ../includes/Elements/Static_Product.php:675
msgid "Reviews Typography"
msgstr ""

#: ../includes/Elements/Static_Product.php:686, ../includes/Elements/Woo_Product_Slider.php:1920
msgid "Price Color"
msgstr ""

#: ../includes/Elements/Static_Product.php:697
msgid "Reviews Color"
msgstr ""

#: ../includes/Elements/Static_Product.php:745
msgid "Product Thumbnail Overlay"
msgstr ""

#: ../includes/Elements/Static_Product.php:753, ../includes/Elements/Static_Product.php:1013
msgid "Gradient Background?"
msgstr ""

#: ../includes/Elements/Static_Product.php:765
msgid "background"
msgstr ""

#: ../includes/Elements/Static_Product.php:766
msgid "Product Thumbnail Overlay Color"
msgstr ""

#: ../includes/Elements/Static_Product.php:778
msgid "Overlay Position"
msgstr ""

#: ../includes/Elements/Static_Product.php:827
msgid "Product Thumbnail Overlay link Text/Icon"
msgstr ""

#: ../includes/Elements/Static_Product.php:962, ../includes/Elements/Woo_Product_Slider.php:128, ../includes/Elements/Woo_Product_Slider.php:1311, ../includes/Extensions/DynamicTags/Woo_Products.php:248
msgid "Product Title"
msgstr ""

#: ../includes/Elements/Static_Product.php:993
msgid "Product Content"
msgstr ""

#: ../includes/Elements/Static_Product.php:1201, ../includes/Elements/Static_Product.php:1468
msgid "Button Gradient Background"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:50
msgid "Team Member Carousel"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:149, ../includes/Elements/Team_Member_Carousel.php:671, ../includes/Elements/Team_Member_Carousel.php:1298, ../includes/Elements/Woo_Account_Dashboard.php:212, ../includes/Elements/Woo_Account_Dashboard.php:428, ../includes/Elements/Woo_Account_Dashboard.php:473, ../includes/Elements/Woo_Account_Dashboard.php:1034, ../includes/Elements/Woo_Thank_You.php:197, ../includes/Elements/Woo_Thank_You.php:846, ../includes/Extensions/DynamicTags/Terms.php:118
msgid "Name"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:154, ../includes/Elements/Testimonial_Slider.php:547
msgid "John Doe"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:169
msgid "WordPress Developer"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:184
msgid "Enter member description here which describes the position of member in company"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:210
msgid "Link image with custom URL"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:217
msgid "Social Links"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:226
msgid "Mail Address"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:234
msgid "Enter Email Address of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:244
msgid "Facebook"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:252
msgid "Enter Facebook page or profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:262
msgid "Twitter"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:270
msgid "Enter Twitter profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:280
msgid "Google+"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:288
msgid "Enter Google+ profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:298
msgid "Linkedin"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:306
msgid "Enter Linkedin profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:316
msgid "Instagram"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:324
msgid "Enter Instagram profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:334
msgid "YouTube"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:342
msgid "Enter YouTube profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:352
msgid "Pinterest"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:360
msgid "Enter Pinterest profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:371
msgid "Dribbble"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:379
msgid "Enter Dribbble profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:390
msgid "XING"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:398
msgid "Enter XING profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:409
msgid "Snapchat"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:417
msgid "Enter Snapchat profile URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:428
msgid "Custom URL"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:436
msgid "Enter Custom URL of team member"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:511, ../includes/Elements/Team_Member_Carousel.php:627, ../includes/Elements/Team_Member_Carousel.php:1910
msgid "Social Icons"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:512
msgid "Description + Social Icons"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:513
msgid "All Content"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:529
msgid "Name HTML Tag"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:578
msgid "Position HTML Tag"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:650
msgid "Before Description"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:651
msgid "After Description"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:662
msgid "Divider shows after"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:706, ../includes/Elements/Woo_Product_Slider.php:568
msgid "Slider Settings"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:820
msgid "Pause slider when hover on slider area."
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:823, ../includes/Extensions/Smooth_Animation.php:1155, ../includes/Extensions/Smooth_Animation.php:1178, ../includes/Extensions/Smooth_Animation.php:1201, ../includes/Extensions/Smooth_Animation.php:1224
msgid "Pause"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:824, ../includes/Extensions/Smooth_Animation.php:1154, ../includes/Extensions/Smooth_Animation.php:1177, ../includes/Extensions/Smooth_Animation.php:1200, ../includes/Extensions/Smooth_Animation.php:1223
msgid "Play"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:916
msgid "Box Style"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:1387, ../includes/Elements/Team_Member_Carousel.php:1591, ../includes/Elements/Team_Member_Carousel.php:1795
msgid "Divider Style"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:1408, ../includes/Elements/Team_Member_Carousel.php:1612, ../includes/Elements/Team_Member_Carousel.php:1816, ../includes/Traits/Extender.php:4347
msgid "Divider Width"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:1438, ../includes/Elements/Team_Member_Carousel.php:1642, ../includes/Elements/Team_Member_Carousel.php:1846, ../includes/Traits/Extender.php:4378
msgid "Divider Height"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:1918
msgid "Icons Gap"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:1978, ../includes/Elements/Team_Member_Carousel.php:2048
msgid "Icons Color"
msgstr ""

#: ../includes/Elements/Team_Member_Carousel.php:2104, ../includes/Elements/Woo_Product_Slider.php:2875
msgid "Choose Arrow"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:29
msgid "Testimonial Slider"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:145
msgid "Slide Items"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:159
msgid "Number of items slides at once."
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:190
msgid "Sliding Speed"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:336
msgid "Outside of the box"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:360, ../includes/Elements/Woo_Product_Slider.php:736
msgid "Image Dots"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:378
msgid "Image Dots Visibility Device wise"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:412
msgid "Classic"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:416
msgid "Content | Icon/Image | Bio"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:420
msgid "Icon/Image | Content"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:424
msgid "Content | Icon/Image"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:428
msgid "Content Top | Icon Title Inline"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:432
msgid "Content Bottom | Icon Title Inline"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:479
msgid "Word Count"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:545, ../includes/Elements/Testimonial_Slider.php:954
msgid "User Name"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:559, ../includes/Elements/Testimonial_Slider.php:993
msgid "Company Name"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:561
msgid "Codetic"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:574
msgid "Add testimonial description here. Edit and place your own text."
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:581
msgid "Rating"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:638
msgid "Item"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:646, ../includes/Elements/Testimonial_Slider.php:673, ../includes/Elements/Testimonial_Slider.php:1180
msgid "Gradient Background"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:692
msgid "Hover Background Type"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:701
msgid "User & Company line break"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:805
msgid "Width Adjustment"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:918
msgid "Rounded Avatar?"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:1087
msgid "Quotation Mark"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:1424
msgid "Left Arrow"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:1443
msgid "Right Arrow"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:1768, ../includes/Elements/Woo_Product_Slider.php:2742
msgid "Images Dots"
msgstr ""

#: ../includes/Elements/Testimonial_Slider.php:1884
msgid "Quote Position"
msgstr ""

#: ../includes/Elements/Toggle.php:125, ../includes/Elements/Toggle.php:419, ../includes/Elements/Toggle.php:580, ../includes/Traits/Extender.php:358, ../includes/Traits/Extender.php:519
msgid "Primary"
msgstr ""

#: ../includes/Elements/Toggle.php:135
msgid "Annual"
msgstr ""

#: ../includes/Elements/Toggle.php:175
msgid "Primary Content"
msgstr ""

#: ../includes/Elements/Toggle.php:207, ../includes/Elements/Toggle.php:460, ../includes/Elements/Toggle.php:626, ../includes/Traits/Extender.php:454, ../includes/Traits/Extender.php:561
msgid "Secondary"
msgstr ""

#: ../includes/Elements/Toggle.php:217
msgid "Lifetime"
msgstr ""

#: ../includes/Elements/Toggle.php:257
msgid "Secondary Content"
msgstr ""

#: ../includes/Elements/Toggle.php:289
msgid "Switch"
msgstr ""

#: ../includes/Elements/Toggle.php:322
msgid "Switch Style"
msgstr ""

#: ../includes/Elements/Toggle.php:325, ../includes/Extensions/EAEL_Tooltip_Section.php:150
msgid "Round"
msgstr ""

#: ../includes/Elements/Toggle.php:326
msgid "Rectangle"
msgstr ""

#: ../includes/Elements/Toggle.php:335
msgid "Switch Size"
msgstr ""

#: ../includes/Elements/Toggle.php:363
msgid "Headings Spacing"
msgstr ""

#: ../includes/Elements/Toggle.php:503
msgid "Controller"
msgstr ""

#: ../includes/Elements/Toggle.php:599, ../includes/Elements/Toggle.php:645
msgid "Active Text Color"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:30
msgid "X (Twitter) Feed Carousel"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:85
msgid "Account Settings"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:92
msgid "Twitter API V2"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:104
msgid "Account Name"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:108
msgid "Use @ sign with your account name."
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:119
msgid "Hashtag Name"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:122
msgid "Remove # sign from your hashtag name."
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:133
msgid "Consumer Key"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:150
msgid "Consumer Secret"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:167
msgid "Bearer Token"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:184
msgid "Auto Cache Clear"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:210
msgid "Clear Cache"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:212
msgid "Clear"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:214
msgid "Note: This will refresh your feed and fetch the latest data from your Twitter account"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:233
msgid "Content Length"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:255
msgid "Show Replies"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:269
msgid "Card Settings"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:276
msgid "Show Avatar"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:288
msgid "Avatar Style"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:304
msgid "Show Date"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:328
msgid "Show Icon"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:340
msgid "Show Media"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:533
msgid "Choose Style"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:538
msgid "Style Two (right icon)"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:539
msgid "Style Three"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:547
msgid "Left Icon Alignment"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:588
msgid "Main Card Padding"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:651
msgid "Left Icon Area"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:684
msgid "Right Content Area"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:742
msgid "Card Hover Style"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:903
msgid "Link Style"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:960
msgid "Choose Left Arrow"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:973
msgid "Choose Right Arrow"
msgstr ""

#: ../includes/Elements/Twitter_Feed_Carousel.php:1007
msgid "Left & Right Arrow Position From Top"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:33
msgid "Woo Account Dashboard"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:81, ../includes/Elements/Woo_Account_Dashboard.php:353, ../includes/Elements/Woo_Account_Dashboard.php:354
msgid "Dashboard"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:82, ../includes/Elements/Woo_Account_Dashboard.php:358, ../includes/Elements/Woo_Account_Dashboard.php:359
msgid "Orders"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:83, ../includes/Elements/Woo_Account_Dashboard.php:363, ../includes/Elements/Woo_Account_Dashboard.php:364, ../includes/Elements/Woo_Thank_You.php:630
msgid "Downloads"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:84, ../includes/Elements/Woo_Account_Dashboard.php:368, ../includes/Elements/Woo_Account_Dashboard.php:369, ../includes/Elements/Woo_Account_Dashboard.php:2553
msgid "Addresses"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:85, ../includes/Elements/Woo_Account_Dashboard.php:373, ../includes/Elements/Woo_Account_Dashboard.php:374, ../includes/Elements/Woo_Account_Dashboard.php:2703
msgid "Account Details"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:86, ../includes/Elements/Woo_Account_Dashboard.php:378, ../includes/Elements/Woo_Account_Dashboard.php:379
msgid "Logout"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:147, ../includes/Elements/Woo_Collections.php:83, ../includes/Elements/Woo_Cross_Sells.php:83, ../includes/Elements/Woo_Product_Slider.php:324, ../includes/Elements/Woo_Thank_You.php:117, ../includes/Extensions/Conditional_Display.php:1862, ../includes/Extensions/DynamicTags/Woo_Products.php:237
msgid "<strong>WooCommerce</strong> is not installed/activated on your site. Please install and activate <a href=\"plugin-install.php?s=woocommerce&tab=search&type=term\" target=\"_blank\">WooCommerce</a> first."
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:183, ../includes/Elements/Woo_Account_Dashboard.php:591, ../includes/Traits/Extender.php:2451
msgid "Tabs"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:214
msgid "Custom Tab"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:227, ../includes/Elements/Woo_Account_Dashboard.php:241
msgid "Tab"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:277, ../includes/Extensions/Conditional_Display.php:318
msgid "Template"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:295, ../includes/Extensions/Conditional_Display.php:340
msgid "Type your content here"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:335
msgid "Note: By default, only the last order is displayed while editing the orders section."
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:390, ../includes/Elements/Woo_Account_Dashboard.php:1000
msgid "Account Profile"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:413, ../includes/Elements/Woo_Account_Dashboard.php:458, ../includes/Elements/Woo_Account_Dashboard.php:1068
msgid "Greeting"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:488
msgid "Greeting Text"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:493, ../includes/Elements/Woo_Account_Dashboard.php:2872, ../includes/Elements/Woo_Thank_You.php:181, ../includes/Elements/Woo_Thank_You.php:183
msgid "Hello"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:600
msgid "Tabs Container"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:702
msgid "Tab Item"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:930
msgid "Highlight Line"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:947
msgid "Tab Icon"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1120
msgid "Content Container"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1240, ../includes/Elements/Woo_Cross_Sells.php:300, ../includes/Elements/Woo_Cross_Sells.php:312, ../includes/Elements/Woo_Cross_Sells.php:486
msgid "Heading"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1295
msgid "Paragraph"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1534, ../includes/Elements/Woo_Thank_You.php:1883, ../includes/Elements/Woo_Thank_You.php:2252
msgid "Table"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1764
msgid "Order Status"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1785
msgid "On Hold"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1797
msgid "Processing"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1810
msgid "Table Header"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1819, ../includes/Elements/Woo_Account_Dashboard.php:1955, ../includes/Elements/Woo_Account_Dashboard.php:2079
msgid "Column"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1914, ../includes/Elements/Woo_Account_Dashboard.php:2038, ../includes/Elements/Woo_Account_Dashboard.php:2129
msgid "First Column"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1930, ../includes/Elements/Woo_Account_Dashboard.php:2054, ../includes/Elements/Woo_Account_Dashboard.php:2145
msgid "Last Column"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:1946
msgid "Table Body"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:2070
msgid "Table Footer"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:2161
msgid "Last Row"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:2227
msgid "Form"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:2319
msgid "Input"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:2544, ../includes/Extensions/DynamicTags/Posts.php:66
msgid "Pages"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:2663
msgid "Billing/Shipping"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:2857
msgid "My Account"
msgstr ""

#: ../includes/Elements/Woo_Account_Dashboard.php:2859, ../includes/Traits/Extender.php:2367, ../includes/Traits/Extender.php:2372
msgid "Login"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:27
msgid "Woo Product Collections"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:92
msgid "Collection Type"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:99
msgid "Attributes"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:120
msgid "Tag"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:133
msgid "Attribute"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:161, ../includes/Elements/Woo_Collections.php:528
msgid "Subtitle"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:166
msgid "Collections"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:177
msgid "Show Badge"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:188
msgid "Badge Label"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:190
msgid "Sale"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:191
msgid "Type your lable here"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:221
msgid "Style Two"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:263
msgid "Overlay Padding"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:337
msgid "Overlay Spacing"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:352
msgid "Overlay Background"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:367
msgid "Overlay Background Hover"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:394
msgid "Horizontal Align"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:410
msgid "Vertical Align"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:458
msgid "Image Hover Effect"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:465, ../includes/Skins/Skin_Default.php:123, ../includes/Skins/Skin_Five.php:141, ../includes/Skins/Skin_Four.php:123, ../includes/Skins/Skin_One.php:123, ../includes/Skins/Skin_Seven.php:123, ../includes/Skins/Skin_Six.php:123, ../includes/Skins/Skin_Three.php:141, ../includes/Skins/Skin_Two.php:140
msgid "ZoomOut"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:513
msgid "Title Color Hover"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:540
msgid "Subtitle Color"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:552
msgid "Subtitle Color Hover"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:571
msgid "Badge"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:643
msgid "Collection Name"
msgstr ""

#: ../includes/Elements/Woo_Collections.php:662, ../includes/Elements/Woo_Collections.php:663
msgid "%s"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:29
msgid "Woo Cross Sells"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:128
msgid "5"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:129
msgid "6"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:171, ../includes/Extensions/DynamicTags/Terms.php:84
msgid "Ascending"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:172, ../includes/Extensions/DynamicTags/Terms.php:85
msgid "Descending"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:178, ../includes/Elements/Woo_Product_Slider.php:827
msgid "Products Count"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:198
msgid "Custom Image Area?"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:212, ../includes/Extensions/EAEL_Parallax_Section.php:207
msgid "Contain"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:293
msgid "Card Components"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:315
msgid "You may be interested in…"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:394, ../includes/Elements/Woo_Cross_Sells.php:397
msgid "View Product"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:410, ../includes/Elements/Woo_Cross_Sells.php:413
msgid "Add to Cart"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:441
msgid "Horizontal Gap"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:466
msgid "Vertical Gap"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:497
msgid "Heading Tag"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:609
msgid "Single Item"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:711
msgid "Thumbnail Width"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:853
msgid "Regular Price Color"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:864, ../includes/Elements/Woo_Product_Slider.php:1368, ../includes/Elements/Woo_Product_Slider.php:1932
msgid "Sale Price Color"
msgstr ""

#: ../includes/Elements/Woo_Cross_Sells.php:1130
msgid "To view the <strong>Woo Cross Sells</strong>, you must add products to the cart that has cross-selling items with it."
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:63
msgid "Woo Product Slider"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:130, ../includes/Elements/Woo_Product_Slider.php:2214, ../includes/Extensions/DynamicTags/Woo_Products.php:250
msgid "SKU"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:133, ../includes/Extensions/DynamicTags/Woo_Products.php:253
msgid "Parent Id"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:141, ../includes/Extensions/DynamicTags/Woo_Products.php:262
msgid "Recent Products"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:142, ../includes/Extensions/DynamicTags/Woo_Products.php:263
msgid "Featured Products"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:143, ../includes/Extensions/DynamicTags/Woo_Products.php:264
msgid "Best Selling Products"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:144, ../includes/Extensions/DynamicTags/Woo_Products.php:265
msgid "Sale Products"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:145, ../includes/Extensions/DynamicTags/Woo_Products.php:266
msgid "Top Rated Products"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:146
msgid "Related Products"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:399
msgid "Show Product Rating?"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:408
msgid "Show Product Price?"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:417
msgid "Short Description?"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:467
msgid "Image Stretch"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:479
msgid "Show Post Terms"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:509
msgid "Products Not Found"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:520
msgid "Show Quick view?"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:553
msgid "Image Clickable?"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:575
msgid "Enable Content Effect"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:587
msgid "Content Effect"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:593
msgid "FadeInUp"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:594
msgid "FadeInDown"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:595
msgid "FadeInLeft"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:596
msgid "FadeInRight"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:597
msgid "SlideInUp"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:598
msgid "SlideInDown"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:751
msgid "Image Dots Visibility"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:785
msgid "Query"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:789, ../includes/Extensions/DynamicTags/Woo_Products.php:77
msgid "Filter By"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:799
msgid "This filter will only affect in <strong>Single Product</strong> page of <strong>Elementor Theme Builder</strong> dynamically."
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:847
msgid "Select Products"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:860, ../includes/Extensions/Conditional_Display.php:1876
msgid "Purchase Type"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:862
msgid "For logged in users only!"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:864
msgid "Both"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:865
msgid "Purchased Only"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:866
msgid "Not Purchased Only"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:906
msgid "Sale / Stock Out Badge"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:913
msgid "Style Preset"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:916
msgid "Select Preset"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:917, ../includes/Elements/Woo_Product_Slider.php:2433, ../includes/Template/Woo-Account-Dashboard/preset-1.php:3, ../includes/Template/Woo-Product-Slider/preset-1.php:4, ../includes/Template/Woo-Thank-You/perest-1.php:4
msgid "Preset 1"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:918, ../includes/Elements/Woo_Product_Slider.php:2434, ../includes/Template/Post-List/preset-2.php:3, ../includes/Template/Woo-Account-Dashboard/preset-2.php:3, ../includes/Template/Woo-Product-Slider/preset-2.php:4, ../includes/Template/Woo-Thank-You/preset-2.php:4
msgid "Preset 2"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:919, ../includes/Elements/Woo_Product_Slider.php:2435, ../includes/Template/Post-List/preset-3.php:3, ../includes/Template/Woo-Account-Dashboard/preset-3.php:3, ../includes/Template/Woo-Product-Slider/preset-3.php:4, ../includes/Template/Woo-Thank-You/preset-3.php:4
msgid "Preset 3"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:920, ../includes/Elements/Woo_Product_Slider.php:2436, ../includes/Template/Woo-Product-Slider/preset-4.php:4
msgid "Preset 4"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:958
msgid "Horizontal (%)"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:975
msgid "Vertical (%)"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:992
msgid "Sale Text"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1004
msgid "Stock Out Text"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1019
msgid "Products"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1061
msgid "Reverse column"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1319
msgid "Product Title Color"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1339, ../includes/Elements/Woo_Thank_You.php:932, ../includes/Elements/Woo_Thank_You.php:2606
msgid "Product Price"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1347
msgid "Product Price Color"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1388
msgid "Star Rating"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1396
msgid "Rating Color"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1463
msgid "Sale Badge"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1471
msgid "Sale Badge Color"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1483
msgid "Sale Badge Background"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1505
msgid "Stock Out Badge"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1513
msgid "Stock Out Badge Color"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1525
msgid "Stock Out Badge Background"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1593
msgid "Icons Size"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1726, ../includes/Elements/Woo_Product_Slider.php:2095
msgid "Cart Button"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1865
msgid "Popup"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1974
msgid "Review Color"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:1998
msgid "Sale / Stockout Badge"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:2038, ../includes/Elements/Woo_Thank_You.php:858, ../includes/Elements/Woo_Thank_You.php:872
msgid "Quantity"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:2262
msgid " Close Button"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:2297
msgid "Button Size"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:2430
msgid "Preset"
msgstr ""

#: ../includes/Elements/Woo_Product_Slider.php:2458
msgid "Use Custom Width/Height?"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:60
msgid "Woo Thank You"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:168, ../includes/Elements/Woo_Thank_You.php:1222
msgid "Thank You Message"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:201
msgid "First Name"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:202
msgid "Last Name"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:203
msgid "Full Name"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:216, ../includes/Elements/Woo_Thank_You.php:232
msgid "Thank You Text"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:234
msgid "Thank you !"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:267
msgid "Thank you. Your order has been received."
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:268
msgid "Type your message here"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:278, ../includes/Elements/Woo_Thank_You.php:360, ../includes/Elements/Woo_Thank_You.php:418, ../includes/Elements/Woo_Thank_You.php:1584
msgid "Order Overview"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:291, ../includes/Elements/Woo_Thank_You.php:605, ../includes/Elements/Woo_Thank_You.php:1819
msgid "Downloads Table"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:295
msgid "This table appears when order will have downloadable item."
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:305, ../includes/Elements/Woo_Thank_You.php:748, ../includes/Elements/Woo_Thank_You.php:777, ../includes/Elements/Woo_Thank_You.php:2180
msgid "Order Details"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:318, ../includes/Elements/Woo_Thank_You.php:3120
msgid "Order Summary"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:331, ../includes/Elements/Woo_Thank_You.php:1004, ../includes/Elements/Woo_Thank_You.php:1030, ../includes/Elements/Woo_Thank_You.php:2786
msgid "Billing Address"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:344, ../includes/Elements/Woo_Thank_You.php:1180, ../includes/Elements/Woo_Thank_You.php:1206, ../includes/Elements/Woo_Thank_You.php:2965
msgid "Shipping Address"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:433
msgid "Order Number"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:448
msgid "Order number:"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:476
msgid "Date:"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:489
msgid "Date Format"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:518, ../includes/Elements/Woo_Thank_You.php:1110, ../includes/Elements/Woo_Thank_You.php:1147
msgid "Email"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:533
msgid "Email:"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:546, ../includes/Elements/Woo_Thank_You.php:821, ../includes/Extensions/Conditional_Display.php:2027
msgid "Total"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:561
msgid "Total:"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:574
msgid "Payment Method"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:589
msgid "Payment Method:"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:642
msgid "Show Product"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:656, ../includes/Elements/Woo_Thank_You.php:806, ../includes/Extensions/Conditional_Display.php:2011, ../includes/Extensions/DynamicTags/Woo_Products.php:71
msgid "Product"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:668
msgid "Show Downloads Remaining"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:682
msgid "Downloads Remaining"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:694
msgid "Show Expires"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:708
msgid "Expires"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:720
msgid "Show Downloads"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:734
msgid "Download"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:791, ../includes/Elements/Woo_Thank_You.php:1923, ../includes/Elements/Woo_Thank_You.php:2296
msgid "Table Heading"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:804
msgid "Product Label"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:819
msgid "Total Label"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:870
msgid "Quantity Label"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:888
msgid "Meta Data"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:900
msgid "Meta Label"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:902
msgid "Variation"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:917
msgid "Meta Data Label"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:948
msgid "Price Label"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:1043
msgid "Mobile No"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:1056, ../includes/Elements/Woo_Thank_You.php:1123
msgid "Label Type"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:1080
msgid "Mobile"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:1423
msgid "Hello Text"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:1788
msgid "Values"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:1981, ../includes/Elements/Woo_Thank_You.php:2377
msgid "Item Details"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:2561
msgid "Product Variation"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:2649
msgid "Product Quantity"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:2692
msgid "Product Meta"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:2744
msgid "Product Total Price"
msgstr ""

#: ../includes/Elements/Woo_Thank_You.php:3296
msgid "To view the widget, you must first place an order."
msgstr ""

#: ../includes/Extensions/Advanced_Dynamic_Tags.php:24
msgid "EA Dynamic Tags"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:36
msgid "<i class=\"eaicon-logo\"></i> Conditional Display"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:44
msgid "Conditional Display will take effect only on preview or live page, and not while editing in Elementor."
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:56
msgid "Enable Conditional Display"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:69
msgid "Visibility Action"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:81
msgid "Hide Without Condition"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:97
msgid "Action Applicable if"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:101
msgid "True All Logic"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:105
msgid "True Any Logic"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:128
msgid "User Status"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:129, ../includes/Extensions/Conditional_Display.php:1688
msgid "Post Type"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:130
msgid "Browser"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:131
msgid "Date & Time"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:132, ../includes/Extensions/Conditional_Display.php:1420
msgid "Recurring Day"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:133, ../includes/Extensions/Conditional_Display.php:1020
msgid "Dynamic Field"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:134
msgid "Query String"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:135, ../includes/Extensions/Conditional_Display.php:277
msgid "Visit Count"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:136
msgid "URL Contains"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:137
msgid "Archive"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:138, ../includes/Extensions/DynamicTags/Woo_Products.php:34
msgid "Woo Products"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:139
msgid "Woo Cart"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:140
msgid "Woo Orders"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:148, ../includes/Extensions/Conditional_Display.php:1072, ../includes/Extensions/Conditional_Display.php:1648
msgid "Logic Operator"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:153, ../includes/Extensions/Conditional_Display.php:1077, ../includes/Extensions/Conditional_Display.php:1932
msgid "Include"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:259, ../includes/Extensions/Conditional_Display.php:1986
msgid "Less Than"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:263, ../includes/Extensions/Conditional_Display.php:1990
msgid "Greater Than"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:289
msgid "Logics"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:310
msgid "Fallback"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:322
msgid "No Fallback"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:339
msgid "The content is hidden"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:393
msgid "Google Chrome"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:394
msgid "Mozilla Firefox"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:395
msgid "Safari"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:396
msgid "Iphone Safari"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:397
msgid "Opera"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:398
msgid "Edge"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:399
msgid "Internet Explorer"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:400
msgid "Internet Explorer for Mac OS X"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:401
msgid "Netscape 4"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:402
msgid "Lynx"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:403
msgid "Others"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:414
msgid "Sunday"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:415
msgid "Monday"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:416
msgid "Tuesday"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:417
msgid "Wednesday"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:418
msgid "Thursday"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:419
msgid "Friday"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:420
msgid "Saturday"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1023
msgid "Please remove Before and After field texts from Advanced tab."
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1041
msgid "Use Custom Separator"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1045
msgid "If the Dynamic Field has multiple values and the output is not separated by ( | ) Pipeline, enable this to input the value separator."
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1057, ../includes/Traits/Extender.php:4335, ../includes/Extensions/DynamicTags/Custom_Post_Types.php:100, ../includes/Extensions/DynamicTags/Posts.php:97, ../includes/Extensions/DynamicTags/Terms.php:127, ../includes/Extensions/DynamicTags/Woo_Products.php:123
msgid "Separator"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1059
msgid "|"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1116
msgid "Separate multiple value with the | (pipe) character. (e.g. <strong>value 1 | value 2</strong>)"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1131
msgid "Login Status"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1135
msgid "Logged In"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1139
msgid "Not Logged In"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1154
msgid "Select User Type"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1158, ../includes/Extensions/Conditional_Display.php:1770
msgid "User Role"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1162, ../includes/Extensions/Conditional_Display.php:1766
msgid "User"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1179, ../includes/Extensions/Conditional_Display.php:1788
msgid "Select User Roles"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1196, ../includes/Extensions/Conditional_Display.php:1805
msgid "Select Users"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1218
msgid "Select Post Types"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1232
msgid "Select Any Post"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1249, ../includes/Extensions/Conditional_Display.php:1744
msgid "Select "
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1268
msgid "Select Browser"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1285
msgid "Date and time"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1290
msgid "Is"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1294
msgid "Is Not"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1298, ../includes/Extensions/Conditional_Display.php:1425
msgid "Between"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1302, ../includes/Extensions/Conditional_Display.php:1429
msgid "Not Between"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1352, ../includes/Extensions/Conditional_Display.php:1486, ../includes/Extensions/Conditional_Display.php:1547
msgid "From"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1386, ../includes/Extensions/Conditional_Display.php:1511, ../includes/Extensions/Conditional_Display.php:1572
msgid "To"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1444
msgid "All Days"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1459
msgid "Recurring Days"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1475
msgid "Date Duration"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1536
msgid "Time Duration"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1599
msgid "Key"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1601
msgid "Query Key"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1613
msgid "Query Value"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1626
msgid "URL Type"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1631
msgid "Current"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1635
msgid "Refferer"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1653, ../includes/Extensions/Smooth_Animation.php:623
msgid "In"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1657
msgid "Not In"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1670
msgid "String"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1684
msgid "Archive Type"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1689, ../includes/Extensions/DynamicTags/Terms.php:66
msgid "Taxonomy"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1728
msgid "Select Taxonomoies"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1762
msgid "Author Type"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1822
msgid "Archive Date From"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1839
msgid "Archive Date To"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1880
msgid "Has No Order"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1881
msgid "From Last Purchase"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1882
msgid "Between All Orders"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1883
msgid "Between Date Period"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1894
msgid "Purchased Date From"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1911
msgid "Purchased Date To"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1928
msgid "Oparator"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1952
msgid "Cart items"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1957
msgid "Empty"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1961
msgid "IN"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1965
msgid "Not IN"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:1981
msgid "Compare Oparator"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2006
msgid "Products By"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2081
msgid "Search & Select Products"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2151
msgid "Select Product Types"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2219
msgid "Search & Select Categories"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2289
msgid "Cart item Count"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2305
msgid "Cart total (%s)"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2320
msgid "Stock Count"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2335
msgid "Product Price (%s)"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2350
msgid "Important Note about item count"
msgstr ""

#: ../includes/Extensions/Conditional_Display.php:2353
msgid "This is only applicable for Logic type Woo Cart & Woo Products"
msgstr ""

#: ../includes/Extensions/Content_Protection.php:33
msgid "<i class=\"eaicon-logo\"></i> Content Protection"
msgstr ""

#: ../includes/Extensions/Content_Protection.php:41
msgid "Enable Content Protection"
msgstr ""

#: ../includes/Extensions/Content_Protection.php:611
msgid "Password Field Hover"
msgstr ""

#: ../includes/Extensions/Content_Protection.php:677
msgid "Submit Button"
msgstr ""

#: ../includes/Extensions/Content_Protection.php:741
msgid "Submit Button Hover"
msgstr ""

#: ../includes/Extensions/Content_Protection.php:860
msgid "<p class=\"protected-content-error-msg\">%s</p>"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:30
msgid "<i class=\"eaicon-logo\"></i> Parallax"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:37
msgid "Enable Parallax"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:57
msgid "Scroll"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:58
msgid "Scroll with Fade"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:60
msgid "Zoom"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:61
msgid "Zoom with Fade"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:62
msgid "In-Motion"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:63
msgid "Multi-Layered"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:74
msgid "Motion Direction"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:78
msgid "Left to Right"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:79
msgid "Right to Left"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:80
msgid "Top to Bottom"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:81
msgid "Bottom to Top"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:92
msgid "Parallax Speed"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:107
msgid "Motion Speed"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:121
msgid "Parallax on Android Devices"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:132
msgid "Parallax on iOS Devices"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:159
msgid "Mouse Hover Interaction"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:167
msgid "Moving Intensity"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:181
msgid "Horizontal Position"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:205
msgid "Auto"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:206
msgid "Cover"
msgstr ""

#: ../includes/Extensions/EAEL_Parallax_Section.php:214
msgid "z-index"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:32
msgid "<i class=\"eaicon-logo\"></i> Particles"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:40
msgid "Enable Particles"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:48
msgid "Z-index"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:82
msgid "Move Speed"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:108
msgid "Defaults"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:126
msgid "Preset Themes"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:131
msgid "Nasa"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:132
msgid "Bubble"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:133
msgid "Snow"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:134
msgid "Nyan Cat"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:149
msgid "You can generate custom particles JSON code from <a href=\"http://vincentgarreau.com/particles.js/#default\" target=\"_blank\">Here!</a>. Simply just past the JSON code above. For more queries <a href=\"https://essential-addons.com/elementor/docs/\" target=\"_blank\">Click Here!</a>"
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:160
msgid "You need to configure a <strong style=\"color:green\">Background Type</strong> to see this in full effect. You can do this by switching to the <strong style=\"color:green\">Style</strong> Tab."
msgstr ""

#: ../includes/Extensions/EAEL_Particle_Section.php:172
msgid "Particles on Mobile Devices?"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:34
msgid "<i class=\"eaicon-logo\"></i> Advanced Tooltip"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:42
msgid "Enable Advanced Tooltip"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:61
msgid "I am a tooltip"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:95
msgid "Position Flip"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:113
msgid "Shift Away"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:114
msgid "Shift Toward"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:117
msgid "Perspective"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:145
msgid "Arrow Type"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:149
msgid "Sharp"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:163
msgid "Follow Cursor"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:167, ../includes/Extensions/Smooth_Animation.php:1269
msgid "False"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:170
msgid "Initial"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:173
msgid "Follow cursor when the tooltip is visible."
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:183
msgid "Trigger"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:216
msgid "Delay out (s)"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:237
msgid "Regular"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:250
msgid "Styles"
msgstr ""

#: ../includes/Extensions/EAEL_Tooltip_Section.php:337
msgid "Distance"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:21
msgid "<i class=\"eaicon-logo\"></i> Interactive Animations"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:28
msgid "Enable Interactive Animations"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:36
msgid "Animation Type"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:40
msgid "Animate To"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:41
msgid "Animate From"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:53
msgid "Animation Colors"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:66
msgid "Bankground Color"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:79
msgid "Transform Effects"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:92
msgid "TranslateX"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:130
msgid "Move the element horizontally by a specified amount"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:141
msgid "TranslateY"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:176
msgid "Move the element vertically by a specified amount"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:229
msgid "Transform Origin Effects"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:243
msgid "Transform Origin X"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:263, ../includes/Extensions/Smooth_Animation.php:319
msgid "Custom Origin"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:288
msgid "Set the horizontal pivot point for transformations"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:299
msgid "Transform Origin Y"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:344
msgid "Set the Vertical pivot point for transformations"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:357
msgid "Scaling Options"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:371
msgid "Keep Proportions"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:405
msgid "ScaleX"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:429
msgid "Scale the width of the element"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:441
msgid "ScaleY"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:465
msgid "Scale the height of the element"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:480
msgid "Skew Effects"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:512
msgid "SkewX"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:533
msgid "SkewY"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:556
msgid "Skew the element to create an angled effect along the x or y axis."
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:583
msgid "Easing"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:588
msgid "Linear"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:589
msgid "Back"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:590
msgid "Power"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:592
msgid "Circ"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:593
msgid "Elastic"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:594
msgid "Expo"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:595
msgid "Sine"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:596, ../includes/Traits/Extender.php:2607
msgid "Steps"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:608
msgid "Adjust how the animation progresses over time for different effects"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:619
msgid "Easing Type"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:624
msgid "Out"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:625
msgid "InOut"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:636
msgid "Yoyo"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:652
msgid "Makes the animation reverse direction on each repeat"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:663
msgid "Stagger"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:679
msgid "Add a delay between the start times of animations on multiple elements"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:693
msgid "Custom Animation Config"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:746
msgid "Loop Count"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:764
msgid "Target Element"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:767, ../includes/Extensions/Smooth_Animation.php:1298
msgid "my-element"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:778
msgid "(Optional) Enter the CSS selector of the element to animate, like: <strong>my-element</strong>"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:799
msgid "ScrollTrigger Options"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:810
msgid "Enable ScrollTrigger"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:824
msgid "Scroll Start Point"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:838
msgid "Start Element"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:857, ../includes/Extensions/Smooth_Animation.php:900, ../includes/Extensions/Smooth_Animation.php:957, ../includes/Extensions/Smooth_Animation.php:1000
msgid "Custom Value"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:881
msgid "Start Controller"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:925
msgid "Start End Point"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:939
msgid "End Element"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:981
msgid "End Controller"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1025
msgid "Enable Markers"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1041
msgid "Show markers during development to see where animations start and end"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1053
msgid "Scrubbing Options"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1067
msgid "Select Options"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1098
msgid "Custom Scrub"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1119
msgid "Allow the animation to sync with the scroll position"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1131
msgid "ToggleActions"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1145
msgid "On Enter"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1150, ../includes/Extensions/Smooth_Animation.php:1173, ../includes/Extensions/Smooth_Animation.php:1196, ../includes/Extensions/Smooth_Animation.php:1219
msgid "Restart"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1151, ../includes/Extensions/Smooth_Animation.php:1174, ../includes/Extensions/Smooth_Animation.php:1197, ../includes/Extensions/Smooth_Animation.php:1220
msgid "Resume"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1152, ../includes/Extensions/Smooth_Animation.php:1175, ../includes/Extensions/Smooth_Animation.php:1198, ../includes/Extensions/Smooth_Animation.php:1221
msgid "Reverse"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1153, ../includes/Extensions/Smooth_Animation.php:1176, ../includes/Extensions/Smooth_Animation.php:1199, ../includes/Extensions/Smooth_Animation.php:1222
msgid "Reset"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1156, ../includes/Extensions/Smooth_Animation.php:1179, ../includes/Extensions/Smooth_Animation.php:1202, ../includes/Extensions/Smooth_Animation.php:1225
msgid "Complete"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1168
msgid "On Leave"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1191
msgid "On Enter Back"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1214
msgid "On Leave Back"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1239
msgid "Define what actions occur at each scroll trigger point"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1251
msgid "Pin Settings"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1266
msgid "Enable Pin"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1268
msgid "True"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1283
msgid "Pin elements to the screen during the scroll for a fixed effect"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1295
msgid "Trigger Element"
msgstr ""

#: ../includes/Extensions/Smooth_Animation.php:1310
msgid "(Optional) Enter the CSS selector for the element that triggers the animation when scrolled into view like: <strong>my-element</strong>"
msgstr ""

#: ../includes/Skins/Skin_Default.php:121, ../includes/Skins/Skin_Five.php:139, ../includes/Skins/Skin_Four.php:121, ../includes/Skins/Skin_One.php:121, ../includes/Skins/Skin_Seven.php:121, ../includes/Skins/Skin_Six.php:121, ../includes/Skins/Skin_Three.php:139, ../includes/Skins/Skin_Two.php:138
msgid "To Top"
msgstr ""

#: ../includes/Skins/Skin_Default.php:135, ../includes/Skins/Skin_Five.php:153, ../includes/Skins/Skin_Four.php:135, ../includes/Skins/Skin_One.php:135, ../includes/Skins/Skin_Seven.php:135, ../includes/Skins/Skin_Six.php:135, ../includes/Skins/Skin_Three.php:153, ../includes/Skins/Skin_Two.php:152
msgid "Expand Active Submenu"
msgstr ""

#: ../includes/Skins/Skin_Default.php:136, ../includes/Skins/Skin_Five.php:154, ../includes/Skins/Skin_Four.php:136, ../includes/Skins/Skin_One.php:136, ../includes/Skins/Skin_Seven.php:136, ../includes/Skins/Skin_Six.php:136, ../includes/Skins/Skin_Three.php:154, ../includes/Skins/Skin_Two.php:153
msgid "Expand submenu if it contains the active page"
msgstr ""

#: ../includes/Skins/Skin_Default.php:250, ../includes/Skins/Skin_Five.php:381, ../includes/Skins/Skin_Four.php:329, ../includes/Skins/Skin_One.php:377, ../includes/Skins/Skin_Seven.php:329, ../includes/Skins/Skin_Six.php:330, ../includes/Skins/Skin_Three.php:349, ../includes/Skins/Skin_Two.php:348
msgid "Item Padding"
msgstr ""

#: ../includes/Skins/Skin_Default.php:260, ../includes/Skins/Skin_Five.php:391, ../includes/Skins/Skin_Four.php:339, ../includes/Skins/Skin_One.php:387, ../includes/Skins/Skin_Seven.php:339, ../includes/Skins/Skin_Six.php:340, ../includes/Skins/Skin_Three.php:359, ../includes/Skins/Skin_Two.php:358
msgid "This controller is <b style='color: #c6c6c6;letter-spacing: .5px;'>deprecated</b>. Please use 'Padding' instead of 'Item Padding,' as this controller will be removed."
msgstr ""

#: ../includes/Skins/Skin_Default.php:360, ../includes/Skins/Skin_Default.php:469, ../includes/Skins/Skin_Default.php:563, ../includes/Skins/Skin_Default.php:681, ../includes/Skins/Skin_Default.php:789, ../includes/Skins/Skin_Default.php:886, ../includes/Skins/Skin_Five.php:433, ../includes/Skins/Skin_Five.php:543, ../includes/Skins/Skin_Five.php:637, ../includes/Skins/Skin_Five.php:778, ../includes/Skins/Skin_Five.php:886, ../includes/Skins/Skin_Five.php:982, ../includes/Skins/Skin_Four.php:381, ../includes/Skins/Skin_Four.php:491, ../includes/Skins/Skin_Four.php:585, ../includes/Skins/Skin_Four.php:723, ../includes/Skins/Skin_Four.php:831, ../includes/Skins/Skin_Four.php:927, ../includes/Skins/Skin_One.php:429, ../includes/Skins/Skin_One.php:539, ../includes/Skins/Skin_One.php:633, ../includes/Skins/Skin_One.php:758, ../includes/Skins/Skin_One.php:866, ../includes/Skins/Skin_One.php:963, ../includes/Skins/Skin_Seven.php:381, ../includes/Skins/Skin_Seven.php:491, ../includes/Skins/Skin_Seven.php:585, ../includes/Skins/Skin_Seven.php:668, ../includes/Skins/Skin_Seven.php:831, ../includes/Skins/Skin_Seven.php:927, ../includes/Skins/Skin_Six.php:382, ../includes/Skins/Skin_Six.php:493, ../includes/Skins/Skin_Six.php:588, ../includes/Skins/Skin_Six.php:727, ../includes/Skins/Skin_Six.php:836, ../includes/Skins/Skin_Six.php:933, ../includes/Skins/Skin_Three.php:401, ../includes/Skins/Skin_Three.php:512, ../includes/Skins/Skin_Three.php:607, ../includes/Skins/Skin_Three.php:745, ../includes/Skins/Skin_Three.php:854, ../includes/Skins/Skin_Three.php:951, ../includes/Skins/Skin_Two.php:400, ../includes/Skins/Skin_Two.php:511, ../includes/Skins/Skin_Two.php:606, ../includes/Skins/Skin_Two.php:744, ../includes/Skins/Skin_Two.php:853, ../includes/Skins/Skin_Two.php:950
msgid "Dropdown Indicator"
msgstr ""

#: ../includes/Skins/Skin_Default.php:386, ../includes/Skins/Skin_Default.php:478, ../includes/Skins/Skin_Default.php:572, ../includes/Skins/Skin_Default.php:708, ../includes/Skins/Skin_Default.php:798, ../includes/Skins/Skin_Default.php:895, ../includes/Skins/Skin_Five.php:460, ../includes/Skins/Skin_Five.php:552, ../includes/Skins/Skin_Five.php:646, ../includes/Skins/Skin_Five.php:805, ../includes/Skins/Skin_Five.php:895, ../includes/Skins/Skin_Five.php:991, ../includes/Skins/Skin_Four.php:408, ../includes/Skins/Skin_Four.php:500, ../includes/Skins/Skin_Four.php:594, ../includes/Skins/Skin_Four.php:750, ../includes/Skins/Skin_Four.php:840, ../includes/Skins/Skin_Four.php:936, ../includes/Skins/Skin_One.php:456, ../includes/Skins/Skin_One.php:548, ../includes/Skins/Skin_One.php:642, ../includes/Skins/Skin_One.php:785, ../includes/Skins/Skin_One.php:875, ../includes/Skins/Skin_One.php:972, ../includes/Skins/Skin_Seven.php:408, ../includes/Skins/Skin_Seven.php:500, ../includes/Skins/Skin_Seven.php:594, ../includes/Skins/Skin_Seven.php:695, ../includes/Skins/Skin_Seven.php:840, ../includes/Skins/Skin_Seven.php:936, ../includes/Skins/Skin_Six.php:409, ../includes/Skins/Skin_Six.php:502, ../includes/Skins/Skin_Six.php:597, ../includes/Skins/Skin_Six.php:754, ../includes/Skins/Skin_Six.php:845, ../includes/Skins/Skin_Six.php:942, ../includes/Skins/Skin_Three.php:428, ../includes/Skins/Skin_Three.php:521, ../includes/Skins/Skin_Three.php:616, ../includes/Skins/Skin_Three.php:772, ../includes/Skins/Skin_Three.php:863, ../includes/Skins/Skin_Three.php:960, ../includes/Skins/Skin_Two.php:427, ../includes/Skins/Skin_Two.php:520, ../includes/Skins/Skin_Two.php:615, ../includes/Skins/Skin_Two.php:771, ../includes/Skins/Skin_Two.php:862, ../includes/Skins/Skin_Two.php:959
msgid "Important Note"
msgstr ""

#: ../includes/Skins/Skin_Default.php:389, ../includes/Skins/Skin_Default.php:481, ../includes/Skins/Skin_Default.php:575, ../includes/Skins/Skin_Default.php:711, ../includes/Skins/Skin_Default.php:801, ../includes/Skins/Skin_Default.php:898, ../includes/Skins/Skin_Five.php:463, ../includes/Skins/Skin_Five.php:555, ../includes/Skins/Skin_Five.php:649, ../includes/Skins/Skin_Five.php:808, ../includes/Skins/Skin_Five.php:898, ../includes/Skins/Skin_Five.php:994, ../includes/Skins/Skin_Four.php:411, ../includes/Skins/Skin_Four.php:503, ../includes/Skins/Skin_Four.php:597, ../includes/Skins/Skin_Four.php:753, ../includes/Skins/Skin_Four.php:843, ../includes/Skins/Skin_Four.php:939, ../includes/Skins/Skin_One.php:459, ../includes/Skins/Skin_One.php:551, ../includes/Skins/Skin_One.php:645, ../includes/Skins/Skin_One.php:788, ../includes/Skins/Skin_One.php:878, ../includes/Skins/Skin_One.php:975, ../includes/Skins/Skin_Seven.php:411, ../includes/Skins/Skin_Seven.php:503, ../includes/Skins/Skin_Seven.php:597, ../includes/Skins/Skin_Seven.php:698, ../includes/Skins/Skin_Seven.php:843, ../includes/Skins/Skin_Seven.php:939, ../includes/Skins/Skin_Six.php:412, ../includes/Skins/Skin_Six.php:505, ../includes/Skins/Skin_Six.php:600, ../includes/Skins/Skin_Six.php:757, ../includes/Skins/Skin_Six.php:848, ../includes/Skins/Skin_Six.php:945, ../includes/Skins/Skin_Three.php:431, ../includes/Skins/Skin_Three.php:524, ../includes/Skins/Skin_Three.php:619, ../includes/Skins/Skin_Three.php:775, ../includes/Skins/Skin_Three.php:866, ../includes/Skins/Skin_Three.php:963, ../includes/Skins/Skin_Two.php:430, ../includes/Skins/Skin_Two.php:523, ../includes/Skins/Skin_Two.php:618, ../includes/Skins/Skin_Two.php:774, ../includes/Skins/Skin_Two.php:865, ../includes/Skins/Skin_Two.php:962
msgid "<div style=\"font-size: 11px;font-style:italic;line-height:1.4;color:#a4afb7;\">Following options are only available in the <span style=\"color:#d30c5c\"><strong>Small</strong></span> screens for <span style=\"color:#d30c5c\"><strong>Horizontal</strong></span> Layout, and all screens for <span style=\"color:#d30c5c\"><strong>Vertical</strong></span> Layout</div>"
msgstr ""

#: ../includes/Skins/Skin_Default.php:1036, ../includes/Skins/Skin_Five.php:1132, ../includes/Skins/Skin_Four.php:1077, ../includes/Skins/Skin_One.php:1113, ../includes/Skins/Skin_Seven.php:1077, ../includes/Skins/Skin_Six.php:1083, ../includes/Skins/Skin_Three.php:1101, ../includes/Skins/Skin_Two.php:1100
msgid "Essential Addons Toggle Menu"
msgstr ""

#: ../includes/Skins/Skin_Five.php:29
msgid "Skin Five"
msgstr ""

#: ../includes/Skins/Skin_Four.php:29
msgid "Skin Four"
msgstr ""

#: ../includes/Skins/Skin_One.php:29
msgid "Skin One"
msgstr ""

#: ../includes/Skins/Skin_Seven.php:29
msgid "Skin Seven"
msgstr ""

#: ../includes/Skins/Skin_Six.php:29
msgid "Skin Six"
msgstr ""

#: ../includes/Skins/Skin_Three.php:29
msgid "Skin Three"
msgstr ""

#: ../includes/Skins/Skin_Two.php:29
msgid "Skin Two"
msgstr ""

#: ../includes/Traits/Extender.php:28
msgid "Line Rainbow"
msgstr ""

#: ../includes/Traits/Extender.php:29
msgid "Circle Fill"
msgstr ""

#: ../includes/Traits/Extender.php:30
msgid "Half Circle Fill"
msgstr ""

#: ../includes/Traits/Extender.php:56
msgid "Enable Table Sorting"
msgstr ""

#: ../includes/Traits/Extender.php:91
msgid "Ticker custom content"
msgstr ""

#: ../includes/Traits/Extender.php:291
msgid "%"
msgstr ""

#: ../includes/Traits/Extender.php:324
msgid "Pricing Style 3"
msgstr ""

#: ../includes/Traits/Extender.php:325
msgid "Pricing Style 4"
msgstr ""

#: ../includes/Traits/Extender.php:326
msgid "Pricing Style 5"
msgstr ""

#: ../includes/Traits/Extender.php:336
msgid "Grid Flow"
msgstr ""

#: ../includes/Traits/Extender.php:340
msgid "Harmonic"
msgstr ""

#: ../includes/Traits/Extender.php:352
msgid "Button Content"
msgstr ""

#: ../includes/Traits/Extender.php:369
msgid "Enter button text"
msgstr ""

#: ../includes/Traits/Extender.php:370
msgid "Enter button text here"
msgstr ""

#: ../includes/Traits/Extender.php:413
msgid "Remove Default SVG Color"
msgstr ""

#: ../includes/Traits/Extender.php:415
msgid "If you are using a custom SVG and want to apply colors from the controller, enable this option. Note that it will override the default color of your SVG."
msgstr ""

#: ../includes/Traits/Extender.php:458
msgid "Button Secondary Text"
msgstr ""

#: ../includes/Traits/Extender.php:465
msgid "Enter button secondary text"
msgstr ""

#: ../includes/Traits/Extender.php:466
msgid "Enter button secondary text here"
msgstr ""

#: ../includes/Traits/Extender.php:493
msgid "Set Button Effect"
msgstr ""

#: ../includes/Traits/Extender.php:498
msgid "Winona"
msgstr ""

#: ../includes/Traits/Extender.php:499
msgid "Ujarak"
msgstr ""

#: ../includes/Traits/Extender.php:500
msgid "Wayra"
msgstr ""

#: ../includes/Traits/Extender.php:501
msgid "Tamaya"
msgstr ""

#: ../includes/Traits/Extender.php:502
msgid "Rayen"
msgstr ""

#: ../includes/Traits/Extender.php:503
msgid "Pipaluk"
msgstr ""

#: ../includes/Traits/Extender.php:504
msgid "Moema"
msgstr ""

#: ../includes/Traits/Extender.php:505
msgid "Wave"
msgstr ""

#: ../includes/Traits/Extender.php:506
msgid "Aylen"
msgstr ""

#: ../includes/Traits/Extender.php:507
msgid "Saqui"
msgstr ""

#: ../includes/Traits/Extender.php:508
msgid "Wapasha"
msgstr ""

#: ../includes/Traits/Extender.php:509
msgid "Nuka"
msgstr ""

#: ../includes/Traits/Extender.php:510
msgid "Antiman"
msgstr ""

#: ../includes/Traits/Extender.php:511
msgid "Quidel"
msgstr ""

#: ../includes/Traits/Extender.php:512
msgid "Shikoba"
msgstr ""

#: ../includes/Traits/Extender.php:646
msgid "Use Gradient Background"
msgstr ""

#: ../includes/Traits/Extender.php:869
msgid "Header Image"
msgstr ""

#: ../includes/Traits/Extender.php:892
msgid "Pricing Position"
msgstr ""

#: ../includes/Traits/Extender.php:897
msgid "On Top"
msgstr ""

#: ../includes/Traits/Extender.php:898
msgid "At Bottom"
msgstr ""

#: ../includes/Traits/Extender.php:909
msgid "Icon Beside Title"
msgstr ""

#: ../includes/Traits/Extender.php:919
msgid "Header Layout"
msgstr ""

#: ../includes/Traits/Extender.php:935
msgid "Header Layout Two"
msgstr ""

#: ../includes/Traits/Extender.php:1187
msgid "Stuck with something? Get help from live chat or support ticket."
msgstr ""

#: ../includes/Traits/Extender.php:1190
msgid "Initiate a Chat"
msgstr ""

#: ../includes/Traits/Extender.php:1207
msgid "Join the Facebook community and discuss with fellow developers and users. Best way to connect with people and get feedback on your projects."
msgstr ""

#: ../includes/Traits/Extender.php:1211
msgid "Join Facebook Community"
msgstr ""

#: ../includes/Traits/Extender.php:1219
msgid "Manage License"
msgstr ""

#: ../includes/Traits/Extender.php:1387, ../includes/Traits/Extender.php:1557
msgid "Select Query"
msgstr ""

#: ../includes/Traits/Extender.php:1405, ../includes/Traits/Extender.php:1575
msgid "Select Table"
msgstr ""

#: ../includes/Traits/Extender.php:1420, ../includes/Traits/Extender.php:1590
msgid "MySQL Query"
msgstr ""

#: ../includes/Traits/Extender.php:1435
msgid "Host"
msgstr ""

#: ../includes/Traits/Extender.php:1467
msgid "Password"
msgstr ""

#: ../includes/Traits/Extender.php:1484
msgid "Database"
msgstr ""

#: ../includes/Traits/Extender.php:1500
msgid "Connect DB"
msgstr ""

#: ../includes/Traits/Extender.php:1502
msgid "Connect"
msgstr ""

#: ../includes/Traits/Extender.php:1515
msgid "Disconnect DB"
msgstr ""

#: ../includes/Traits/Extender.php:1517
msgid "Disconnect"
msgstr ""

#: ../includes/Traits/Extender.php:1647
msgid "Empty Rows"
msgstr ""

#: ../includes/Traits/Extender.php:1678
msgid "<strong>TablePress</strong> is not installed/activated on your site. Please install and activate <a href=\"plugin-install.php?s=TablePress&tab=search&type=term\" target=\"_blank\">TablePress</a> first."
msgstr ""

#: ../includes/Traits/Extender.php:1665
msgid "Table ID"
msgstr ""

#: ../includes/Traits/Extender.php:1692
msgid "EventON"
msgstr ""

#: ../includes/Traits/Extender.php:1699
msgid "Get Events"
msgstr ""

#: ../includes/Traits/Extender.php:1705
msgid "Date Range"
msgstr ""

#: ../includes/Traits/Extender.php:1710
msgid "Start Date"
msgstr ""

#: ../includes/Traits/Extender.php:1719
msgid "End Date"
msgstr ""

#: ../includes/Traits/Extender.php:1728
msgid "Event Tag"
msgstr ""

#: ../includes/Traits/Extender.php:1759
msgid "Event Location"
msgstr ""

#: ../includes/Traits/Extender.php:1771
msgid "Event Organizer"
msgstr ""

#: ../includes/Traits/Extender.php:1783
msgid "Max Result"
msgstr ""

#: ../includes/Traits/Extender.php:1798
msgid "<strong>EventON</strong> is not installed/activated on your site. Please install and activate <a href=\"https://wordpress.org/plugins/eventon-lite/\" target=\"_blank\">EventON</a> first."
msgstr ""

#: ../includes/Traits/Extender.php:2288
msgid "No Title"
msgstr ""

#: ../includes/Traits/Extender.php:2333, ../includes/Traits/Extender.php:2330
msgid "Multi Steps"
msgstr ""

#: ../includes/Traits/Extender.php:2334
msgid "Split (Pro)"
msgstr ""

#: ../includes/Traits/Extender.php:2331
msgid "Split"
msgstr ""

#: ../includes/Traits/Extender.php:2359
msgid "Tabs Label"
msgstr ""

#: ../includes/Traits/Extender.php:2379, ../includes/Traits/Extender.php:2384
msgid "Coupon"
msgstr ""

#: ../includes/Traits/Extender.php:2390, ../includes/Traits/Extender.php:2395
msgid "Billing & Shipping"
msgstr ""

#: ../includes/Traits/Extender.php:2401, ../includes/Traits/Extender.php:2406
msgid "Payment"
msgstr ""

#: ../includes/Traits/Extender.php:2413
msgid "Previous/Next Label"
msgstr ""

#: ../includes/Traits/Extender.php:2421, ../includes/Traits/Extender.php:2426
msgid "Next"
msgstr ""

#: ../includes/Traits/Extender.php:2432, ../includes/Traits/Extender.php:2437
msgid "Previous"
msgstr ""

#: ../includes/Traits/Extender.php:2587
msgid "Bottom Gap"
msgstr ""

#: ../includes/Traits/Extender.php:2656, ../includes/Traits/Extender.php:2682
msgid "Connector Color"
msgstr ""

#: ../includes/Traits/Extender.php:2741
msgid "Section"
msgstr ""

#: ../includes/Traits/Extender.php:2808
msgid "Previous/Next Button"
msgstr ""

#: ../includes/Traits/Extender.php:2986
msgid "Submit Form via AJAX"
msgstr ""

#: ../includes/Traits/Extender.php:3001
msgid "Show Spinner"
msgstr ""

#: ../includes/Traits/Extender.php:3012
msgid "In preview, Spinner is only visible after clicking on the button."
msgstr ""

#: ../includes/Traits/Extender.php:3075
msgid "Size (px)"
msgstr ""

#: ../includes/Traits/Extender.php:3143
msgid "Social Login"
msgstr ""

#: ../includes/Traits/Extender.php:3147
msgid "Enable Login with Google"
msgstr ""

#: ../includes/Traits/Extender.php:3154
msgid "Google Client ID is missing. Please add it from %sDashboard >> Essential Addons >> Elements >> Login | Register Form %sSettings"
msgstr ""

#: ../includes/Traits/Extender.php:3162
msgid "Enable Login with Facebook"
msgstr ""

#: ../includes/Traits/Extender.php:3169
msgid "Facebook API keys are missing. Please add them from %sDashboard >> Essential Addons >> Elements >> Login | Register Form %sSettings"
msgstr ""

#: ../includes/Traits/Extender.php:3177
msgid "Text for Facebook Button"
msgstr ""

#: ../includes/Traits/Extender.php:3182, ../includes/Traits/Extender.php:3183
msgid "Login with Facebook"
msgstr ""

#: ../includes/Traits/Extender.php:3190
msgid "Show on Register Form"
msgstr ""

#: ../includes/Traits/Extender.php:3196
msgid "Show Separator"
msgstr ""

#: ../includes/Traits/Extender.php:3214
msgid "Separator Type"
msgstr ""

#: ../includes/Traits/Extender.php:3227
msgid "Separator Text"
msgstr ""

#: ../includes/Traits/Extender.php:3232
msgid "Or"
msgstr ""

#: ../includes/Traits/Extender.php:3233
msgid "Eg. Or"
msgstr ""

#: ../includes/Traits/Extender.php:3372
msgid "You are already logged in."
msgstr ""

#: ../includes/Traits/Extender.php:3383, ../includes/Traits/Extender.php:3392
msgid "User data was not verified by Google"
msgstr ""

#: ../includes/Traits/Extender.php:3407, ../includes/Traits/Extender.php:3414
msgid "Facebook authorization failed"
msgstr ""

#: ../includes/Traits/Extender.php:3422
msgid "Facebook email validation failed"
msgstr ""

#: ../includes/Traits/Extender.php:3454
msgid "User not registered"
msgstr ""

#: ../includes/Traits/Extender.php:3503, ../includes/Traits/Extender.php:3524
msgid "Logging user failed."
msgstr ""

#: ../includes/Traits/Extender.php:3528
msgid "You are logged in successfully"
msgstr ""

#: ../includes/Traits/Extender.php:3639
msgid "Social Login Style"
msgstr ""

#: ../includes/Traits/Extender.php:3657
msgid "Social Container"
msgstr ""

#: ../includes/Traits/Extender.php:3798
msgid "Display Button as"
msgstr ""

#: ../includes/Traits/Extender.php:3824
msgid "Enable Mailchimp Integration"
msgstr ""

#: ../includes/Traits/Extender.php:3825
msgid "Enable to create new Mailchimp audience contact on each user registration."
msgstr ""

#: ../includes/Traits/Extender.php:3856
msgid "Google Button"
msgstr ""

#: ../includes/Traits/Extender.php:3868, ../includes/Traits/Extender.php:4049
msgid "Button Style"
msgstr ""

#: ../includes/Traits/Extender.php:3884
msgid "Theme"
msgstr ""

#: ../includes/Traits/Extender.php:3887
msgid "Outline"
msgstr ""

#: ../includes/Traits/Extender.php:3888
msgid "Filled Blue"
msgstr ""

#: ../includes/Traits/Extender.php:3889
msgid "Filled Black"
msgstr ""

#: ../includes/Traits/Extender.php:3909
msgid "Sign in with Google"
msgstr ""

#: ../includes/Traits/Extender.php:3910
msgid "Sign up with Google"
msgstr ""

#: ../includes/Traits/Extender.php:3911
msgid "Continue with Google"
msgstr ""

#: ../includes/Traits/Extender.php:3912
msgid "Sign in"
msgstr ""

#: ../includes/Traits/Extender.php:3918
msgid "Shape"
msgstr ""

#: ../includes/Traits/Extender.php:3921
msgid "Rectangular"
msgstr ""

#: ../includes/Traits/Extender.php:3922
msgid "Pill"
msgstr ""

#: ../includes/Traits/Extender.php:3930
msgid "Logo Alignment"
msgstr ""

#: ../includes/Traits/Extender.php:3960
msgid "Locale"
msgstr ""

#: ../includes/Traits/Extender.php:4038
msgid "%s Button"
msgstr ""

#: ../includes/Traits/Extender.php:4057
msgid "Button Width"
msgstr ""

#: ../includes/Traits/Extender.php:4086
msgid "Button Height"
msgstr ""

#: ../includes/Traits/Extender.php:4189
msgid "%s Button Typography"
msgstr ""

#: ../includes/Traits/Extender.php:4195
msgid "%s  Button Icon"
msgstr ""

#: ../includes/Traits/Extender.php:4487
msgid "Separator Typography"
msgstr ""

#: ../includes/Traits/Extender.php:4523, ../includes/Traits/Extender.php:4678
msgid "Show Field Icons"
msgstr ""

#: ../includes/Traits/Extender.php:4527
msgid "Show Password Strength Meter"
msgstr ""

#: ../includes/Traits/Extender.php:4531
msgid "Show Password Strength Text"
msgstr ""

#: ../includes/Traits/Extender.php:4535
msgid "Password Strength Text"
msgstr ""

#: ../includes/Traits/Extender.php:4547, ../includes/Traits/Extender.php:4914
msgid "Very Weak"
msgstr ""

#: ../includes/Traits/Extender.php:4548, ../includes/Traits/Extender.php:4915
msgid "Weak"
msgstr ""

#: ../includes/Traits/Extender.php:4550, ../includes/Traits/Extender.php:4917
msgid "Strong"
msgstr ""

#. translators: %s: Strength of the Password eg. Bad, Good etc.
#: ../includes/Traits/Extender.php:4555, ../includes/Traits/Extender.php:4558, ../includes/Traits/Extender.php:4945
msgid "%s Password"
msgstr ""

#: ../includes/Traits/Extender.php:4559
msgid "Eg. Weak or Good etc."
msgstr ""

#: ../includes/Traits/Extender.php:4569
msgid "Enable use of weak Password"
msgstr ""

#: ../includes/Traits/Extender.php:4575
msgid "Validation Text"
msgstr ""

#: ../includes/Traits/Extender.php:4588
msgid "Custom Validation Text"
msgstr ""

#: ../includes/Traits/Extender.php:4590
msgid "Your custom validation text..."
msgstr ""

#: ../includes/Traits/Extender.php:4598
msgid "Minimum Password Length"
msgstr ""

#: ../includes/Traits/Extender.php:4610
msgid "One Uppercase Letter"
msgstr ""

#: ../includes/Traits/Extender.php:4619
msgid "One Lowercase Letter"
msgstr ""

#: ../includes/Traits/Extender.php:4628
msgid "One Number"
msgstr ""

#: ../includes/Traits/Extender.php:4637
msgid "One Special Character"
msgstr ""

#: ../includes/Traits/Extender.php:4648, ../includes/Traits/Extender.php:4954
msgid "Password Hint"
msgstr ""

#: ../includes/Traits/Extender.php:4653
msgid "WordPress Default"
msgstr ""

#: ../includes/Traits/Extender.php:4662
msgid "Custom Password Hint"
msgstr ""

#: ../includes/Traits/Extender.php:4664
msgid "Your custom password hint..."
msgstr ""

#: ../includes/Traits/Extender.php:4754
msgid "Password Strength"
msgstr ""

#: ../includes/Traits/Extender.php:4813
msgid "Box Margin"
msgstr ""

#: ../includes/Traits/Extender.php:4826
msgid "Box Padding"
msgstr ""

#: ../includes/Traits/Extender.php:4839
msgid "Meter Margin"
msgstr ""

#: ../includes/Traits/Extender.php:4851
msgid "Strength Text Margin"
msgstr ""

#: ../includes/Traits/Extender.php:4863
msgid "Password Hint Margin"
msgstr ""

#: ../includes/Traits/Extender.php:4879
msgid "Strength Text Typography"
msgstr ""

#: ../includes/Traits/Extender.php:4884
msgid "Password Hint Typography"
msgstr ""

#: ../includes/Traits/Extender.php:4909
msgid "Colors"
msgstr ""

#: ../includes/Traits/Extender.php:5137
msgid "<strong>Short Description</strong> is only applicable for List Layout."
msgstr ""

#: ../includes/Traits/Extender.php:5161
msgid "Content Custom Ordering"
msgstr ""

#: ../includes/Traits/Extender.php:5165
msgid "This option allows you to reorder Title, Price, Ratings and Sold Count position."
msgstr ""

#: ../includes/Traits/Extender.php:5197
msgid "Ratings"
msgstr ""

#: ../includes/Traits/Extender.php:5201
msgid "Sold Count"
msgstr ""

#: ../includes/Traits/Extender.php:5230, ../includes/Traits/Extender.php:5215
msgid "Content Ordering"
msgstr ""

#: ../includes/Traits/Extender.php:5259
msgid "&#10029;&#10029;&#10029;&#10029;&#10027;"
msgstr ""

#: ../includes/Traits/Extender.php:5260
msgid "&#10029; 4.7"
msgstr ""

#: ../includes/Traits/Extender.php:5261
msgid "4.7/5"
msgstr ""

#: ../includes/Traits/Extender.php:5317, ../includes/Traits/Extender.php:5305
msgid "Show Sold Count?"
msgstr ""

#: ../includes/Traits/Extender.php:5332
msgid "Only Count"
msgstr ""

#: ../includes/Traits/Extender.php:5333
msgid "Count with Progress Bar"
msgstr ""

#: ../includes/Traits/Extender.php:5334
msgid "Only Progress Bar"
msgstr ""

#: ../includes/Traits/Extender.php:5362
msgid "This width applied in progress bar for those products which stocks are not managed"
msgstr ""

#: ../includes/Traits/Extender.php:5405
msgid "<strong>[sold_count]</strong> Will be replaced with actual amount."
msgstr ""

#: ../includes/Traits/Extender.php:5455
msgid "Product Sold Count"
msgstr ""

#: ../includes/Traits/Extender.php:5498
msgid "Sold Count Progress Bar"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:26, ../includes/Traits/Filterable_Gallery_Extender.php:218
msgid "Gallery Item Title"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:38
msgid "Writing Mode"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:42
msgid "Horizontal TB"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:43
msgid "Vertical LR"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:44
msgid "Sideways LR"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:55, ../includes/Traits/Filterable_Gallery_Extender.php:287
msgid "Gallery Filter Title"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:101
msgid "Gallery Icon"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:122
msgid "Select Gallery Icon"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:165, ../includes/Traits/Filterable_Gallery_Extender.php:623
msgid "Transition Duration (ms)"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:390
msgid "Border Hover Color"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:516
msgid "Transition Color"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:528
msgid "Transition Duration (s) "
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:838, ../includes/Traits/Filterable_Gallery_Extender.php:1203
msgid "Gallery"
msgstr ""

#: ../includes/Traits/Filterable_Gallery_Extender.php:1069
msgid "View More"
msgstr ""

#: ../includes/Traits/Helper.php:269, ../includes/Traits/Helper.php:392
msgid "You must be logged in to checkout."
msgstr ""

#: ../includes/Traits/Helper.php:328
msgid "Billing Postcode is not a valid postcode / ZIP"
msgstr ""

#: ../includes/Traits/Helper.php:444
msgid "Insecure form submitted without security token"
msgstr ""

#: ../includes/Traits/Helper.php:449
msgid "Security token did not match"
msgstr ""

#: ../includes/Traits/Instagram_Feed.php:27
msgid "Post ID is missing"
msgstr ""

#: ../includes/Traits/Instagram_Feed.php:37
msgid "Widget ID is missing"
msgstr ""

#: ../includes/Traits/Instagram_Feed.php:167
msgid "Photo by "
msgstr ""

#: ../includes/Classes/License/LicenseManager.php:147
msgid "%1$sActivate your %3$s License Key%2$s to receive regular updates and secure your WordPress website."
msgstr ""

#: ../includes/Classes/License/LicenseManager.php:393
msgid "%1$sAttention:%2$s Please %3$sVerify your %5$s License Key%4$s to get regular updates & secure your WordPress website."
msgstr ""

#. translators: the plugin name.
#: ../includes/Classes/License/PluginUpdater.php:245
msgid "There is a new version of %1$s available."
msgstr ""

#: ../includes/Classes/License/PluginUpdater.php:257
msgid "Update now."
msgstr ""

#: ../includes/Classes/License/PluginUpdater.php:255
msgid "%1$sView version %2$s details%3$s or %4$supdate now%5$s."
msgstr ""

#: ../includes/Classes/License/PluginUpdater.php:252
msgid "%1$sView version %2$s details%3$s."
msgstr ""

#: ../includes/Classes/License/PluginUpdater.php:249
msgid "Contact your network administrator to install the update."
msgstr ""

#: ../includes/Classes/License/PluginUpdater.php:484
msgid "You do not have permission to install plugin updates"
msgstr ""

#: ../includes/Classes/License/PluginUpdater.php:484
msgid "Error"
msgstr ""

#: ../includes/Extensions/DynamicTags/Acf_Relationship.php:28
msgid "ACF Relationship"
msgstr ""

#: ../includes/Extensions/DynamicTags/Acf_Relationship.php:40
msgid "ACF Relationship Field"
msgstr ""

#: ../includes/Extensions/DynamicTags/Acf_Relationship.php:42
msgid "Select the field"
msgstr ""

#: ../includes/Extensions/DynamicTags/Custom_Post_Types.php:34
msgid "Custom Post Types"
msgstr ""

#: ../includes/Extensions/DynamicTags/Custom_Post_Types.php:88, ../includes/Extensions/DynamicTags/Posts.php:85, ../includes/Extensions/DynamicTags/Terms.php:115
msgid "Data Format"
msgstr ""

#: ../includes/Extensions/DynamicTags/Custom_Post_Types.php:92, ../includes/Extensions/DynamicTags/Posts.php:89
msgid "Title | ID"
msgstr ""

#: ../includes/Extensions/DynamicTags/Custom_Post_Types.php:104, ../includes/Extensions/DynamicTags/Posts.php:101, ../includes/Extensions/DynamicTags/Terms.php:131, ../includes/Extensions/DynamicTags/Woo_Products.php:127
msgid "Line Break"
msgstr ""

#: ../includes/Extensions/DynamicTags/Custom_Post_Types.php:105, ../includes/Extensions/DynamicTags/Posts.php:102, ../includes/Extensions/DynamicTags/Terms.php:132, ../includes/Extensions/DynamicTags/Woo_Products.php:128
msgid "Comma"
msgstr ""

#: ../includes/Extensions/DynamicTags/Posts.php:34, ../includes/Extensions/DynamicTags/Posts.php:65
msgid "Posts"
msgstr ""

#: ../includes/Extensions/DynamicTags/Posts.php:74, ../includes/Extensions/DynamicTags/Woo_Products.php:100
msgid "Post Status"
msgstr ""

#: ../includes/Extensions/DynamicTags/Terms.php:92
msgid "Hide Empty"
msgstr ""

#: ../includes/Extensions/DynamicTags/Terms.php:119
msgid "Name | ID"
msgstr ""

#: ../includes/Extensions/DynamicTags/Terms.php:130
msgid "New Line"
msgstr ""

#: ../includes/Template/Post-List/advanced.php:3
msgid "Advanced"
msgstr ""

#: ../includes/Template/Woo-Thank-You/perest-1.php:234, ../includes/Template/Woo-Thank-You/perest-1.php:278, ../includes/Template/Woo-Thank-You/preset-2.php:285, ../includes/Template/Woo-Thank-You/preset-2.php:329, ../includes/Template/Woo-Thank-You/preset-3.php:99, ../includes/Template/Woo-Thank-You/preset-3.php:143, ../includes/Template/Woo-Thank-You/preset-3.php:315, ../includes/Template/Woo-Thank-You/preset-3.php:359
msgid "N/A"
msgstr ""

#: ../includes/Template/Woo-Thank-You/perest-1.php:298, ../includes/Template/Woo-Thank-You/preset-2.php:229, ../includes/Template/Woo-Thank-You/preset-3.php:258
msgid "Note:"
msgstr ""

#: ../includes/templates/ld-courses/default.php:61, ../includes/templates/ld-courses/layout__1.php:67
msgid "By"
msgstr ""

#: ../includes/templates/ld-courses/default.php:64, ../includes/templates/ld-courses/layout__1.php:70
msgid "in"
msgstr ""
