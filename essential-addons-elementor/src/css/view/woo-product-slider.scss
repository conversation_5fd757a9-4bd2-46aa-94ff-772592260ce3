
$theme-preset1: #004eff;
$theme-preset2: #ff3f56;
$theme-preset3: #00a983;
$theme-preset4: #7139ff;

.eael-woo-product-slider-container {
    //theme
    ins {
        background: transparent;
    }

    .woocommerce {
        ul.products {
            padding: 0 !important;

            &:before,
            &:after {
                display: none;
            }

            .product {
                width: 100%;
                margin: 0;
                padding: 0;
                a.add_to_cart_button,
                span.price,
                h2.woocommerce-loop-product__title,
                .eael-wc-compare{
                    //margin-left: 10px !important;
                    //margin-right: 10px !important;
                }

                .star-rating {
                    margin: 14px 0 0;
                    display: inline-block;
                    float: none;
                    height: 1em;
                    width: 7em;
                    font-size: 18px !important;
                    line-height: 1em;

                    &:before {
                        content: '\f005\f005\f005\f005\f005';
                        font-family: "Font Awesome 5 Free";
                        font-weight: 400;
                        opacity: 1;
                        letter-spacing: .3em;
                    }

                    span {
                        display: inline-block;

                        &:before {
                            content: '\f005 \f005 \f005 \f005 \f005';
                            font-family: "Font Awesome 5 Free";
                            font-weight: 900;
                            letter-spacing: .3em;
                        }
                    }
                }

                a.button.loading::after,
                button.button.loading::after {
                    left: 47%;
                    right: auto;
                    transform: translateX(-47%);
                }
            }

            &.products[class*='columns-'] li.product {
                width: 100%;
            }
        }
    }

    .eael-woo-product-slider {
        @media (min-width: 601px) {
            .eael-reverse-column > div:first-child {
                grid-row: 1;
                grid-column: 2;
            }
        }

        .eael-product-slider {
            direction: ltr;
            display: grid;
            grid-gap: 5%;
            grid-template-columns: repeat(2, 1fr);


            // title
            .eael-product-title * {
                font-size: 60px;
                line-height: 1.2em;
                color: #2d3d44;
                font-weight: 500;
                margin: 0;
                padding: 0;
            }

            // desc
            .eael-product-excerpt {
                font-size: 22px;
                line-height: 1.5em;
                color: #5a5a5a;
                font-weight: 500;
                margin-top: 30px;

                p {
                    font-size: inherit;
                    margin-bottom: 0;
                }
            }

            // price
            .eael-product-price {
                color: $theme-preset1;
                font-size: 35px;
                line-height: 1.2em;
                font-weight: 500;
                margin-top: 15px;

                del {
                    font-size: 25px;
                    line-height: 1.2em;
                    color: #698e9e;
                    font-weight: 500;
                }

                ins {
                    color: $theme-preset1;
                    font-size: 35px;
                    line-height: 1.2em;
                    font-weight: 500;
                    text-transform: none;
                    text-decoration: none;
                    font-style: normal;
                    letter-spacing: normal;
                    display: inline-block;
                }
            }

            .eael-add-to-cart-button {
                margin-top: 20px;

                a.button.add_to_cart_button,
                a.button,
                a.added_to_cart {
                    display: inline-block;
                    margin: 0 !important;
                    padding: 13px 25px !important;
                    font-size: 18px;
                    line-height: 1.2em;
                    border-radius: 10px;
                    background-color: #00a983;
                    color: #fff;

                    &:hover {
                        background-color: #338e79;
                        color: #fff;
                    }
                }
            }

            .product-image-wrap .image-wrap {
                position: relative;
                overflow: hidden;
                margin: 15px;
                box-shadow: 0 ​4px 11px 3px rgb(35 53 93 / 21%);
            }

            .product-details-wrap {

            }
        }

        a.button.add_to_cart_button.added {
            display: none !important;
        }

        .eael-product-quick-view a {
            cursor: pointer;
        }

        .swiper-wrapper.products {
            margin: 0;
            flex-wrap: unset;

            .product {
                float: none;
            }
        }

        ~ .swiper-button-prev:after,
        ~ .swiper-button-next:after {
            content: none;
        }
    }

    &.preset-1 {
        .swiper-pagination .swiper-pagination-bullet {

            &.swiper-pagination-bullet-active {
                //width: 20px;
                background: $theme-preset1;
            }
        }

        .swiper-container .swiper-button-next,
        .swiper-container .swiper-button-prev,
        &.swiper-container-wrap .swiper-button-next,
        &.swiper-container-wrap .swiper-button-prev {
            &:hover {
                background-color: $theme-preset1;
                color: #fff;
            }
        }

        .eael-product-slider {
            text-align: left;

            // default

            .image-wrap img, .image-wrap {
                border-radius: 20px;
            }

            .product-image-wrap, .product-details-wrap {
                align-self: center;
            }

            .eael-onsale {
                top: 5%;
                left: 95%;
                transform: translateX(-95%);
                background: $theme-preset1;

                &.sale-preset-4:after {
                    border-left-color: $theme-preset1;
                }

                &.sale-preset-4.right:after {
                    border-right-color: $theme-preset1;
                }
            }

            .icons-wrap.box-style {
                opacity: 1;
                visibility: visible;
                margin: 20px 0 0;
                position: relative;
                justify-content: left;
                bottom: auto;

                li a {
                    box-shadow: 2px 3px 10px rgba(61,70,79,0.12);
                }
            }
        }

        .product-details-wrap {
            padding: 0;

            .eael-product-price {
                color: $theme-preset1;
                font-weight: 700;

                ins {
                    color: $theme-preset1;
                }
            }
        }

        .eael-product-title * {
            font-size: 60px;
            color: #2d3d44;
        }
    }

    &.preset-2 {
        .swiper-pagination {
            &.dots-preset-4 .swiper-pagination-bullet {
                border: 1px solid $theme-preset2;
            }

            .swiper-pagination-bullet-active {
                background: $theme-preset2;
            }
        }

        .swiper-container .swiper-button-next,
        .swiper-container .swiper-button-prev,
        &.swiper-container-wrap .swiper-button-next,
        &.swiper-container-wrap .swiper-button-prev {
            &:hover {
                background-color: $theme-preset2;
                color: #fff;
            }
        }

        .eael-product-slider {
            background-color: #ffecec;
            text-align: left;
            margin: 15px;
            overflow: hidden;
            grid-gap: 0;
            min-height: 500px;

            .image-wrap img, .image-wrap {
                //border-radius: 20px;
            }

            .eael-onsale {
                background: $theme-preset2;

                &.sale-preset-4:after {
                    border-left-color: $theme-preset2;
                }

                &.sale-preset-4.right:after {
                    border-right-color: $theme-preset2;
                }
            }

            .icons-wrap.box-style-list {
                opacity: 1;
                visibility: visible;
                top: 50%;
                left: 50%;
                right: auto;
                transform: translate(-50%, -50%);
                margin: 0;
            }

            .product-image-wrap {
                position: relative;
                overflow: hidden;

                .image-wrap {
                    margin: 0;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .product-details-wrap {
            padding: 50px;
            align-self: center;

            .eael-product-price {
                color: $theme-preset2;
                font-size: 35px;
                line-height: 1.2em;
                font-weight: 500;
                margin-top: 15px;

                ins {
                    color: $theme-preset2;
                }

                del {
                    font-size: 25px;
                    line-height: 1.2em;
                    color: #917c7c;
                    font-weight: 500;
                }
            }
        }

        .eael-product-title * {
            font-size: 55px;
            line-height: 1.2em;
            color: #2d3d44;
            font-weight: 500;
        }

        .eael-product-excerpt {
            font-size: 16px;
            line-height: 1.5em;
            color: #5a5a5a;
            font-weight: 500;
            margin-top: 30px;
        }
    }

    &.preset-3 {
        &.swiper-container-wrap-dots-outside,
        &.swiper-container-wrap-dots-inside {
            .swiper-pagination {
                &.dots-preset-4 .swiper-pagination-bullet {
                    //border-radius: 2px;
                    border: 1px solid $theme-preset3;

                    &.swiper-pagination-bullet-active {
                        background: $theme-preset3;
                    }
                }
            }
        }

        .swiper-pagination {
            .swiper-pagination-bullet-active {
                background: $theme-preset3;
            }
        }

        .swiper-container .swiper-button-next,
        .swiper-container .swiper-button-prev,
        &.swiper-container-wrap .swiper-button-next,
        &.swiper-container-wrap .swiper-button-prev {
            &:hover {
                background-color: $theme-preset3;
                color: #fff;
            }
        }

        .eael-product-slider {
            //background-color: #fff;
            text-align: left;

            .image-wrap img, .image-wrap {
                border-radius: 20px;
            }

            .eael-onsale {
                background: $theme-preset3;

                &.sale-preset-4:after {
                    border-left-color: $theme-preset3;
                }

                &.sale-preset-4.right:after {
                    border-right-color: $theme-preset3;
                }
            }

            .icons-wrap.box-style {
                opacity: 1;
                visibility: visible;
                margin: 20px 0 0;
                position: relative;
                justify-content: left;
                bottom: auto;

                li a {
                    box-shadow: 2px 3px 10px rgba(61,70,79,0.12);
                    color: $theme-preset3;

                    &:hover {
                        color: #fff;
                        background-color: $theme-preset3;
                    }
                }
            }
        }

        .product-image-wrap {

        }

        .product-details-wrap {
            padding: 0;

            .eael-product-price {
                color: $theme-preset3;
                font-weight: 700;
                margin: 0 0 15px 0;

                ins {
                    color: $theme-preset3;
                }
            }

            .eael-product-excerpt {
                margin-top: 15px;
            }
        }

        .eael-product-title * {
            font-size: 60px;
            color: #2d3d44;
        }
    }

    &.preset-4 {
        .swiper-pagination .swiper-pagination-bullet {

            &.swiper-pagination-bullet-active {
                //width: 20px;
                background: $theme-preset4;
            }
        }

        .swiper-container .swiper-button-next,
        .swiper-container .swiper-button-prev,
        &.swiper-container-wrap .swiper-button-next,
        &.swiper-container-wrap .swiper-button-prev {
            &:hover {
                background-color: $theme-preset4;
                color: #fff;
            }
        }

        .eael-product-slider {
            text-align: left;

            // default
            .image-wrap img, .image-wrap {
                border-radius: 50%;
            }

            .product-image-wrap, .product-details-wrap {
                align-self: center;
            }

            .eael-add-to-cart-button {
                margin-top: 40px;
                
                a.button.add_to_cart_button,
                a.button,
                a.added_to_cart {
                    background: $theme-preset4;
                    border-radius: 30px;

                    &:hover {
                        background: #5f3ff1;
                    }
                }
            }

            .eael-onsale {
                top: 5%;
                left: 50%;
                transform: translateX(-50%);
                background: $theme-preset4;

                &.sale-preset-4:after {
                    border-left-color: $theme-preset4;
                }

                &.sale-preset-4.right:after {
                    border-right-color: $theme-preset4;
                }
            }

            .icons-wrap.box-style {
                li a {
                    box-shadow: 2px 3px 10px rgba(61,70,79,0.12);
                    border-radius: 50%;
                    color: $theme-preset4;
                    
                    &:hover {
                        background: $theme-preset4;
                        color: #fff;
                    }
                }
            }
        }

        .product-image-wrap {
            position: relative;

            &:hover {
                .icons-wrap {
                    &.box-style {
                        bottom: 25%;
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }
        }

        .product-details-wrap {
            padding: 0;

            .eael-product-price {
                color: $theme-preset4;
                font-weight: 700;

                ins {
                    color: $theme-preset4;
                }
            }
        }

        .eael-product-title * {
            font-size: 60px;
            color: #2d3d44;
        }
    }

    .eael-onsale {
        padding: 7px 16px;
        font-size: 16px;
        font-weight: 500;
        position: absolute;
        text-align: center;
        line-height: 1.2em;
        top: 30px;
        left: 0;
        margin: 0;
        background-color: #ff7a80;
        color: #fff;
        z-index: 9;
        width: max-content;

        &.sale-preset-1 {

            &.outofstock {
                br {
                    display: none;
                }
            }

            &.right {
                left: auto;
                right: 0;
            }
        }

        &.sale-preset-2 {
            padding: 0;
            top: 5px;
            left: 5px;
            display: inline-table;
            min-width: 45px;
            min-height: 45px;
            line-height: 45px;
            border-radius: 100%;
            -webkit-font-smoothing: antialiased;

            &.outofstock {
                line-height: normal;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            &.right {
                left: auto;
                right: 5px;
            }
        }

        &.sale-preset-3 {
            border-radius: 50px;
            left: 15px;
            top: 15px;

            &.outofstock {
                br {
                    display: none;
                }
            }

            &.right {
                left: auto;
                right: 15px;
            }
        }

        &.sale-preset-4 {

            &.outofstock {
                br {
                    display: none;
                }
            }

            &:after {
                position: absolute;
                right: -15px;
                bottom: 0;
                width: 15px;
                height: 24px;
                border-top: 16px solid transparent;
                border-bottom: 16px solid transparent;
                border-left: 10px solid #23a454;
                content: '';
            }

            &.right {
                left: auto;
                right: 0;

                &:after {
                    right: auto;
                    left: -15px;
                    border-left: 0;
                    border-right: 10px solid #23a454;
                }
            }
        }

        &.sale-preset-5 {
            display: block;
            line-height: 74px;
            height: 50px;
            width: 100px;
            left: -35pX;
            top: -8px;
            right: auto;
            padding: 0;
            transform: rotate(-45deg);

            &.outofstock {
                line-height: normal;
                display: flex;
                align-items: flex-end;
                justify-content: center;
                padding-bottom: 7px;
            }

            &.right {
                left: auto;
                right: -35px;
                transform: rotate(45deg);
            }
        }

    }

    // slider
    .swiper-image-stretch {
        .product-image-wrap img {
            width: 100%;
            height: 100%;
        }
    }

    // dot
    &.swiper-container-dots-outside .swiper-pagination,
    &.swiper-container-wrap-dots-outside .swiper-pagination {
        position: static;
    }

    &.swiper-container-dots-outside .swiper-pagination,
    &.swiper-container-wrap-dots-outside .swiper-pagination,
    &.swiper-container-dots-inside .swiper-pagination,
    &.swiper-container-wrap-dots-inside .swiper-pagination {

        &.dots-preset-1 .swiper-pagination-bullet {
            border-radius: 2px;
            width: 8px;
            height: 3px;

            &.swiper-pagination-bullet-active {
                width: 20px;
            }
        }

        &.dots-preset-2 .swiper-pagination-bullet {
            border-radius: 0;
        }

        &.dots-preset-3 .swiper-pagination-bullet {

            &.swiper-pagination-bullet-active {
                transform: scale(2);
                margin: 0 7px;
            }
        }

        &.dots-preset-4 .swiper-pagination-bullet {
            border: 1px solid $theme-preset4;
            background: transparent;

            &.swiper-pagination-bullet-active {
                background: $theme-preset4;
            }
        }
    }

    // gallery pagination style
    .eael-woo-product-slider-gallary-pagination {
        width: 350px !important;
        margin-top: 20px;

        .swiper-slide {
            opacity: 0.4;

            &.swiper-slide-active {
                opacity: 1;

                img {
                    //transform: scale(1.2);
                }
            }

            img {
                width: 60px;
                height: 60px;
                transition: all 0.3s ease;
            }
        }

        // pagination visibility
        @media all and (min-width: 1024px) {
            &.eael_gallery_pagination_hide_on_desktop {
                display: none !important;
            }
        }
        @media all and (min-width: 768px) and (max-width: 1024px) {
            &.eael_gallery_pagination_hide_on_tablet {
                display: none !important;
            }
        }
        @media all and (max-width: 767px) {
            &.eael_gallery_pagination_hide_on_mobile {
                display: none !important;
            }
        }
    }


    &.swiper-container-wrap {
        .swiper-pagination {
            bottom: 10px;
            left: 0;
            width: 100%;
        }

        .swiper-pagination-bullet {
            background: #ccc;
            margin: 0 4px;
            opacity: 1;
            height: 8px;
            width: 8px;
            transition: all 0.2s;

            &:focus {
                outline: none;
            }
        }
    }

    .swiper-container .swiper-button-next,
    .swiper-container .swiper-button-prev,
    &.swiper-container-wrap .swiper-button-next,
    &.swiper-container-wrap .swiper-button-prev {
        font-size: 20px;
        margin: 0;
        text-align: center;
        transform: translateY(-50%);
        width: 40px;
        height: 40px;
        border-radius: 5px;
        filter: drop-shadow(0px 23px 13.5px rgba(28,34,56,0.05));
        background-color: #eee;
        background-image: none;
        color: #000;
        transition: all .3s ease;

        &:focus {
            outline: none;
        }

        &.swiper-button-disabled {
            color: #c3c9d0;
            opacity: .7;
        }

        i {
            position: absolute;
            transform: translate(-50%, -50%);
            top: 50%;
            left: 50%;
        }
    }

    .swiper-container .swiper-button-next,
    &.swiper-container-wrap .swiper-button-next {
        right: -40px;
    }

    .swiper-container .swiper-button-prev,
    &.swiper-container-wrap .swiper-button-prev {
        left: -40px;
    }

    // icon
    .product.product-type-grouped,
    .product.product-type-variable,
    .product.product-type-external,
    .product.outofstock {
        .icons-wrap {
            &.block-style {
                grid-template-columns: repeat(2, 1fr);
            }
            li:first-child {
                display: none;
            }
        }

    }

    &.preset-3,
    &.preset-4 {
        .product.product-type-grouped,
        .product.product-type-variable,
        .product.product-type-external,
        .product.outofstock {
            .icons-wrap {
                li {
                    &.view-details {
                        display: none;
                    }
                    &.eael-product-quick-view {
                        display: block;
                    }
                }
            }
        }

        .product.outofstock {
            .icons-wrap {
                li.view-details {
                    display: none;
                }
            }
        }
    }

    .icons-wrap {
        padding: 0;
        list-style: none;
        position: absolute;
        z-index: 9;
        display: block;
        top: 50%;
        left: 0;
        right: 0;
        transform: translateY(0);
        opacity: 0;
        visibility: hidden;
        transform-origin: center center;
        margin: 0 auto;
        transition: all ease 0.4s;


        &.box-style {
            display: inline-flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            top: auto;
            bottom: -100px;

            li {

                a {
                    position: relative;
                    width: 55px;
                    height: 55px;
                    margin: 3px;
                    box-shadow: 0px 15px 10px rgba(61, 70, 79, 0.12);
                    background-color: #ffffff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 5px;
                    color: $theme-preset1;

                    &:hover {
                        background: $theme-preset1;
                    }

                    i {
                        line-height: 1rem;
                    }

                    &.added_to_cart {
                        font-size: 0;

                        &:after {
                            content: '\f217';
                            font-weight: 900;
                            font-family: 'Font Awesome 5 Free';
                            font-size: 20px;
                            text-rendering: auto;
                            -webkit-font-smoothing: antialiased;
                            vertical-align: middle;
                            margin: 0;
                            padding: 0;
                        }
                    }

                    &.button.add_to_cart_button {
                        padding: 0 !important;
                        margin: 3px;
                        font-size: 0px;
                        display: block;
                        border: none;
                        color: $theme-preset1;
                        background-color: #fff;

                        &:before {
                            content: "\f07a";
                            display: block;
                            font-family: "Font Awesome 5 Free";
                            font-size: 20px;
                            font-weight: 900;
                            transform: translate(-50%, -50%);
                            top: 50%;
                            left: 50%;
                            position: absolute;
                        }

                        &:hover {
                            color: #fff;
                            background-color: $theme-preset1;
                        }

                        &.product_type_variable {
                            &:before {
                                content: "\f00c";
                            }
                        }
                    }
                }
            }
        }

        &.box-style-list {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            right: -50px;
            top: 30px;
            transition: .3s ease-in;

            li {
                transition: .3s ease-in;

                &:nth-child(1) {
                    transition-delay: 0.1s;
                }&:nth-child(2) {
                    transition-delay: 0.2s;
                }&:nth-child(3) {
                    transition-delay: 0.3s;
                }&:nth-child(4) {
                    transition-delay: 0.4s;
                }

                a {
                    position: relative;
                    width: 55px;
                    height: 55px;
                    margin: 3px;
                    box-shadow: 0px 15px 10px rgba(61, 70, 79, 0.12);
                    background-color: #ffffff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 5px;
                    color: $theme-preset2;

                    &:hover {
                        background: $theme-preset2;
                    }

                    i {
                        line-height: 1rem;
                    }

                    &.added_to_cart {
                        font-size: 0;

                        &:after {
                            content: '\f217';
                            font-weight: 900;
                            font-family: 'Font Awesome 5 Free';
                            font-size: 20px;
                            text-rendering: auto;
                            -webkit-font-smoothing: antialiased;
                            vertical-align: middle;
                            margin: 0;
                            padding: 0;
                        }
                    }

                    &.button.add_to_cart_button {
                        padding: 0 !important;
                        margin: 3px;
                        font-size: 0px;
                        display: block;
                        border: none;
                        color: $theme-preset2;
                        background-color: #fff;

                        &:before {
                            content: "\f07a";
                            display: block;
                            font-family: "Font Awesome 5 Free";
                            font-size: 20px;
                            font-weight: 900;
                            transform: translate(-50%, -50%);
                            top: 50%;
                            left: 50%;
                            position: absolute;
                        }

                        &:hover {
                            color: #fff;
                            background-color: $theme-preset2;
                        }

                        &.product_type_variable {
                            &:before {
                                content: "\f00c";
                            }
                        }
                    }
                }
            }
        }

        &.block-style {
            background: $theme-preset3;
            display: flex;
            height: 40px;
            width: 100%;
            top: auto;
            bottom: -50px;
            margin: 0;
            color: #fff;

            &:before, &:after {
                content: none;
            }

            li {
                flex: 1;
                border-right: 1px solid #fff;

                &:last-child{
                    border: none;
                }

                &.add-to-cart {
                    //flex: 4;
                }

                a {
                    position: relative;
                    color: #fff;
                    background: $theme-preset3;
                    
                    &:hover {
                        background: transparent;
                        color: #fff;
                    }

                    &.added_to_cart {
                        font-size: 0;
                        border-radius: 0;

                        &:after {
                            content: '\f217';
                            font-weight: 900;
                            font-family: 'Font Awesome 5 Free';
                            font-size: 20px;
                            text-rendering: auto;
                            -webkit-font-smoothing: antialiased;
                            vertical-align: middle;
                            margin: 0;
                            padding: 0;
                        }
                    }

                    &.button.add_to_cart_button {
                        padding: 0 !important;
                        margin: 0;
                        font-size: 0;
                        border-radius: 0;
                        background: $theme-preset3;
                        display: block;
                        border: none;
                        color: inherit;

                        &:hover {
                            background: inherit;
                            color: inherit;
                        }

                        &:before {
                            content: "\f07a";
                            display: block;
                            font-family: "Font Awesome 5 Free";
                            font-size: 20px;
                            font-weight: 900;
                            transform: translate(-50%, -50%);
                            top: 50%;
                            left: 50%;
                            position: absolute;
                        }

                        &.product_type_variable {
                            &:before {
                                content: "\f00c";
                            }
                        }
                    }
                }
            }
        }

        li {
            display: inline-block;
            margin: 0;
            padding: 0;

            a {
                display: flex;
                flex-direction: column;
                justify-content: center;
                position: absolute;
                color: #000;
                width: 100%;
                height: 100%;
                text-align: center;
                transition: all ease 0.4s;

                &:hover {
                    background: #ff7a80;
                    color: #fff;
                }

                i {
                    position: relative;
                    font-size: 22px;
                    line-height: 1.2em;
                }

                svg {
                    width: 22px;
                }
            }
        }
    }

    // Cats
    .eael-product-cats {
        display: inline-flex;
        margin-bottom: 10px !important;

        a {
            font-size: 14px;
            line-height: 1.2em;
            padding: 5px 10px;
            margin-right: 5px;
            position: relative;
            display: inline-block;

            &:after {
                content: "";
                position: absolute;
                top: 100%;
                left: 50%;
                transform: translateX(-50%);
                border-top: 9px solid black;
                border-top-color: #6F35E1;
                border-left: 7px solid transparent;
                border-right: 7px solid transparent;
            }
        }
    }

    // no posts
    .eael-no-posts-found {
        margin: 0;
        background: #ccc;
        color: #000;
        font-size: 16px;
        line-height: 1.2em;
        direction: ltr;
    }
}
//----Responsive -------

@media only screen and (max-width: 1024px) {
    .eael-woo-product-slider-container {
        .eael-woo-product-slider .eael-product-slider {
            grid-gap: 0;

            .eael-product-title * {
                font-size: 30px;
            }

            .eael-product-price {
                font-size: 20px;

                ins {
                    font-size: 20px;
                }

                del {
                    font-size: 18px;
                }
            }

            .eael-product-excerpt {
                font-size: 16px;
                margin-top: 20px;
            }

            .eael-add-to-cart-button {
                a.button,
                a.button.add_to_cart_button,
                a.added_to_cart {
                    font-size: 16px;
                }
            }
        }

        &.preset-2 .eael-product-slider {
            min-height: 350px;

            .product-details-wrap {
                padding: 30px;
            }
        }

        .icons-wrap.box-style li a,
        .icons-wrap.box-style-list li a {
            width: 35px;
            height: 35px;
            font-size: 16px;

            i {
                font-size: 16px;
            }

            &.button.add_to_cart_button:before,
            &.added_to_cart:after {
                font-size: 16px;
            }
        }

        & .swiper-container .swiper-button-prev,
        &.swiper-container-wrap .swiper-button-prev {
            left: 0;
        }

        & .swiper-container .swiper-button-next,
        &.swiper-container-wrap .swiper-button-next {
            right: 0;
        }
    }

}

@media only screen and (max-width: 767px) {
    .eael-woo-product-slider-container {
        .woocommerce ul.products .product .star-rating {
            font-size: 14px!important;
        }

        .eael-woo-product-slider .eael-product-slider {

            .eael-product-title * {
                font-size: 20px;
            }

            .eael-product-price {
                font-size: 16px;

                ins {
                    font-size: 16px;
                }

                del {
                    font-size: 14px;
                }
            }

            .eael-product-excerpt {
                font-size: 14px;
                margin-top: 10px;
            }

            .eael-add-to-cart-button {
                margin-top: 20px;

                a.button,
                a.button.add_to_cart_button,
                a.added_to_cart {
                    font-size: 14px;
                    padding: 9px 20px !important;
                }
            }

        }

        &.preset-2 .eael-product-slider .product-details-wrap {
            padding: 20px;
        }

        &.preset-3 .eael-product-slider {
            .icons-wrap.box-style{
                margin: 10px 0 0;
            }

            .eael-add-to-cart-button {
                margin-top: 10px;
            }
        }

        .icons-wrap.box-style li a,
        .icons-wrap.box-style-list li a {
            width: 25px;
            height: 25px;
            font-size: 12px;

            i {
                font-size: 12px;
            }

            &.button.add_to_cart_button:before,
            &.added_to_cart:after {
                font-size: 12px;
            }
        }

        .eael-onsale {
            font-size: 12px;
            padding: 6px 12px;

            &.sale-preset-4:after {
                border-top: 13px solid transparent;
                border-bottom: 13px solid transparent;
            }
        }

        &.swiper-container-wrap .swiper-pagination {
            position: static;
        }
    }
}

@media only screen and (max-width: 600px) {

    .eael-woo-product-slider-container {
        & .eael-woo-product-slider .eael-reverse-column>div:first-child {

        }

        .eael-woo-product-slider .eael-product-slider {
            grid-template-columns: repeat(1,1fr);

            .product-image-wrap {
                grid-column: 1;
                grid-row: 1;
            }
        }
        .icons-wrap.box-style-list {
            flex-direction: row;
            top: 47%
        }

        &.preset-2 {
            .eael-product-slider {
                min-height: auto;

                .product-image-wrap .image-wrap {
                    position: relative;
                }
            }
            .icons-wrap {
                position: relative;
                grid-column: 1;
                grid-row: 2;
                margin: 10px 0 !important;
            }
        }
    }
}

.theme-oceanwp {
    &.elementor-editor-active {
        //.eael-product-popup.woocommerce div.product form.cart:not(.grouped_form) div.quantity {
        //    width: 37%;
        //}
        //
        //.eael-product-popup.woocommerce div.product form.cart div.quantity .qty {
        //    max-width: 100px;
        //}
    }

    .amount {
        color: inherit;
    }

    //.eael-product-popup.woocommerce div.product form.cart.grouped_form div.quantity {
    //
    //    * {
    //        min-height: 33px;
    //        line-height: 33px;
    //        margin-top: 1px;
    //    }
    //
    //    .qty {
    //        min-width: 100px;
    //
    //        @media only screen and (max-width: 1023px) {
    //            min-width: 70px;
    //        }
    //    }
    //}
    //
    //.eael-product-popup.woocommerce div.product form.cart div.quantity .qty:focus {
    //    border: 1px solid;
    //}
    //
    //@media only screen and (max-width: 767px) {
    //
    //    .eael-product-popup.woocommerce div.product form.cart div.quantity {
    //        width: 50%;
    //
    //        .minus,
    //        .plus {
    //            width: 20%;
    //        }
    //
    //        .qty {
    //            width: 60%;
    //            min-width: auto !important;
    //        }
    //    }
    //
    //    .eael-product-popup.woocommerce div.product form.cart .button.single_add_to_cart_button{
    //        padding: 10px 18px !important;
    //    }
    //}
}
.theme-astra {
    .eael-woo-product-slider {
        ul.swiper-wrapper {
            display: flex;
            column-gap: unset;
        }
    }
    .eael-woo-product-slider-container{
        &.preset-1,
        &.preset-2{
            .eael-product-slider li a.added_to_cart{
                flex-direction: row;
            }
        }
    }
}

// blocksy theme conflicts
.theme-blocksy {
    .button:before {
        -ms-filter: "progid:DXImageTransform.Microsoft.gradient(enabled=false)" /* IE 8+ */;
        filter: none !important; /* IE 7 and the rest of the world */
        opacity: 1;
        z-index: 0;
        bottom: 0!important;
        right: 0;
        line-height: 1.2em
    }

    .button:hover {
        transform: none;
    }
}

.theme-woodmart {
    .amount {
        font-weight: inherit;
        font-size: inherit;
        color: inherit;
    }
    del .amount {
        color: inherit;
        font-weight: inherit;
    }
    .price del {
        color: inherit;
    }
}

//savoy theme conflicts
.theme-savoy {
    .eael-woo-product-slider.woocommerce ul.products li.product {
        .star-rating {
            font-size: 12px;
            letter-spacing: 2px;
            width: 75px;

            &:before {
                font-size: 12px;
                letter-spacing: 2px;
                line-height: 12px;
                left: 0px;
            }

            span{
               font-size: 12px;
               letter-spacing: 2px;

               &:before {
                   font-size: 12px;
                   letter-spacing: 2px;
                   left: 0px;
                   line-height: 12px;
               } 
            }
        } 
    }
}

//Buddyboss theme conflicts
.theme-buddyboss-theme{
    .woocommerce {
        &.eael-woo-product-slider{
            ul.products{
                li.product{
                    max-width: 100%;
                    &:first-of-type{
                        flex: none;
                    }
                    &.type-product{
                        margin: 0;
                    }
                    .product-details-wrap{
                        padding-left: 10px;
                    }
                }
            }
        }
    }
    #content .elementor-widget-eael-woo-product-slider {
        .preset-1 .eael-product-slider,
        .preset-2 .eael-product-slider,
        .preset-3 .eael-product-slider,
        .preset-4 .eael-product-slider{
            width: 100%;
        }
        .preset-1,.preset-2{
            .eael-woo-product-slider {
                li.product {
                    background: transparent;
                    .button {
                        &.add_to_cart_button {
                            width: 55px;
                            border-radius: 5px;
                        }
                    }
                }
                li.product a.added_to_cart{
                    width: 55px;
                    line-height: 0;
                    font-size: 0;
                    border: none;
                    border-radius: 3px;
                    margin: 3px;
                }
            }
        }
        .preset-3,.preset-4{
            .eael-woo-product-slider {
                li.product {
                    background: transparent;
                    .button {
                        &.add_to_cart_button {
                            width: auto;
                            float: left;
                            border-radius: 30px;
                            border: none;
                        }
                    }
                    a.added_to_cart{
                        width: auto;
                        float: left;
                        border-radius: 30px;
                    }
                }
            }
        }
    }
}

//CartZilla
.theme-cartzilla{
    .eael-woo-product-slider-container {
        .eael-woo-product-slider {
            .swiper-wrapper.products {
                .product{
                    flex: none;
                    max-width: 100%;
                }
            }
        }
        &.preset-1 .icons-wrap.box-style li a.button.add_to_cart_button {
            .czi-cart{
                display: none;
            }
            color: #004eff !important;
            &:hover{
                color: #fff !important;
            }
        }

        &.preset-2 .icons-wrap.box-style-list li a.button.add_to_cart_button {
            .czi-cart{
                display: none;
            }
            color: #ff3f56 !important;
            &:hover{
                color: #fff !important;
            }
        }
        &.preset-4,
        &.preset-3{
            .btn-primary{
                border-color: transparent;
                width: auto;
            }
        }
    }
}