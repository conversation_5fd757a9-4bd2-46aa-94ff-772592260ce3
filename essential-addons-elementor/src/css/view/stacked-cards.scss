// Variables
$card-padding: 20px;
$card-margin: 20px;
$border-radius: 15px;
$transition-speed: 0.3s;
$title-color: #333;
$text-color: #666;
$button-color: #05c46b;

body {
   overflow-x: hidden;
}
.eael-stacked-cards__item_hr {
   display: flex;
   align-items: center;
   box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
   position: absolute;
   top: 0;
   left: 0;
   overflow: hidden;
}

// .elementor-widget-eael-stacked-cards {
//    margin-bottom: 20%;
// }

.eael-stacked-cards {
   position: relative;
   // margin-bottom: 80px;
   &__container {
      position: relative;
      width: 100%;
      height: 100vh;
      will-change: position;
      // overflow: hidden;
   }

   &__item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      // height: 80vh;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      position: relative;
   }

   &__media {
      flex: 0 0 60%;
      margin-right: 30px;
   }

   &__image {
      width: 100%;
      height: auto;
      border-radius: 10px;
      object-fit: cover;
   }

   &__content {
      flex: 1;
   }

   &__body {
      padding: 10px 0;
   }

   &__title {
      margin: 0 0 15px;
   }

   &__link {
      font-size: 15px;
      margin-top: 20px;
      display: inline-block;
      overflow: hidden;
      position: relative;
      z-index: 1;
      transition: all 0.3s ease-in;
      &:hover {
         text-decoration: none;
      }
   }
}

@media only screen and (max-width: 767px) {
   .eael-stacked-cards__item,
   .eael-stacked-cards__item_hr {
      flex-direction: column !important;
   }
   .eael-stacked-cards__media {
      margin-right: 0;
   }
}
