.eael-image-hotspots {
	position: relative;
}

.eael-hot-spot-wrap {
	cursor: pointer;
	position: absolute;
	width: 14px;
	height: 14px;
	background: #000;
	border-radius: 50%;
	font-size: 14px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	-webkit-transition: all .3s ease;
	-moz-transition: all .3s ease;
	transition: all .3s ease;
}

.eael-hot-spot-image {
	position: relative;
}

.eael-hotspot-text{
	z-index: 5;
}

.eael-hot-spot-inner {
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
}

.eael-hot-spot-inner.hotspot-animation:before {
	content: '';
	display: block;
	position: absolute;
	z-index: 0;
	pointer-events: none;
	animation: eael-hotspot 4s infinite;
	left: 0;
	top: 0;
}

.eael-hot-spot-inner.hotspot-animation:hover:before {
	animation: none;
}

.eael-hot-spot-inner,
.eael-hot-spot-inner:before {
	background-color: #000;
	border-radius: 50%;
	color: #fff;
	height: 100%;
	position: absolute;
	width: 100%;
	justify-content: center;
	-webkit-transition: all .3s ease;
	-moz-transition: all .3s ease;
	transition: all .3s ease;
}

.eael-hotspot-icon {
	position: relative;
}

.eael-hotspot-icon-wrap {
	display: inline-flex;
	width: 100%;
	height: 100%;
	vertical-align: middle;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
}

.eael-single-tooltip p {
    margin: 0;
}
.eael-hotspot-icon.eael-hotspot-tooltip {
	opacity:1
}

@keyframes eael-hotspot {
	0% {
		transform: scale(1);
		box-shadow: inset 0 0 1px 1px rgba(0, 0, 0, 0.8); }
	50% {
		transform: scale(1.5);
		box-shadow: inset 0 0 1px 1px transparent;
	}
	100% {
		transform: scale(1);
		box-shadow: inset 0 0 1px 1px rgba(0, 0, 0, 0.8); }
}

.tipso_content p:last-child {
	margin-bottom: 0;
}

/*--- Tipso Bubble Styles ---*/
.tipso_bubble, .tipso_bubble > .tipso_arrow{
	-webkit-box-sizing: border-box;
	-moz-box-sizing:    border-box;
	box-sizing:         border-box;
}

.tipso_bubble {
	position: absolute;
	text-align: center;
	border-radius: 6px;
	z-index: 9999;
}

.tipso_style{
	cursor: help;
	border-bottom: 1px dotted;
}

.tipso_title {
	border-radius: 6px 6px 0 0;
}

.tipso_content {
		word-wrap: break-word;
	padding: 0.5em;
}

/*--- Tipso Bubble size classes - Similar to Foundation's syntax ---*/
.tipso_bubble.tiny {
	font-size: 0.6rem;
}

.tipso_bubble.small {
	font-size: 0.8rem;
}

.tipso_bubble.default {
	font-size: 1rem;
}

.tipso_bubble.large {
	font-size: 1.2rem;
	width: 100%;
}

/*--- Tipso Bubble Div ---*/
.tipso_bubble > .tipso_arrow{
	position: absolute;
	width: 0; height: 0;
	border: 8px solid;
	pointer-events: none;
}

.tipso_bubble.top > .tipso_arrow {
	border-top-color: #000;
	border-right-color: transparent;
	border-left-color: transparent;
	border-bottom-color: transparent;
	top: 100%;
	left: 50%;
	margin-left: -8px;
}

.tipso_bubble.bottom > .tipso_arrow {
	border-bottom-color: #000;
	border-right-color: transparent;
	border-left-color: transparent;
	border-top-color: transparent;
	bottom: 100%;
	left: 50%;
	margin-left: -8px;
}

.tipso_bubble.left > .tipso_arrow {
	border-left-color: #000;
	border-top-color: transparent;
	border-bottom-color: transparent;
	border-right-color: transparent;
	top: 50%;
	left: 100%;
	margin-top: -8px;
}

.tipso_bubble.right > .tipso_arrow {
	border-right-color: #000;
	border-top-color: transparent;
	border-bottom-color: transparent;
	border-left-color: transparent;
	top: 50%;
	right: 100%;
	margin-top: -8px;
}

.tipso_bubble .top_right_corner, 
.tipso_bubble.top_right_corner {
	border-bottom-left-radius: 0;
}

.tipso_bubble .bottom_right_corner, 
.tipso_bubble.bottom_right_corner  {
	border-top-left-radius: 0;
}

.tipso_bubble .top_left_corner, 
.tipso_bubble.top_left_corner {
	border-bottom-right-radius: 0;
}

.tipso_bubble .bottom_left_corner, 
.tipso_bubble.bottom_left_corner  {
	border-top-right-radius: 0;
}

.eael-image-hotspot-align-left {
	.eael-hot-spot-image {
		margin: auto 0 0 0;
	}
}

.eael-image-hotspot-align-centered {
	.eael-hot-spot-image {
		margin: 0 auto;
	}
}

.eael-image-hotspot-align-right {
	.eael-hot-spot-image {
		margin: 0 0 0 auto;
	}
}

.eael-hot-spot-image {
	img {
		display: block;
		margin: auto;
        z-index: -1;
	}
}

// RTL

.rtl {
	.eael-image-hotspot-align-left .eael-hot-spot-image {
		margin: auto auto 0 0;
	}
}
