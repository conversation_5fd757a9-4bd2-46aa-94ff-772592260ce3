/*------------------------------*/
/* Login Register Pro Style
/*------------------------------*/


/*-----Form Specific---*/
.eael-lr-form-wrapper {
    //Style when when input icon is showing
    &.lr-icon-showing {
        .eael-lr-form-group {
            position: relative;
        }

        .eael-lr-form-control {
            padding: 15px 15px 15px 30px;

        }

        i {
            position: absolute;
            bottom: 20px;
            left: 10px;
            color: inherit;
        }
        svg {
            position: absolute;
            bottom: 20px;
            left: 10px;
            color: inherit;
            height: 1rem;
            width: 1rem;
            line-height: 1rem;
        }
        .lr-social-login-container svg {
            position: relative;
            bottom: 0;
            left: 0;
            right: 5px;
        }
    }

    .eael-lr-form-loader-wrapper {
        display: flex;
        align-items: center;
        position: relative;

        .eael-lr-form-loader {
            position: absolute;
            right: 10px;

            svg{
                animation: eael-lr-spin 2s linear infinite;
            }
        }

        @keyframes eael-lr-spin {
            from {
                transform:rotate(0deg);
            }
            to {
                transform:rotate(360deg);
            }
        }
    }

    /* Password Strength Meter */
    meter {
        /* Reset the default appearance */
        display: block;
        margin: 0 auto;
        width: 100%;
        height: 1em;
    }

    @supports (-moz-appearance: none) {
        /* Applicable only to Firefox */
        meter {
            -moz-appearance: none;
            height: 0.5em;
            background: none;
            background-color: rgba(0, 0, 0, 0.1);
        }
    }


    meter::-webkit-meter-bar {
        background: none;
        background-color: rgba(0, 0, 0, 0.1);
    }

    /* Webkit based browsers */
    meter[value="0"]::-webkit-meter-optimum-value,
    meter[value="1"]::-webkit-meter-optimum-value,
    meter[value="2"]::-webkit-meter-optimum-value {
        background: #ff2828;
    }

    meter[value="3"]::-webkit-meter-optimum-value {
        background: #ffa500;
    }

    meter[value="4"]::-webkit-meter-optimum-value {
        background: #008000;
    }

    /* Gecko based browsers */
    meter[value="0"]::-moz-meter-bar,
    meter[value="1"]::-moz-meter-bar,
    meter[value="2"]::-moz-meter-bar {
        background: #ff2828;
    }

    meter[value="3"]::-moz-meter-bar {
        background: #ffa500;
    }

    meter[value="4"]::-moz-meter-bar {
        background: #008000;
    }

    meter::-webkit-meter-optimum-value {
        transition: width .4s linear;
    }

    meter::-moz-meter-bar {
        transition: width .4s linear;
    }

}

// strength text
.eael-pass-notice {
    &.mismatch {
        color: #ff2828;
    }

    &.short {
        color: #ff2828;
    }

    &.bad {
        color: #ff2828;
    }

    &.good {
        color: #FFA500;
    }

    &.strong {
        color: #5cb85c;
    }
}

//Social Login
.lr-social-login-container {
    margin-top: 1rem;

    .lr-separator {
        width: 100%;
        text-align: center;

        p {
            padding: 0;
            margin: 0;
        }

        hr {
            width: 100%;
            height: 1px;
            background: gray;
        }
    }

    .lr-social-buttons-container {
        display: flex;
        flex-wrap: wrap;
    }

    .eael-social-button {
        display: flex;
        max-width: 100%;
        justify-content: space-evenly;
        align-items: center;
        padding-top: 10px;
        margin-left: auto;
        margin-right: auto;
        cursor: pointer;

        &.eael-gis iframe {
            margin: 0 !important;
        }

        &.eael-facebook {
            width: 190px;
        }
    }
}

