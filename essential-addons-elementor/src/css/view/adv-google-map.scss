.eael-google-map-marker-search{
    position: absolute;
    z-index: 999999;

    input{
        width: 100%;
        border: 1px solid #d9d9d9;
        border-radius: 3px;
        padding: .5rem 1rem;
        transition: all .3s;
        background: #fff;

        &::placeholder{
            opacity: .5 !important;
        }
    }
    ul{
        position: absolute;
        width: 100%;
        background: #fff;
        list-style: none;
        z-index: 999999;
        margin: 0;
        padding: 0;
        border-radius: 0 0 3px 3px;

        li{
            border: none;
            border-bottom: 1px solid #d9d9d9;
            padding: 5px 15px;
            font-size: 14px;
            opacity: .7;

            &:hover{
                cursor: pointer;
                opacity: 1;
            }

            &:last-child{
                border: none;
            }
        }
    }
}
.eael-google-map {
    .gm-svpc img {
        max-width: unset;
    }
    .gm-style-iw-chr{
        position: relative;
        button{
            position: absolute !important;
            right: -10px;
            top: -10px;
            font-size: 14px;
        }
    }
    .gmap-info-window{
        .gmap-info-title{
            font-size: 16px;
            font-weight: 600;
            margin: 5px;
        }
        .gmap-info-content{
            font-size: 14px;
            margin: 5px;
        }
    }
}