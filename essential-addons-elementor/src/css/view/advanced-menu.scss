.eael-advanced-menu-container{
    min-height: 50px;
}
.elementor-widget-eael-advanced-menu.eael-hamburger--mobile.eael-hamburger--responsive,
.elementor-widget-eael-advanced-menu.eael-hamburger--mobile_extra.eael-hamburger--responsive,
.elementor-widget-eael-advanced-menu.eael-hamburger--tablet.eael-hamburger--responsive,
.elementor-widget-eael-advanced-menu.eael-hamburger--tablet_extra.eael-hamburger--responsive,
.elementor-widget-eael-advanced-menu.eael-hamburger--laptop.eael-hamburger--responsive,
.elementor-widget-eael-advanced-menu.eael-hamburger--desktop.eael-hamburger--responsive,
.elementor-widget-eael-advanced-menu.eael-hamburger--widescreen.eael-hamburger--responsive {
    .eael-advanced-menu-container {
        min-height: 50px;
        overflow: auto;
    
        .eael-advanced-menu {
            > li {
                > a {
                    text-align: center;
                }
            }
    
            &.eael-advanced-menu-horizontal,
            &.eael-advanced-menu-vertical {
                position: absolute;
                top: 50px;
                background-color: #54595f;
                width: 100%;
                z-index: 9;
                display: none;
    
                > li {
                    > a {
                        text-align: right;
                    }
                }
    
                li {
                    float: none;
                    display: block;
    
                    a {
                        display: block;
                        text-align: inherit;
    
                        span {
                            display: none;
                        }
                    }
    
                    ul {
                        position: relative;
                        top: unset;
                        left: unset;
                        width: 100%;
                        box-shadow: none;
                        visibility: visible;
                        opacity: 1;
                        display: none;
    
                        li {
                            position: relative;
                            display: block;
    
                            .eael-advanced-menu-indicator {
                                border: 1px solid #00000080;
    
                                &:before {
                                    color: #000000;
                                }
                            }
    
                            ul {
                                position: relative;
                                width: 100%;
                                top: unset;
                                left: unset;
    
                                li {
                                    &:last-child {
                                        > a {
                                            border-bottom-width: 1px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    
            &.eael-hamburger-center {
                li {
                    .eael-admin-menu-indicator {
                        left: initial !important;
                        right: 10px !important;
                    }
                    a {
                        text-align: center !important;
                    }
                }
            }
            &.eael-hamburger-left {
                li {
                    .eael-admin-menu-indicator {
                        left: initial !important;
                        right: 10px !important;
                    }
                    a {
                        text-align: left !important;
                    }
                }
        
            }
            &.eael-hamburger-right {
                li {
                    .eael-admin-menu-indicator {
                        right: initial !important;
                        left: 10px !important;
                    }
                    a {
                        text-align: right !important;
                    }
                }
        
            }
    
        }
    }
}

.eael_advanced_menu_hamburger_disable_selected_menu_hide.eael-hamburger--mobile.eael-hamburger--responsive,
.eael_advanced_menu_hamburger_disable_selected_menu_hide.eael-hamburger--mobile_extra.eael-hamburger--responsive,
.eael_advanced_menu_hamburger_disable_selected_menu_hide.eael-hamburger--tablet.eael-hamburger--responsive,
.eael_advanced_menu_hamburger_disable_selected_menu_hide.eael-hamburger--tablet_extra.eael-hamburger--responsive,
.eael_advanced_menu_hamburger_disable_selected_menu_hide.eael-hamburger--laptop.eael-hamburger--responsive,
.eael_advanced_menu_hamburger_disable_selected_menu_hide.eael-hamburger--desktop.eael-hamburger--responsive,
.eael_advanced_menu_hamburger_disable_selected_menu_hide.eael-hamburger--widescreen.eael-hamburger--responsive {
    .eael-advanced-menu-toggle-text {
        display: none;
    }
}

.eael-advanced-menu-hamburger-align-left.eael-hamburger--mobile.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-left.eael-hamburger--mobile_extra.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-left.eael-hamburger--tablet.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-left.eael-hamburger--tablet_extra.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-left.eael-hamburger--laptop.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-left.eael-hamburger--desktop.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-left.eael-hamburger--widescreen.eael-hamburger--responsive {
    .eael-advanced-menu-toggle {
        top: 0;
        left: 0;
        right: auto;
    }
}

.eael-advanced-menu-hamburger-align-center.eael-hamburger--mobile.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-center.eael-hamburger--mobile_extra.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-center.eael-hamburger--tablet.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-center.eael-hamburger--tablet_extra.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-center.eael-hamburger--laptop.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-center.eael-hamburger--desktop.eael-hamburger--responsive,
.eael-advanced-menu-hamburger-align-center.eael-hamburger--widescreen.eael-hamburger--responsive {
    .eael-advanced-menu-toggle {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
    }
}

.elementor-widget-eael-advanced-menu.eael-hamburger--none,
.elementor-widget-eael-advanced-menu.eael-hamburger--not-responsive { 
    .eael-advanced-menu-container {
        .eael-advanced-menu-toggle {
            display: none;
        }
    
        .eael-advanced-menu-toggle-text {
            display: none;
        }
        
        .eael-advanced-menu {
            &.eael-advanced-menu-horizontal {
                // animation
                &.eael-advanced-menu-dropdown-animate-fade {
                    li {
                        ul {
                            transition: all 300ms;
                        }
                    }
                }
    
                &.eael-advanced-menu-dropdown-animate-to-top {
                    li {
                        ul {
                            transform: translateY(20px);
                            transition: all 300ms;
                        }
    
                        &:hover {
                            > ul {
                                transform: translateY(0);
                            }
                        }
                    }
                }
    
                &.eael-advanced-menu-dropdown-animate-zoom-in {
                    li {
                        ul {
                            transform: scale(0.8);
                            transition: all 300ms;
                        }
    
                        &:hover {
                            > ul {
                                transform: scale(1);
                            }
                        }
                    }
                }
    
                &.eael-advanced-menu-dropdown-animate-zoom-out {
                    li {
                        ul {
                            transform: scale(1.2);
                            transition: all 300ms;
                        }
    
                        &:hover {
                            > ul {
                                transform: scale(1);
                            }
                        }
                    }
                }
    
                // hide indicator
                .eael-advanced-menu-indicator {
                    display: none;
                }
            }
        }
    
        &.eael-advanced-menu-align-center {
            text-align: center;
    
            .eael-advanced-menu {
                &.eael-advanced-menu-horizontal {
                    display: inline-flex;
                }
            }
        }
    
        &.eael-advanced-menu-align-right {
            text-align: right;
    
            .eael-advanced-menu {
                &.eael-advanced-menu-horizontal {
                    display: inline-flex;
                }
            }
        }
    }
}

.eael-advanced-menu-container {
    background-color: #54595f;

    .eael-advanced-menu-toggle {
        position: absolute;
        top: 0;
        right: 0;
        min-height: 50px;
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 15px;
        padding-right: 15px;
        background-color: #000000;
        color: #ffffff;
        border: none;
        border-radius: 0;
        outline: none;
        display: block;
        border-color:#333;
    }

    .eael-advanced-menu-toggle-text {
        position: absolute;
        top: 0;
        left: 20px;
        font-size: 14px;
        line-height: 50px !important;
        letter-spacing: 0 !important;
        color: #ffffff;
    }

    .eael-advanced-menu {
        margin: 0;
        padding: 0;
        list-style: none;

        &:after {
            content: "";
            display: table;
            clear: both;
        }

        a {
            text-decoration: none;
            outline: none;
            box-shadow: none;
        }

        li {
            a {
                font-size: 14px;
                font-weight: 400;
                line-height: 50px;
                text-align: center;
                color: #ffffff;
                padding-left: 20px;
                padding-right: 20px;
                transition: all 100ms;
            }

            ul {
                margin: 0;
                padding: 0;
                list-style: none;

                li {
                    a {
                        font-size: 13px;
                        font-weight: 400;
                        line-height: 40px;
                        text-align: left;
                        color: #000;
                        padding-left: 20px;
                        padding-right: 20px;
                    }

                    &:hover,
                    &.current-menu-item {
                        > a.eael-item-active {
                            color: #ee355f;
                            background-color: #ffffff;
                        }
                    }
                }
            }

            &:hover,
            &.current-menu-item {
                > a.eael-item-active {
                    color: #ffffff;
                    background-color: #ee355f;
                }
            }
        }

        .eael-advanced-menu-indicator {
            position: absolute;
            top: 0;
            right: 10px;
            width: 28px;
            height: 28px;
            display: block;
            border: 1px solid #ffffff80;
            border-radius: 2px;
            text-align: center;
            transform: translateY(-50%);
            cursor: pointer;
            svg {
                height: 15px;
                margin: 20% auto;
                transition: transform 300ms;
            } 
            &:before, i {
                display: block;
                font-weight: 900;
                line-height: 26px;
                color: #ee355f;
                transition: transform 300ms;
            }

            &.eael-advanced-menu-indicator-open {
                &:before, svg, i {
                    transform: rotate(180deg);
                }
            }
        }

        &.eael-advanced-menu-horizontal {
            li {
                position: relative;
                float: left;
                display: inline-block;
                padding-top: 0;
                padding-bottom: 0;

                a {
                    display: inline-block;
                    border-left: 1px solid transparent;

                    // indicator
                    span {
                        position: relative;
                        margin-left: 5px;
                    }
                }

                ul {
                    position: absolute;
                    top: 100%;
                    left: auto;
                    width: 220px;
                    visibility: hidden;
                    opacity: 0;
                    background-color: #ffffff;
                    box-shadow: 1px 1px 5px #0000001a;
                    z-index: 1;

                    li {
                        position: relative;
                        float: none;
                        display: block;

                        a {
                            display: block;

                            // indicator
                            span {
                                transform: rotate(-90deg);
                            }
                        }

                        &:last-child {
                            > a {
                                border-bottom-width: 0;
                                border-bottom: 1px solid transparent;
                                border-right: 0px solid transparent;
                                border-left: 0px solid transparent;
                            }
                        }

                        ul {
                            top: 0;
                            left: 100%;
                        }
                    }
                }

                &:hover {
                    > ul {
                        visibility: visible;
                        opacity: 1;
                    }
                }
                &:last-child a {
                    border-right: 1px solid transparent;
                }
            }
        }

        // Vertical Menu
        &.eael-advanced-menu-vertical {
            li {
                position: relative;

                a {
                    display: block;
                    text-align: left;
                    border-bottom: 1px solid transparent;
                }

                ul {
                    display: none;

                    li {
                        position: relative;
                        display: block;

                        a {
                            border-bottom-style: solid;
                            border-bottom-width: 1px;
                            border-bottom-color: transparent;
                        }

                        &:last-child {
                            > a {
                                border-bottom-width: 0;
                            }
                        }

                        .eael-advanced-menu-indicator {
                            border: 1px solid #00000080;

                            &:before {
                                color: #000000;
                            }
                        }

                        ul {
                            li {
                                &:last-child {
                                    > a {
                                        border-bottom-width: 1px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // alignment
    &.eael-advanced-menu-align-center {
        .eael-advanced-menu {
            &.eael-advanced-menu-vertical {
                > li {
                    > a {
                        text-align: center;
                    }
                }
            }
        }
    }

    &.eael-advanced-menu-align-right {
        .eael-advanced-menu {
            &.eael-advanced-menu-horizontal {
                > li {
                    > .eael-advanced-menu-indicator {
                        right: initial;
                        left: 10px;
                    }

                    &:last-child {
                        a {
                            border-right: none;
                        }
                    }
                }
            }

            &.eael-advanced-menu-vertical {
                > li {
                    > a {
                        text-align: right;
                    }

                    > .eael-advanced-menu-indicator {
                        right: initial;
                        left: 10px;
                    }
                }
            }
        }
    }

    &.eael-advanced-menu-dropdown-align-center {
        .eael-advanced-menu {
            li ul li a {
                text-align: center;
            }
        }
    }

    &.eael-advanced-menu-dropdown-align-right {
        .eael-advanced-menu {
            li ul li {
                a {
                    text-align: right;
                }

                > .eael-advanced-menu-indicator {
                    right: initial;
                    left: 10px;
                }
            }
        }
    }
}

.eael-advanced-menu--stretch {
    .eael-advanced-menu.eael-advanced-menu-responsive {
        left: 50%;
        transform: translateX(-50%);
        transition: max-height .3s,transform .3s,-webkit-transform .3s;
        transform-origin: top;
        overflow: auto;
        z-index: 9999;
        max-height: 100vh;
    }
}

// Twenty TwentyOne Support

.theme-twentytwentyone .eael-advanced-menu-container button.sub-menu-toggle {
    display: none;
}


// Astra Theme Support
.theme-astra .eael-advanced-menu-container .eael-advanced-menu span.ast-header-navigation-arrow,
.ast-container .eael-advanced-menu-container span.dropdown-menu-toggle {
    display: none;
}

.theme-astra .eael-advanced-menu-container .eael-advanced-menu li ul li a span.ast-icon,
.ast-container .eael-advanced-menu-container .eael-advanced-menu.eael-advanced-menu-horizontal li ul li a span {
    display: none;
}

.theme-astra {

}

.eael-advanced-menu-container {
    .eael-advanced-menu-toggle {
        svg {
            width: 35px;
            height: auto;
        }
    }
}

.eael-advanced-menu-container .eael-advanced-menu-toggle {
    display: none;
}