// Fonts
$bodyFont: 'Lato', sans-serif;

// color
$white: #ffffff !default;
$black: #000000 !default;
$danger: #dc4444 !default;

.template__wrapper {
	padding: 100px;
}

.eael-advanced-search-widget {
	font-family: $bodyFont;
	font-size: 14px;

	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}
}

.eael-advanced-search-widget {
	position: relative;
	padding-bottom: 10px;

	.eael-advanced-search-result {
		display: none;
		z-index: 9999;
		overflow-y: auto;
		max-height: 50vh;
	}

	.eael-advanced-search-form {
		display: flex;

		.eael-advanced-search-wrap {
			position: relative;
			flex: 1 1 auto;
			display: flex;

			.icon {
				position: absolute;
				top: 50%;
				left: 20px;
				transform: translateY(-50%);
				z-index: 4;
				pointer-events: none;
				font-size: 18px;
				line-height: 1;

				i {
					color: #D5DBF0;
				}
			}

			.icon + .eael-advanced-search {
				padding-left: 55px;
			}

			.eael-adv-search-close {
				position: absolute;
				top: 50%;
				right: 20px;
				z-index: 4;
				display: none;
				transform: translateY(-50%);
				height: 30px;
				width: 30px;
				line-height: 30px;
				text-align: center;
				border-radius: 50%;
				cursor: pointer;
				background: rgba($danger, .1);
				color: $danger;

				&:hover {
					color: $white;
					background: $danger;
				}
			}

			.eael-adv-search-loader {
				position: absolute;
				top: 50%;
				right: 20px;
				z-index: 4;
				display: none;
			}

			.eael-adv-search-loader,
			.eael-adv-search-loader:after {
				border-radius: 50%;
			}

			.eael-adv-search-loader {
				height: 30px;
				width: 30px;
				text-indent: -9999em;
				border-top: 3px solid rgba(0, 0, 0, 0.2);
				border-right: 3px solid rgba(0, 0, 0, 0.2);
				border-bottom: 3px solid rgba(0, 0, 0, 0.2);
				border-left: 3px solid rgb(255, 255, 255);
				-webkit-animation: eaelLoad8 1.1s infinite linear;
				animation: eaelLoad8 1.1s infinite linear;
			}

			@-webkit-keyframes eaelLoad8 {
				0% {
					-webkit-transform: translateY(-50%) rotate(0deg);
					transform: translateY(-50%) rotate(0deg);
				}
				100% {
					-webkit-transform: translateY(-50%) rotate(360deg);
					transform: translateY(-50%) rotate(360deg);
				}
			}
			@keyframes eaelLoad8 {
				0% {
					-webkit-transform: translateY(-50%) rotate(0deg);
					transform: translateY(-50%) rotate(0deg);
				}
				100% {
					-webkit-transform: translateY(-50%) rotate(360deg);
					transform: translateY(-50%) rotate(360deg);
				}
			}

			.eael-advanced-search {
				height: 100%;
				width: 100%;
				border-radius: 10px;
				padding: 0 20px;
				font-size: 20px;
				font-family: $bodyFont;
				color: #434872;
				position: relative;
				z-index: 3;
				outline: none;
				border: 0;
				background: white;

				&::-webkit-input-placeholder {
					color: rgb(67, 72, 114, .7);
				}

				&::-moz-placeholder {
					color: rgb(67, 72, 114, .7);
				}

				&:-ms-input-placeholder {
					color: rgb(67, 72, 114, .7);
				}

				&:-moz-placeholder {
					color: rgb(67, 72, 114, .7);
				}
			}
		}

		.eael-advance-search-select {
			margin-left: -10px;
			z-index: 2;
			width: 200px;
			position: relative;

			.icon {
				position: absolute;
				top: 50%;
				right: 20px;
				transform: translateY(-50%);
				// color: #434872;
				pointer-events: none;
			}

			select {
				width: 100%;
				height: 100%;
				font-size: 20px;
				line-height: 1.4;
				color: #434872;
				-webkit-appearance: none;
				outline: none;
				box-shadow: none;
				background-image: none;
			}
		}

		.eael-advanced-search-button {
			height: 70px;
			line-height: 70px;
			margin-left: -10px;
			background: #470BF1;
			color: $white;
			border: 0;
			border-top-right-radius: 10px;
			border-bottom-right-radius: 10px;
			text-transform: uppercase;
			font-size: 18px;
			font-weight: 400;
			letter-spacing: 2px;
			cursor: pointer;
			border: 0;
			box-shadow: none;
			outline: none;
		}

		&.eael-advanced-search-form-style-1 {
			.eael-advanced-search-wrap {
				.eael-advanced-search {
					box-shadow: 0px 4px 8px rgba(165, 174, 213, 0.15);
					border: 1px solid #E0E5F8;
				}
			}

			.eael-advance-search-select {
				select {
					border: 1px solid #E0E5F8;
					border-top-right-radius: 10px;
					border-bottom-right-radius: 10px;
					padding: 0;
					padding-left: 20px;
					height: 100%;
					cursor: pointer;
				}
			}
		}

		&.eael-advanced-search-form-style-2 {
			padding: 10px;
			background: $white;
			box-shadow: 0px 4px 8px rgba(165, 174, 213, 0.15);
			border-radius: 10px;

			.eael-advanced-search-wrap {
				.eael-advanced-search {
					border-right: 1px solid #E0E5F8;
				}
			}

			.eael-advance-search-select {
				margin-left: 0;

				select {
					border: 0;
					padding: 0;
					padding-left: 20px;
					outline: none;
				}
			}

			.eael-advanced-search-button {
				border-top-left-radius: 10px;
				border-bottom-left-radius: 10px;
				margin-left: 0;
			}
		}

		&.eael-advanced-search-form-style-3 {
			padding: 10px;
			background: $white;
			box-shadow: 0px 4px 8px rgba(165, 174, 213, 0.15);
			border-radius: 10px;

			.eael-advance-search-select {
				margin-left: 0;

				select {
					border: 0;
					padding: 0;
					padding-left: 20px;
					outline: none;
				}
			}

			.eael-advanced-search-button {
				border-top-left-radius: 10px;
				border-bottom-left-radius: 10px;
				margin-left: 0;
			}
		}

		@media screen and (max-width: 600px) {
			flex-wrap: wrap;
			.eael-advance-search-select,
			.eael-advanced-search-button {
				width: 100%;
				margin-left: 0;
			}
			.eael-advanced-search-wrap {
				flex: unset;
			}
		}
	}

	.eael-advanced-search-result {
		position: absolute;
		top: 100%;
		left: 0;
		width: 100%;
		background: $white;
		border: 1px solid #E0E5F8;
		box-shadow: 0px 4px 8px rgba(165, 174, 212, 0.15);
		border-radius: 5px;
		padding: 20px 30px;

		> *:not(:last-child) {
			margin-bottom: 20px;
		}

		.eael-advanced-search-popular-keyword {
			display: flex;
			align-items: flex-start;
			padding-bottom: 15px;
			border-bottom: 1px solid #F4F6FE;

			.eael-advanced-search-popular-keyword-text {
				flex: 0 0 auto;
				font-size: 14px;
				font-weight: 400;
				color: #8588A6;
				margin-right: 15px;
				margin-top: 5px;
			}

			.eael-popular-keyword-content {
				.eael-popular-keyword-item {
					padding: 6px 25px;
					border-radius: 3px;
					color: #434872;
					background: #F0FDFF;
					text-transform: capitalize;
					text-decoration: none;
					margin-right: 5px;
					margin-bottom: 5px;
					display: inline-block;
				}
			}
		}

		.eael-advanced-search-category {
			.eael-advanced-search-category-text {
				margin: 0;
				font-size: 15px;
				font-weight: 400;
				color: #8588A6;
				padding: 0 0 10px 0;
				border-bottom: 1px solid #F4F6FE;
			}

			.eael-popular-category-content {
				border-bottom: 1px solid #F4F6FE;

				ul {
					padding: 15px 0;
					margin: 0;
					list-style: none;

					li {
						&:not(:last-child) {
							margin-bottom: 12px;
						}

						a {
							color: #434872;
							font-weight: 400;
							font-size: 16px;
							transition: color, .3s ease;
							text-transform: capitalize;
							text-decoration: none;
							display: inline-block;

							&:hover {
								color: #00D6C9;
							}
						}
					}
				}
			}
		}

		.eael-advanced-search-content {
			.eael-advanced-search-content-item {
				display: flex;
				align-items: center;
				text-decoration: none;
				transition: all .3s ease;

				&:not(:last-child) {
					margin-bottom: 20px;
				}

				.item-thumb {
					flex: 0 0 120px;
					width: 120px;
					height: 70px;
					border-radius: 3px;
					overflow: hidden;
					margin-right: 20px;

					img {
						height: 100%;
						width: 100%;
						object-fit: cover;
					}
				}

				.item-content {
					h4 {
						margin: 0;
						font-size: 20px;
						font-weight: 400;
						color: #434872;
						margin-bottom: 5px;
						transition: color, .3s ease;
					}

					p {
						margin: 0;
						font-size: 16px;
						font-weight: 400;
						color: #B2B9C6;
					}
				}
			}

			&.eael-item-style-1 {
				a:hover {
					.item-content {
						h4 {
							color: #470BF1;
						}
					}
				}
			}


			&.eael-item-style-2 {
				a {
					padding: 10px;

					&:hover {
						background: #fff7df;
					}
				}
			}

			&.eael-item-style-3 {
				a {
					border-radius: 3px;
					padding: 10px;

					&:hover {
						box-shadow: 0px 8px 18px rgba(102, 107, 145, 0.2);
					}
				}
			}
		}

		@media screen and (max-width: 600px){
			padding: 10px;
			> *:not(:last-child) {
				margin-bottom: 10px;
			}
			.eael-advanced-search-popular-keyword{
				padding-bottom: 7px;
				.eael-advanced-search-popular-keyword-text{
					font-size: 12px;
				}
				.eael-popular-keyword-content{
					.eael-popular-keyword-item{
						padding: 2px 10px;
						font-size: 12px;
					}
				}
			}
			.eael-advanced-search-category{
				.eael-advanced-search-category-text{
					font-size: 12px;
				}
				.eael-popular-category-content{
					ul {
						padding: 5px 0;
						li a{
							font-size: 13px;
						}
					}
				}
			}
			.eael-advanced-search-content .eael-advanced-search-content-item {
				.item-thumb{
					flex: 0 0 90px;
				}
				.item-content {
					h4{
						font-size: 15px;
					}
					p{
						font-size: 14px;
					}
				}
			}
			.eael-advanced-search-not-found p{
				font-size: 15px;
			}
		}
	}

	.eael-after-adv-search {
		margin-top: 20px;

		&.eael-advanced-search-popular-keyword {
			display: flex;
			align-items: flex-start;

			.eael-advanced-search-popular-keyword-text {
				flex: 0 0 auto;
				font-size: 14px;
				font-weight: 400;
				color: #8588A6;
				margin-right: 15px;
				margin-top: 10px;
			}

			.eael-popular-keyword-content {
				.eael-popular-keyword-item {
					padding: 6px 25px;
					border-radius: 3px;
					color: #434872;
					background: #F0FDFF;
					text-transform: capitalize;
					text-decoration: none;
					margin-right: 5px;
					margin-bottom: 5px;
					display: inline-block;
					border: 1px solid #DDDEFF;
					box-sizing: border-box;
				}
			}
		}
	}

	.eael-advanced-search-not-found {
		display: none;
		margin-bottom: 1px !important;

		p {
			font-size: 20px;
			text-align: center;
		}
	}

	.eael-advanced-search-load-more {
		// display: inline-block;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.eael-advanced-search-load-more-button {
			display: none;
			padding: 10px;
			background: #470bf1;
			color: white;
			border-radius: 5px;

			&:focus {
				outline: none;
			}
		}

		.eael-advanced-search-post-count-wrap {
			color: #434872;
		}
	}
}
