.eael-team-member-carousel-wrap {

	.eael-tm-wrapper {
		position: relative;
	
		&.swiper-container {
			width: 100%;
	
			~ .swiper-button-prev:after,
			~ .swiper-button-next:after {
				content: none;
			}
		}

		.eael-tm-social-links {
			list-style: none;
			margin: 0;
			padding: 0;
			.eael-tm-social-icon {
				-webkit-transition: all 0.25s linear 0s;
				transition: all 0.25s linear 0s;
			}

			li {
				list-style: none;
				margin: 0;
				padding: 0;

				a {
					-webkit-transition: all 0.25s linear 0s;
					transition: all 0.25s linear 0s;
				}
			}
		}

		.eael-tm-title-divider-wrap {
			font-size: 0;
			line-height: 1;
		}
		
		li,
		.eael-tm-social-icon,
		.eael-tm-divider {
			display: inline-block;
		}
		
		.eael-tm:hover .eael-tm-overlay-content-wrap {
			opacity: 1;
			visibility: visible;
		}
		
		.eael-tm-image {
			display: inline-block;
			position: relative;
		}
	}

	.swiper-button-next:after, .swiper-rtl .swiper-button-prev:after,
    .swiper-button-prev:after, .swiper-rtl .swiper-button-next:after {
        content: '';
    }

	.eael-tm-content-normal {
		position: relative;
		z-index: 1;
		padding: 10px 0;
	}
	
	.eael-marquee-carousel .swiper-wrapper{
		transition-timing-function: linear !important; 
	}

	.eael-tm-overlay-content-wrap {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 1;
		opacity: 0;
		visibility: hidden;
		-webkit-transition: all 0.25s linear 0s;
		transition: all 0.25s linear 0s;

		&:before {
			background-color: #000;
			content: '';
			display: block;
			position: absolute;
			left: 0;
			top: 0;
			right: 0;
			bottom: 0;
			opacity: 0.5;
			z-index: -1;
		}

		.eael-tm-content {
			padding: 20px;
			width: 100%;
			position: absolute;
			top: 50%;
			-webkit-transform: translateY(-50%);
			-ms-transform: translateY(-50%);
			transform: translateY(-50%);
		}
	}

	.eael-tm-social-icon-wrap {
		display: inline-flex;
		
	}
	.eael-tm-carousel {
		position: relative;
	}
	
	.eael-tm-name {
		margin-top: 0;
	}

	&.swiper-container-wrap .swiper-button-prev i {
		transform: rotate(0deg);
	}

	.eael-tm-carousel-dots-outside .swiper-pagination {
		position: static;
	}

	&.swiper-container-wrap .swiper-slide,
	&.swiper-container .swiper-slide {
		text-align: center;

		img {
			width: auto;
		}
	}

	&.swiper-container-wrap-dots-outside .swiper-pagination,
	&.swiper-container-dots-outside .swiper-pagination {
		position: static;
	}

	&.swiper-container-wrap .swiper-button-next,
	&.swiper-container-wrap .swiper-button-prev,
	&.swiper-container .swiper-button-next,
	&.swiper-container .swiper-button-prev {
		background: transparent;
		font-size: 20px;
		height: auto;
		line-height: 1;
		margin: 0;
		text-align: center;
		transform: translateY(-50%);
		width: auto;
	}

	&.swiper-container-wrap .swiper-button-next .fa,
	&.swiper-container-wrap .swiper-button-prev .fa,
	&.swiper-container .swiper-button-next .fa,
	&.swiper-container .swiper-button-prev .fa {
		vertical-align: top;
	}

	&.swiper-container-wrap .swiper-pagination {
		bottom: 10px;
		left: 0;
		width: 100%;
	}

	&.swiper-container-wrap-dots-outside .swiper-pagination {
		position: static;
	}

	&.swiper-container-wrap .swiper-pagination-bullet {
		background: #ccc;
		margin: 0 4px;
		opacity: 1;
		height: 8px;
		width: 8px;
	}

	&.swiper-container-wrap .swiper-pagination-bullet-active {
		background: #000;
	}

	&.swiper-container-3d .swiper-slide {
		transition-property: all;
	} 
}