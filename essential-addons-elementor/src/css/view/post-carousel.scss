.eael-post-carousel-wrap {
  .eael-post-carousel {
    &.grayscale-normal {
      img {
        filter: grayscale(100%);
      }

      .swiper-slide:hover img {
        filter: none;
      }
    }
    &.grayscale-hover{
      .swiper-slide:hover {
        img {
          filter: grayscale(100%);
        }
      }
    }

    &.swiper-container .swiper-slide {
      text-align: center;
    }
    .eael-grid-post-holder {
      transition: all .3s ease;
    }
    .eael-entry-thumbnail > img,
    .swiper-slide img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }

    .eael-entry-medianone {
      position: relative;
    }
    .eael-entry-content {
      padding: 0 15px;
    }

    .eael-entry-thumbnail {
      position: relative;
    
      &.eael-image-ratio {
        img {
          position: absolute;
          top: calc(50% + 1px);
          left: calc(50% + 1px);
          transform: scale(1.01) translate(-50%, -50%);
        }
      }
      a {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
      }
    }
    .eael-post-carousel-title a {
      color: inherit;
    }
    .eael-author-avatar > a {
      display: block;
    }

    .eael-entry-footer {
      overflow: hidden;
      display: flex;

      &> div {
        display: inline-block;
        float: left;
      }
    }

    .post_carousel_meta_alignment-{
      &left .eael-entry-meta {
        text-align: left;
        align-items: flex-start;
        justify-content: flex-start;
      }

      &right .eael-entry-meta {
        text-align: right;
        align-items: flex-end;
        justify-content: flex-end;

        .eael-entry-footer {
          display: block;

          &> div {
            float: right;
          }

          .eael-entry-meta {
            text-align: right;
            padding-left: 0;
            padding-right: 15px;
          }
        }
      }

      &center .eael-entry-meta {
        text-align: center;
        align-items: center;
        justify-content: center;

        .eael-entry-footer {
          margin: 0 auto 15px;
          display: inline-flex;

          &> div {
            float: none;
            display: block;
          }
        }
      }
    }
    .show-read-more-button
      .eael-post-elements-readmore-btn {
        display: inline-block;
      }
  }
  
  .swiper-button-prev {
    i {
      transform: rotate(0deg);
    }
  }
  
  .eael-entry-media:hover .eael-entry-overlay.zoom-in {
    transform: scale(1);
    visibility: visible;
    opacity: 1;
  }
  
  .eael-entry-media:hover .eael-entry-overlay.fade-in {
    visibility: visible;
    opacity: 1;
  }
  
  .eael-entry-media:hover .eael-entry-overlay.slide-up {
    transform: translateY(0);
    visibility: visible;
    opacity: 1;
  }
  .eael-entry-overlay {
    &.none{
      background: none !important;
    }
    i,svg{
      font-size: 20px;
      height: 20px;
      width: 20px;
    }
  }
  .eael-post-block-item-holder .eael-entry-media {
    overflow: hidden;
  }
  
  .swiper-button-prev i {
    transform: rotate(0deg);
  }
  
  &.swiper-container-wrap .swiper-pagination {
    bottom: 10px;
    left: 0;
    width: 100%;
  }
  
  &.swiper-container-wrap-dots-outside .swiper-pagination {
    position: static;
  }
  
  &.swiper-container-wrap .swiper-pagination-bullet {
    background: #ccc;
    margin: 0 4px;
    opacity: 1;
    height: 8px;
    width: 8px;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    transition: all .3s ease;
  }
  
  &.swiper-container-wrap
    .swiper-pagination-bullet-active {
    background: #000;
  }
  
  &.swiper-container-wrap {
    .swiper-button-next,
    .swiper-button-prev {
      background-image: none;
      outline: none;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  
  .eael-post-grid.eael-post-carousel {
    margin: 0;
    .eael-grid-post {
      width: 100%;
    }
  }
  
  
  .post-carousel-categories {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 11;
    width: 100%;
    margin: 0;
    padding: 15px;
    text-align: left;
    li {
      display: inline-block;
      text-transform: capitalize;
      margin-right: 5px;
      position: relative;
      &:after {
        content: ",";
        color: #ffffff;
      }
      &:last-child:after {
        display: none;
      }
      a {
        color: #fff;
      }
    }
  }
  
  
  .eael-entry-content-btn {
    margin-top: 15px;
  }
  
  &.eael-post-carousel-style {
    &-three {
      .eael-entry-content {
        padding: 0 15px 15px 15px;
      }
      .eael-meta-posted-on {
        min-width: 60px;
        height: 50px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.5);
        text-align: center;
        font-size: 16px;
        line-height: 18px;
        padding: 5px;
        margin-top: 5px;
        span {
          display: block;
        }
      }
    }
    &-two {
      .eael-entry-content {
        padding: 0 15px;
      }
      .eael-entry-footer-two {
        padding: 15px;
      }
      .eael-entry-meta {
        align-items: center;
        .eael-meta-posted-on {
          padding: 0;
          font-size: 12px;
          margin-right: 15px;
          color: #929292;
          i {
            margin-right: 7px;
          }
        }
        .post-meta-categories {
          list-style: none;
          display: inline-flex;
          margin: 0;
          li {
            font-size: 12px;
            margin-right: 7px;
            color: #929292;
            a {
              color: #929292;
            }
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
    }
  }
  
  .rtl {
    .eael-logo-carousel-wrap .eael-entry-footer {
      direction: ltr;
    }
  }
  
  .swiper-container {
    width: 100%;

    ~ .swiper-button-prev:after,
    ~ .swiper-button-next:after {
      content: none;
    }
  }

  .swiper-button-next:after, .swiper-rtl .swiper-button-prev:after,
  .swiper-button-prev:after, .swiper-rtl .swiper-button-next:after {
    content: '';
  }
  .eael-marquee-carousel .swiper-wrapper{
    transition-timing-function: linear !important; 
  }
}