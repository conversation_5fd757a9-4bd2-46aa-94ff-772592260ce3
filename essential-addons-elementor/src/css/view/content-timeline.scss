.eael-content-timeline-container {
    width: 100%;
    max-width: 980px;
    margin: 0 auto;
}

.eael-content-timeline-container::after {
    content: '';
    display: table;
    clear: both;
}

.eael-content-timeline-container {
    position: relative;
    // padding: 2em 0;
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
    z-index: 0;
}

.eael-content-timeline-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 18px;
    height: 100%;
    width: 4px;
    background: #d7e4ed;
    z-index: -1;
    display: none;
}

.eael-content-timeline-block {
    position: relative;
    padding: 0 0 2em 0;
    z-index: 0;
}

.eael-content-timeline-line {
    position: absolute;
    top: 5px;
    left: 18px;
    height: 100%;
    width: 4px;
    z-index: -2;
    overflow: hidden;
}
.eael-content-timeline-line .eael-content-timeline-inner {
    position: static;
    width: 100%;
    top: 0;
    left: 0;
    margin: 0;
    height: 100%;
    width: 100%;
}

.eael-content-timeline-line {
    background: #d7e4ed;
}

.eael-content-timeline-line .eael-content-timeline-inner {
    z-index: -1;
    height: 0px;
}

.eael-content-timeline-block:last-child .eael-content-timeline-line,
.eael-content-timeline-block:last-child
    .eael-content-timeline-line
    .eael-content-timeline-inner {
    display: none;
}

.eael-content-timeline-line .eael-content-timeline-inner {
    opacity: 0;
}

.eael-content-timeline-block.eael-highlight
    .eael-content-timeline-line
    .eael-content-timeline-inner {
    opacity: 1;
}

.eael-content-timeline-inner.eael-prev-highlighted {
    opacity: 1 !important;
}

.eael-content-timeline-inner.eael-highlighted {
    background: #3ccd94;
    opacity: 1;
}

.eael-content-timeline-inner.eael-muted {
    opacity: 0;
}
@media only screen and (min-width: 992px) {
    .eael-content-timeline-container {
        margin-top: 0;
        margin-bottom: 0;
    }
    .eael-content-timeline-line,
    .eael-content-timeline-line .eael-content-timeline-inner {
        left: 50%;
    }

    .content-timeline-layout-left.date-position-outside {
        .eael-content-timeline-line,
        .eael-content-timeline-line .eael-content-timeline-inner {
            left: 75%;
        }

        .eael-content-timeline-block:nth-child(2n)
            .eael-content-timeline-content
            .eael-date {
            text-align: right;
        }
    }

    .date-position-inside {
        &.content-timeline-layout-left {
            .eael-content-timeline-line,
            .eael-content-timeline-line .eael-content-timeline-inner {
                left: 100%;
            }
        }
    }

    .content-timeline-layout-right {
        .eael-content-timeline-line,
        .eael-content-timeline-line .eael-content-timeline-inner {
            left: 25%;
        }
    }
    .date-position-inside {
        &.content-timeline-layout-right {
            .eael-content-timeline-line,
            .eael-content-timeline-line .eael-content-timeline-inner {
                left: 0;
            }

            .eael-content-timeline-block
                .eael-content-timeline-content
                .eael-date {
                text-align: left;
            }
        }
    }
}

.eael-content-timeline-block:after {
    content: '';
    display: table;
    clear: both;
}

.eael-content-timeline-block:first-child {
    margin-top: 0;
}

.eael-content-timeline-block:last-child {
    margin-bottom: 0;
}

@media only screen and (min-width: 992px) {
    .eael-content-timeline-block {
        padding: 0 0 4em 0;
    }
    .eael-content-timeline-block:first-child {
        margin-top: 0;
    }
    .eael-content-timeline-block:last-child {
        margin-bottom: 0;
    }
}

.eael-content-timeline-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    text-align: center;
    line-height: 1;
    border: 6px solid #f9f9f9;
    box-shadow: 0 1px 0 1px rgba(0, 0, 0, 0.1);
}

.eael-horizontal-timeline-item__point-content .eael-elements-icon {
    border-radius: 50%;
    border: 6px solid #f9f9f9;
    box-shadow: 0 1px 0 1px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.eael-content-timeline-img img {
    display: block;
    width: 24px;
    height: 24px;
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.eael-horizontal-timeline-item__point-content .eael-elements-icon img {
    display: block;
    width: 24px;
    height: 24px;
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.eael-content-timeline-img {
    i, svg {
        line-height: 1;
        margin-top: 50%;
        transform: translateY(-50%);
    }
}

.eael-content-timeline-img.eael-content-timeline-bullet {
    width: 40px;
    height: 40px;
    margin-left: -20px;
    background-color: #3ccd94;
}

.eael-content-timeline-bullet {
    background-color: #3ccd94;
}

.eael-content-timeline-img.eael-picture {
    transition: 0.5s;
    background: #f1f2f3;
}

.eael-content-timeline-block.eael-highlight
    .eael-content-timeline-img.eael-picture {
    background: #3ccd94;
    transition: 0.5s;
}

@media only screen and (min-width: 992px) {
    .eael-content-timeline-img {
        left: 50%;
        margin-left: -20px;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }
    .content-timeline-layout-left {
        .eael-content-timeline-img {
            left: 75%;
        }
    }
    .content-timeline-layout-right {
        .eael-content-timeline-img {
            left: 25%;
        }
    }

    .date-position-inside {
        &.content-timeline-layout-left {
            .eael-content-timeline-img {
                left: 100% !important;
            }
        }

        &.content-timeline-layout-right {
            .eael-content-timeline-img {
                left: 0% !important;
            }
        }
    }

    .content-timeline-layout-right {
        .eael-content-timeline-block:nth-child(odd)
            .eael-content-timeline-content::before {
            border-left: none;
            border-right: 7px solid;
            left: auto;
            right: 100%;
        }
    }

    .cssanimations .eael-content-timeline-img.is-hidden {
        visibility: hidden;
    }
    .cssanimations .eael-content-timeline-img.bounce-in {
        visibility: visible;
        -webkit-animation: eael-bounce-1 0.6s;
        -moz-animation: eael-bounce-1 0.6s;
        animation: eael-bounce-1 0.6s;
    }
}

@media only screen and (max-width: 991px) {
    .eael-content-timeline-block .eael-content-timeline-content::before {
        border-left: none;
    }

    .eael-content-timeline-img {
        margin-left: 0;
    }
}

@-webkit-keyframes eael-bounce-1 {
    0% {
        opacity: 0;
        -webkit-transform: scale(0.5);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale(1.2);
    }

    100% {
        -webkit-transform: scale(1);
    }
}

@-moz-keyframes eael-bounce-1 {
    0% {
        opacity: 0;
        -moz-transform: scale(0.5);
    }

    60% {
        opacity: 1;
        -moz-transform: scale(1.2);
    }

    100% {
        -moz-transform: scale(1);
    }
}

@keyframes eael-bounce-1 {
    0% {
        opacity: 0;
        -webkit-transform: scale(0.5);
        -moz-transform: scale(0.5);
        -ms-transform: scale(0.5);
        -o-transform: scale(0.5);
        transform: scale(0.5);
    }

    60% {
        opacity: 1;
        -webkit-transform: scale(1.2);
        -moz-transform: scale(1.2);
        -ms-transform: scale(1.2);
        -o-transform: scale(1.2);
        transform: scale(1.2);
    }

    100% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1);
    }
}

.eael-content-timeline-content {
    position: relative;
    margin-left: 60px;
    border-radius: 0.25em;
    padding: 1em;
}

.eael-content-timeline-content:after {
    content: '';
    display: table;
    clear: both;
}

.eael-content-timeline-content .eael-timeline-title,
.eael-content-timeline-content .eael-timeline-title a,
.eael-horizontal-timeline-item .eael-horizontal-timeline-item__card-title,
.eael-horizontal-timeline-item .eael-horizontal-timeline-item__card-title a {
    color: #303e49;
    margin: 0;
    font-size: 100%;
    line-height: 1.5;
    font-weight: bold;
}

.eael-content-timeline-content .eael-read-more,
.eael-content-timeline-content .eael-date, 
.eael-horizontal-timeline-item .eael-horizontal-timeline-item__meta, 
.eael-horizontal-timeline-item .eael-read-more {
    display: inline-block;
}

.eael-content-timeline-content p {
    margin: 1em 0;
    line-height: 1.6;
}

.eael-content-timeline-content img,
.eael-horizontal-timeline-item__card-inner img {
    margin: 1em 0 0;
}

.eael-content-timeline-content .eael-read-more,
.eael-horizontal-timeline-item .eael-read-more {
    float: right;
    display: inline-block;
    padding: 10px 25px;
    font-size: 0.85em;
    line-height: 1.5;
    color: #fff;
    background-color: #3ccd94;
    -webkit-transition: 0.4s;
    transition: 0.4s;
    text-decoration: none;
    text-transform: uppercase;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}

.eael-content-timeline-content .eael-read-more:hover,
.eael-horizontal-timeline-item .eael-read-more:hover {
    background-color: #bac4cb;
}

.no-touch .eael-content-timeline-content .eael-read-more:hover,
.no-touch .eael-horizontal-timeline-item .eael-read-more:hover {
    background-color: #bac4cb;
}

.eael-content-timeline-content .eael-date {
    float: left;
    padding: 0.8em 0;
    opacity: 0.7;
}

.eael-horizontal-timeline-item .eael-horizontal-timeline-item__meta {
    opacity: 0.7;
}

.eael-content-timeline-content::before {
    content: '';
    position: absolute;
    top: 16px;
    right: 100%;
    height: 0;
    width: 0;
    border: 7px solid transparent;
    border-right: 7px solid white;
}

.eael-content-timeline-content p:empty::before {
    content: unset;
}

@media only screen and (min-width: 992px) {
    .eael-content-timeline-content {
        margin-left: 0;
        padding: 1.6em;
        width: 45%;
    }
    .content-timeline-layout-left,
    .content-timeline-layout-right {
        .eael-content-timeline-content {
            width: 68%;
        }
    }
    .date-position-inside {
        &.content-timeline-layout-left {
            .eael-content-timeline-content {
                width: 93%;
            }
        }
        &.content-timeline-layout-right {
            .eael-content-timeline-content {
                width: 93%;
            }
        }
    }
    .eael-content-timeline-content::before {
        top: 24px;
        left: 100%;
        border-color: transparent;
        border-left-color: #f1f2f3;
    }

    .eael-content-timeline-block:nth-child(odd)
        .eael-content-timeline-content::before {
        border-right: none;
    }

    .eael-content-timeline-content .eael-date {
        position: absolute;
        width: 100%;
        left: calc(100% + 85px);
        top: 0;
        font-size: 1em;
        padding-left: 5px;
        text-align: left;
    }

    .eael-horizontal-timeline-item .eael-horizontal-timeline-item__meta {
        font-size: 1em;
    }

    .eael-content-timeline-block:nth-child(even)
        .eael-content-timeline-content {
        float: right;
    }

    .content-timeline-layout-right {
        .eael-content-timeline-content {
            float: right;
        }
    }

    .content-timeline-layout-left {
        .eael-content-timeline-block:nth-child(even)
            .eael-content-timeline-content {
            float: left;
        }
    }

    .eael-content-timeline-block:nth-child(even)
        .eael-content-timeline-content::before {
        top: 24px;
        left: auto;
        right: 100%;
        border-color: transparent;
        border-right-color: #f1f2f3;
        border-left: none;
    }

    .eael-content-timeline-block:nth-child(2n)
        .eael-content-timeline-content::before {
        border-left: none;
    }

    .content-timeline-layout-left {
        .eael-content-timeline-block:nth-child(2n)
            .eael-content-timeline-content::before {
            border-left: 7px solid;
            border-right: none;
            left: 100%;
            right: auto;
        }
    }

    .eael-content-timeline-block:nth-child(even)
        .eael-content-timeline-content
        .eael-read-more {
        float: left;
    }

    .eael-content-timeline-block:nth-child(2n)
        .eael-content-timeline-content
        .eael-date {
        left: auto;
        right: calc(100% + 85px);
        text-align: right;
        font-size: 1em;
        padding-right: 5px;
    }

    .content-timeline-layout-left {
        .eael-content-timeline-block:nth-child(2n)
            .eael-content-timeline-content
            .eael-date {
            left: calc(100% + 85px);
            right: auto;
            text-align: left;
        }
    }

    .content-timeline-layout-right {
        .eael-content-timeline-content .eael-date {
            right: calc(100% + 85px);
            left: auto;
            text-align: right;
        }
    }

    .cssanimations .eael-content-timeline-content.is-hidden {
        visibility: hidden;
    }

    .cssanimations .eael-content-timeline-content.bounce-in {
        visibility: visible;
        -webkit-animation: eael-bounce-2 0.6s;
        -moz-animation: eael-bounce-2 0.6s;
        animation: eael-bounce-2 0.6s;
    }

    .content-timeline-layout-left.date-position-outside,
    .content-timeline-layout-right.date-position-outside {
        .eael-content-timeline-content .eael-date {
            width: 40%;
        }
    }
}

@media only screen and (min-width: 992px) {
    /* inverse bounce effect on even content blocks */
    .cssanimations
        .eael-content-timeline-block:nth-child(even)
        .eael-content-timeline-content.bounce-in {
        -webkit-animation: eael-bounce-2-inverse 0.6s;
        -moz-animation: eael-bounce-2-inverse 0.6s;
        animation: eael-bounce-2-inverse 0.6s;
    }
}
@-webkit-keyframes eael-bounce-2 {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-100px);
    }

    60% {
        opacity: 1;
        -webkit-transform: translateX(20px);
    }

    100% {
        -webkit-transform: translateX(0);
    }
}
@-moz-keyframes eael-bounce-2 {
    0% {
        opacity: 0;
        -moz-transform: translateX(-100px);
    }

    60% {
        opacity: 1;
        -moz-transform: translateX(20px);
    }

    100% {
        -moz-transform: translateX(0);
    }
}
@keyframes eael-bounce-2 {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-100px);
        -moz-transform: translateX(-100px);
        -ms-transform: translateX(-100px);
        -o-transform: translateX(-100px);
        transform: translateX(-100px);
    }

    60% {
        opacity: 1;
        -webkit-transform: translateX(20px);
        -moz-transform: translateX(20px);
        -ms-transform: translateX(20px);
        -o-transform: translateX(20px);
        transform: translateX(20px);
    }

    100% {
        -webkit-transform: translateX(0);
        -moz-transform: translateX(0);
        -ms-transform: translateX(0);
        -o-transform: translateX(0);
        transform: translateX(0);
    }
}
@-webkit-keyframes eael-bounce-2-inverse {
    0% {
        opacity: 0;
        -webkit-transform: translateX(100px);
    }

    60% {
        opacity: 1;
        -webkit-transform: translateX(-20px);
    }

    100% {
        -webkit-transform: translateX(0);
    }
}
@-moz-keyframes eael-bounce-2-inverse {
    0% {
        opacity: 0;
        -moz-transform: translateX(100px);
    }

    60% {
        opacity: 1;
        -moz-transform: translateX(-20px);
    }

    100% {
        -moz-transform: translateX(0);
    }
}
@keyframes eael-bounce-2-inverse {
    0% {
        opacity: 0;
        -webkit-transform: translateX(100px);
        -moz-transform: translateX(100px);
        -ms-transform: translateX(100px);
        -o-transform: translateX(100px);
        transform: translateX(100px);
    }

    60% {
        opacity: 1;
        -webkit-transform: translateX(-20px);
        -moz-transform: translateX(-20px);
        -ms-transform: translateX(-20px);
        -o-transform: translateX(-20px);
        transform: translateX(-20px);
    }

    100% {
        -webkit-transform: translateX(0);
        -moz-transform: translateX(0);
        -ms-transform: translateX(0);
        -o-transform: translateX(0);
        transform: translateX(0);
    }
}

.content-timeline-layout-left.date-position-inside,
.content-timeline-layout-right.date-position-inside {
    .eael-content-timeline-content {
        display: flex;
        flex-direction: column;

        .eael-date {
            position: static;
            order: 1;
        }

        .eael-timeline-title {
            order: 3;
        }

        p {
            order: 3;
        }

        .eael-read-more {
            align-self: flex-start;
            order: 3;
        }

        ul,
        ol {
            order: 3;
            padding-left: 20px;
        }
    }
}

.content-timeline-layout-left.date-position-outside,
.content-timeline-layout-right.date-position-outside {
    .eael-content-timeline-content .eael-read-more {
        float: left;
    }

    .eael-content-timeline-content .eael-date {
        width: 25%;
    }
}

.content-timeline-layout-left.date-position-outside {
    .eael-content-timeline-content .eael-date {
        text-align: right;
    }
}

.content-timeline-layout-right.date-position-outside {
    .eael-content-timeline-content .eael-date {
        text-align: left;
    }
}

/* Left layout responsive */
@media only screen and (max-width: 991px) {
    .content-timeline-layout-left {
        .eael-content-timeline-content {
            margin-left: 0px;
            margin-right: 60px;
            &::before {
                border-left: 7px solid;
                border-right: none;
                right: auto;
                left: 100%;
            }
        }
        .eael-content-timeline-line {
            left: auto;
            right: 10px;
        }
        .eael-content-timeline-img {
            left: auto;
            right: -8px;
        }
    }

    .content-timeline-layout-left.date-position-outside
        .eael-content-timeline-content
        .eael-date,
    .content-timeline-layout-right.date-position-outside
        .eael-content-timeline-content
        .eael-date {
        width: 100%;
        text-align: left !important;
    }
}

@media only screen and (max-width: 767px) {
    .eael-horizontal-timeline .eael-horizontal-timeline-item {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
// RTL
.rtl {
    @media only screen and (min-width: 992px) {
        .content-timeline-layout-left {
            .eael-content-timeline-block
                .eael-content-timeline-content::before {
                right: auto;
            }
            .eael-content-timeline-content {
                margin-right: auto;
            }
        }
        .content-timeline-layout-center {
            .eael-content-timeline-block:nth-child(even)
                .eael-content-timeline-content {
                float: left;
            }

            .content-timeline-layout-right {
                .eael-content-timeline-content {
                    float: left;
                }
            }

            .content-timeline-layout-left {
                .eael-content-timeline-block:nth-child(even)
                    .eael-content-timeline-content {
                    float: right;
                }
            }
            .eael-content-timeline-block:nth-child(even)
                .eael-content-timeline-content::before {
                left: 100%;
                right: auto;
                transform: rotate(180deg);
            }
            .eael-content-timeline-block:nth-child(odd)
                .eael-content-timeline-content::before {
                transform: rotate(180deg);
            }
            .eael-content-timeline-block:nth-child(2n)
                .eael-content-timeline-content
                .eael-date {
                right: auto;
                left: calc(100% + 85px);
                text-align: left;
            }
            .eael-content-timeline-content .eael-date {
                left: auto;
                right: calc(100% + 85px);
                text-align: right;
            }
        }
    }
}

// Horizontal Timeline
.eael-horizontal-timeline {
	position: relative;

	&-inner {
		.eael-horizontal-timeline--arrows & {
			overflow: hidden;
		}
	}

	&-track {
		.eael-horizontal-timeline--scrollbar & {
			overflow-x: auto;
			-webkit-overflow-scrolling: touch;
			-ms-overflow-style: -ms-autohiding-scrollbar;

			padding-bottom: 30px;

			&::-webkit-scrollbar {
				height: 8px;
				background: #EFEFF1;
				border-radius: 4px;
			}
			&::-webkit-scrollbar-button {
				width: 0;
				height: 0;
			}
			&::-webkit-scrollbar-thumb {
				background-color: #34314B;
				border: none;
				border-radius: 4px;
			}
			&::-webkit-scrollbar-track {
				border: none;
				background: transparent;
			}
			&::-webkit-scrollbar-corner {
				background: transparent;
			}
		}

		.eael-horizontal-timeline--arrows & {
			transition: transform 500ms ease;
		}
	}

	&-list {
		position: relative;
		display: flex;

		&--top    { 
            align-items: flex-end; 
            
            &.bottom {
                align-items: flex-start; 
            }
        }

		&--middle { align-items: flex-end; }
		&--bottom { align-items: flex-start; }
	}
	
	&-item {
		display: flex;
		flex-direction: column;
		flex-wrap: nowrap;
		padding-left: 15px;
		padding-right: 15px;

		flex: 0 0 33.33%;
		max-width: 33.33%;

		transition: .2s;

		&.top {
			padding-top: 15px;

		}
		&.bottom {
			padding-bottom: 15px;
		}

		&__card {
			position: relative;
			display: flex;
			flex-direction: column;
			flex-wrap: nowrap;

			border-width: 1px;
			background-color: #f8f8f8;
			transition: inherit;

			&.top {
				margin-bottom: 18px;

			}
			&.bottom {
				margin-top: 18px;
			}

			&-inner {
				display: flex;
				flex-direction: column;
				flex-wrap: nowrap;
				padding: 30px;
				overflow: hidden;
				z-index: 1;

				background-color: #f8f8f8;
				transition: inherit;
			}

			&-img {
				margin-bottom: 10px;

				img {
					vertical-align: top;
				}
			}

			&-title {
				margin: 0 0 10px;
				padding: 0;
				transition: inherit;
			}

			&-btn-wrap {
				margin-top: 15px;
			}

			&-arrow {
				position: absolute;
				width: 20px;
				height: 20px;
				border-width: 1px;
				transition: inherit;

				.eael-horizontal-timeline & {
					box-sizing: content-box;
				}

				&:before {
					content: '';
					display: block;
					width: 100%;
					height: 100%;
					background-color: #f8f8f8;
					transition: inherit;
				}

				&.top {
					top: 100%;
					transform: translateY(-50%) rotate(45deg);
					border-left-color: transparent !important;
					border-top-color: transparent !important;

				}

				&.bottom {
					bottom: 100%;
					transform: translateY(50%) rotate(45deg);
					border-right-color: transparent !important;
					border-bottom-color: transparent !important;
				}

				.eael-horizontal-timeline--align-left & {
					left: 20px;
				}

				.eael-horizontal-timeline--align-right & {
					right: 20px;
				}

				.eael-horizontal-timeline--align-center & {
					left: 50%;
				}
				.eael-horizontal-timeline--align-center .eael-horizontal-timeline-list--top & {
					transform: translateX(-50%) translateY(-50%) rotate(45deg);
				}
				.eael-horizontal-timeline--align-center .eael-horizontal-timeline-list--bottom & {
					transform: translateX(-50%) translateY(50%) rotate(45deg);
				}
			}
		}

		&__meta {
			transition: inherit;

			.eael-horizontal-timeline-list--top & {
				margin-bottom: 15px;

			}
			.eael-horizontal-timeline-list--bottom & {
				margin-top: 15px;
			}

			.eael-horizontal-timeline--align-left & {
				text-align: left;
				margin-right: auto;
			}

			.eael-horizontal-timeline--align-center & {
				text-align: center;
				margin-left: auto;
				margin-right: auto;
			}

			.eael-horizontal-timeline--align-right & {
				text-align: right;
				margin-left: auto;
			}
		}

		&__point {
			z-index: 1;
			transition: inherit;

			&-content {
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 40px;
				height: 40px;
				min-width: 10px;
				min-height: 10px;

				font-size: 16px;
				color: #34314B;
				background-color: #EFEFF1;
				border-radius: 50%;
				transition: inherit;

				.eael-horizontal-timeline--align-left & {
					margin-left: 10px;
					margin-right: auto;
				}

				.eael-horizontal-timeline--align-center & {
					margin-left: auto;
					margin-right: auto;
				}

				.eael-horizontal-timeline--align-right & {
					margin-left: auto;
					margin-right: 10px;
				}
			}
		}

		&.is-active {
			.eael-horizontal-timeline-item__point-content .eael-elements-icon {
				background-color: #3CCD94;
				color: #fff;
			}
		}
	}
	
	&__line {
		position: absolute;
		top: 50%;
		height: 2px;
		transform: translateY(-50%);
		overflow: hidden;

		background-color: #EFEFF1;

		&-progress {
			width: 0;
			height: 100%;
			background-color: #55cdff;
			transition: 500ms ease;
		}
	}

    .eael-horizontal-timeline-item__highlight {
        width: 0;
        height: 4px;
        background-color: #3CCD94;
    }

	.eael-arrow {
		position: absolute;
		top: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		z-index: 999;
		transition: all 200ms linear;

		width: 36px;
		height: 36px;
		border-radius: 50%;

		font-size: 30px;
		line-height: 1;

		background-color: #45CD94;
		color: #fff;

		&:before {
			line-height: 0;
		}

		&.eael-prev-arrow {
            left: 0;
			transform: translateY(-50%);
            // #ToDo rtl support
		}
		&.eael-next-arrow {
            right: 0;
			transform: translateY(-50%) scale(-1, 1);
            // #ToDo rtl support
		}

		&.eael-arrow-disabled {
			opacity: .5;
			cursor: default;
			pointer-events: none;
		}
	}
}