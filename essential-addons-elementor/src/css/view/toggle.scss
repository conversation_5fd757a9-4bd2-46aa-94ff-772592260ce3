.eael-toggle-container {
	-webkit-tap-highlight-color: transparent;
}
.eael-toggle-switch-inner {
	align-items: center;
	display: flex;
}
.eael-toggle-center {
	.eael-toggle-switch-inner {
		justify-content: center;
	}
}
.eael-toggle-right {
	.eael-toggle-switch-inner {
		justify-content: flex-end;
	}
}
.eael-toggle-switch-container {
	display: inline-block;
	font-size: 26px;
	line-height: 1;
	margin: 0 15px;
	overflow: hidden;
}
.eael-toggle-switch-round {
	border-radius: 1.31em;
	.eael-toggle-slider {
		&:before {
			border-radius: 50%;
		}
	}
	&:focus-visible {
		outline-offset: 1px;
	}
}
.eael-toggle-switch {
	position: relative;
	display: inline-block;
	width: 2.31em;
	height: 1.29em;
	margin: 0;
	vertical-align: middle;
	font-size: inherit;
	input {
		display: none;
	}
}
.eael-toggle-content-wrap.primary {
	> .eael-toggle-secondary-wrap {
		display: none;
	}
}
.eael-toggle-content-wrap.secondary {
	> .eael-toggle-primary-wrap {
		display: none;
	}
}
.eael-toggle-content-wrap {
	.elementor-section-stretched {
		left: 0 !important;
		width: 100% !important;
	}
}
.eael-toggle-slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: 0.4s;
	transition: 0.4s;
	
	&:before {
		position: absolute;
		content: "";
		height: 1em;
		width: 1em;
		left: 0.16em;
		bottom: 0.15em;
		background-color: white;
		-webkit-transition: 0.4s;
		transition: 0.4s;
	}
}
.eael-toggle-switch-on {
	.eael-toggle-slider {
		background-color: #2196f3;
		box-shadow: 0 0 1px #2196f3;
		&:before {
			-webkit-transform: translateX(99%);
			-ms-transform: translateX(99%);
			transform: translateX(99%);
		}
	}
}

.rtl {
	.eael-toggle-switch-inner {
		direction: ltr;
	}
}
