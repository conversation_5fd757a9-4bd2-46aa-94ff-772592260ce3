var WooAccountDashboardHandler = function ($scope, $) {
    let $wooAccountDashboardWrapper = $(".eael-account-dashboard-wrapper", $scope);
	let wrap = $('.eael-account-dashboard-wrap', $scope);
	let icons = wrap.data( 'eawct-icons' );
	if( wrap.hasClass('eawct-has-custom-tab') && icons ) {
		icons = JSON.parse( atob( icons ) );
		if ( icons ) {
			$.each(icons,function (index, icon) {
				if ( icon ) {
					$('.eael-custom-tab-'+index).removeClass('eael-wcd-icon').find('a').prepend( icon );
				}
			});
		}
	}
	
	
	if( elementorFrontend.isEditMode() ){
		$( '.eael-account-dashboard-navbar li, .woocommerce-orders-table__cell-order-actions .view', $scope ).on('click', function(){
			const woo_nav_class = 'woocommerce-MyAccount-navigation-link';
			let classes = $(this).attr('class').split(' ');
			let target = '';
			
			if( classes.length ) {
				classes.forEach(function(className){
					if( className.includes( woo_nav_class + '--' ) ) {
						target = className.replace( woo_nav_class + '--', '' );
					}
				});
			}
			let $this_attr_class = $(this).attr('class');
			if( $this_attr_class.includes('woocommerce-button') && $this_attr_class.includes('view') ) {
				target = 'view-order';
			}
	
			$(`.eael-account-dashboard-body .${woo_nav_class}`, $scope).removeClass('is-active');
			$(`.eael-account-dashboard-body .${woo_nav_class}--${target}`, $scope).addClass('is-active');
	
			$('.eael-account-dashboard-body .tab-content', $scope).removeClass('active');
			$(`.eael-account-dashboard-body .tab-${target}`, $scope).addClass('active');
			
			let pageHeading = target[0].toUpperCase() + target.substring(1);
			$(`.eael-account-dashboard-header h3`, $scope).html(pageHeading);
		});
	}

	// Oceanwp, Travel Ocean 
	if( $('body').hasClass('theme-oceanwp') ){
		var navBar = $('.eael-account-dashboard-navbar .woocommerce-MyAccount-navigation');
		$('.eael-account-dashboard-navbar .woocommerce-MyAccount-tabs').remove();
		$('.eael-account-dashboard-navbar').append(navBar);
	}
};

eael.hooks.addAction("init", "ea", () => {
	if (eael.elementStatusCheck('eaelAccountDashboard')) {
		return false;
	}

	elementorFrontend.hooks.addAction(
		"frontend/element_ready/eael-woo-account-dashboard.default",
		WooAccountDashboardHandler
	);
});