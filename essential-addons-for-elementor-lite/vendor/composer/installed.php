<?php return array(
    'root' => array(
        'name' => 'mukul/essential-addons-for-elementor-lite',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '121d81bbe09fd3f6d431fce576b3901317b96416',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'mukul/essential-addons-for-elementor-lite' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '121d81bbe09fd3f6d431fce576b3901317b96416',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'priyomukul/wp-notice' => array(
            'pretty_version' => 'v2.x-dev',
            'version' => '2.9999999.9999999.9999999-dev',
            'reference' => 'f3d02f6e772cb459e9b89d77605e02646f9c5d65',
            'type' => 'library',
            'install_path' => __DIR__ . '/../priyomukul/wp-notice',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
