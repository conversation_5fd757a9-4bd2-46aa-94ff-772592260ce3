/* Essential Addons Code Snippet Frontend Styles */

.eael-code-snippet-wrapper {
   position: relative;
   border-radius: 8px;
   overflow: hidden;
   font-family: "Monaco", "<PERSON>lo", "Ubuntu Mono", "<PERSON><PERSON><PERSON>", "Courier New",
      monospace;
   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
   transition: box-shadow 0.3s ease;

   &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
   }

   /* Light */
   &.theme-light {
      background-color: #ffffff;
      border: 1px solid #e1e5e9;

      .eael-code-snippet-header,
      .eael-file-preview-header {
         background-color: #f6f8fa;
         border-bottom: 1px solid #e1e5e9;
      }

      .eael-code-snippet-language {
         color: #586069;
      }

      .eael-file-name .file-name-text {
         color: #24292e;
      }

      .eael-traffic-lights .traffic-light {
         &.traffic-light-red {
            background-color: #ff5f57;
            border: 1px solid rgba(0, 0, 0, 0.1);
         }

         &.traffic-light-yellow {
            background-color: #ffbd2e;
            border: 1px solid rgba(0, 0, 0, 0.1);
         }

         &.traffic-light-green {
            background-color: #28ca42;
            border: 1px solid rgba(0, 0, 0, 0.1);
         }
      }

      .eael-code-snippet-code {
         background-color: #ffffff;
         color: #24292e;
      }

      .eael-code-snippet-line-numbers {
         background-color: #f6f8fa;
         color: #586069;
         border-right: 1px solid #e1e5e9;
      }

      .eael-code-snippet-copy-button {
         background: transparent;
         color: #6c757d;
         border: 1px solid currentColor;
         border-radius: 8px;
         padding: 8px;
         width: 32px;
         height: 32px;
         display: flex;
         align-items: center;
         justify-content: center;
         cursor: pointer;
         transition: all 0.2s ease;

         svg {
            width: 16px;
            height: 16px;
         }
      }
   }

   /* Dark Theme */
   &.theme-dark {
      background-color: #0d1117;
      border: 1px solid #30363d;

      .eael-code-snippet-header,
      .eael-file-preview-header {
         background-color: #161b22;
         border-bottom: 1px solid #30363d;
      }

      .eael-code-snippet-language {
         color: #8b949e;
      }

      .eael-file-name .file-name-text {
         color: #e6edf3;
      }

      .eael-traffic-lights .traffic-light {
         &.traffic-light-red {
            background-color: #ff6058;
            border: 1px solid rgba(255, 255, 255, 0.1);
         }

         &.traffic-light-yellow {
            background-color: #ffbd2e;
            border: 1px solid rgba(255, 255, 255, 0.1);
         }

         &.traffic-light-green {
            background-color: #28ca42;
            border: 1px solid rgba(255, 255, 255, 0.1);
         }
      }

      .eael-code-snippet-code {
         background-color: #0d1117;
         color: #e6edf3;
      }

      .eael-code-snippet-line-numbers {
         background-color: #161b22;
         color: #656d76;
         border-right: 1px solid #30363d;
      }

      .eael-code-snippet-copy-button {
         background: transparent;
         color: #a0aec0;
         border: 1px solid currentColor;
         border-radius: 8px;
         padding: 8px;
         width: 32px;
         height: 32px;
         display: flex;
         align-items: center;
         justify-content: center;
         cursor: pointer;
         transition: all 0.2s ease;

         svg {
            width: 16px;
            height: 16px;
         }

         &:hover {
            color: #e2e8f0;
            border-color: #e2e8f0;
         }

         &:active {
            color: #cbd5e0;
            border-color: #cbd5e0;
         }

         &:focus {
            outline: none;
         }
      }
   }
}

/* File Preview Header Styles */
.eael-file-preview-header {
   display: flex;
   justify-content: space-between;
   align-items: center;
   padding: 0.75rem 1rem;
   font-size: 0.875rem;
   font-weight: 500;
   min-height: 3rem;

   .eael-file-preview-left {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      flex: 1;
   }

   .eael-file-preview-right {
      display: flex;
      align-items: center;
      gap: 0.5rem;
   }
}

/* Traffic Lights */
.eael-traffic-lights {
   display: flex;
   align-items: center;
   gap: 0.5rem;

   .traffic-light {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: block;

      &.traffic-light-red {
         background-color: #ff5f57;
      }

      &.traffic-light-yellow {
         background-color: #ffbd2e;
      }

      &.traffic-light-green {
         background-color: #28ca42;
      }
   }
}

/* File Info */
.eael-file-info {
   display: flex;
   align-items: center;
   gap: 0.5rem;
   flex: 1;
   min-width: 0; /* Allow text truncation */
}

.eael-file-icon {
   display: flex;
   align-items: center;
   justify-content: center;
   width: 20px;
   height: 20px;
   flex-shrink: 0;

   img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 2px;
   }

   .eael-file-icon-emoji {
      font-size: 16px;
      line-height: 1;
      display: block;
   }
}

.eael-file-name {
   flex: 1;
   min-width: 0;

   .file-name-text {
      font-family: "Monaco", "Menlo", "Ubuntu Mono", "Consolas", "Courier New",
         monospace;
      font-size: 0.875rem;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
   }
}

/* Legacy header styles for backward compatibility */
.eael-code-snippet-header:not(.eael-file-preview-header) {
   display: flex;
   justify-content: space-between;
   align-items: center;
   padding: 0.75rem 1rem;
   font-size: 0.875rem;
   font-weight: 500;
   min-height: 3rem;
}

.eael-code-snippet-language {
   font-family: inherit;
   text-transform: uppercase;
   letter-spacing: 0.05em;
   font-weight: 600;
   font-size: 0.8125rem;
}

.eael-code-snippet-copy-button {
   display: inline-flex;
   align-items: center;
   gap: 0.5rem;
   padding: 0.5rem 0.875rem;
   border: none;
   border-radius: 6px;
   font-size: 0.875rem;
   font-weight: 500;
   cursor: pointer;
   transition: all 0.2s ease;
   text-decoration: none;
   white-space: nowrap;

   &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
   }

   &:active {
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
   }

   &:focus {
      outline: none;
   }

   svg {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
   }
}

.eael-code-snippet-content {
   display: flex;
   position: relative;
   overflow: hidden;
   align-items: stretch;
}

.eael-code-snippet-line-numbers,
.eael-code-snippet-code,
.eael-code-snippet-code code {
   font-size: 0.875rem;
   line-height: 1.5;
}

.eael-code-snippet-code,
.eael-code-snippet-code code {
   white-space: pre;
   word-break: normal;
   overflow-x: auto;
}

.eael-code-snippet-line-numbers {
   white-space: pre;
}

.eael-code-snippet-line-numbers {
   display: flex;
   flex-direction: column;
   padding: 1rem 0.75rem;
   text-align: right;
   user-select: none;
   min-width: 3rem;
   flex-shrink: 0;

   .line-number {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-variant-numeric: tabular-nums;
   }
}

.eael-code-snippet-code {
   flex: 1;
   margin: 0;
   padding: 1rem;
   font-family: inherit;
   white-space: pre;
   overflow-x: auto;
   background: transparent;
   border: none;
   min-height: 3rem;

   code {
      font-family: inherit;
      font-size: inherit;
      line-height: inherit;
      background: transparent;
      padding: 0;
      border: none;
      display: block;
      white-space: pre;
   }

   /* Custom scrollbar for webkit browsers */
   &::-webkit-scrollbar {
      height: 8px;
   }

   &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
   }

   &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 4px;

      &:hover {
         background: rgba(0, 0, 0, 0.5);
      }
   }
}

/* Tooltip Styles */
.eael-code-snippet-tooltip {
   position: fixed;
   padding: 6px 12px;
   font-size: 12px;
   font-weight: 500;
   line-height: 1.2;
   white-space: nowrap;
   border-radius: 6px;
   pointer-events: none;
   z-index: 10000;
   opacity: 0;
   visibility: hidden;
   transition: opacity 0.2s ease, visibility 0.2s ease;

   /* Light tooltip */
   background-color: #f6f8fa;
   color: #24292f;
   border: 1px solid #6c757d;
   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

   /* Show tooltip on hover */
   &.show {
      opacity: 1;
      visibility: visible;
   }
}

/* Dark theme tooltip */
.eael-code-snippet-wrapper.theme-dark .eael-code-snippet-tooltip {
   background-color: #24292f;
   color: #f6f8fa;
   border-color: #a0aec0;
   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

   &::after {
      border-top-color: #24292f;
   }
}

/* Copy button container for tooltip positioning */
.eael-code-snippet-copy-container {
   position: relative;
   display: inline-block;
}

/* Responsive Design */
@media (max-width: 768px) {
   .eael-code-snippet-wrapper {
      margin: 1rem 0;
      border-radius: 6px;
   }

   .eael-code-snippet-header,
   .eael-file-preview-header {
      padding: 0.625rem 0.75rem;
      font-size: 0.8125rem;
      min-height: auto;
   }

   .eael-file-preview-header {
      .eael-file-preview-left {
         gap: 0.5rem;
      }

      .eael-traffic-lights {
         gap: 0.375rem;

         .traffic-light {
            width: 10px;
            height: 10px;
         }
      }

      .eael-file-name .file-name-text {
         font-size: 0.8125rem;
      }
   }

   /* Legacy header responsive styles */
   .eael-code-snippet-header:not(.eael-file-preview-header) {
      flex-direction: column;
      gap: 0.5rem;
      align-items: flex-start;
   }

   .eael-code-snippet-copy-button {
      padding: 0.375rem 0.625rem;
      font-size: 0.8125rem;
      align-self: flex-end;

      svg {
         width: 14px;
         height: 14px;
      }
   }

   .eael-code-snippet-line-numbers {
      padding: 0.75rem 0.5rem;
      font-size: 0.75rem;
      min-width: 2.5rem;
   }

   .eael-code-snippet-code {
      padding: 0.75rem;
      font-size: 0.8125rem;
   }
}

@media (max-width: 480px) {
   .eael-code-snippet-wrapper {
      margin: 0.75rem 0;
      border-radius: 4px;
   }

   .eael-code-snippet-header,
   .eael-file-preview-header {
      padding: 0.5rem;
   }

   .eael-file-preview-header {
      .eael-file-preview-left {
         gap: 0.375rem;
      }

      .eael-traffic-lights {
         gap: 0.25rem;

         .traffic-light {
            width: 8px;
            height: 8px;
         }
      }

      .eael-file-icon {
         width: 16px;
         height: 16px;

         .eael-file-icon-emoji {
            font-size: 12px;
         }
      }

      .eael-file-name .file-name-text {
         font-size: 0.75rem;
      }
   }

   .eael-code-snippet-copy-button {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      gap: 0.25rem;

      svg {
         width: 12px;
         height: 12px;
      }
   }

   .eael-code-snippet-line-numbers {
      padding: 0.5rem 0.375rem;
      font-size: 0.6875rem;
      min-width: 2rem;
   }

   .eael-code-snippet-code {
      padding: 0.5rem;
      font-size: 0.75rem;
   }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
   .eael-code-snippet-wrapper {
      border-width: 2px;

      &.theme-light {
         border-color: #000000;

         .eael-code-snippet-header {
            border-bottom-color: #000000;
         }

         .eael-code-snippet-line-numbers {
            border-right-color: #000000;
         }
      }

      &.theme-dark {
         border-color: #ffffff;

         .eael-code-snippet-header {
            border-bottom-color: #ffffff;
         }

         .eael-code-snippet-line-numbers {
            border-right-color: #ffffff;
         }
      }
   }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
   .eael-code-snippet-wrapper,
   .eael-code-snippet-copy-button,
   .eael-code-snippet-tooltip {
      transition: none;
   }
}

/* Print styles */
@media print {
   .eael-code-snippet-wrapper {
      box-shadow: none;
      border: 1px solid #000000;
      break-inside: avoid;
   }

   .eael-code-snippet-header {
      background-color: #f5f5f5 !important;
      border-bottom: 1px solid #000000;
   }

   .eael-code-snippet-copy-button {
      display: none;
   }

   .eael-code-snippet-code {
      background-color: #ffffff !important;
      color: #000000 !important;
      overflow: visible;
      white-space: pre-wrap;
      word-wrap: break-word;
   }

   .eael-code-snippet-line-numbers {
      background-color: #f5f5f5 !important;
      color: #666666 !important;
      border-right: 1px solid #000000;
   }
}
