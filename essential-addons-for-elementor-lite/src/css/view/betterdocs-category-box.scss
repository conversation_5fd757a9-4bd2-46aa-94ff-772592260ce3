
.eael-better-docs-category-box-post {
  .eael-bd-cb-inner {
    background: #f8f8fc;
    padding: 20px;
    border: 1px solid #efffff;
    text-align: center;
    margin: 7.5px;
    transition: 300ms;
    &:hover {
      background: #fff;
      box-shadow: 0px 20px 50px 0px rgba(0, 9, 78, 0.1);
    }
  }
  .eael-bd-cb-cat-icon {
    height: 80px;
    width: 80px;
    margin: 0 auto 20px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      max-height: 100%;
      max-width: 100%;
    }
  }
  .eael-bd-cb-cat-title {
    color: #3f5876;
    font-size: 18px;
    line-height: 1.3;
    font-weight: 700;
    margin-bottom: 15px;
  }

  .eael-bd-cb-cat-count {
    color: #707070;
    font-size: 15px;
    .count-suffix {
      margin-left: 5px;
    }
    .count-prefix {
      margin-right: 5px;
    }
  }
}


/* ============================================= */
/* Only Layout 2 modification from Default Layout
/* ============================================= */

.layout__2 {
  .eael-bd-cb-inner {
    display: flex;
    flex-direction: row;
    padding: 0px;
    box-shadow: 0px 1px 5px 0px rgba(101, 99, 233, 0.18);
    background: #fff;
    &:hover {
      box-shadow: 0px 8px 15px 0px rgba(101, 99, 233, 0.09);
    }
  }

  .eael-bd-cb-cat-icon__layout-2 {
    flex-basis: 20%;
    border-right: 1px solid #e4e4f9;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 50px;
    }
  }

  .eael-bd-cb-cat-title__layout-2 {
    flex-basis: 60%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 0;
    line-height: 1;
    font-size: 20px;
    color: #333333;
    font-weight: 500;
    span {
      padding: 45px 0px 45px 30px;
    }
  }

  .eael-bd-cb-cat-count__layout-2 {
    flex-basis: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    .count-inner__layout-2 {
      height: 60px;
      width: 60px;
      background: #ffffff;
      display: flex;
      margin: 0;
      align-items: center;
      justify-content: center;
      border-radius: 5px;
      box-shadow: 0px 8px 15px 0px rgba(101, 99, 233, 0.09);
      font-size: 20px;
      color: #7E7CFF;
    }
  }

}



/* ===================================== */
/* Column CSS
/* ===================================== */

// For Desktop
@media only screen and (min-width: 1025px) {
  .elementor-element.elementor-grid-1 {
    position: relative;
  }
  .elementor-element.elementor-grid-1 .eael-better-docs-category-box-post {
    width: 100%;
    float: left;
  }
  .elementor-element.elementor-grid-2 {
    position: relative;
  }
  .elementor-element.elementor-grid-2 .eael-better-docs-category-box-post {
    width: 50%;
    float: left;
  }
  .elementor-element.elementor-grid-2
  .eael-better-docs-category-box-post:nth-of-type(2n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-2
  .eael-better-docs-category-box-post:nth-of-type(2n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-3 {
    position: relative;
  }
  .elementor-element.elementor-grid-3 .eael-better-docs-category-box-post {
    width: 33.3333%;
    float: left;
  }
  .elementor-element.elementor-grid-3
  .eael-better-docs-category-box-post:nth-of-type(3n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-3
  .eael-better-docs-category-box-post:nth-of-type(3n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-4 {
    position: relative;
  }
  .elementor-element.elementor-grid-4 .eael-better-docs-category-box-post {
    width: 25%;
    float: left;
  }
  .elementor-element.elementor-grid-4
  .eael-better-docs-category-box-post:nth-of-type(4n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-4
  .eael-better-docs-category-box-post:nth-of-type(4n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-5 {
    position: relative;
  }
  .elementor-element.elementor-grid-5 .eael-better-docs-category-box-post {
    width: 20%;
    float: left;
  }
  .elementor-element.elementor-grid-5
  .eael-better-docs-category-box-post:nth-of-type(5n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-5
  .eael-better-docs-category-box-post:nth-of-type(5n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-6 {
    position: relative;
  }
  .elementor-element.elementor-grid-6 .eael-better-docs-category-box-post {
    width: 16%;
    float: left;
  }
  .elementor-element.elementor-grid-6
  .eael-better-docs-category-box-post:nth-of-type(6n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-6
  .eael-better-docs-category-box-post:nth-of-type(6n + 1) {
    clear: left;
  }
}

// For Tablets
@media only screen and (max-width: 1024px) and (min-width: 766px) {
  .elementor-element.elementor-grid-tablet-1 {
    position: relative;
  }
  .elementor-element.elementor-grid-tablet-1
  .eael-better-docs-category-box-post {
    width: 100%;
    float: left;
  }
  .elementor-element.elementor-grid-tablet-2 {
    position: relative;
  }
  .elementor-element.elementor-grid-tablet-2
  .eael-better-docs-category-box-post {
    width: 50%;
    float: left;
  }
  .elementor-element.elementor-grid-tablet-2
  .eael-better-docs-category-box-post:nth-of-type(2n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-tablet-2
  .eael-better-docs-category-box-post:nth-of-type(2n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-tablet-3 {
    position: relative;
  }
  .elementor-element.elementor-grid-tablet-3
  .eael-better-docs-category-box-post {
    width: 33.3333%;
    float: left;
  }
  .elementor-element.elementor-grid-tablet-3
  .eael-better-docs-category-box-post:nth-of-type(3n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-tablet-3
  .eael-better-docs-category-box-post:nth-of-type(3n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-tablet-4 {
    position: relative;
  }
  .elementor-element.elementor-grid-tablet-4
  .eael-better-docs-category-box-post {
    width: 25%;
    float: left;
  }
  .elementor-element.elementor-grid-tablet-4
  .eael-better-docs-category-box-post:nth-of-type(4n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-tablet-4
  .eael-better-docs-category-box-post:nth-of-type(4n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-tablet-5 {
    position: relative;
  }
  .elementor-element.elementor-grid-tablet-5
  .eael-better-docs-category-box-post {
    width: 20%;
    float: left;
  }
  .elementor-element.elementor-grid-tablet-5
  .eael-better-docs-category-box-post:nth-of-type(5n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-tablet-5
  .eael-better-docs-category-box-post:nth-of-type(5n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-tablet-6 {
    position: relative;
  }
  .elementor-element.elementor-grid-tablet-6
  .eael-better-docs-category-box-post {
    width: 16%;
    float: left;
  }
  .elementor-element.elementor-grid-tablet-6
  .eael-better-docs-category-box-post:nth-of-type(6n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-tablet-6
  .eael-better-docs-category-box-post:nth-of-type(6n + 1) {
    clear: left;
  }
}

// For Mobiles
@media only screen and (max-width: 767px) {
  .elementor-element.elementor-grid-mobile-1 {
    position: relative;
  }
  .elementor-element.elementor-grid-mobile-1
  .eael-better-docs-category-box-post {
    width: 100%;
    float: left;
  }
  .elementor-element.elementor-grid-mobile-2 {
    position: relative;
  }
  .elementor-element.elementor-grid-mobile-2
  .eael-better-docs-category-box-post {
    width: 50%;
    float: left;
  }
  .elementor-element.elementor-grid-mobile-2
  .eael-better-docs-category-box-post:nth-of-type(2n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-mobile-2
  .eael-better-docs-category-box-post:nth-of-type(2n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-mobile-3 {
    position: relative;
  }
  .elementor-element.elementor-grid-mobile-3
  .eael-better-docs-category-box-post {
    width: 33.3333%;
    float: left;
  }
  .elementor-element.elementor-grid-mobile-3
  .eael-better-docs-category-box-post:nth-of-type(3n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-mobile-3
  .eael-better-docs-category-box-post:nth-of-type(3n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-mobile-4 {
    position: relative;
  }
  .elementor-element.elementor-grid-mobile-4
  .eael-better-docs-category-box-post {
    width: 25%;
    float: left;
  }
  .elementor-element.elementor-grid-mobile-4
  .eael-better-docs-category-box-post:nth-of-type(4n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-mobile-4
  .eael-better-docs-category-box-post:nth-of-type(4n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-mobile-5 {
    position: relative;
  }
  .elementor-element.elementor-grid-mobile-5
  .eael-better-docs-category-box-post {
    width: 20%;
    float: left;
  }
  .elementor-element.elementor-grid-mobile-5
  .eael-better-docs-category-box-post:nth-of-type(5n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-mobile-5
  .eael-better-docs-category-box-post:nth-of-type(5n + 1) {
    clear: left;
  }
  .elementor-element.elementor-grid-mobile-6 {
    position: relative;
  }
  .elementor-element.elementor-grid-mobile-6
  .eael-better-docs-category-box-post {
    width: 16%;
    float: left;
  }
  .elementor-element.elementor-grid-mobile-6
  .eael-better-docs-category-box-post:nth-of-type(6n) {
    margin-right: 0 !important;
  }
  .elementor-element.elementor-grid-mobile-6
  .eael-better-docs-category-box-post:nth-of-type(6n + 1) {
    clear: left;
  }
}