.eael-ticker-wrap {
	position: relative;
	overflow: hidden;
	display: flex;
	flex-flow: row nowrap;
	align-items: center;
	width: 100%;
}

.eael-ticker-wrap .ticker-badge {
	flex: 0 0 auto;
	padding: 8px 12px;
}

.eael-ticker-wrap .eael-ticker {
	overflow: hidden;
	flex: 1 1 auto;
}

.eael-ticker-wrap .eael-ticker .ticker-content {
	padding: 8px 12px;
	line-height: 1.8;
}

.eael-ticker-wrap .eael-ticker .ticker-content p{
	margin: 0;
}

.eael-ticker-wrap .swiper-button-prev {
	left: auto !important;
	right: 25px;
}

.eael-ticker-wrap div.swiper-slide,
.eael-ticker-wrap div.swiper-slide.swiper-slide-prev,
.eael-ticker-wrap div.swiper-slide.swiper-slide-next {
	opacity: 0 !important;
}

.eael-ticker-wrap div.swiper-slide.swiper-slide-active {
	opacity: 1 !important;
}

.eael-ticker-wrap .eael-content-ticker{
	&.swiper-container .swiper-slide {
		text-align: left;
	}
	&.swiper-initialized{
		.swiper-wrapper{
			overflow: initial;
		}
	}
}


@media only screen and (max-width: 767px) {
	.eael-ticker-wrap {
		flex-flow: row wrap;
	}
	.content-ticker-pagination {
		display: none;
	}
}

.eael-ticker-wrap {
    .swiper-button-next, .swiper-button-prev {
        background-image: none;
        outline: none;
        display: flex;
        align-items: center;
        justify-content: center;
        -webkit-transition: all .3s ease;
        -moz-transition: all .3s ease;
        transition: all .3s ease;

        &:before,
        &:after {
            display: none;
        }
    }
}

.rtl {
	.eael-ticker-wrap .eael-content-ticker.swiper-container .swiper-slide {
		text-align: right;
	}
	.eael-ticker-wrap .swiper-button-prev {
		left: 0 !important;
		right: auto !important;
	}
	.eael-ticker-wrap .swiper-button-next {
		right: auto;
		left: 12px;
	}
}