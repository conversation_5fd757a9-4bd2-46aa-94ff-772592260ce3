.eaelsv-overlay {
	position: absolute;
	display: block;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 4;
	cursor: pointer;
	background-size: cover;
	background-position: 50%;
	text-align: center;
	overflow: hidden;
}

.eaelsv-overlay-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	font-size: 90px;
	color: white;
	transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
}

.eael-sticky-video-wrapper {
	position: relative;
	width: 100%;
	margin: 0px;
	padding: 0px;
	transition: 0.5s;
	text-align: left;
	overflow: hidden;
	height: auto !important;

	/* Plyr CSS Started  */
	.plyr__controls button {
		box-shadow: none !important;
	}

	.plyr__controls button:hover {
		box-shadow: none !important;
	}

	.plyr__controls {
		display: none !important;
	}
	/* Plyr CSS Ended */
}

.eael-sticky-video-player2 {
	min-height: 20px;
	overflow: visible;
}

.eael-sticky-video-player2.out {
	position: fixed;
	z-index: 999;
	border: 0 !important;
	border-radius: 0px !important;
	height: 200px;
	width: 300px;
}

.eael-sticky-video-wrapper.out .eael-sticky-video-player2,
.eael-sticky-video-wrapper.out .eael-sticky-video-player {
	border-radius: 0px !important;
}

.eael-sticky-video-player2.in {
	position: relative;
	margin: 0px;
	padding: 0px;
	height: 100%;
	border: 0;
	line-height: 1;
}

.owp-play {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translateX(-50%) translateY(-50%);
	-ms-transform: translateX(-50%) translateY(-50%);
	transform: translateX(-50%) translateY(-50%);
}

.owp-play i {
	font-size: 100px;
	color: #fff;
	opacity: 0.8;
	text-shadow: 1px 0 6px rgba(0, 0, 0, 0.3);
	-webkit-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
}

.eael-sticky-video-player:hover .owp-play i {
	opacity: 1;
}

/* === Close Icon === */
.eaelsv-sticky-player-close {
	position: absolute;
	right: -25px;
	top: -36px;
	display: none;
	padding: 7px;
	font-size: 24px;
	z-index: 9999;
	cursor: pointer;
	box-sizing: content-box;
	overflow: visible;
}
.eaelsv-sticky-player-close:hover {
	color: #009900;
}
.eaelsv-sticky-player-close:before,
.eaelsv-sticky-player-close:after {
	position: absolute;
	left: 15px;
	background-color: #333;
}
.eaelsv-sticky-player-close:before {
	transform: rotate(45deg);
}
.eaelsv-sticky-player-close:after {
	transform: rotate(-45deg);
}

/* For Box Shadow issue in astra bar */
.plyr__progress input[type="range"]::-webkit-slider-runnable-track,
.plyr__volume input[type="range"]::-webkit-slider-runnable-track {
	box-shadow: 1px 1px 1px #000000, 0px 0px 1px #0d0d0d;
}
.plyr__progress input[type="range"]::-moz-range-track,
.plyr__volume input[type="range"]::-moz-range-track {
	box-shadow: 1px 1px 1px #000000, 0px 0px 1px #0d0d0d;
}

.eaelsv-overlay-visibility-transparent {
	.plyr--stopped.plyr__poster-enabled .plyr__poster,
	.plyr--youtube.plyr--full-ui.plyr--video .plyr__control--overlaid {
		// display: none;
		opacity: 0;
	}
}

/*******************
	Theme Support
********************/
//Blocksy Theme
.theme-blocksy .eael-sticky-video-player2[data-sticky*=yes]:not(.in, .out), 
.theme-blocksy .eael-sticky-video-player2[data-sticky*=fixed]:not(.in, .out) {
	position: initial !important;
}
