// popup

.eael-popup-details-render {
   width: 80%;
   max-width: 900px;

   .eael-preloader {
      margin: 0 auto;
      border: 5px solid #f3f3f3;
      border-radius: 50%;
      border-top: 5px solid #3498db;
      width: 50px;
      height: 50px;
      -webkit-animation: eaelSpin 2s linear infinite; /* Safari */
      animation: eaelSpin 2s linear infinite;
   }

   /* Safari */
   @-webkit-keyframes eaelSpin {
      0% {
         -webkit-transform: rotate(0deg);
      }
      100% {
         -webkit-transform: rotate(360deg);
      }
   }

   @keyframes eaelSpin {
      0% {
         transform: rotate(0deg);
      }
      100% {
         transform: rotate(360deg);
      }
   }
}

.eael-product-popup {
   position: fixed;
   left: 0;
   top: 0;
   width: 100%;
   height: 100%;
   display: -webkit-box;
   display: -ms-flexbox;
   display: flex;
   -webkit-box-align: center;
   -ms-flex-align: center;
   align-items: center;
   -webkit-box-pack: center;
   -ms-flex-pack: center;
   justify-content: center;
   z-index: -1;

   &.eael-product-popup-ready {
      z-index: 999;
      opacity: 1 !important;
   }

   &.eael-product-zoom-in {
      opacity: 0;
      transition: all 0.3s ease-out;

      .eael-product-popup-details {
         opacity: 0;
         transition: all 0.3s ease-in-out;
         transform: scale(0.5);
      }
   }

   &.eael-product-zoom-in.eael-product-popup-ready {
      .eael-product-popup-details {
         opacity: 1;
         transform: scale(0.9);
      }

      .eael-product-modal-bg {
         opacity: 0.7;
      }
   }

   &.eael-product-zoom-in.eael-product-modal-removing {
      .eael-product-modal-bg {
         opacity: 0;
      }

      .eael-product-popup-details {
         transform: scale(0.5);
         opacity: 0;
      }
   }

   .eael-product-modal-bg {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      background: #000000;
      opacity: 0;
      transition: all 0.3s ease-out;
   }

   .eael-product-popup-details {
      position: relative;
      margin: 5vh auto;
      padding: 20px;
      border: 1px solid #888;
      max-width: 900px;
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2),
         0 6px 20px 0 rgba(0, 0, 0, 0.19);
      background-color: #fefefe;
      width: 100%;
      border-radius: 10px;
      height: auto;
      max-height: fit-content;

      .single_add_to_cart_button {
         position: relative;
      }

      .single_add_to_cart_button.eael-addtocart-added:after {
         font-family: "Font Awesome 5 Free";
         content: "\f00c";
         font-weight: 900;
         display: inline-block;
         position: absolute;
         right: 12px;
      }

      .single_add_to_cart_button.eael-addtocart-loading:after {
         font-family: "Font Awesome 5 Free";
         content: "\f110";
         font-weight: 900;
         position: absolute;
         right: 12px;
         animation: eaelSpin 2s linear infinite;
      }
   }

   &.woocommerce div.product {
      display: flex;
      height: 100%;
      overflow-y: auto;
      background: transparent;
      position: relative;
      width: inherit;
      float: inherit;

      &.ast-article-single,
      .ast-article-single {
         background: transparent;
      }

      @media (max-width: 767px) {
         display: block;
      }

      div.images {
         width: 100%;
         margin-bottom: 0;

         &.woocommerce-product-gallery .flex-viewport {
            transform-style: preserve-3d;
            margin-bottom: 1em;
         }
         .flex-control-thumbs {
            li:nth-child(4n) {
               margin-right: 0;
            }
         }
      }

      div.woocommerce-product-gallery--columns-4,
      div.woocommerce-product-gallery--columns-5 {
         .flex-control-thumbs li {
            width: calc(25% - 0.75em);
            margin-right: 1em;
            margin-bottom: 1em;
            img {
               cursor: pointer;
               opacity: 0.5;
               &:hover {
                  opacity: 1;
               }
            }
            img.flex-active {
               opacity: 1;
            }

            &:last-child {
               margin-right: 0;
            }
         }
         ol.flex-control-thumbs {
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
         }
      }

      .product_title {
         font-size: 28px;
         line-height: 1.2em;
         font-weight: 700;
         margin-bottom: 10px;
         letter-spacing: normal;
         text-transform: capitalize;

         &.entry-title {
            display: block;
         }

         &:before {
            content: none;
         }
      }

      .price {
         font-size: 25px;
         line-height: 1.2em;
         margin: 0 0 15px;
         text-align: left !important;
      }

      .woocommerce-product-details__short-description {
         font-size: 18px;
         line-height: 1.2em;
         margin: 0;

         p,
         p:last-child {
            margin-bottom: 20px;
         }
      }

      .button {
         margin: 0 !important;
         line-height: 1.5em;
      }

      form.cart {
         margin: 0 0 1.2em;
         padding: 0;
         border: none;
         width: 100%;

         table {
            border: none;
            margin: 0 0 1.2em;
            border-collapse: collapse;
            width: 100%;

            tbody {
               display: table-row-group;
               border: none;
            }
         }

         p.stock {
            margin-bottom: 0;
         }

         .group_table {
            td {
               border: none;
            }

            .button {
               padding: 0.8em;
               font-weight: 400;
               font-size: 0.9rem;
               white-space: nowrap;
            }
         }

         div.quantity {
            margin-right: 15px;
            width: auto;

            input,
            a {
               height: 100%;
               border: 1px solid;
            }

            input[type="number"] {
               min-width: 90px;
            }

            .qty {
               text-align: center;

               &:focus {
                  outline: none;
               }
            }
         }

         .button.single_add_to_cart_button {
            padding: 10px 40px;
         }

         .product-single-quantity {
            button.plus,
            button.minus {
               display: none;
            }
         }
      }

      .woocommerce-product-rating {
         margin-bottom: 5px;

         .star-rating {
            font-size: 16px;
         }

         .woocommerce-review-link {
            display: inline-block;
            vertical-align: top;
            font-size: 16px;
            line-height: 18px;
         }
      }

      table {
         tbody {
            tr {
               border-bottom: 1px solid #ccc;

               td {
                  background: transparent;
                  vertical-align: middle !important;
                  padding: 15px 15px 15px 0 !important;
               }
            }
         }
      }

      .product_meta {
         font-size: 14px;
         border-top: 1px solid #ccc;
         border-bottom: 0;
         padding: 1em 0 0;
         margin: 0 0 0.8em;

         & > span {
            display: block;
            border: none;
            padding: 5px 0;
         }
      }
   }

   .eael-product-image-wrap {
      width: 40%;
      background-image: url("../../../wp-admin/images/spinner.gif");
      background-repeat: no-repeat;
      background-position: center center;

      @media (max-width: 767px) {
         width: 100%;
      }

      .badge-container {
         display: none;
      }

      .eael-new-product .flex-viewport {
         height: auto !important;
      }
   }

   .eael-product-details-wrap {
      width: 56%;
      margin-left: 4%;
      text-align: left;

      @media (max-width: 767px) {
         width: 100%;
         margin-left: 0;
         margin-top: 25px;
      }

      .is-divider {
         display: none;
      }
   }

   button.eael-product-popup-close {
      position: absolute;
      right: -15px;
      top: -18px;
      font-size: 20px;
      padding: 0;
      cursor: pointer;
      box-sizing: content-box;
      overflow: visible;
      background: #fff;
      text-align: center;
      box-shadow: 2px 0px 12px 3px rgba(0, 0, 0, 0.2),
         0 6px 20px 0 rgba(0, 0, 0, 0.19);
      color: #000 !important;
      height: 40px;
      width: 40px;
      min-height: 40px;
      max-width: 40px;
      display: flex !important;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      opacity: 1;
      border: 0;
      margin: 0;

      &:focus {
         outline: none;
      }
   }

   form.cart {
      display: flex;
      margin-bottom: 20px;

      &.variations_form {
         display: block;

         .variations {
            border: none;

            tr {
               margin-bottom: 5px;
            }

            td {
               display: block;
               width: 100%;
               border: none;
               padding: 0;
               margin-bottom: 5px;

               select {
                  width: 100%;
               }
            }
         }

         .variations_button {
            display: flex;
         }
      }

      &.grouped_form {
         display: block;

         table {
            .button {
               line-height: 1.2em;
            }
         }

         .quantity {
            width: 100%;

            input,
            a {
               height: auto !important;
               min-height: 2.507em;
               line-height: 1.2em;
            }
         }
      }

      .quantity {
         width: 37%;
         margin-right: 20px;

         input {
            width: 100%;
         }
      }
   }

   .eael-onsale {
      padding: 5px 10px;
      font-size: 12px;
      font-weight: 500;
      position: absolute;
      text-align: center;
      line-height: 1.2em;
      top: 30px;
      left: 0;
      margin: 0;
      background-color: #ff7a80;
      color: #fff;
      z-index: 9;

      &.sale-preset-1 {
         &.outofstock {
            br {
               display: none;
            }
         }

         &.right {
            left: auto;
            right: 0;
         }
      }

      &.sale-preset-2 {
         padding: 0;
         top: 5px;
         left: 5px;
         min-width: 50px;
         min-height: 50px;
         line-height: 50px;
         border-radius: 100%;
         -webkit-font-smoothing: antialiased;

         &.outofstock {
            line-height: 1.2em;
            display: flex;
            align-items: center;
            justify-content: center;
         }
      }

      &.sale-preset-3 {
         border-radius: 50px;
         left: 15px;
         top: 15px;

         &.outofstock {
            br {
               display: none;
            }
         }
      }

      &.sale-preset-4 {
         left: 0;
         top: 15px;

         &.outofstock {
            br {
               display: none;
            }
         }

         &:after {
            position: absolute;
            right: -15px;
            bottom: 0px;
            width: 15px;
            height: 24px;
            border-top: 12px solid transparent;
            border-bottom: 12px solid transparent;
            border-left: 10px solid #23a454;
            content: "";
         }
      }

      &.sale-preset-5 {
         display: block;
         line-height: 74px;
         height: 60px;
         width: 120px;
         left: -37px;
         top: -8px;
         right: auto;
         padding: 0;
         transform: rotate(-45deg);

         &.outofstock {
            line-height: normal;
            padding-top: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
         }
      }
   }
}

.theme-oceanwp {
   &.elementor-editor-active {
      .eael-product-popup.woocommerce div.product form.cart div.quantity .qty {
         max-width: 150px;
      }
   }

   .eael-product-popup.woocommerce
      div.product
      form.cart
      div.quantity
      .qty:focus {
      border: 1px solid;
   }

   .eael-product-popup.woocommerce
      div.product
      form.cart.grouped_form
      div.quantity {
      * {
         min-height: 33px;
         line-height: 33px;
         margin-top: 1px;
      }

      .qty {
         min-width: 100px;

         @media only screen and (max-width: 1023px) {
            min-width: 70px;
         }
      }
   }

   .eael-product-popup.woocommerce
      div.product
      form.cart
      div.quantity
      .qty:focus {
      border: 1px solid;
   }

   @media only screen and (max-width: 767px) {
      .eael-product-popup.woocommerce div.product form.cart div.quantity {
         width: 50%;

         .minus,
         .plus {
            width: 20%;
         }

         .qty {
            width: 60%;
            min-width: auto !important;
         }
      }

      .eael-product-popup.woocommerce
         div.product
         form.cart
         .button.single_add_to_cart_button {
         padding: 10px 18px !important;
      }
   }
}

// savoy theme conflicts
.theme-savoy {
   #elementor-lightbox-slideshow-single-img {
      display: none !important;
   }
   .eael-product-popup {
      &.woocommerce {
         .nm-quantity-wrap {
            .quantity {
               .input-text {
                  height: 42px;
               }
            }
         }
      }
      .flex-control-nav {
         display: flex;
         flex-wrap: wrap;
      }
      .nm-quantity-wrap {
         width: auto;
         border: none;
      }
      .nm-quantity-wrap {
         .quantity {
            display: flex;
         }
      }
      .variations_form.nm-custom-select tr .value select {
         opacity: 1;
         border: 1px solid #ccc;
      }

      #nm-product-meta {
         padding-left: 0;
         text-align: left;

         .nm-row {
            padding-left: 0;

            .col-xs-12 {
               padding-left: 0;
               display: grid;
               justify-content: start;
            }
         }
      }
      .eael-product-popup-ready ~ #elementor-lightbox-slideshow-single-img {
         display: none !important;
      }
   }
}

//Buddyboss theme conflicts
.theme-buddyboss-theme {
   .eael-product-popup.woocommerce
      div.product
      form.cart
      .button.single_add_to_cart_button {
      padding: 5px 36px;
   }
}
