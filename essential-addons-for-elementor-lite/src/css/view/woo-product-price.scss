.eael-product-price-edit {
   display: flex;
   gap: 8px;
   .price {
      display: flex;
      margin: 0;
      color: #434347;
      font-size: 18px;
      font-weight: 400;
      line-height: 29px;
      del {
         opacity: 0.5;
      }
      ins {
         text-decoration: none;
         font-weight: 600;
      }
   }
}

.eael-single-product-price {
   display: flex;
   align-items: center;
   gap: 10px;
}

.woocommerce:where(body:not(.woocommerce-uses-block-theme)) div.product {
   .eael-single-product-price {
      display: flex;
      p.price {
         display: flex;
         margin: 0;
         color: #434347;
         font-size: 18px;
         font-weight: 400;
         line-height: 29px;
         del {
            opacity: 0.5;
         }
         ins {
            text-decoration: none;
            font-weight: 600;
         }
      }
   }
}
