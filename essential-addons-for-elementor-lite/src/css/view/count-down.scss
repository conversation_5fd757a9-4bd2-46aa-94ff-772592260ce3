
.eael-countdown-items {
	list-style: none;
	margin: 0;
	padding: 0;
	display: table;
	table-layout: fixed;
	width: 100%;
}

.eael-countdown-items > li {
	list-style: none;
	margin: 0;
	padding: 0;
	display: table-cell;
	position: relative;
}

.eael-countdown-item > div {
	text-align: center;
	padding: 20px;
}


.eael-countdown-digits {
	font-size: 54px;
	line-height: 1;
}

.eael-countdown-label {
	font-size: 18px;
	line-height: 1;
	color: #ffffff;
}
.eael-countdown-show-separator {
	&.eael-countdown-separator-solid {
		.eael-countdown-digits::after {
			content: "|";
			position: absolute;
			left: 98%;
			z-index: 1;
		}
		
	}
	&.eael-countdown-separator-dotted {
		.eael-countdown-digits::after {
			content: ":";
			position: absolute;
			left: 98%;
			z-index: 1;
		}
	}
}

.eael-countdown-item:last-child .eael-countdown-digits::after {
	display: none;
}

/*--- Style 1 ---*/
.eael-countdown-items.style-1 .eael-countdown-item > div {
	background: #262625;
}

// responsive
@media all and (min-width: 1025px){
	.eael-countdown-label-block .eael-countdown-digits,
	.eael-countdown-label-block .eael-countdown-label {
		display: block;
		text-align: center;
	}
}
@media all and (max-width: 1025px){
	.eael-countdown-label-inline-tablet .eael-countdown-digits,
	.eael-countdown-label-inline-tablet .eael-countdown-label {
		display: inline-block !important;
	}
	.eael-countdown-label-block-tablet .eael-countdown-digits,
	.eael-countdown-label-block-tablet .eael-countdown-label {
		display: block !important;
	}
}
@media all and (max-width: 767px){
	.eael-countdown-label-inline-mobile .eael-countdown-digits,
	.eael-countdown-label-inline-mobile .eael-countdown-label {
		display: inline-block !important;
	}
	.eael-countdown-label-block-mobile .eael-countdown-digits,
	.eael-countdown-label-block-mobile .eael-countdown-label {
		display: block !important;
	}
    .eael-countdown-container .eael-countdown-item > div {
        padding: 10px;
    }
}
