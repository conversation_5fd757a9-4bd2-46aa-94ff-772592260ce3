.eael-creative-button-align-center {
    text-align: center;
}
.eael-creative-button-wrapper {
    display: flex;
}

.eael-creative-button {
    flex: 0 0 auto;
    min-width: 150px;
    text-align: center;
    vertical-align: middle;
    position: relative;
    z-index: 1;
    border-radius: 2px;
    padding: 20px 30px;
    font-size: 16px;
    line-height: 1;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -moz-osx-font-smoothing: grayscale;
}

.eael-creative-button:focus {
    outline: none;
}

/*--- Winona ---*/
.eael-creative-button--winona {
    overflow: hidden;
    padding: 0 !important;
    -webkit-transition: border-color 0.3s, background-color 0.3s;
    transition: border-color 0.3s, background-color 0.3s;
    -webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}

.eael-creative-button--winona::after {
    content: attr(data-text);
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    -webkit-transform: translate3d(0, 25%, 0);
    transform: translate3d(0, 25%, 0);
    display: flex;
    align-items: center;
    justify-content: center;
}

.eael-creative-button--winona::after,
.eael-creative-button--winona > .creative-button-inner {
    padding: 1em 2em;
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    -webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}

.eael-creative-button--winona:hover {
    &::after{
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    .eael-creative-button-icon-left,
    .eael-creative-button-icon-right{
        opacity: 1;
    }
    &> .creative-button-inner {
        -webkit-transform: translate3d(0, -25%, 0);
        transform: translate3d(0, 0%, 0);
    }
    .cretive-button-text{
        opacity: 0;
    }
}

/*--- Ujarak ---*/
.eael-creative-button--ujarak {
    -webkit-transition: border-color 0.4s, color 0.4s;
    transition: border-color 0.4s, color 0.4s;
}

.eael-creative-button--ujarak::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0;
    -webkit-transform: scale3d(0.7, 1, 1);
    transform: scale3d(0.7, 1, 1);
    -webkit-transition: -webkit-transform 0.4s, opacity 0.4s;
    transition: transform 0.4s, opacity 0.4s;
    -webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}

.eael-creative-button--ujarak,
.eael-creative-button--ujarak::before {
    -webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}

.eael-creative-button--ujarak:hover::before {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

/*--- Wayra ---*/
.eael-creative-button--wayra {
    overflow: hidden;
    -webkit-transition: border-color 0.3s, color 0.3s;
    transition: border-color 0.3s, color 0.3s;
    -webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}

.eael-creative-button--wayra::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 150%;
    height: 100%;
    z-index: -1;
    -webkit-transform: rotate3d(0, 0, 1, -45deg) translate3d(0, -3em, 0);
    transform: rotate3d(0, 0, 1, -45deg) translate3d(0, -3em, 0);
    -webkit-transform-origin: 0% 100%;
    transform-origin: 0% 100%;
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s,
        background-color 0.3s;
    transition: transform 0.3s, opacity 0.3s, background-color 0.3s;
}

.eael-creative-button--wayra:hover::before {
    opacity: 1;
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
    -webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}

/* Tamaya */
.eael-creative-button--tamaya {
    float: left;
    min-width: 150px;
    max-width: 250px;
    display: block;
    margin: 1em;
    padding: 1em 2em;
    border: none;
    background: none;
    color: inherit;
    vertical-align: middle;
    position: relative;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
    color: #7986cb;
    min-width: 180px;
}
.eael-creative-button--tamaya.button--inverted {
    color: #37474f;
    border-color: #37474f;
}
.eael-creative-button--tamaya-before,
.eael-creative-button--tamaya-after {
    position: absolute;
    width: 100%;
    left: 0;
    background: transparent;
    overflow: hidden;
    transition: transform 0.3s;
}
.eael-creative-button--tamaya.button--inverted {
    .eael-creative-button--tamaya-before,
    .eael-creative-button--tamaya-after {
        background: #fff;
        color: #37474f;
    }
}
.eael-creative-button--tamaya {

    .eael-creative-button--tamaya-before {
        bottom: 50%;
        span {
            transform: translateY(50%);
            display: block;
            opacity: 1 !important;
        }
    }
    .eael-creative-button--tamaya-after {
        top: 50%;
        span {
            transform: translateY(-50%);
            display: block;
            opacity: 1 !important;
        }
    }
    span{
        display: block;
        transform: scale3d(0.2, 0.2, 1);
        opacity: 0;
        transition: transform 0.3s, opacity 0.3s;
        transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    }
}
.eael-creative-button--tamaya:hover {
    .eael-creative-button--tamaya-before{
        transform: translate3d(0, -100%, 0);
    }

    .eael-creative-button--tamaya-after{
        transform: translate3d(0, 100%, 0);
    }
}
.eael-creative-button--tamaya:hover {
    .eael-creative-button--tamaya-before {
        bottom: 50%;
        transform: translateY(-750%);
    }
    .eael-creative-button--tamaya-after {
        top: 50%;
        transform: translateY(750%);
    }
    span{
        opacity: 1;
        transform: scale3d(1, 1, 1);
        content: 'Bangladesh';
    }
}

/*--- Rayen ---*/
.eael-creative-button--rayen {
    overflow: hidden;
    padding: 0 !important;

    &> .creative-button-inner{
        padding: 1em 2em;
        transition: transform 0.3s;
        transition-timing-function: cubic-bezier(0.75, 0, 0.125, 1);
    }
    &::before{
        content: attr(data-text);
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        transform: translate3d(-100%, 0, 0);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        transition: transform 0.3s;
        transition-timing-function: cubic-bezier(0.75, 0, 0.125, 1);
    }
}
.eael-creative-button--rayen:hover {
    &::before{
        transform: translate3d(0, 0, 0);
    }
    &> .creative-button-inner{
        transform: translate3d(0, 100%, 0);
    }
}
.creative-button-inner {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.eael-creative-button-icon-left {
    margin-right: 5px;
}

.eael-creative-button-icon-right {
    margin-left: 5px;
}

.rtl {
    .eael-creative-button-wrapper {
        direction: ltr;
    }
}
