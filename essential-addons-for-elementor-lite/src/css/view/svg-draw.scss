.eael-svg-draw-container {
  &.none {
    path, circle, rect, polygon {
      stroke-dasharray: none;
    }
  }

  svg {
    height: 200px;
    width: 200px;
    overflow: visible;

    path, circle, rect, polygon {
      fill: none;
      stroke: #2c3e50;
      stroke-width: 1.5;
      stroke-linecap: round;
      stroke-linejoin: round;
      stroke-dasharray: 4000000;
      stroke-dashoffset: 4000000;
    }
  }

  &.fill-svg {
    svg {
      path, circle, rect, polygon {
        animation-name: eaelFillIn;
        animation-fill-mode: forwards;
      }
    }
  }

  @keyframes eaelFillIn {
    0% {
      fill-opacity: 0;
    }
    40% {
      fill-opacity: 1;
    }
    100% {
      fill-opacity: 1;
    }
  }
}