.eael-dual-header {
    display: block;
    margin-bottom: 50px;
    .eaa-svg {
        font-size: 36px;
    }
    svg {
        height: 1em;
        width: 1em;
    }
    .dch-sep-icon,
    .dch-icon {
        display: block;
        padding: 0;
        margin: 20px 0 10px 0;
        font-size: 36px;
    }
    .eael-dch-title {
        font-size: 36px;
        font-weight: 700;
        text-transform: uppercase;
        line-height: 48px;
        margin: 10px 0;

        .eael-dch-title-text {
            font-size: 36px;
            font-weight: 700;
            text-transform: uppercase;
            line-height: 48px;
            margin: 10px 0;

            &.eael-dch-title-lead{
                color: #1abc9c;
            }
        }


        .gradient-color,
        .eael-dch-title-gradient{
            background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
        }
    }
    .subtext {
        font-size: 16px;
        display: block;
    }
}



/*--- Builder Related Css ---*/
.eael-dual-header-content-align{
    &-center {
        text-align: center;
    }
    &-left {
        text-align: left;
    }
    &-right {
        text-align: right;
    }
}
@media screen and (max-width: 1024px) and (min-width: 768px) {
    .eael-dual-header-content-tablet-align{
        &-center {
            text-align: center;
        }
        &-left {
            text-align: left;
        }
        &-right {
            text-align: right;
        }
    }
}
@media screen and (max-width: 767px) {
    .eael-dual-header-content-mobile-align{
        &-center {
            text-align: center;
        }
        &-left {
            text-align: left;
        }
        &-right {
            text-align: right;
        }
    }
}

// separator
.eael-dch-separator-wrap {
    display: flex;
    justify-content: center;

    .separator-one,
    .separator-two {
        display: inline-block;
        width: 15%;
        height: 5px;
    }
    .separator-one {
        background: #207eff;
    }
    .separator-two {
        background: #4f6592;
    }
}
