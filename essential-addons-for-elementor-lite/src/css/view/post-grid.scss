.eael-post-grid-container .eael-post-grid {
	margin: 0 -10px;
}

.eael-post-grid-container .eael-post-grid .eael-grid-post {
	float: left;
	padding: 10px;
}

.eael-post-carousel .eael-grid-post {
	float: none;
	padding: 0;
}

.eael-grid-post-holder {
	border: 1px solid rgba(0, 0, 0, 0.1);
}

.eael-grid-post-holder-inner {
	height: 100%;
}

.eael-entry-media {
	position: relative;
}

.eael-entry-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 2;
	-webkit-transition: opacity 0.2s ease-in-out, -webkit-transform 0.25s cubic-bezier(0.19, 1, 0.22, 1);
	-moz-transition: opacity 0.2s ease-in-out, -moz-transform 0.25s cubic-bezier(0.19, 1, 0.22, 1);
	transition: opacity 0.2s ease-in-out, transform 0.25s cubic-bezier(0.19, 1, 0.22, 1);
}

.eael-entry-overlay > a {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 3;
}

.eael-entry-title {
	margin: 10px 0 5px;
	font-size: 1.2em;
}

.eael-entry-thumbnail {
	img {
		width: 100%;
		max-width: 100%;
		vertical-align: middle;
	}
	&.eael-image-ratio {
		img {
			position: absolute;
			top: calc(50% + 1px);
			left: calc(50% + 1px);
			transform: scale(1.01) translate(-50%, -50%);
		}
	}
}

.eael-entry-thumbnail > img {
	height: 100%;
}

.eael-entry-footer .eael-author-avatar,
.eael-entry-header-after .eael-author-avatar {
	width: 50px;
	padding-right: 8px;
}

.eael-entry-footer .eael-author-avatar .avatar,
.eael-entry-header-after .eael-author-avatar .avatar {
	border-radius: 50%;
}

.eael-entry-header-after.style-two {
	flex-wrap: wrap;
}

.eael-post-grid .eael-entry-footer .eael-entry-meta {
	text-align: left;
}

.eael-grid-post .eael-entry-meta {
	display: flex;
	flex-direction: row;
	white-space: nowrap;
	flex-wrap: wrap;
}

.eael-grid-post .eael-entry-footer .eael-entry-meta {
	flex-direction: column;
}

.eael-entry-meta > div {
	font-size: 12px;
	line-height: 1.2;
	padding-bottom: 5px;
}

.eael-grid-post-excerpt p {
	margin: 0;
	font-size: 14px;
}

.eael-entry-meta .eael-entry-footer .eael-posted-by {
	display: block;
}

.eael-grid-post .eael-entry-wrapper {
	padding: 15px;
}

.eael-grid-post .eael-entry-footer .eael-author-avatar,
.eael-grid-post .eael-entry-header-after .eael-author-avatar {
	// padding: 15px 0;
}

.eael-post-grid .eael-entry-wrapper > .eael-entry-meta span.eael-posted-by,
.eael-post-grid .eael-entry-wrapper > .eael-entry-header-after .eael-entry-meta span.eael-posted-by {
	padding-right: 8px;
}

.eael-post-grid .eael-entry-wrapper > .eael-entry-meta span.eael-posted-on::before,
.eael-post-grid .eael-entry-wrapper > .eael-entry-header-after .eael-entry-meta span.eael-posted-on::before {
	content: "\f111";
	font-family: "Font Awesome 5 Free";
	font-weight: 700;
	color: inherit;
	opacity: 0.4;
	font-size: 0.8em;
	padding-right: 7px;
}

.eael-post-grid .eael-entry-wrapper > .eael-entry-header-after.style-two .eael-entry-meta span.eael-posted-on::before {
	content: "";
	padding-right: 0;
}
/*--- Post Grid Thumbnail Hover Effects ---*/

.eael-entry-media {
	position: relative;
}

.eael-entry-overlay {
	display: flex;
	align-items: center;
	justify-content: center;
}

.eael-entry-overlay > i {
	color: #fff;
}

/*--- fade in ---*/
.eael-entry-overlay.fade-in {
	visibility: hidden;
	opacity: 0;
	transition: 300ms;
}

.eael-entry-media:hover .eael-entry-overlay.fade-in {
	visibility: visible;
	opacity: 1;
}

.eael-entry-media:hover .eael-entry-overlay.fade-in > i {
	transform: translate(0);
	opacity: 1;
}

/*--- zoom in --- */
.eael-entry-overlay.zoom-in {
	transform: scale(0.9);
	visibility: hidden;
	opacity: 0;
	transition: 300ms;
}

.eael-entry-media:hover .eael-entry-overlay.zoom-in {
	visibility: visible;
	opacity: 1;
	transform: scale(1);
}

/*--- slide up ---*/
.eael-entry-overlay.slide-up {
	transform: translateY(100%);
	visibility: hidden;
	opacity: 0;
	transition: 300ms;
}

.eael-entry-media:hover .eael-entry-overlay.slide-up {
	transform: translateY(0);
	visibility: visible;
	opacity: 1;
}

.eael-entry-media {
	overflow: hidden;
}

/*--- Post Grid & Carousel Hover Styles ---*/
.eael-entry-media.grid-hover-style-fade-in .eael-entry-overlay {
	opacity: 0;
}

.eael-entry-media.grid-hover-style-fade-in:hover .eael-entry-overlay {
	opacity: 1;
}

.eael-entry-media.grid-hover-style-none .eael-entry-overlay {
	display: none;
}

.eael-entry-overlay.none { opacity: 0; }

.eael-entry-media.grid-hover-style-zoom-in .eael-entry-overlay {
	transform: scale(0.4);
	opacity: 0;
}

.eael-entry-media.grid-hover-style-zoom-in:hover .eael-entry-overlay {
	transform: scale(1);
	opacity: 1;
}

.eael-entry-media.grid-hover-style-animate-down .eael-entry-overlay {
	transform: translateY(-100%);
}

.eael-entry-media.grid-hover-style-animate-down .eael-entry-overlay > i {
	transform: translateY(-100px);
	transition-delay: 100ms;
	transition-duration: 300ms;
}

.eael-entry-media.grid-hover-style-animate-down:hover .eael-entry-overlay {
	transform: translate(0);
}

.eael-entry-media.grid-hover-style-animate-down:hover .eael-entry-overlay > i {
	transform: translateY(0);
}

.eael-entry-media.grid-hover-style-animate-up .eael-entry-overlay {
	transform: translateY(100%);
	visibility: hidden;
	opacity: 0;
}

.eael-entry-media.grid-hover-style-animate-up .eael-entry-overlay > i {
	transform: translateY(100px);
	transition-delay: 100ms;
	transition-duration: 300ms;
}

.eael-entry-media.grid-hover-style-animate-up:hover .eael-entry-overlay {
	transform: translate(0);
	visibility: visible;
	opacity: 1;
}

.eael-entry-media.grid-hover-style-animate-up:hover .eael-entry-overlay > i {
	transform: translateY(0);
}

.eael-grid-post {
	.eael-entry-thumbnail > img {
		height: 100%;
		object-fit: cover;
	}
}

.eael-grid-post-excerpt .eael-post-elements-readmore-btn {
	display: block;
}

/*--- Gallery Column CSS ---*/
@media only screen and (min-width: 1025px) {
	/* For Desktop: */
	.elementor-element.elementor-grid-eael-col-1 {
		position: relative;
	}
	.elementor-element.elementor-grid-eael-col-1 .eael-grid-post {
		width: 100%;
		float: left;
	}

	.elementor-element.elementor-grid-eael-col-2 {
		position: relative;
	}
	.elementor-element.elementor-grid-eael-col-2 .eael-grid-post {
		width: 50%;
		float: left;
	}
	.elementor-element.elementor-grid-eael-col-2 .eael-grid-post:nth-of-type(2n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-eael-col-2 .eael-grid-post:nth-of-type(2n + 1) {
		clear: both;
	}

	.elementor-element.elementor-grid-eael-col-3 {
		position: relative;
	}
	.elementor-element.elementor-grid-eael-col-3 .eael-grid-post {
		width: 33.3333%;
		float: left;
	}
	.elementor-element.elementor-grid-eael-col-3 .eael-grid-post:nth-of-type(3n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-eael-col-3 .eael-grid-post:nth-of-type(3n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-eael-col-4 {
		position: relative;
	}
	.elementor-element.elementor-grid-eael-col-4 .eael-grid-post {
		width: 25%;
		float: left;
	}
	.elementor-element.elementor-grid-eael-col-4 .eael-grid-post:nth-of-type(4n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-eael-col-4 .eael-grid-post:nth-of-type(4n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-eael-col-5 {
		position: relative;
	}
	.elementor-element.elementor-grid-eael-col-5 .eael-grid-post {
		width: 20%;
		float: left;
	}
	.elementor-element.elementor-grid-eael-col-5 .eael-grid-post:nth-of-type(5n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-eael-col-5 .eael-grid-post:nth-of-type(5n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-eael-col-6 {
		position: relative;
	}
	.elementor-element.elementor-grid-eael-col-6 .eael-grid-post {
		width: 16%;
		float: left;
	}
	.elementor-element.elementor-grid-eael-col-6 .eael-grid-post:nth-of-type(6n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-eael-col-6 .eael-grid-post:nth-of-type(6n + 1) {
		clear: both;
	}
}

@media only screen and (max-width: 1024px) and (min-width: 766px) {
	/* For tablets: */
	.elementor-element.elementor-grid-tablet-eael-col-1 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-eael-col-1 .eael-grid-post {
		width: 100%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-eael-col-2 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-eael-col-2 .eael-grid-post {
		width: 50%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-eael-col-2 .eael-grid-post:nth-of-type(2n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-eael-col-2 .eael-grid-post:nth-of-type(2n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-tablet-eael-col-3 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-eael-col-3 .eael-grid-post {
		width: 33.3333%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-eael-col-3 .eael-grid-post:nth-of-type(3n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-eael-col-3 .eael-grid-post:nth-of-type(3n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-tablet-eael-col-4 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-eael-col-4 .eael-grid-post {
		width: 25%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-eael-col-4 .eael-grid-post:nth-of-type(4n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-eael-col-4 .eael-grid-post:nth-of-type(4n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-tablet-eael-col-5 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-eael-col-5 .eael-grid-post {
		width: 20%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-eael-col-5 .eael-grid-post:nth-of-type(5n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-eael-col-5 .eael-grid-post:nth-of-type(5n + 1) {
		clear: both;
	}

	// For column 6
	.elementor-element.elementor-grid-tablet-eael-col-6 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-eael-col-6 .eael-grid-post {
		width: 16%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-eael-col-6 .eael-grid-post:nth-of-type(6n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-eael-col-6 .eael-grid-post:nth-of-type(6n + 1) {
		clear: both;
	}
}

@media only screen and (max-width: 767px) {
	.elementor-element.elementor-grid-mobile-eael-col-1 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-eael-col-1 .eael-grid-post {
		width: 100%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-eael-col-2 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-eael-col-2 .eael-grid-post {
		width: 50%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-eael-col-2 .eael-grid-post:nth-of-type(2n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-eael-col-2 .eael-grid-post:nth-of-type(2n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-mobile-eael-col-3 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-eael-col-3 .eael-grid-post {
		width: 33.3333%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-eael-col-3 .eael-grid-post:nth-of-type(3n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-eael-col-3 .eael-grid-post:nth-of-type(3n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-mobile-eael-col-4 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-eael-col-4 .eael-grid-post {
		width: 25%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-eael-col-4 .eael-grid-post:nth-of-type(4n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-eael-col-4 .eael-grid-post:nth-of-type(4n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-mobile-eael-col-5 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-eael-col-5 .eael-grid-post {
		width: 20%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-eael-col-5 .eael-grid-post:nth-of-type(5n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-eael-col-5 .eael-grid-post:nth-of-type(5n + 1) {
		clear: both;
	}
	.elementor-element.elementor-grid-mobile-eael-col-6 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-eael-col-6 .eael-grid-post {
		width: 16%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-eael-col-6 .eael-grid-post:nth-of-type(6n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-eael-col-6 .eael-grid-post:nth-of-type(6n + 1) {
		clear: both;
	}
}

.eael-author-avatar > a {
	display: block;
}

.eael-entry-footer,
.eael-entry-header-after {
	overflow: hidden;
	display: flex;
}

.eael-entry-footer > div,
.eael-entry-header-after > div {
	display: inline-block;
	float: left;
}

.post-carousel-categories {
	position: absolute;
	left: 0;
	top: 0;
	z-index: 11;
	width: 100%;
	margin: 0;
	padding: 15px;
	text-align: left;
	visibility: hidden;
	opacity: 0;
	transition: 300ms;
	li {
		display: inline-block;
		text-transform: capitalize;
		margin-right: 5px;
		position: relative;
		&:after {
			content: ",";
			color: #ffffff;
		}
		&:last-child:after {
			display: none;
		}
		a {
			color: #fff;
		}
	}
}

.eael-entry-media:hover {
	.post-carousel-categories {
		visibility: visible;
		opacity: 1;
	}
}



.eael-post-grid-style {
	&-three {
		.eael-meta-posted-on {
			min-width: 60px;
			height: 50px;
			padding: 5px;
			display: inline-flex;
			justify-content: center;
			align-items: center;
			border-radius: 4px;
			box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.5);
			text-align: center;
			font-size: 16px;
			line-height: 18px;
			margin-top: 12px;
			margin-left: 12px;
			span {
				display: block;
			}
		}
	}
  &-two {
    .eael-entry-meta {
		align-items: baseline;
		.eael-meta-posted-on {
			padding: 0;
			font-size: 12px;
			margin-right: 15px;
			color: #929292;
			i {
			margin-right: 7px;
			}
		}
		.eael-posted-by.style-two-footer {
			padding: 0;
			font-size: 12px;
			a {
				color: #929292;
				// margin-right: 7px;
			}
		}
	.post-meta-categories {
	list-style: none;
	display: inline-flex;
	flex-flow: wrap;
	margin: 0;
	padding-left: 0;
	li {
		font-size: 12px;
		margin-right: 4px;
		color: #929292;
		&:last-child {
		margin-right: 0;
		}
		a {
			color: #929292;
		}
	}
	}
    }
  }
}

.rtl {
	.eael-post-grid-container .eael-post-grid .eael-grid-post {
		float: right;
	}

	.eael-post-grid .eael-entry-footer {
		.eael-entry-meta {
			text-align: right;
		}

		.eael-author-avatar {
			padding-right: 0;
			padding-left: 8px;
		}
	}

	.eael-entry-header-after .eael-author-avatar {
		padding-right: 0;
		padding-left: 8px;
	}

	.eael-post-grid-style {
		&-two {
			.eael-entry-meta {
				.eael-meta-posted-on {
					margin-right: 0;
					margin-left: 15px;

					i {
						margin-right: 0;
						margin-left: 7px;
					}
				}
			}
		}
	}

	.eael-post-grid .eael-entry-wrapper > .eael-entry-meta span.eael-posted-by,
	.eael-post-grid .eael-entry-wrapper > .eael-entry-header-after .eael-entry-meta span.eael-posted-by {
		padding-right: 0;
		padding-left: 8px;
	}

	.eael-post-grid .eael-entry-wrapper > .eael-entry-meta span.eael-posted-on::before,
	.eael-post-grid .eael-entry-wrapper > .eael-entry-header-after .eael-entry-meta span.eael-posted-on::before {
		padding-right: 0;
		padding-left: 7px;
	}
}
