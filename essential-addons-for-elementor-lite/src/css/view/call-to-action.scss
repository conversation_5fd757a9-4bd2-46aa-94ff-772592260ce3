.eael-call-to-action {
   display: block;
   padding: 30px;
   font-size: 16px;
   color: #4d4d4d;
   font-weight: 400;
   line-height: 27px;
   margin: 0 auto;


   .eael-cta-heading {
      font-size: 36px;
      font-weight: 600;
      line-height: 36px;
      margin-bottom: 10px;
      text-transform: capitalize;
      font-style: normal;

      &.eael-cta-gradient-title,
      .eael-cta-gradient-text{
         background-clip: text !important;
         -webkit-text-fill-color: transparent !important;
      }
   }
   p {
      margin-bottom: 10px;
   }

   &.bg-lite {
      background: #f4f4f4;
   }

   &.bg-img {
      background-image: url("../img/bg.jpg");
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      position: relative;
      z-index: 0;
      color: rgba(255, 255, 255, 0.7);
   }

   &.bg-img {
      &.bg-fixed {
         background-attachment: fixed;
         background-position: center center;
      }
      .icon {
         color: #fff;
      }
      .eael-cta-heading {
         color: rgba(255, 255, 255, 0.9);
      }
   }

   /*--- Cta Flex ---*/
   &.cta-flex {
      display: flex;
      justify-content: space-between;
      align-items: flex-start; 

      /*--- Cta Flex ---*/
      .content {
         padding: 0px 15px;
         flex-grow: 1;
      }

      .action {
         flex-grow: 1;
         text-align: right;
         padding-top: 25px;
         flex-basis: 23%;
      }
      &.cta-preset-2 .action {
         flex-basis: 40%;
      }
   }
   &.cta-icon-flex {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      /*--- Cta Icon Flex ---*/
      .icon {
         flex-grow: 1;
         font-size: 80px;
         text-align: left;
         line-height: 130px;
      }

      .action {
         flex-grow: 1;
         text-align: right;
         padding-top: 22px;
         flex-basis: 22%;
      }

      &.cta-preset-2 .action {
         flex-basis: 35%;
      }
      .content {
         flex-grow: 1;
         padding: 0px 30px;
      }
   }

   /*--- Cta Button ---*/
   .cta-button {
      position: relative;
      display: inline-block;
      padding: 12px 30px;
      background: #f9f9f9;
      font-size: 16px;
      text-decoration: none;
      color: #4d4d4d;
      transition: 0.5s;
      box-shadow: 0px 0px 3px -1px rgba(0, 0, 0, 0.2);
      margin-right: 10px;
      margin-top: 10px;
      z-index: 0;
      overflow: hidden;

      &:last-child {
         margin-right: 0px;
      }
      &:focus {
         outline: none;
      }

      /*--- Hover ---*/
      &:hover {
         color: #fff;
         background: #3f51b5;
         box-shadow: 0px 1px 12px 1px rgba(0, 0, 0, 0.1);
      }

      &.cta-secondary-button {
         .eael-secondary-btn-flex{
            display: flex;
            align-items: center;
         }
         .btn-icon{
            i{
               font-size: 14px;
            }
            svg{
               width: 14px;
               height: 14px;
            }
         }
      }

      &.effect-1{
         &:after {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            background: #3f51b5;
            top: 0px;
            left: 0px;
            transform: translateY(-100%);
            -webkit-transform: translateY(-100%);
            -ms-transform: translateY(-100%);
            z-index: -1;
            -webkit-transition: 0.5s;
            -ms-transition: 0.5s;
            transition: 0.5s;
            color: #fff;
         }

         &:hover::after {
            transform: translateY(0);
         }
      }

      /*--- Cta Button effect 2 ---*/
      &.effect-2{
         :after {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            background: #3f51b5;
            top: 0px;
            left: 0px;
            z-index: -1;
            transition: 0.5s;
            color: #fff;
            transform: translateX(-100%);

            &:hover {
               transform: translateX(0);
            }
         }
      }
   }

   /*--- Media Queries ---*/
   @media only screen and (max-width: 768px) {
      &.cta-flex,
      &.cta-icon-flex {
         flex-wrap: wrap;
      }
       .eael-cta-heading {
         font-size: 28px;
         line-height: 36px;
         margin-top: 0px;
      }
      &.cta-icon-flex .icon {
         flex-grow: 1;
         font-size: 48px;
         line-height: 90px;
         text-align: center;
      }
      &.cta-flex .content,
      &.cta-icon-flex .content {
         flex-grow: 1;
         text-align: center;
         padding: 0px;
      }
      &.cta-flex .action,
      &.cta-icon-flex .action {
         text-align: center;
         padding-top: 0px;
      }
      & .cta-button {
         padding: 12px 25px;
      }
   }

   @media only screen and (max-width: 360px) {
      font-size: 14px;
      line-height: 26px;
         
      .cta-button:not(.cta-btn-preset-2) {
         padding: 4px 20px;
         font-size: 12px;
      }
       .eael-cta-heading {
         font-size: 20px;
         line-height: 30px;
      }
   }

   &.cta-preset-2 {
      padding: 77px 77px 100px 85px;
      border-radius: 5px;
      box-shadow: 0 25px 35px 0 rgb(0 9 78 / 18%);

      @media all and (max-width: 767px) {
         padding: 50px;
      }

      .sub-title {
         font-family: "Poppins", Sans-serif;
         color: #fff;
         font-size: 25px;
         font-weight: 400;
         line-height: 1.2em;
      }
      .eael-cta-heading {
         color: #fff;
         font-family: "Poppins", Sans-serif;
         font-size: 35px;
         font-weight: 500;
         line-height: 1.2em;
      }

      p {
         color: #e9deff;
         font-family: "Poppins", Sans-serif;
         font-size: 16px;
         font-weight: 400;
         line-height: 1.5em;
      }

      .cta-button {
         color: #4d4d4d;
         background: #fff;
         border-radius: 100px;
         box-shadow: 0 10px 20px 0 rgb(0 9 78 / 12%);
         font-family: "Poppins", Sans-serif;
         font-size: 15px;
         font-weight: 400;
         line-height: 1.8em;

         &.cta-secondary-button {
            background: transparent;
            border: 1px solid #fff;
            color: #fff;
            box-shadow: none;

            &:hover {
               color: #000;
               background: #fff;
            }
         }

         &:hover {
            background: #f92c8b;
            color: #fff;
         }

         &.cta-btn-preset-2 {
            border-bottom-right-radius: 0;
            padding-left: 70px;

            &:hover {
               .btn-icon {
                  background: #fff;
               }

               i {
                  color: #f92c8b;
               }
               svg {
                  fill: #f92c8b;
               }
            }

            .btn-icon {
               background: #f92c8b;
               color: #fff;
               position: absolute;
               left: 0;
               top: 0;
               height: 100%;
               width: 50px;
               text-align: center;
               border-radius: 50%;
               font-size: 20px;
               display: flex;
               justify-content: center;
               align-items: center;
            }

            //i {
            //    color: #fff;
            //}

            svg {
               fill: #fff;
            }
         }
      }

      .icon {
         color: #fff;
      }

      &.cta-basic {
         background-color: #8b54ff;
         //text-align: center;
      }
      &.cta-flex {
         background-color: #22cbd0;

         p {
            color: #fff;
         }
      }
      &.cta-icon-flex {
         background-color: #8b54ff;
      }
   }


   &.cta-preset-1 {
      .btn-icon {
         float: left;
         height: 20px;
         width: auto;
      }
   }
}

.elementor-widget-eael-cta-box{
   &.content-align-cta{
      &-default {
         text-align: left;
      }
      &-center {
         text-align: center;
      }
      &-right {
         text-align: right;
      }
   }
   @media all and (min-width: 768px) and (max-width: 1024px) {
      &.content-align--tabletcta{
         &-default {
            text-align: left;
         }
         &-center {
            text-align: center;
         }
         &-right {
            text-align: right;
         }
      }
   }
   @media all and (max-width: 767px) {
      &.content-align--mobilecta{
         &-default {
            text-align: left;
         }
         &-center {
            text-align: center;
         }
         &-right {
            text-align: right;
         }
      }
   }
}

.eael-cta-overlay-yes .eael-call-to-action.bg-img:after {
   content: "";
   position: absolute;
   width: 100%;
   height: 100%;
   top: 0px;
   left: 0px;
   z-index: -1;
   background: rgba(0, 0, 0, 0.8);
}
