.eael-tooltip {
	position: relative;
	display: inline-block;
	// min-width: 150px;
	padding: 12px 24px;
	font-size: .93rem;
	color: #333;
	line-height: 1;
	cursor: pointer;
	transition: all 0.3s ease-in-out;
}

.eael-tooltip .eael-tooltip-text {
	display: block;
	width: 100%;
	visibility: hidden;
	background-color: black;
	color: #fff;
	border-radius: 4px;
	padding: 10px;
	position: absolute;
	z-index: 1;
	font-size: .93rem;
	line-height: 1.3;
	p {
		margin: 0;
	}
	a {
		transition: none;
	}
}

.eael-tooltip .eael-tooltip-text::after {
	content: "";
	position: absolute;
	border-width: 5px;
	border-style: solid;
}

.eael-tooltip:hover .eael-tooltip-text,
.eael-tooltip-content:focus + .eael-tooltip-text {
	visibility: visible;
}

/*--- Left ---*/
.eael-tooltip .eael-tooltip-text.eael-tooltip-left {
	top: 50%;
	right: 100%;
	transform: translateY(-50%);
	margin-right: 10px;
}

.eael-tooltip:hover .eael-tooltip-text.eael-tooltip-left,
.eael-tooltip-content:focus + .eael-tooltip-text.eael-tooltip-left {
	animation: eaelTooltipLeftIn 300ms ease-in-out;
}

.eael-tooltip .eael-tooltip-text.eael-tooltip-left::after {
	top: calc(50% - 5px);
	left: 100%;
	border-color: transparent transparent transparent black;
}

/*--- Right ---*/
.eael-tooltip .eael-tooltip-text.eael-tooltip-right {
	top: 50%;
	left: 100%;
	transform: translateY(-50%);
	transition: all 0.3s ease-in-out;
	margin-left: 10px;
}

.eael-tooltip:hover .eael-tooltip-text.eael-tooltip-right,
.eael-tooltip-content:focus + .eael-tooltip-text.eael-tooltip-right {
	animation: eaelTooltipRightIn 300ms linear;
}

.eael-tooltip .eael-tooltip-text.eael-tooltip-right::after {
	top: calc(50% - 5px);
	right: 100%;
	border-color: transparent black transparent transparent;
}

/*--- Top ---*/
.eael-tooltip .eael-tooltip-text.eael-tooltip-top {
	bottom: calc(100%);
	left: 50%;
	transform: translateX(-50%);
	margin: 0 auto 10px auto;
}

.eael-tooltip .eael-tooltip-text.eael-tooltip-top::after {
	margin-top: 0;
	top: 100%;
	left: calc( 50% - 5px);
	border-color: black transparent transparent transparent;
}

.eael-tooltip:hover .eael-tooltip-text.eael-tooltip-top,
.eael-tooltip-content:focus + .eael-tooltip-text.eael-tooltip-top {
	animation: eaelTooltipTopIn 300ms linear;
}

/*--- Bottom ---*/
.eael-tooltip .eael-tooltip-text.eael-tooltip-bottom {
	top: 100%;
	left: 50%;
	transform: translateX(-50%);
	margin: 10px auto 0 auto;
}

.eael-tooltip .eael-tooltip-text.eael-tooltip-bottom::after {
	margin-top: 0;
	bottom: 100%;
	left: calc( 50% - 5px);
	border-color: transparent transparent black transparent;
}

.eael-tooltip:hover .eael-tooltip-text.eael-tooltip-bottom,
.eael-tooltip-content:focus + .eael-tooltip-text.eael-tooltip-bottom {
	animation: eaelTooltipBottomIn 300ms linear;
}

/*--- Alignments ---*/

.eael-tooltip-align-left {
    text-align: left;
}

.eael-tooltip-align-right {
    text-align: right;
}

.eael-tooltip-align-center {
    text-align: center;
}

.eael-tooltip-align-justify .eael-tooltip {
	display: flex;
	justify-content: center;
	align-items: center;
}

@media (min-width: 481px) and (max-width: 960px)  {
    .eael-tooltip-align-tablet-left {
        text-align: left;
    }
    
    .eael-tooltip-align-tablet-right {
        text-align: right;
    }
    
    .eael-tooltip-align-tablet-center {
        text-align: center;
    }

    .eael-tooltip-align-tablet-justify .eael-tooltip {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

@media (max-width: 480px){
    .eael-tooltip-align-mobile-left {
        text-align: left;
    }
    
    .eael-tooltip-align-mobile-right {
        text-align: right;
    }
    
    .eael-tooltip-align-mobile-center {
        text-align: center;
    }

    .eael-tooltip-align-mobile-justify .eael-tooltip {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

/*--- Tooltip Keyframes ---*/
@keyframes eaelTooltipRightIn {
	0% {
		opacity: 0;
		left: 105%;
	}
	100% {
		opacity: 1;
		left: 100%;
	}
}

@keyframes eaelTooltipLeftIn {
	0% {
		opacity: 0;
		right: 105%;
	}
	100% {
		opacity: 1;
		right: 100%;
	}
}

@keyframes eaelTooltipTopIn {
	0% {
		opacity: 0;
		bottom: 110%;
	}
	100% {
		opacity: 1;
		bottom: 100%;
	}
}

@keyframes eaelTooltipBottomIn {
	0% {
		opacity: 0;
		top: 110%;
	}
	100% {
		opacity: 1;
		top: 100%;
	}
}

span.eael-tooltip-content,
span.eael-tooltip-content a {
    width: 100%;
	display: block;
	text-align: center;
}

.eael-tooltip-text-align-left {
	.eael-tooltip-text, .eael-tooltip-content a { text-align: left; }
}
.eael-tooltip-text-align-right {
	.eael-tooltip-text, .eael-tooltip-content a { text-align: right; }
}
.eael-tooltip-text-align-center {
	.eael-tooltip-text, .eael-tooltip-content a { text-align: center; }
}
.eael-tooltip-text-align-justify {
	.eael-tooltip-text, .eael-tooltip-content a { text-align: justify; }
}