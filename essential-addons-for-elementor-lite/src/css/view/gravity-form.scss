.eael-gravity-form-align-default,
.eael-gravity-form-align-left,
.eael-gravity-form-btn-align-left {
	text-align: left;
}

.eael-gravity-form-align-right,
.eael-gravity-form-btn-align-right {
	text-align: right;
}

.eael-gravity-form-align-center,
.eael-gravity-form-btn-align-center {
	text-align: center;
}

.gform_wrapper form li,
.gform_wrapper li {
	list-style: none;
}

.eael-gravity-form .gform_wrapper ul.gform_fields li.gfield {
  padding: 0px;
}

.eael-gravity-form .gform_wrapper textarea {
	padding: 0;
	font-weight:normal!important;
	font-family: inherit;
}

.eael-gravity-form .gform_wrapper .gform_footer input.button,
.eael-gravity-form .gform_wrapper .gform_footer input[type=submit],
.eael-gravity-form .gform_wrapper .gform_page_footer input.button,
.eael-gravity-form .gform_wrapper .gform_page_footer input[type=submit] {
	margin: 0;
}

.eael-gravity-form.title-description-hide .gform_heading {
	display: none;
}

.eael-gravity-form.labels-hide .gform_wrapper .top_label .gfield_label,
.eael-gravity-form.labels-hide .gform_wrapper .field_sublabel_below .ginput_complex.ginput_container label {
	display: none;
}

.eael-gravity-form-button-full-width .gform_wrapper .gform_footer input[type="submit"] {
	width: 100%;
}

.eael-gravity-form .gform_wrapper .gf_scroll_text .gsection_description {
	margin: 0;
}


.gform_wrapper .gf_progressbar {
  @media only screen and (max-width: 767px) {
     width: calc(100%)!important;
  }
  @media only screen and (min-width: 768px) {
    width: calc(100%)!important;
 }
   
}


.gform_wrapper.gform_validation_error .gform_body ul li.gfield.gfield_error:not(.gf_left_half):not(.gf_right_half) {
  @media only screen and (min-width: 641px){
    max-width: calc(100%)!important;
  }
}

// File Upload
.ginput_container_fileupload input[type="file"]::file-selector-button,
.ginput_container_fileupload input[type="file"]::-webkit-file-upload-button,
.ginput_container_fileupload .button {
	cursor: pointer;

	background-color: rgba(0,0,0,.75);
	color: #fff;
	position: relative;
	display: inline-block;
	text-decoration: none;
	border: 1px solid rgba(0,0,0,.75);
	padding: 0.5em 1em;
	transition: all .2s;
	border-radius: 3px;

	&:hover {
		background-color: #262625;
		color: #fff;
	}
}