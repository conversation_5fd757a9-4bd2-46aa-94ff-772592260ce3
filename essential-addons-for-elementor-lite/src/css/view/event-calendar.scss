.eael-event-calendar-wrapper {
	font-family: Segoe UI;
	.eael-event-calendar-cls{
		min-height: 800px;
		min-width: 100%;
	}
	.fc-event{
		padding: 5px 10px;
	}
	a{
		color: #181818;
	}
	.fc-timeGridWeek-view,
	fc-timeGridDay-view{
		.fc-event{
			padding: 5px;
		}
	}
	.fc {
		.fc-timegrid .fc-daygrid-day-events{
			position: relative;
		}
		.fc-daygrid-day-events{
			position: absolute;
			width: 100%;
		}
		table {
			margin: auto;
			overflow: auto;
		}
	}

	.fc-view table thead:first-child tr:first-child td {
		border-top: 1px solid #ddd !important;
		border-bottom: none !important;
	}
	.fc-daygrid-event-dot{
		display: none !important;
	}
	.fc-day-grid-event {
		margin-top: 2px;
		padding: 8px;
		font-size: 12px;
		border-left-width: 5px;
	}
	.fc-day.fc-today {
		background: transparent;
	}
	.fc-day-top.fc-today {
		.fc-day-number {
			font-weight: bold;
		}
	}

	.fc-row table thead:first-child tr:first-child th {
		font-size: 14px;
	}

	.fc-day-number {
		font-size: 14px;
		float: center !important;
	}
	.fc-col-header-cell-cushion{
		padding: 10px;
	}
	.fc-daygrid-block-event .fc-event-time,
	.fc-daygrid-dot-event .fc-event-title{
		font-weight: 400;
	}

	.eaelec-modal {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: -1;

		.eael-ec-modal-bg {
			position: absolute;
			left: 0;
			top: 0;
			height: 100%;
			width: 100%;
			background: #000000;
			opacity: 0;
			transition: all 0.3s ease-out;
		}

		&.eael-ec-popup-ready {
			z-index: 99999;
			opacity: 1;
		}
	}

	.eaelec-modal-content {
		position: relative;
		margin: auto;
		border: 1px solid #888;
		max-width: 900px;
		box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
		background-color: #fefefe;
		padding: 20px 0;
		width: 100%;
		border-radius: 10px;
	}

	.eaelec-modal-header {
		padding: 0px 30px;
		color: white;
		border-color: #009900;
		overflow: auto;
		margin-top: 8px;
	}

	.eaelec-modal-header h2 {
		color: #242424;
		margin: 0;
		padding: 0;
	}

	.eaelec-modal-header .eaelec-event-popup-date {
		color: #555;
		padding-top: 2px;
		display: inline-block;
    text-transform: capitalize;

		i {
			color: #5725ff;
		}
	}

	.eaelec-modal-close {
		position: absolute;
		right: -15px;
		top: -18px;
		font-size: 24px;
		z-index: 9999;
		cursor: pointer;
		box-sizing: content-box;
		overflow: visible;
		border: 2px solid #fff;
		background: #fff;
		text-align: center;
		box-shadow: 2px 0px 12px 3px rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
		color: #000 !important;
		height: 40px;
		width: 40px;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;

		> span {
			font-size: 20px;
		}
	}

	.eaelec-modal-close:hover,
	.eaelec-modal-close:focus {
		color: #fff;
		text-decoration: none;
		cursor: pointer;
	}

	.eaelec-modal-body {
		padding: 0 10px 0 35px;
		margin: 10px 0;
		color: #555555;
		height: 300px;
		overflow-y: auto;
		word-break: break-word;
	}

	.eaelec-modal-body::-webkit-scrollbar {
		width: 6px;
	}
	.eaelec-modal-body::-webkit-scrollbar-thumb {
		background: #aaa;
		border-radius: 10px;
	}

	.eaelec-modal-footer {
		padding: 0px 10px 0 35px;
		color: white;
		border: 0px solid #ff0000;
	}

	.eaelec-event-date-start,
	.eaelec-event-date-end {
		font-size: 15px;
	}

	@media (max-width: 575px) {
		.fc-toolbar {
			margin: 10px !important;
		}
		.fc-toolbar .fc-center {
			width: 100% !important;
			margin: 0 !important;
			padding: 0 !important;
		}
		.fc-toolbar h2 {
			font-size: 16px !important;
			width: 100% !important;
			text-align: center !important;
			padding: 5px !important;
		}

		.fc-ltr .fc-axis {
			font-size: 12px !important;
		}

		.fc-row table thead:first-child tr:first-child th {
			font-weight: normal !important;
			font-size: 12px !important;
			padding: 5px !important;
		}
	}
	.fc-toolbar.fc-header-toolbar {
		.fc-center {
			h2 {
				font-size: 22px;
			}
		}
		.fc-left {
			.fc-button .fc-icon {
				font-size: 13px;
			}
		}
	}

	&.layout-table{
		table{
			margin-bottom: 0;
			tbody{
				tr{
					td{
						.hide{
							display: none !important;
						}
					}
				}
			}
		}
		.ea-advanced-data-table-search-left {
			text-align: left;
		}
		.ea-advanced-data-table-search-right {
			text-align: right;
		}
		.eael-event-calendar-pagination{
			border: none !important;
			padding: 0;
			span,
			a{
				display: inline-block;
				border: 1px solid #eee;
				margin: 2px;
				padding: 3px 8px;
				font-size: 14px;
				border-radius: 4px;
			}
			a{
				cursor: pointer;
			}
		}
	}
}
.elementor-page .entry-content .eael-event-calendar-wrapper a{
	text-decoration: none;
}
// Calendar Header
.fc-toolbar.fc-header-toolbar {
	.fc-button-group {
		button:not(:first-child) {
			margin-left: 5px;
		}
	}

	button.fc-timeGridWeek-button,
	button.fc-timeGridDay-button,
	button.fc-listWeek-button,
	button.fc-listMonth-button,
	button.fc-dayGridMonth-button {
		position: relative;

		&:before {
			font-family: "Font Awesome 5 Free";
			font-weight: 900;
			font-size: 13px;
			display: inline-block;
			padding-right: 5px;
			padding-left: 0;
			-webkit-font-smoothing: antialiased;
		}
	}

	button.fc-timeGridWeek-button:before {
		content: "\f03a";
		left: 8px;
	}

	button.fc-timeGridDay-button:before {
		content: "\f0c9";
		left: 21px;
	}

	button.fc-dayGridMonth-button:before {
		content: "\f00a";
	}

	button.fc-listWeek-button:before ,
	button.fc-listMonth-button:before {
		content: "\f00b";
	}

	button.fc-button.fc-button-primary {
		box-shadow: none;
		height: auto;
		width: auto;
		float: none;
		font-size: 13px;
		font-weight: 400;
		color: #5e5e5e;
		border-radius: 3px;
		text-transform: capitalize;
		background: #f7f7fb;
		text-shadow: none;
		line-height: 19px;
		padding: 8px 12px;
		border: none;

		&.fc-state-active,
		&:hover,
		&:visited{
			background: #5725ff;
			color: #fff;
			box-shadow: none;
		}

		&.fc-button-active {
			background: #5725ff;
			color: #fff;
		}
	}
}

// Calendar body
th.fc-widget-header > span {
	padding: 14px;
	display: block;
	font-size: 16px;
	color: #424344;
}

span.fc-day-number {
	padding: 17px;
	display: block;
}

.fc-basic-view .fc-day-number,
.fc-basic-view .fc-week-number {
	padding: 12px 2px 2px 2px;
	display: block;
	border-bottom: 0px solid;
}

.eael-event-calendar-wrapper {
	.fc-view {
		.fc-body {
			thead:first-child {
				tr:first-child {
					td {
						border-bottom: 3px solid transparent;
					}
				}
			}
		}
	}
}

.eael-event-calendar-wrapper {
	.fc-view {
		table {
			thead:first-child {
				tr:first-child {
					td {
						border-top: 1px solid #dfdfe9;
					}
				}
			}
		}
	}
}

.fc-unthemed {
	.fc-timeGridDay-view,
	.fc-timeGridWeek-view {
		.fc-event {
			border-left: 1px solid;
			border-color: #4d4d4d !important;

			.fc-time {
				padding-left: 3px;
			}
		}
		.fc-bg {
			table tbody tr > td {
				padding-left: 1px;
			}
		}

		.fc-time-grid .fc-event .fc-content {
			justify-content: end;
		}
	}

	.fc-timeGridWeek-view {
		.fc-time-grid .fc-event .fc-content {
			display: inherit;
		}
	}
	.fc-listWeek-view ,
	.fc-listMonth-view {
		.fc-list-table {
			tr.fc-list-item {
				cursor: pointer;
			}
			tr td {
				padding-bottom: 10px;
				padding-top: 10px;
			}
			tr.fc-list-item .fc-list-item-title a{
				text-decoration: none;
			}
		}
	}
	.fc-dayGridMonth-view {
		.fc-day-grid-event .fc-time {
			font-weight: 400;
		}
	}

	.fc-content,
	.fc-divider,
	.fc-list-heading td,
	.fc-list-view,
	.fc-popover,
	.fc-row,
	tbody,
	td,
	th,
	thead {
		border-color: #dfdfe9;
	}

	a.fc-day-grid-event,
	.fc-event {
		color: #707070;
		font-size: 14px;
		background: #fff;
		border-radius: 5px;
		font-weight: 400;
		margin: 1px;
		border: none;

		.fc-day-grid-event .fc-time {
			font-weight: normal;
		}
	}

	.fc-time-grid {
		.fc-event {
			min-height: 20px;

			.fc-content {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100%;
				width: 100%;
				.fc-title {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
		}
	}

	.fc-event:hover {
		color: #707070;
	}
}

.eaelec-modal.eael-zoom-in {
	opacity: 0;
	transition: all 0.3s ease-out;
	display: none;

	.eaelec-modal-content {
		opacity: 0;
		transition: all 0.3s ease-in-out;
		transform: scale(0.5);
	}

	&.eael-ec-popup-ready {
		.eael-ec-modal-bg {
			opacity: 0.7;
		}

		.eaelec-modal-content {
			opacity: 1;
			transform: scale(0.9);
		}
	}

	&.eael-ec-modal-removing {
		.eaelec-modal-content {
			transform: scale(0.5);
			opacity: 0;
		}

		.eael-ec-modal-bg {
			opacity: 0;
		}
	}
}
.eaelec-modal.eael-ec-popup-ready{
	display:flex;
}
@media (min-width: 768px) and (max-width: 1024px) {
	.eael-event-calendar-wrapper {
		.fc-toolbar.fc-header-toolbar {
			margin-bottom: 12px;

			.fc-center {
				h2 {
					font-size: 22px;
				}
			}
		}

		.fc-right {
			.fc-button-group {
				.fc-button.fc-button-primary {
					&:before {
						font-size: 15px;
					}
				}
			}
		}

		.fc-timeGridWeek-view {
			a .fc-content {
				display: inherit !important;
			}
		}

		.fc-day-header.fc-widget-header {
			span {
				font-size: 15px;
			}
		}

		.fc-toolbar.fc-header-toolbar {
			.fc-button-group {
				button:not(:first-child) {
					margin-left: 5px;
				}
			}
		}

		.eaelec-modal-body {
			height: 200px;
		}
	}
}

// For mobile
@media only screen and (max-width: 767px) {
	.eael-event-calendar-wrapper {
		.fc-dayGridMonth-view .fc-day-grid-event {
			.fc-content {
				font-size: 12px;
			}
			padding: 8px 0px 8px 0px !important;
		}
		.fc-daygrid.fc-dayGridMonth-view.fc-view {
			overflow: auto;
			> table.fc-scrollgrid.fc-scrollgrid-liquid {
				position: relative;
				height: 600px;
				width: auto !important;
			}

			.fc-col-header-cell,
			td.fc-daygrid-day{
				width: 160px;
			}
		}
	}

	.fc .fc-toolbar.fc-header-toolbar {
		margin-bottom: 10px;
	}
	.fc-toolbar-chunk {
		display: flex;
		justify-content: center;
		margin-bottom: 5px;
		text-align: center;
		width: 100%;

		.fc-button-group {
			.fc-button.fc-button-primary {
				font-size: 11px;

				&:before {
					font-size: 11px;
					padding-left: 0;
				}
			}
		}
	}
	.fc-center {
		text-align: center;
	}

	.fc-toolbar.fc-header-toolbar {
		display: grid;
		width: 100%;
		justify-content: center;
		grid-row-gap: 5px;

		.fc-button-group button.fc-button.fc-button-primary {
			margin-left: 5px;
			font-size: 12px;
		}
	}

	th.fc-widget-header > span {
		padding: 0;
		font-size: 14px;
	}

	.fc-basic-view .fc-day-number,
	.fc-basic-view .fc-week-number {
		padding: 0;
		margin: 0;
		font-size: 10px;
	}

	.fc-basic-view .fc-body .fc-row {
		min-height: 2em;
	}
	.eael-event-calendar-wrapper {
		.eaelec-modal-body {
			height: 100px;
		}
	}
}
