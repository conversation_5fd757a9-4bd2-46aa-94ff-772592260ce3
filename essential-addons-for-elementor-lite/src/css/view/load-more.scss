@keyframes eaelLoaderSpin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.eael-button-wrap,
.eael-load-more-button-wrap {
	display: flex;

	&.eael-force-hide {
		display: none !important;
	}

	&.eael-infinity-scroll{
		justify-content: center;

		.eael-load-more-button:not(.button--loading){
			display: none !important;
			height: 1px;
		}
	}
}

.eael-button,
.eael-load-more-button {
	display: flex !important;
	align-items: center;
	justify-content: center;
	padding: 1em 2em;
	border: 0px solid;
	font-size: 16px;
	overflow: hidden;

	&.hide {
		display: none !important;
	}

	> span {
		//margin-left: -20px;
	}
	&.button--loading {
		.eael-btn-loader {
			display: block;
		}
	}
	.eael-btn-loader {
		display: none;
		border-radius: 50%;
		width: 20px;
		height: 20px;
		font-size: 10px;
		position: relative;
		top: auto;
		left: -200%;
		border-top: 4px solid rgba(255, 255, 255, 0.2);
		border-right: 4px solid rgba(255, 255, 255, 0.2);
		border-bottom: 4px solid rgba(255, 255, 255, 0.2);
		border-left: 4px solid #ffffff;
		transform: translateZ(0);
		animation: eaelLoaderSpin 1.1s infinite linear;
		margin-right: 5px;
		transition: all 0.2s;

		&:after {
			border-radius: 50%;
			width: 20px;
			height: 20px;
		}
	}

	&:focus {
		outline: none;
	}

	&.button--loading {
		> span {
			margin-left: 0;
		}

		.eael-btn-loader {
			left: 0;
		}
	}
}

// RTL

.rtl {
	.eael-load-more-button-wrap {
		direction: ltr;
	}
}
