$color_0: #eeeeee;
$color_1: #000000;
$color_2: rgba(255, 255, 255, .15);
@-webkit-keyframes eaelAnimateStripe {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(35px, 0);
    }
}

@keyframes eaelAnimateStripe {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(35px, 0);
    }
}

@-webkit-keyframes eaelAnimateStripeRTL {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(-35px, 0);
    }
}

@keyframes eaelAnimateStripeRTL {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(-35px, 0);
    }
}

.eael-progressbar {
    position: relative;
}

.eael-progressbar-title {
    font-size: 20px;
    font-weight: 400;
}

.eael-progressbar-line {
    position: relative;
    display: block;
    width: 100%;
    height: 12px;
    background-color: $color_0;
    .eael-progressbar-count-wrap {
        position: absolute;
        right: 0;
        bottom: calc(100% + 5px);
        font-size: 16px;
        font-weight: 400;
        line-height: 1;
    }
}

.eael-progressbar-line-fill {
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 0;
    width: 0;
    height: 12px;
    background-color: $color_1;
    transform: translateY(-50%);
    transition: width 1500ms linear;
    overflow: hidden;
}

.eael-progressbar-circle {
    position: relative;
    width: 200px;
    height: 200px;
    .eael-progressbar-title {
        font-size: 16px;
        font-weight: 400;
    }
    .eael-progressbar-count-wrap {
        font-size: 28px;
        font-weight: 700;
    }
}

.eael-progressbar-circle-shadow {
    width: 220px;
    height: 220px;
    padding: 10px;
    border-radius: 50%;
}

.eael-progressbar-circle-pie {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    clip-path: inset(0 0 0 50%);
}

.eael-progressbar-circle-inner {
    height: 100%;
    width: 100%;
    border-width: 12px;
    border-style: solid;
    border-color: $color_0;
    border-radius: 50%;
}

.eael-progressbar-circle-half {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    border-width: 12px;
    border-style: solid;
    border-color: $color_1;
    border-radius: 50%;
    clip-path: inset(0 50% 0 0);
}

.eael-progressbar-circle-half-left {
    transform: rotate(0deg);
}

.eael-progressbar-circle-half-right {
    transform: rotate(180deg);
    visibility: hidden;
}

.eael-progressbar-circle-inner-content {
    position: absolute;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
    text-align: center;
}

.eael-progressbar-half-circle {
    position: relative;
    width: 200px;
    height: 100px;
    overflow: hidden;
    .eael-progressbar-circle-pie {
        clip-path: inset(0 0 50% 0);
    }
    .eael-progressbar-circle-half {
        clip-path: inset(50% 0 0 0);
        transform: rotate(0deg);
        transition: transform 1500ms linear;
    }
    .eael-progressbar-circle-inner-content {
        top: initial;
        bottom: 0;
        transform: translateY(0);
    }
    .eael-progressbar-title {
        font-size: 16px;
        font-weight: 400;
    }
    .eael-progressbar-count-wrap {
        font-size: 28px;
        font-weight: 700;
    }
}

.eael-progressbar-half-circle-after {
    position: relative;
    font-size: 12px;
    font-weight: 400;
    clear: both;
}

.eael-progressbar-postfix-label {
    float: right;
}

.eael-progressbar-line-stripe .eael-progressbar-line-fill:after {
    content: '';
    position: absolute;
    top: 0;
    left: -35px;
    width: calc(100% + 70px);
    height: 100%;
    background-image: linear-gradient(45deg, $color_2 25%, transparent 25%, transparent 50%, $color_2 50%, $color_2 75%, transparent 75%, transparent);
    background-size: 35px 35px;
}

.eael-progressbar-line-animate .eael-progressbar-line-fill:after {
    animation: eaelAnimateStripe 2s linear infinite;
}

.eael-progressbar-line-animate-rtl .eael-progressbar-line-fill:after {
    animation: eaelAnimateStripeRTL 2s linear infinite;
}

.eael-progressbar-circle-wrap>div {
    margin: 0 auto;
}

/*alignment*/
.eael-progressbar-line-container.left,
.eael-progressbar-circle-container.left > div,
.eael-progressbar-box-container.left > div {
    margin: 0 auto 0 0;
}

.eael-progressbar-line-container.center,
.eael-progressbar-circle-container.center > div,
.eael-progressbar-box-container.center > div {
    margin: 0 auto;
}

.eael-progressbar-line-container.right,
.eael-progressbar-circle-container.right > div,
.eael-progressbar-box-container.right > div {
    margin: 0 0 0 auto;
}


.rtl {
    .eael-progressbar-line-container {
        text-align: right;

        .eael-progressbar-count-wrap {
            left: 0;
            right: auto;
        }

        .eael-progressbar-line-fill {
            left: auto;
            right: 0;
        }
    }

    .eael-progressbar {
        &.eael-progressbar-circle {
            .eael-progressbar-circle-pie {
                clip-path: inset(0 50% 0 0);
            }

            .eael-progressbar-circle-half {
                clip-path: inset(0 0 0 50% );
            }
        }
    }

    .eael-progressbar-circle-container {
        .eael-progressbar-half-circle-after{
            .eael-progressbar-prefix-label {
                float: left;
            }
        }
    }

}