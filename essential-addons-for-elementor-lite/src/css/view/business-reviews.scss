.eael-business-reviews-wrapper {
    .eael-business-reviews-items {
        padding: 0 15px;
    }
    .preset-2 {
        .preset-content-wrap {
            border: 1px solid #f5f5f5;
            border-radius: 10px;
            padding: 30px;
            margin: 5px;
            box-shadow: 0px 0 5px rgba(19, 26, 64, 0.1);
        }

        .preset-content-footer {
            display: flex;
            column-gap: 20px;
            margin-top: 35px;
            padding-top: 20px;
            border-top: 1px solid #f5f5f5;
        }

        .preset-content-footer-rating {
            margin-left: auto;
        }

        .eael-google-review-text,
        .eael-google-review-reviewer-name,
        .eael-google-review-time {
            text-align: left;
        }
    }
    .preset-3 {
        .preset-content-body {
            border-radius: 10px;
            padding: 10px;
            margin: 5px;
            box-shadow: 0px 0px 5px rgba(19, 26, 64, 0.1);
            position: relative;
        }

        .preset-extra-shadow {
            position: absolute;
            bottom: -68px;
            left: 0;
        }

        .eael-google-review-rating,
        .eael-google-review-text,
        .eael-google-review-reviewer-name,
        .eael-google-review-time {
            text-align: left;
        }

        .preset-content-footer {
            display: flex;
            column-gap: 20px;
            margin-top: 40px;
        }
    }

    .eael-google-reviews-slider-header,
    .eael-google-reviews-grid-header {
        text-align: center;
        margin-bottom: 40px;
        cursor: pointer;

        .eael-google-reviews-business-name,
        .eael-google-reviews-business-name a {
            color: #292844;
            font-size: 36px;
            line-height: 1.5em;
        }

        .eael-google-reviews-business-rating,
        .eael-google-reviews-business-rating a {
            color: #8A8EAA;
            font-size: 12px;
        }

        .eael-google-reviews-business-address,
        .eael-google-reviews-business-address a {
            color: #8A8EAA;
            font-size: 13px;
        }

        .eael-google-reviews-business-address p,
        .eael-google-reviews-business-rating p {
            margin: 5px 0 0;
        }

        .eael-google-reviews-business-address {
            margin-top: 10px;
        }
    }

    .eael-google-reviews-business-rating {
        display: flex;
        column-gap: 5px;
        align-items: center;
        justify-content: center;
    }

    .eael-google-review-reviewer-photo {
        text-align: center;
    }

    .eael-google-review-reviewer-name,
    .eael-google-review-reviewer-name a {
        color: #292844;
        font-size: 16px;
        text-align: center;
        font-weight: 500;
    }
    
    .eael-google-review-time {
        color: #8A8EAA;
        font-size: 12px;
        text-align: center;
        font-weight: 400;
    }

    .eael-google-review-text {
        color: #000000;
        font-size: 14px;
        text-align: center;
        font-weight: 400;
        margin-top: 10px;
    }

    .eael-google-review-rating {
        text-align: center;
        margin-top: 20px;
    }

    // Swiper Dots
    .swiper-container-wrap-dots-outside .swiper-pagination{
        position: static;
    }

    // Swiper Arrows
    .swiper-button-prev,
    .swiper-button-next {
        background-image: none !important;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .swiper-button-next:after, .swiper-rtl .swiper-button-prev:after,
    .swiper-button-prev:after, .swiper-rtl .swiper-button-next:after {
        content: '';
    }

    .swiper-button-prev {
        left: -40px;
        right: auto;
    }

    .swiper-button-next {
        right: -40px;
        left: auto;
    }

    .swiper-button-prev:focus,
    .swiper-button-next:focus{
        outline: none;
    }

    .swiper-pagination {
        cursor: pointer;
    }

    .eael-google-reviews-grid-body {
        display: grid;

        &.eael-column {
            &-1 {
                grid-template-columns: auto;
            }
    
            &-2 {
                grid-template-columns: auto auto;
            }
    
            &-3 {
                grid-template-columns: auto auto auto;
            }
    
            &-4 {
                grid-template-columns: auto auto auto auto;
            }
        }
    }

    @media only screen and (min-width: 768px) and (max-width: 1023px) {
        .eael-google-reviews-grid-body {
            &.eael-column-tablet {
                &-1 {
                    grid-template-columns: auto;
                }
        
                &-2 {
                    grid-template-columns: auto auto;
                }
        
                &-3 {
                    grid-template-columns: auto auto auto;
                }
        
                &-4 {
                    grid-template-columns: auto auto auto auto;
                }
            }
        }
    }

    @media only screen and (max-width: 767px) {
        .eael-google-reviews-grid-body {
            &.eael-column-mobile {
                &-1 {
                    grid-template-columns: auto;
                }
        
                &-2 {
                    grid-template-columns: auto auto;
                }
        
                &-3 {
                    grid-template-columns: auto auto auto;
                }
        
                &-4 {
                    grid-template-columns: auto auto auto auto;
                }
            }
        }
    }
}
