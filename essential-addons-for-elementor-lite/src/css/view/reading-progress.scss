.eael-reading-progress-wrap {
    &.eael-reading-progress-wrap-local {
        .eael-reading-progress-global {
            display: none;
        }        
    }
    &.eael-reading-progress-wrap-global {
        .eael-reading-progress-local {
            display: none;
        }        
    }
    &.eael-reading-progress-wrap-disabled {
        .eael-reading-progress-global,
        .eael-reading-progress-local {
            display: none;
        }        
    }

    .eael-reading-progress {
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        height: 5px;
        z-index: 999999;
    
        &.eael-reading-progress-bottom {
            top: unset;
            bottom: 0;
        }
        
        .eael-reading-progress-fill {
            height: 5px;
            background-color: #1fd18e;
            width: 0%;
            transition: width 50ms ease;
        }
    }
}