.left-right-prev {
   &.swiper-button-prev {
      top: 20px;
      left: 35%;
      transform: rotate(90deg);
   }
   &.swiper-button-next {
      top: auto;
      bottom: 0;
      right: 37%;
      transform: rotate(90deg);
   }
   &::after {
      font-size: 18px !important;
   }
}

.eael-pi {
   button.mfp-arrow {
      width: 85px;
   }

   .mfp-arrow-right:before {
      margin-left: -28px;
   }
   .mfp-arrow-right:after {
      margin-left: -25px;
   }
}

.eael-pi-thumb-left {
   .product_image_slider {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 10px;
      .product_image_slider__thumbs {
         flex-direction: column;
         height: 42vh;
         .single-thumb-img {
            .swiper-slide {
               height: 90px !important;
            }
         }
         .product_image_slider__prev {
            position: relative;
            height: auto;
            justify-content: center;
         }
         .product_image_slider__next {
            position: relative;
            height: auto;
            justify-content: center;
         }
      }
   }
}

.eael-pi-thumb-right {
   .product_image_slider {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: flex-start;
      gap: 10px;
      .product_image_slider__thumbs {
         flex-direction: column;
         height: 42vh;
         .single-thumb-img {
            .swiper-slide {
               height: 90px !important;
            }
         }
         .product_image_slider__prev {
            position: relative;
            height: auto;
            justify-content: center;
         }
         .product_image_slider__next {
            position: relative;
            height: auto;
            justify-content: center;
         }
      }
   }
}

.eael-single-product-images {
   .product_image_slider {
      width: 100%;
      &__thumbs {
         // width: 100%;
         padding: 0;
         position: relative;
         display: flex;
         // flex: 1;
         .swiper-button-prev,
         .swiper-button-next {
            &::after {
               font-size: 12px !important;
            }
            background-color: #fff;
            border: 1px solid #edecf6;
            height: 26px;
            width: 25px;
            border-radius: 3px;
         }
         &__image {
            width: 100%;
            height: auto;
         }
         .swiper-slide {
            width: 100px;
            height: 100%;
            overflow: hidden;
            opacity: 0.5;
            &-thumb-active {
               opacity: 1;
            }
         }
         .swiper-container:not(.swiper-container-initialized) > .swiper-wrapper,
         .swiper:not(.swiper-initialized) > .swiper-wrapper {
            overflow: visible;
         }
      }
      &__container {
         height: auto;
         margin: 0 0 10px 0;
         flex: 1;
         position: relative;
         .swiper-button-prev,
         .swiper-button-next {
            &::after {
               font-size: 16px !important;
            }
            background-color: #fff;
            border: 1px solid #edecf6;
            height: 30px;
            width: 30px;
            border-radius: 3px;
         }
         .product_image_slider__trigger {
            position: absolute;
            right: 0;
            background-color: #ddd;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            padding: 5px 8px;
            z-index: 9;
            svg {
               width: 15px;
            }
         }
         .swiper-slide {
            .image_slider__image {
               width: 100%;
               height: 100%;
            }
            img {
               display: block !important;
               opacity: 1 !important;
               margin: 0 auto;
            }
         }
         .swiper-container:not(.swiper-container-initialized) > .swiper-wrapper,
         .swiper:not(.swiper-initialized) > .swiper-wrapper {
            overflow: visible;
         }
         .swiper {
            &-button-next::after,
            &-button-prev::after {
               display: flex;
               align-items: center;
               justify-content: center;
               font-weight: 800;
               opacity: 0.75;
               border-radius: 50%;
               font-size: 20px;
            }
         }
      }
      span.onsale {
         z-index: 999;
         min-height: 3.236em;
         min-width: 3.236em;
         padding: 0.202em;
         font-size: 1em;
         font-weight: 700;
         position: absolute;
         text-align: center;
         line-height: 3.236;
         top: -0.5em;
         left: -0.5em;
         margin: 0;
         border-radius: 100%;
         background-color: #777335;
         color: #fff;
         font-size: 0.857em;
      }
   }
}

.container_width_full {
   width: 100%;
}

.container_width {
   width: 0%;
}

@media all and (max-width: 767px) {
   .eael-single-product-images {
      .product_image_slider {
         &__thumbs {
            margin: 0 auto;
            .swiper-button-prev,
            .swiper-button-next {
               height: 20px;
               width: 20px;
            }
            .swiper-slide {
               width: 60px;
            }
         }
         &__container {
            .swiper-button-prev,
            .swiper-button-next {
               height: 20px;
               width: 20px;
               &::after {
                  font-size: 12px !important;
               }
            }
         }
      }
   }
}
