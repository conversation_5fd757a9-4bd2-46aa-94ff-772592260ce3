.eael-filter-gallery-control {
	width: 100%;
}

.eael-filter-gallery-control ul,
.eael-filter-gallery-control ul li {
	text-align: center;
}

.eael-filter-gallery-control ul {
	margin: 0px 0px 20px 0px;
	padding: 0px;
	text-align: center;
}

.eael-filter-gallery-control {
	display: flex;
	flex-flow: 1 1 auto;
	align-items: center;
	justify-content: center;
	flex-flow: row wrap;
	padding: 0px;
	margin: 0px;
}

.eael-filter-gallery-control ul li {
	list-style: none;
	font-size: 24px;
	display: inline-block;
	text-align: center;

	&.control{
		&:focus-visible{
			outline: 1px solid #a9d204 !important;
		}
	}
}

.eael-filter-gallery-control ul li a.control {
	font-family: 'Montserrat', sans-serif;
	font-size: 16px;
	font-weight: 600;
	padding: 10px 25px;
	margin: 10px 6px;
}

.eael-filter-gallery-container {
	text-align: justify;
	font-size: 0.1px;
	overflow: hidden;

	#eael-fg-no-items-found{
		font-size: 14px;
	}
}

.elementor-editor-active{
	.eael-filter-gallery-container{
		overflow: initial;
	}
}

.eael-filter-gallery-container:after {
	content: '';
	display: inline-block;
	width: 100%;
}

.eael-filter-gallery-container:not(.eael-cards) .item:before {
	content: '';
	display: inline-block;
	padding-top: 56.25%;
}

.eael-filter-gallery-container .item .caption {
	position: absolute;
	display: flex;
	flex-flow: 1 1 100%;
	align-items: center;
	justify-content: center;
	z-index: 1;
	top: 0px;
	left: 0px;
	right: 0px;
	bottom: 0px;
	transition: transform .4s;
}

.eael-filter-gallery-container.eael-cards .item .caption {
	display: none;
}

.eael-filter-gallery-container.eael-cards .item .item-img .caption {
	position: absolute;
	display: flex;
	flex-flow: 1 1 100%;
	align-items: center;
	justify-content: center;
	z-index: 10;
	top: 0px;
	left: 0px;
	right: 0px;
	bottom: 0px;
	transition: transform .4s;
}

.gallery-item-caption-over {
	width: 100%;
	height: auto;
}

body.rtl .gallery-item-caption-over {
	text-align: right;
}

.eael-gallery-load-more, .eael-gallery-load-more:hover {
	text-decoration: none;
	border: none;
}

/*--- Caption Animation ---*/
.eael-filter-gallery-container .item .caption.eael-zoom-in,
.eael-filter-gallery-container.eael-cards .item .item-img .caption.eael-zoom-in {
	transform: scale(0);
}

.eael-filter-gallery-container .item:hover .caption.eael-zoom-in,
.eael-filter-gallery-container.eael-cards .item:hover .item-img .caption.eael-zoom-in {
	transform: scale(1);
}

.eael-filter-gallery-container .item .caption.eael-slide-left,
.eael-filter-gallery-container.eael-cards .item .item-img .caption.eael-slide-left {
	transform: translateX(-100%);
}

.eael-filter-gallery-container .item:hover .caption.eael-slide-left,
.eael-filter-gallery-container.eael-cards .item:hover .item-img .caption.eael-slide-left {
	transform: translateX(0%);
}

.eael-filter-gallery-container .item .caption.eael-slide-right,
.eael-filter-gallery-container.eael-cards .item .item-img .caption.eael-slide-right {
	transform: translateX(100%);
}

.eael-filter-gallery-container .item:hover .caption.eael-slide-right,
.eael-filter-gallery-container.eael-cards .item:hover .item-img .caption.eael-slide-right {
	transform: translateX(0%);
}

.eael-filter-gallery-container .item .caption.eael-slide-top,
.eael-filter-gallery-container.eael-cards .item .item-img .caption.eael-slide-top {
	transform: translateY(-100%);
}

.eael-filter-gallery-container .item:hover .caption.eael-slide-top,
.eael-filter-gallery-container.eael-cards .item:hover .item-img .caption.eael-slide-top {
	transform: translateY(0%);
}

.eael-filter-gallery-container .item .caption.eael-slide-bottom,
.eael-filter-gallery-container.eael-cards .item .item-img .caption.eael-slide-bottom {
	transform: translateY(100%);
}

.eael-filter-gallery-container .item:hover .caption.eael-slide-bottom,
.eael-filter-gallery-container.eael-cards .item:hover .item-img .caption.eael-slide-bottom {
	transform: translateY(0%);
}

.eael-filter-gallery-container .item .caption a {
	display: inline-block;
	width: 50px;
	height: 50px;
	text-align: center;
	line-height: 50px;
	border-radius: 50%;
	margin: 0 5px;
	font-size: 20px;
	cursor: pointer;
	transition: .3s;
}

.eael-filter-gallery-container .item .caption a:nth-child(1),
.eael-container .item .caption a:nth-child(2) {
	transition: .6s;
	opacity: 0;
}

.eael-filter-gallery-container .item .caption a:nth-child(1) {
	transform: translateY(-100%);
}

.eael-filter-gallery-container .item .caption a:nth-child(2) {
	transform: translateY(100%);
}

.eael-filter-gallery-container .item .caption:hover a:nth-child(1),
.eael-filter-gallery-container .item .caption:hover a:nth-child(2) {
	transform: translateY(0%);
	opacity: 1;
}

.eael-filter-gallery-container .item .caption .eael-popup-link {
	outline: 0;
}


/*--- Filter Gallery Card Style ---*/

.eael-filter-gallery-container.eael-cards .item:before {
	padding-top: 0px;
}

.eael-filter-gallery-container.eael-cards .item-img {
	position: relative;
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
	height: 220px;
	z-index: 0;
	overflow: hidden;
}

.eael-filter-gallery-container.eael-cards .item-content {
	padding: 15px;
}

.eael-filter-gallery-container.eael-cards .item-content .title,
.eael-filter-gallery-container.eael-cards .item-content .title a {
	font-size: 20px;
	line-height: 1;
	margin-bottom: 0px;
	transition: .3s;
}

.eael-filter-gallery-container.eael-cards .item-content p {
	font-size: 14px;
	line-height: 26px;
}

.eael-fg-card-content-align-center .eael-filterable-gallery-item-wrap .gallery-item-caption-wrap .gallery-item-caption-over {
	text-align: center;
}

.eael-fg-card-content-align-right .eael-filterable-gallery-item-wrap .gallery-item-caption-wrap .gallery-item-caption-over {
	text-align: right;
}

.eael-fg-hoverer-content-align-center .eael-filterable-gallery-item-wrap .gallery-item-caption-wrap .gallery-item-caption-over {
	text-align: center;
}

.eael-fg-hoverer-content-align-right .eael-filterable-gallery-item-wrap .gallery-item-caption-wrap .gallery-item-caption-over {
	text-align: right;
}

body.rtl {
	.eael-fg-card-content-align-right .eael-filterable-gallery-item-wrap .gallery-item-caption-wrap .gallery-item-caption-over,
	.eael-fg-hoverer-content-align-right .eael-filterable-gallery-item-wrap .gallery-item-caption-wrap .gallery-item-caption-over {
		text-align: left;
	}
}


/*--- Editor Specific Style ---*/

.eael-fg-content-align-left .eael-cards .item .item-content {
	text-align: left;
}

.eael-fg-content-align-center .eael-cards .item .item-content {
	text-align: center;
}

.eael-fg-content-align-right .eael-cards .item .item-content {
	text-align: right;
}


/*--- Magnific Gallery Fix ---*/

.mfp-wrap~div.dialog-widget {
	display: none!important;
}

.eael-filterable-gallery-item-wrap {
	float: left;
	width: 33.33%;
}

.eael-filter-gallery-container.eael-col-1 .eael-filterable-gallery-item-wrap {
	width: 100%;
}

.eael-filter-gallery-control>ul li {
	font-size: 13px;
	line-height: 1.8;
	text-transform: uppercase;
	letter-spacing: 1px;
	padding: 6px 15px;
	cursor: pointer;
	margin: 0 5px;
	font-weight: normal;
}

.eael-filter-gallery-container.eael-col-2 .eael-filterable-gallery-item-wrap {
	width: 50%;
}

.eael-filter-gallery-container.eael-col-4 .eael-filterable-gallery-item-wrap {
	width: 25%;
}

.eael-filter-gallery-container.eael-col-5 .eael-filterable-gallery-item-wrap {
	width: 20%;
}

.eael-gallery-grid-item {
	margin-left: calc(20px/2);
	margin-right: calc(20px/2);
	margin-bottom: 20px;
	position: relative;
	overflow: hidden;
}

.gallery-item-caption-wrap,
.media-content-wrap {
	font-size: 14px;
	text-align: left;
}

.gallery-item-thumbnail-wrap.caption-style-card img {
	max-height: 100%;
	width: 100%;
}

.gallery-item-caption-wrap.caption-style-hoverer {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	padding: 15px;
	z-index: 10;
}

.eael-filterable-gallery-item-wrap .eael-gallery-grid-item .gallery-item-thumbnail-wrap>img {
	height: 100%;
	width: 100%;
	object-fit: cover;
}


/*--- Gallery content css ---*/

.fg-item-title,
.fg-item-content {
	color: #ffffff;
	font-family: inherit;
}


/*--- Gallery Card Style CSS ---*/
.gallery-item-caption-wrap.caption-style-card .fg-item-title {
	font-size: 18px;
	margin: 15px 0 15px;
	line-height: 1;
}

.gallery-item-caption-wrap.caption-style-card {
	background: #f7f7f7;
}

.gallery-item-caption-wrap.caption-style-card .fg-item-title {
	margin: 10px 0 15px;
	color: #000000;
}

.gallery-item-caption-wrap.caption-style-card .fg-item-content {
	color: #212529;
	line-height: 1.5;
	font-weight: normal;
}

.gallery-item-buttons>a {
	display: inline-block;
	font-size: 16px;
	color: #000;
	margin-right: 15px;
}

.gallery-item-buttons>a:visited {
	color: #000;
}

.gallery-item-caption-wrap.caption-style-card {
	padding: 10px;
	box-sizing: border-box;
	font-family: inherit;
}


/*--- Gallery item hover style ---*/
.gallery-item-caption-wrap .gallery-item-hoverer-bg {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	content: '';
	background: #000000;
	z-index: -1;
	opacity: .7;
}

.gallery-item-caption-wrap.caption-style-hoverer {
	visibility: hidden;
	opacity: 0;
	transition: 0.6s all ease;
	display: flex;
	align-items: center;
}

.eael-gallery-grid-item:hover .gallery-item-caption-wrap.caption-style-hoverer {
	opacity: 1;
	visibility: visible;
}

.gallery-item-caption-wrap.caption-style-hoverer h5 {
	font-size: 20px;
	margin-bottom: 5px;
	line-height: 1;
}

.gallery-item-caption-wrap.caption-style-hoverer.eael-slide-up h5 {
	transform: translateY(20px);
	transition: 0.6s all ease;
}

.gallery-item-caption-wrap.caption-style-hoverer p {
	margin-bottom: 5px;
}

.gallery-item-caption-wrap.caption-style-hoverer.eael-slide-up p {
	transform: translateY(40px);
	transition: 0.6s all ease;
}

.gallery-item-caption-wrap.caption-style-hoverer.eael-slide-up .gallery-item-buttons {
	transform: translateY(40px);
	transition: 0.6s all ease;
}

.gallery-item-caption-wrap .gallery-item-buttons > a span {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	outline: none;
	text-decoration: none;
	display: inline-block;
	text-align: center;
	
}

.gallery-item-caption-wrap.caption-style-hoverer.eael-zoom-in {
	transform: scale(.8);
	transition: 0.6s all ease;
}

.eael-gallery-grid-item:hover .gallery-item-caption-wrap.caption-style-hoverer.eael-slide-up h5,
.eael-gallery-grid-item:hover .gallery-item-caption-wrap.caption-style-hoverer.eael-slide-up .gallery-item-buttons,
.eael-gallery-grid-item:hover .gallery-item-caption-wrap.caption-style-hoverer.eael-slide-up p {
	transform: translate(0);
}

.eael-gallery-grid-item:hover .gallery-item-caption-wrap.caption-style-hoverer.eael-zoom-in {
	transform: scale(1);
	visibility: visible;
	opacity: 1;
}

.gallery-item-caption-wrap.caption-style-hoverer.eael-none {
	transition: none;
}

/*--- Video Gallery ---*/
.mfp-bottom-bar {
	margin-top: 0;
}

figcaption .mfp-bottom-bar {
	margin-top: -36px;
}

.gallery-item-thumbnail-wrap {
	position: relative;
}

.gallery-item-thumbnail-wrap.video_gallery_switch_on .gallery-item-thumbnail {
	height: 100%;
	width: 100%;
	object-fit: cover;
}

.video-popup,
.video-popup-bg {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
}

.video-popup>img {
	width: 62px;
	position: relative;
	z-index: 1;
	transition: 300ms;
}

.video-popup {
	display: flex;
	align-items: center;
	justify-content: center;
}

.video-popup:hover>img {
	transform: scale(1.1);
}

.video-popup-bg {
	visibility: hidden;
	opacity: 0;
	transition: 350ms;
}

.eael-gallery-grid-item:hover .video-popup-bg {
	visibility: visible;
	opacity: 1;
}

.gallery-item-thumbnail-wrap>.gallery-item-caption-wrap {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.gallery-item-caption-wrap.card-hover-bg {
	visibility: hidden;
	opacity: 0;
	transition: 800ms;
}

.gallery-item-thumbnail-wrap:hover .gallery-item-caption-wrap.card-hover-bg {
	visibility: visible;
	opacity: 1;
}

.gallery-item-caption-wrap .gallery-item-buttons>a>i {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}

/*--- Gallery Column CSS ---*/
@media only screen and (min-width: 1025px) {
	/* For Desktop: */
	.elementor-element.elementor-grid-1 {
		position: relative;
	}
	.elementor-element.elementor-grid-1 .eael-filterable-gallery-item-wrap {
		width: 100%;
		float: left;
	}
	.elementor-element.elementor-grid-2 {
		position: relative;
	}
	.elementor-element.elementor-grid-2 .eael-filterable-gallery-item-wrap {
		width: 50%;
		float: left;
	}
	.elementor-element.elementor-grid-2 .eael-filterable-gallery-item-wrap:nth-of-type(2n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-2 .eael-filterable-gallery-item-wrap:nth-of-type(2n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-3 {
		position: relative;
	}
	.elementor-element.elementor-grid-3 .eael-filterable-gallery-item-wrap {
		width: 33.3333%;
		float: left;
	}
	.elementor-element.elementor-grid-3 .eael-filterable-gallery-item-wrap:nth-of-type(3n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-3 .eael-filterable-gallery-item-wrap:nth-of-type(3n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-4 {
		position: relative;
	}
	.elementor-element.elementor-grid-4 .eael-filterable-gallery-item-wrap {
		width: 25%;
		float: left;
	}
	.elementor-element.elementor-grid-4 .eael-filterable-gallery-item-wrap:nth-of-type(4n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-4 .eael-filterable-gallery-item-wrap:nth-of-type(4n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-5 {
		position: relative;
	}
	.elementor-element.elementor-grid-5 .pp-logo-grid {
		margin-right: -5px;
	}
	.elementor-element.elementor-grid-5 .eael-filterable-gallery-item-wrap {
		width: 20%;
		float: left;
	}
	.elementor-element.elementor-grid-5 .eael-filterable-gallery-item-wrap:nth-of-type(5n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-5 .eael-filterable-gallery-item-wrap:nth-of-type(5n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-6 {
		position: relative;
	}
	.elementor-element.elementor-grid-6 .pp-logo-grid {
		margin-right: -6px;
	}
	.elementor-element.elementor-grid-6 .eael-filterable-gallery-item-wrap {
		width: 16.65%;
		float: left;
	}
	.elementor-element.elementor-grid-6 .eael-filterable-gallery-item-wrap:nth-of-type(6n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-6 .eael-filterable-gallery-item-wrap:nth-of-type(6n+1) {
		clear: left;
	}
}

@media only screen and (max-width: 1024px) and (min-width: 766px) {
	/* For tablets: */
	.elementor-element.elementor-grid-tablet-1 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-1 .eael-filterable-gallery-item-wrap {
		width: 100%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-2 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-2 .eael-filterable-gallery-item-wrap {
		width: 50%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-2 .eael-filterable-gallery-item-wrap:nth-of-type(2n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-2 .eael-filterable-gallery-item-wrap:nth-of-type(2n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-tablet-3 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-3 .eael-filterable-gallery-item-wrap {
		width: 33.3333%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-3 .eael-filterable-gallery-item-wrap:nth-of-type(3n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-3 .eael-filterable-gallery-item-wrap:nth-of-type(3n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-tablet-4 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-4 .eael-filterable-gallery-item-wrap {
		width: 25%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-4 .eael-filterable-gallery-item-wrap:nth-of-type(4n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-4 .eael-filterable-gallery-item-wrap:nth-of-type(4n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-tablet-5 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-5 .eael-filterable-gallery-item-wrap {
		width: 20%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-5 .eael-filterable-gallery-item-wrap:nth-of-type(5n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-5 .eael-filterable-gallery-item-wrap:nth-of-type(5n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-tablet-6 {
		position: relative;
	}
	.elementor-element.elementor-grid-tablet-6 .pp-logo-grid {
		margin-right: -6px;
	}
	.elementor-element.elementor-grid-tablet-6 .eael-filterable-gallery-item-wrap {
		width: 16%;
		float: left;
	}
	.elementor-element.elementor-grid-tablet-6 .eael-filterable-gallery-item-wrap:nth-of-type(6n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-tablet-6 .eael-filterable-gallery-item-wrap:nth-of-type(6n+1) {
		clear: left;
	}
}

@media only screen and (max-width: 767px) {
	.elementor-element.elementor-grid-mobile-1 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-1 .eael-filterable-gallery-item-wrap {
		width: 100%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-2 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-2 .eael-filterable-gallery-item-wrap {
		width: 50%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-2 .eael-filterable-gallery-item-wrap:nth-of-type(2n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-2 .eael-filterable-gallery-item-wrap:nth-of-type(2n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-mobile-3 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-3 .eael-filterable-gallery-item-wrap {
		width: 33.3333%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-3 .eael-filterable-gallery-item-wrap:nth-of-type(3n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-3 .eael-filterable-gallery-item-wrap:nth-of-type(3n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-mobile-4 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-4 .eael-filterable-gallery-item-wrap {
		width: 25%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-4 .eael-filterable-gallery-item-wrap:nth-of-type(4n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-4 .eael-filterable-gallery-item-wrap:nth-of-type(4n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-mobile-5 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-5 .eael-filterable-gallery-item-wrap {
		width: 20%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-5 .eael-filterable-gallery-item-wrap:nth-of-type(5n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-5 .eael-filterable-gallery-item-wrap:nth-of-type(5n+1) {
		clear: left;
	}
	.elementor-element.elementor-grid-mobile-6 {
		position: relative;
	}
	.elementor-element.elementor-grid-mobile-6 .pp-logo-grid {
		margin-right: -6px;
	}
	.elementor-element.elementor-grid-mobile-6 .eael-filterable-gallery-item-wrap {
		width: 16%;
		float: left;
	}
	.elementor-element.elementor-grid-mobile-6 .eael-filterable-gallery-item-wrap:nth-of-type(6n) {
		margin-right: 0 !important;
	}
	.elementor-element.elementor-grid-mobile-6 .eael-filterable-gallery-item-wrap:nth-of-type(6n+1) {
		clear: left;
	}
}

/* ------------- Layout 3 Style ----------------- */
.fg-layout-3-filter-controls {
	margin: 0;
	padding: 0;
	list-style: none;
}

.fg-layout-3-item-content {
	text-align: center;
}

.fg-layout-3-item-content .fg-item-title {
	font-size: 18px;
	line-height: 1;
	font-weight: 500;
	margin: 0;
	transition: 300ms;
}

.fg-layout-3-item-content .fg-item-content p {
	font-size: 13px;
	font-weight: 400;
}

.fg-filter-wrap {
	position: relative;
	flex-basis: 30%;
}

.fg-layout-3-filter-controls {
    position: absolute;
    left: 0;
    background: #fff;
    z-index: 999;
	width: 150px;
	visibility: hidden;
	opacity: 0;
	transition: 300ms;
	width: 100%;
	border-radius: 5px;
	padding-top: 7px;
}

.fg-layout-3-filter-controls.open-filters {
    visibility: visible;
    opacity: 1;
}

.fg-layout-3-filter-controls li.control {
	padding: 5px 5px 5px 10px;
	cursor: pointer;
	transition: 300ms;
	font-size: 14px;
	color: #7f8995;
	font-weight: normal;
}

.fg-layout-3-filter-controls li.control:hover {
	color: #2d425a;
}

.fg-layout-3-filter-controls li.control:first-child {
	border-top: 0px solid;
}

.fg-layout-3-filter-controls li.control:last-child {
	border-bottom: 0px solid;
}

.fg-layout-3-filters-wrap {
    max-width: 600px;
    margin: 15px auto 50px;
}

.fg-filter-wrap button {
    width: 100%;
    border: 0px solid;
    border-radius: 0px;
    padding: 17px 13px;
    font-size: 14px;
	color: #2d425a;
	background: #f7f8ff;
	height: 55px;
    border-right: 1px solid #abb5ff;
	border-radius: 10px 0px 0 10px;
	outline: none;
	text-align: center;
    position: relative;
}

.fg-filter-wrap button > i {
    font-size: 18px;
    position: absolute;
	top: 50%;
	transform: translateY(-50%);
	margin-left: 10px;
}

.fg-layout-3-search-box {
    flex-basis: 70%;
    height: 100%;
}

.fg-layout-3-search-box input[type="text"] {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 15px;
    border: 0px solid;
    outline: none;
    background: none;
}

.fg-layout-3-search-box input[type="text"]::-webkit-input-placeholder { /* Chrome/Opera/Safari */
	color: #7f8995;
	font-size: 13px;
}

.fg-layout-3-search-box input[type="text"]::-moz-placeholder { /* Firefox 19+ */
	color: #7f8995;
	font-size: 13px;
}

.fg-layout-3-search-box input[type="text"]:-ms-input-placeholder { /* IE 10+ */
	color: #7f8995;
	font-size: 13px;
}

.fg-layout-3-search-box input[type="text"]:-moz-placeholder { /* Firefox 18- */
	color: #7f8995;
	font-size: 13px;
}

.fg-layout-3-filters-wrap {
    height: 55px;
	border-radius: 5px;
    display: flex;
	flex-wrap: wrap;
	align-items: center;
}

.fg-layout-3-filters-wrap .fg-layout-3-search-box {
	background: #f7f8ff;
	border-radius: 0 10px 10px 0;
}

.fg-layout-3-item-thumb {
	position: relative;
	overflow: hidden;
}

.fg-layout-3-item-thumb .gallery-item-buttons {
	text-align: center;
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 4;
}

.fg-layout-3-item-content {
    padding: 27px 27px 30px;
    text-align: center;
}

.eael-fg-card-content-align-left .fg-layout-3-item-content { text-align: left; }
.eael-fg-card-content-align-center .fg-layout-3-item-content { text-align: center; }
.eael-fg-card-content-align-right .fg-layout-3-item-content { text-align: right; }

body.rtl {
	.eael-fg-card-content-align-left .fg-layout-3-item-content { text-align: right; }
	.eael-fg-card-content-align-right .fg-layout-3-item-content { text-align: left; }
}

.eael-filterable-gallery-item-wrap .fg-layout-3-item.eael-gallery-grid-item {
	box-shadow: 0px 0px 30px 0px rgba(3, 29, 60, 0.05);
	border-radius: 5px;
}

.fg-caption-head {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
	padding: 35px 35px;
	z-index: 3;
	color: #fff;
    font-size: 18px;
    font-weight: 700;
}

.fg-item-category > span {
	font-size: 12px;
	color: #fff;
	background: #fa9196;
	padding: 10px 12px;
	display: inline-block;
}

.fg-layout-3-item-content {
    position: relative;
}

.fg-item-category {
    position: absolute;
    left: 0;
    top: 0;
    text-align: center;
    width: 100%;
    height: 30px;
    margin-top: -15px;
    z-index: 11;
}

.fg-item-category span {
    border-radius: 5px;
	line-height: 1;
	visibility: hidden;
	opacity: 0;
	transition: 300ms;
}

.fg-layout-3-item.eael-gallery-grid-item:hover .fg-item-category span {
	visibility: visible;
	opacity: 1;
}

.mfp-iframe-holder{
	&.eael-gf-vertical-video-popup{
		.mfp-content{
			max-width: 400px;
		}
		.mfp-iframe-scaler{
			padding-top: 177%;
		}
	}

    .mfp-close {
		width: auto;
	}
}

.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
	cursor: pointer;
	text-align: right !important;
}

.mfp-zoom-out-cur .mfp-image-holder .mfp-close:hover {
	text-decoration: none;
}

button.mfp-close:not(.toggle),
button.mfp-arrow:not(.toggle) {
	background: transparent;
}

.eael-privacy-message:empty {
	display: none;
}

.eael-privacy-message {
	position:absolute;
	top: -7%;
    background-color: #f8d7da;
    padding: 15px;
    border: 1px solid #f1aeb5;
    border-radius: 5px;
    color: #58151c;
	width: calc( 100% - 30px );
}

body.rtl .mfp-counter {
	direction: ltr;
}

//Salient theme support
body.theme-salient.material button.mfp-arrow,
body.material button.mfp-arrow,
body.theme-salient.material button.mfp-close,
body.material button.mfp-close {
    // background: transparent;
    border: 0;
}

body.theme-salient.material .mfp-arrow,
body.material .mfp-arrow,
body.theme-salient.material .mfp-arrow:hover,
body.material .mfp-arrow:hover {
    background: transparent;
}

body.theme-salient.material .mfp-arrow-left,
body.material .mfp-arrow-left {
	left: 0;
	border: none;
}

// body.material .mfp-arrow-left {
// 	border: none;
// }

body.theme-salient.material .mfp-arrow-right,
body.material .mfp-arrow-right {
	transition: none;
}

body.theme-salient.material .mfp-arrow,
body.material .mfp-arrow {
    margin: -55px 0 0;
    width: 90px;
    height: 110px;
    -webkit-tap-highlight-color: transparent;
}

body.theme-salient.material .mfp-arrow-left:after,
body.material .mfp-arrow-left:after,
body.theme-salient.material .mfp-arrow-right:after,
body.material .mfp-arrow-right:after {
	transform: none;
	transition: none;
	background-color: transparent;
}

body.theme-salient.material .mfp-arrow-left:before,
body.material .mfp-arrow-left:before,
body.theme-salient.material .mfp-arrow-right:before,
body.material .mfp-arrow-right:before {
	transform: none;
}