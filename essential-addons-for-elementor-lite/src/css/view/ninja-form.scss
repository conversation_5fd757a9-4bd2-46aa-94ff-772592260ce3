.eael-ninja-container {
	input,textarea {
		height: auto;
		padding: 10px;
	}
}
.eael-contact-form-align-center,
.eael-contact-form-btn-align-center{
	.eael-ninja-container,
	.eael-ninja-container .nf-field .nf-field-element input[type="button"]{
		margin-left: auto !important;
		margin-right: auto !important;
		display: block;
		float: none;
	}
}

.eael-contact-form-align-left,
.eael-contact-form-btn-align-left{
	.eael-ninja-container,
	.eael-ninja-container .nf-field .nf-field-element input[type="button"]{
		float: left;
		width: auto;
	}
}

.eael-contact-form-align-right,
.eael-contact-form-btn-align-right{
	.eael-ninja-container,
	.eael-ninja-container .nf-field .nf-field-element input[type="button"] {
		float: right;
		width: auto;
	}
}
.eael-ninja-container{
	ul.wpuf-form li .wpuf-fields input[type="text"],
	.nf-field .nf-field-element input[type="password"],
	ul.wpuf-form li .wpuf-fields input[type="email"],
	.nf-field .nf-field-element input[type="url"],
	ul.wpuf-form li .wpuf-fields input[type="number"],
	.nf-field .nf-field-element textarea {
		max-width: 100%;
	}
}
.eael-ninja-form{
	.nf-form-title,
	.title-description-hide .nf-form-title,
	&.title-description-hide .nf-form-title,
	.nf-field-labe{
		display: none;
	}
	.submit-container input[type="button"] {
		border: 0;
		border-radius: 0;
	}
}

.eael-ninja-form-title-yes .nf-form-title,
.eael-ninja-form-labels-yes .nf-field-label {
	display: block;
}

.eael-ninja-form-button-full-width .submit-container input[type="button"],
.eael-ninja-form-button-full-width .submit-container input[type="submit"]{
	width: 100%;
}