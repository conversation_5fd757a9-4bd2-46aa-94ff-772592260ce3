.eael-product-gallery {
   display: flex;

   &.eael-terms-layout-horizontal {
      flex-direction: column;

      .eael-cat-tab {
         margin: 0 0 50px 0;

         li:first-child a {
            margin-left: 0;
         }
         li:last-child a {
            margin-right: 0;
         }

         img {
            display: block;
            margin: 0 auto 5px auto;
         }
      }
   }

   &.eael-terms-layout-vertical {
      flex-direction: row;
      align-items: flex-start;
      gap: 2%;

      @media (max-width: 767px) {
         flex-direction: column !important;
      }

      .eael-cat-tab {
         width: 25%;

         @media (max-width: 767px) {
            width: 100%;
         }

         li {
            @media (min-width: 768px) {
               display: block;
            }
         }

         li:first-child a {
            margin-top: 0;
         }
         li:last-child a {
            margin-bottom: 0;
         }

         a {
            padding: 20px 30px;
         }

         img {
            margin: 0 5px 0 0;
         }
      }

      .woocommerce {
         width: 75%;
         @media (max-width: 767px) {
            width: 100%;
         }
      }
   }

   .eael-cat-tab {
      list-style: none;
      margin: 0;
      padding: 0;
      border-radius: 5px;

      li {
         display: inline-block;
      }

      img {
         width: 35px;
      }

      a {
         padding: 20px 30px;
         display: block;
         background-color: #f8f6ff;
         color: #7d7a94;
         font-size: 20px;
         line-height: 1.2em;
         font-weight: 600;
         border-radius: 5px;
         margin: 3px;

         &:hover,
         &.active {
            color: #fff;
            background-color: #8941ff;
         }
      }
   }

   // end terms styles

   .woocommerce {
      ul.products {
         display: grid;
         grid-gap: 25px;
         margin: 0 0 15px 0 !important;
         padding: 0 !important;

         &:before,
         &:after {
            display: none;
         }

         .product {
            width: 100%;
            margin: 0;
            padding: 0;
            a.add_to_cart_button,
            span.price,
            h2.woocommerce-loop-product__title,
            .eael-wc-compare {
               //margin-left: 10px !important;
               //margin-right: 10px !important;
            }

            .eael-product-sold-count-progress-bar-wrapper {
               background-color: #f1f1f1;
               border-radius: 10px;
               overflow: hidden;
            }
            .eael-product-sold-count-progress-bar {
               background-color: #2196f3;
               height: 10px;
               border-radius: 10px;
            }

            .star-rating {
               margin: 0 auto 5px;
               display: inline-block;
               float: none;
               height: 1em;
               width: 5.6em;
               font-size: 1em;
               line-height: 1em;

               &:before {
                  content: "\f005\f005\f005\f005\f005";
                  font-family: "Font Awesome 5 Free";
                  font-weight: 400;
                  opacity: 1;
               }

               span {
                  display: inline-block;

                  &:before {
                     content: "\f005 \f005 \f005 \f005 \f005";
                     font-family: "Font Awesome 5 Free";
                     font-weight: 900;
                  }
               }
            }
            .ast-on-card-button.ast-onsale-card {
               display: none !important;
            }
         }

         li.product {
            width: 100%;
         }

         &.products[class*="columns-"] li.product {
            width: 100%;
         }
      }
   }

   // simple & reveal style
   &.eael-product-preset-4 {
      .woocommerce {
         ul.products {
            li.product {
               position: relative;
               float: left;
               display: block;
               overflow: hidden;
               text-align: center;
               padding: 0;
               border-radius: 0;
               background-color: #fff;
               box-shadow: none;
               border: 1px solid #eee;

               a {
                  text-decoration: none;

                  &:hover {
                     outline: none;
                     box-shadow: none;
                  }
               }

               img {
                  width: 100%;
                  height: auto;
                  margin: 0;
                  max-width: 100%;
                  backface-visibility: hidden;
               }

               // product title
               .woocommerce-loop-product__title {
                  font-size: 16px;
                  font-weight: 700;
                  line-height: 1;
                  color: #333;
                  margin: 25px 0 12px;
                  padding: 0;
               }

               // onsale
               .onsale {
                  display: block;
                  line-height: 170px;
                  font-size: 13px;
                  text-align: center;
                  letter-spacing: 0;
                  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.6);
                  text-transform: uppercase;
                  color: #fff;
                  background-color: #ff2a13;
                  border-radius: 0;
                  border: none;
                  box-shadow: none;
                  position: absolute;
                  height: 100px;
                  width: 200px;
                  z-index: 1;
                  left: -100px;
                  top: -50px;
                  right: auto;
                  margin: 0;
                  padding: 0;
                  transform: rotate(-45deg);
               }

               // out of stock
               .outofstock-badge {
                  line-height: 16px;
                  font-size: 13px;
                  font-weight: 600;
                  display: flex;
                  justify-content: center;
                  align-items: flex-end;
                  letter-spacing: 0;
                  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.6);
                  text-transform: uppercase;
                  color: #fff;
                  background-color: #ff2a13;
                  border-radius: 0;
                  border: none;
                  box-shadow: none;
                  position: absolute;
                  height: 100px;
                  width: 200px;
                  z-index: 1;
                  left: -95px;
                  top: -45px;
                  right: auto;
                  margin: 0;
                  padding: 0;
                  transform: rotate(-45deg);
               }

               .price {
                  font-size: 14px;
                  margin-bottom: 0;

                  del {
                     opacity: 0.5;
                     display: inline-block;
                  }

                  ins {
                     font-weight: 400;
                     background-color: transparent;
                     color: #ff2a13;
                  }
               }

               // star rating
               .star-rating {
                  display: block;
                  float: none;
                  font-size: 14px;
                  margin: 10px auto;
               }

               // add to cart button
               .button,
               .button.add_to_cart_button {
                  display: block;
                  font-size: 14px;
                  font-weight: 400;
                  line-height: 38px;
                  text-align: center;
                  text-transform: uppercase;
                  color: #fff;
                  background-color: #333;
                  padding: 0;
                  margin: 15px;
                  border-radius: 0;

                  &::before {
                     content: "\f07a";
                     font-family: "Font Awesome 5 Free";
                     font-weight: 900;
                     padding-right: 8px;
                  }
                  &.product_type_variable {
                     &:before {
                        content: "\f00c";
                     }
                  }
                  &:focus {
                     outline: none;
                  }
               }

               .button::before {
                  content: none;
               }

               .eael-wc-compare {
                  color: #fff;
                  background-color: #333;
               }

               a.added_to_cart {
                  display: block;
                  margin: 15px;
                  padding: 0;
                  font-size: 14px;
                  line-height: 38px;
                  text-transform: capitalize;
                  color: #333;
                  background-color: transparent;
               }
            }
         }
      }
   }

   &.eael-product-preset-4 {
      .woocommerce ul.products .product {
         a.add_to_cart_button,
         span.price,
         h2.woocommerce-loop-product__title {
            //margin-left: 10px !important;
            //margin-right: 10px !important;
         }
      }
   }

   &.eael-product-preset-6,
   &.eael-product-preset-2,
   &.eael-product-preset-1 {
      ul.products {
         li.product {
            .image-wrap {
               img {
                  backface-visibility: hidden;
               }
            }
         }
      }
   }

   &.masonry {
      .woocommerce {
         ul.products {
            display: block;

            @media (min-width: 766px) {
               margin: 0 -1% !important;
            }

            &:before,
            &:after {
               display: table;
               content: " ";
            }

            li.product {
               float: left;
               margin: 15px 0;

               @media (min-width: 766px) {
                  margin: 1%;
               }
            }
         }
      }
   }
}

.eael-product-gallery {
   .woocommerce ul.products {
      h2.eael-product-not-found {
         grid-column: 1 / -1;
         text-align: center;
         font-size: 20px;
      }
   }

   .woocommerce ul.products li.product {
      a img,
      img {
         margin-bottom: 0;
         display: block;
         width: 100%;
         backface-visibility: hidden;
      }

      .woocommerce-loop-product__title {
         letter-spacing: normal;
         font-weight: 700;
         text-transform: capitalize;
      }

      ins {
         background: transparent;
      }

      .button {
         text-transform: capitalize;
         border: none;
         letter-spacing: normal;
         display: flex;

         &:hover,
         &:visited {
            text-decoration: none;
         }
      }

      .star-rating {
         margin: 0 auto 5px;
         display: inline-block;
         float: none;
         height: 1em;
         width: 5.6em;
         font-size: 1em;
         line-height: 1em;

         &:before {
            content: "\f005\f005\f005\f005\f005";
            font-family: "Font Awesome 5 Free";
            font-weight: 400;
            opacity: 1;
         }

         span {
            display: inline-block;

            &:before {
               content: "\f005 \f005 \f005 \f005 \f005";
               font-family: "Font Awesome 5 Free";
               font-weight: 900;
            }
         }
      }
   }
}
.eael-product-gallery {
   .woocommerce ul.products .product {
      overflow-y: auto;
   }

   .eael-load-more-button-wrap {
      clear: both;
      margin-top: 40px;
   }

   .eael-product-wrap {
      .eael-onsale {
         padding: 5px 10px;
         font-size: 12px;
         font-weight: 500;
         position: absolute;
         text-align: center;
         line-height: 1.2em;
         top: 30px;
         left: 0;
         margin: 0;
         background-color: #ff7a80;
         color: #fff;
         z-index: 9;

         &.sale-preset-1 {
            &.outofstock {
               br {
                  display: none;
               }
            }

            &.right {
               left: auto;
               right: 0;
            }
         }

         &.sale-preset-2 {
            padding: 0;
            top: 5px;
            left: 5px;
            display: inline-table;
            min-width: 50px;
            min-height: 50px;
            line-height: 50px;
            border-radius: 100%;
            -webkit-font-smoothing: antialiased;

            &.outofstock {
               line-height: 1.2em;
               display: flex;
               align-items: center;
               justify-content: center;
            }

            &.right {
               left: auto;
               right: 5px;
            }
         }

         &.sale-preset-3 {
            border-radius: 50px;
            left: 15px;
            top: 15px;

            &.outofstock {
               br {
                  display: none;
               }
            }

            &.right {
               left: auto;
               right: 15px;
            }
         }

         &.sale-preset-4 {
            left: 0;
            top: 15px;

            &.outofstock {
               br {
                  display: none;
               }
            }

            &:after {
               position: absolute;
               right: -15px;
               bottom: 0px;
               width: 15px;
               height: 24px;
               border-top: 12px solid transparent;
               border-bottom: 12px solid transparent;
               border-left: 10px solid #23a454;
               content: "";
            }

            &.right {
               left: auto;
               right: 0;

               &:after {
                  right: auto;
                  left: -15px;
                  border-left: 0;
                  border-right: 10px solid #23a454;
               }
            }
         }

         &.sale-preset-5 {
            display: block;
            line-height: 74px;
            height: 60px;
            width: 120px;
            left: -37px;
            top: -8px;
            right: auto;
            padding: 0;
            transform: rotate(-45deg);

            &.outofstock {
               line-height: normal;
               padding-top: 12px;
               display: flex;
               align-items: center;
               justify-content: center;
            }

            &.right {
               left: auto;
               right: -35px;
               transform: rotate(45deg);
            }
         }
      }

      .eael-product-title * {
         font-size: 20px;
         line-height: 1.2em;
         color: #252525;
         font-weight: 500;
         margin: 0 0 8px;
         padding: 0;

         &:before {
            content: none;
         }
      }

      .eael-product-price {
         font-size: 18px;
         line-height: 1.2em;
         color: #ff7a80;
         font-weight: 600;
         margin-bottom: 10px;
      }

      .star-rating {
         margin: 0 auto 10px;
      }

      .star-rating span:before,
      .star-rating::before,
      p.stars a:hover:after,
      p.stars a:after {
      }

      a.button.add_to_cart_button.added {
         display: none !important;
      }
   }

   .eael-product-wrap {
      &:hover {
         .icons-wrap {
            &.box-style {
               bottom: 30px;
               visibility: visible;
               opacity: 1;
            }

            &.block-box-style {
               visibility: visible;
               opacity: 1;
               transform: translateY(-50px);
            }

            &.block-style {
               visibility: visible;
               opacity: 1;
               transform: translateY(-50px);
            }
         }
      }

      .product-image-wrap {
         position: relative;
         overflow: hidden;
      }

      .icons-wrap {
         padding: 0;
         list-style: none;
         position: absolute;
         z-index: 9;
         display: block;
         top: 50%;
         left: 0;
         right: 0;
         -webkit-transform: translateY(0);
         -moz-transform: translateY(0);
         -ms-transform: translateY(0);
         -o-transform: translateY(0);
         transform: translateY(0);
         opacity: 0;
         visibility: hidden;
         -webkit-transform-origin: center center;
         -moz-transform-origin: center center;
         -ms-transform-origin: center center;
         -o-transform-origin: center center;
         transform-origin: center center;
         margin: 0 auto;
         -webkit-transition: all ease 0.4s;
         -moz-transition: all ease 0.4s;
         -ms-transition: all ease 0.4s;
         -o-transition: all ease 0.4s;
         transition: all ease 0.4s;

         &.block-style {
            background: red;
            display: flex;
            align-items: center;
            justify-content: stretch;
            width: 100%;
            top: auto;
            bottom: -50px;

            li {
               flex: 1;

               &:not(:last-child) {
                  border-right: 1px solid #fff;
               }

               &.add-to-cart {
                  flex: 4;
               }

               a {
                  position: relative;
                  background-color: transparent;
                  margin: 0;
                  padding: 10px 5px;
                  font-size: 15px;
                  line-height: 1.2em;
                  color: #fff;
                  display: flex;
                  flex-direction: column;

                  &:hover {
                     background-color: transparent;
                     color: #000;
                  }

                  i {
                     line-height: normal;
                  }
               }
            }
         }

         &.box-style {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            top: auto;
            bottom: -100px;

            li {
               a {
                  position: relative;
                  width: 42px;
                  height: 42px;
                  margin: 3px;
                  box-shadow: 0px 15px 10px rgba(61, 70, 79, 0.12);
                  background-color: #ffffff;
                  display: flex;
                  justify-content: center;
                  align-items: center;

                  i {
                     line-height: 1rem;
                  }

                  &.added_to_cart {
                     font-size: 0 !important;

                     &:after {
                        content: "\f217";
                        font-weight: 900;
                        font-family: "Font Awesome 5 Free";
                        font-size: 18px;
                        text-rendering: auto;
                        -webkit-font-smoothing: antialiased;
                        vertical-align: middle;
                        margin: 0;
                        padding: 0;
                     }
                  }

                  &.button.add_to_cart_button {
                     padding: 0;
                     margin: 3px;
                     font-size: 0px !important;

                     &:before {
                        content: "\f07a";
                        display: block;
                        font-family: "Font Awesome 5 Free";
                        font-size: 18px;
                        font-weight: 900;
                        transform: translate(-50%, -50%);
                        top: 50%;
                        left: 50%;
                        position: absolute;
                     }

                     &.product_type_variable {
                        &:before {
                           content: "\f00c";
                        }
                     }
                  }
               }
            }
         }

         &.over-box-style {
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;
            visibility: visible;
            opacity: 1;
            top: auto;
            bottom: -24px;
            margin: 0 5%;

            li {
               a {
                  position: relative;
                  width: 42px;
                  height: 42px;
                  margin: 3px;
                  box-shadow: 0px 15px 10px rgba(61, 70, 79, 0.12);
                  background-color: #ffffff;
                  display: flex;
                  justify-content: center;
                  align-items: center;

                  i {
                     line-height: 1rem;
                  }

                  &.added_to_cart {
                     font-size: 0 !important;

                     &:after {
                        content: "\f217";
                        font-weight: 900;
                        font-family: "Font Awesome 5 Free";
                        font-size: 18px;
                        line-height: 38px;
                        text-rendering: auto;
                        -webkit-font-smoothing: antialiased;
                        vertical-align: middle;
                        margin: 3px;
                        padding: 0;
                     }
                  }

                  &.button.add_to_cart_button {
                     padding: 0;
                     margin: 3px;
                     font-size: 0px !important;

                     &:before {
                        content: "\f07a";
                        display: block;
                        font-family: "Font Awesome 5 Free";
                        font-size: 18px;
                        font-weight: 900;
                        transform: translate(-50%, -50%);
                        top: 50%;
                        left: 50%;
                        position: absolute;
                     }

                     &.product_type_variable {
                        &:before {
                           content: "\f00c";
                        }
                     }
                  }

                  &.product_type_external {
                     width: auto;
                     height: auto;
                  }
               }
            }
         }

         &.block-box-style {
            background: white;
            width: 100%;
            top: auto;
            bottom: -50px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;

            li {
               a {
                  position: relative;
                  width: 42px;
                  height: 42px;
                  margin: 10px 2px 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;

                  i {
                     line-height: 1rem;
                  }

                  &.added_to_cart {
                     font-size: 0 !important;

                     &:after {
                        content: "\f217";
                        font-weight: 900;
                        font-family: "Font Awesome 5 Free";
                        font-size: 18px;
                        text-rendering: auto;
                        -webkit-font-smoothing: antialiased;
                        vertical-align: middle;
                        margin: 0;
                        padding: 0;
                     }
                  }

                  &.button.add_to_cart_button {
                     padding: 0;
                     margin: 10px 2px 0;
                     font-size: 0px !important;

                     &:before {
                        content: "\f07a";
                        display: block;
                        font-family: "Font Awesome 5 Free";
                        font-size: 18px;
                        font-weight: 900;
                        transform: translate(-50%, -50%);
                        top: 50%;
                        left: 50%;
                        position: absolute;
                     }

                     &.product_type_variable {
                        &:before {
                           content: "\f00c";
                        }
                     }
                  }

                  i {
                     //line-height: normal;
                  }
               }
            }
         }

         li {
            display: inline-block;
            margin: 0;
            padding: 0;

            a {
               display: block;
               position: absolute;
               color: #000;
               width: 100%;
               height: 100%;
               text-align: center;
               transition: all ease 0.4s;
               cursor: pointer;

               &:hover {
                  background: #ff7a80;
                  color: #fff;
               }

               i {
                  position: relative;
                  font-size: 18px;
                  line-height: 42px;
               }

               svg {
                  width: 18px;
               }
            }
         }
      }

      .product-details-wrap {
         padding: 10px;
      }
   }

   &.eael-product-preset-1 {
      .product-image-wrap {
         overflow: inherit;
      }

      .product-details-wrap {
         & > div:first-child {
            margin-top: 20px;
         }
      }
   }

   &.eael-product-preset-6,
   &.eael-product-preset-2,
   &.eael-product-preset-1 {
      ul.products {
         padding: 0;
         margin: 0;
         list-style: none;

         li.product {
            text-align: center;
            border: 1px solid black;
            overflow: hidden;

            &.first {
               clear: none;
            }
         }
      }
   }

   &.eael-product-preset-3,
   &.eael-product-preset-2,
   &.eael-product-preset-1 {
      .product.outofstock {
         .icons-wrap .button {
            display: none;
         }
      }

      .icons-wrap {
         .button.product_type_grouped,
         .button.product_type_variable {
            display: none !important;
         }
      }
   }
}

// hide-load-more
.eael-load-more-button.hide-load-more {
   display: none !important;
}

//----Responsive -------
@media only screen and (min-width: 1025px) {
   .eael-product-gallery-column-1
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: 100%;
   }

   .eael-product-gallery-column-2
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(2, 1fr);
   }

   .eael-product-gallery-column-3
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(3, 1fr);
   }

   .eael-product-gallery-column-4
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(4, 1fr);
   }

   .eael-product-gallery-column-5
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(5, 1fr);
   }

   .eael-product-gallery-column-6
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(6, 1fr);
   }

   // Masonry
   .eael-product-gallery-column-1
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 100%;
      margin: 15px 0;
   }
   .eael-product-gallery-column-2
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 48%;
   }
   .eael-product-gallery-column-3
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 31.3333%;
   }
   .eael-product-gallery-column-4
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 23%;
   }
   .eael-product-gallery-column-5
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 18%;
   }
   .eael-product-gallery-column-6
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 14.66666667%;
   }
}

@media only screen and (max-width: 1024px) and (min-width: 766px) {
   .eael-product-gallery-column-tablet-1
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: 100%;
   }

   .eael-product-gallery-column-tablet-2
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(2, 1fr);
   }

   .eael-product-gallery-column-tablet-3
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(3, 1fr);
   }

   .eael-product-gallery-column-tablet-4
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(4, 1fr);
   }

   .eael-product-gallery-column-tablet-5
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(5, 1fr);
   }

   .eael-product-gallery-column-tablet-6
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(6, 1fr);
   }

   // Masonry
   .eael-product-gallery-column-tablet-1
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 100%;
      margin: 15px 0;
   }
   .eael-product-gallery-column-tablet-2
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 48%;
   }
   .eael-product-gallery-column-tablet-3
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 31.3333%;
   }
   .eael-product-gallery-column-tablet-4
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 23%;
   }
   .eael-product-gallery-column-tablet-5
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 18%;
   }
   .eael-product-gallery-column-tablet-6
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 14.66666667%;
   }
}

@media only screen and (max-width: 767px) {
   .eael-product-gallery-column-mobile-1
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: 100%;
   }

   .eael-product-gallery-column-mobile-2
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(2, 1fr);
   }

   .eael-product-gallery-column-mobile-3
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(3, 1fr);
   }

   .eael-product-gallery-column-mobile-4
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(4, 1fr);
   }

   .eael-product-gallery-column-mobile-5
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(5, 1fr);
   }

   .eael-product-gallery-column-mobile-6
      .eael-product-gallery
      .woocommerce
      ul.products {
      grid-template-columns: repeat(6, 1fr);
   }

   // Masonry
   .eael-product-gallery-column-mobile-1
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 100%;
      margin: 15px 0;
   }
   .eael-product-gallery-column-mobile-2
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 48% !important;
      margin: 1%;
   }
   .eael-product-gallery-column-mobile-3
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 31.3333% !important;
      margin: 1%;
   }
   .eael-product-gallery-column-mobile-4
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 23% !important;
      margin: 1%;
   }
   .eael-product-gallery-column-mobile-5
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 18% !important;
      margin: 1%;
   }
   .eael-product-gallery-column-mobile-6
      .eael-product-gallery.masonry
      .woocommerce
      ul.products
      li.product {
      width: 14.66666667% !important;
      margin: 1%;
   }
}

@-webkit-keyframes eaelPloaderSpin {
   0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
   }
   100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
   }
}
@keyframes eaelPloaderSpin {
   0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
   }
   100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
   }
}

.eael-product-loader {
   opacity: 0.6 !important;
   pointer-events: none !important;
}

// Flexia theme conflict with pricing section position

.theme-flexia
   .woocommerce
   ul.products
   li.product
   .woocommerce-LoopProduct-link {
   position: unset;
   display: unset;
}

// Astra Default stockout issue resolve
.woocommerce ul.products li.product .ast-shop-product-out-of-stock,
.woocommerce-page ul.products li.product .ast-shop-product-out-of-stock {
   display: none;
}
.theme-astra {
   .eael-product-gallery.eael-product-preset-4
      .woocommerce
      ul.products
      li.product
      a.added_to_cart {
      line-height: 38px;
      width: 90%;
      margin: 12px auto;
   }
}

// blocksy theme conflicts
.theme-blocksy {
   .button:before {
      -ms-filter: "progid:DXImageTransform.Microsoft.gradient(enabled=false)"
         /* IE 8+ */;
      filter: none !important; /* IE 7 and the rest of the world */
      opacity: 1;
      z-index: 0;
      bottom: 0 !important;
      right: 0;
      line-height: 1.2em;
   }

   .button:hover {
      transform: none;
   }
}

//savoy theme conflicts
.theme-savoy {
   .eael-product-gallery {
      &.eael-product-preset-4 {
         .woocommerce ul.products li.product {
            .star-rating {
               width: 85px;

               &:before {
                  line-height: 13px;
               }
               span {
                  &:before {
                     line-height: 11px;
                  }
               }
            }
         }
      }

      .woocommerce ul.products li.product {
         .star-rating {
            font-size: 12px;
            letter-spacing: 2px;

            &:before {
               font-size: 12px;
               letter-spacing: 2px;
               line-height: 18px;
            }

            span {
               font-size: 12px;
               letter-spacing: 2px;

               &:before {
                  font-size: 12px;
                  letter-spacing: 2px;
                  left: 9px;
                  line-height: 18px;
               }
            }
         }
      }
   }
}

//Buddyboss theme conflicts
.theme-buddyboss-theme {
   #content .elementor-widget-eael-woo-product-gallery {
      .eael-product-preset-1,
      .eael-product-preset-2,
      .eael-product-preset-3 {
         &.eael-product-gallery .woocommerce li.product {
            .button {
               width: 42px;
               border-radius: 4px;
            }
            a.added_to_cart {
               width: 42px;
            }
         }
      }
      .eael-product-preset-2 {
         &.eael-product-gallery li.product .button {
            margin-top: 10px;
         }
      }
      .eael-product-preset-4 {
         &.eael-product-gallery .woocommerce li.product {
            .button {
               width: 92%;
               border-radius: 4px;
            }
            .star-rating {
               display: inline-block;
            }
            a.added_to_cart {
               height: 55px;
               width: 92%;
               margin: auto;
               float: initial;
               border-radius: 4px;
               line-height: 50px;
            }
         }
      }
      .eael-product-gallery {
         .eael-product-preset-1,
         .eael-product-preset-2,
         .eael-product-preset-3 {
            li.product a.added_to_cart {
               width: 42px;
               line-height: 0;
               font-size: 0;
               margin-right: 3px;
            }
         }
      }
      .eael-product-gallery .woocommerce {
         ul.products:not(.elementor-grid) {
            display: grid;
         }
         li.product {
            max-width: 100%;
            display: block;
            .star-rating {
               min-width: 110px;
            }
            a.button.add_to_cart_button {
               margin-bottom: 0;
            }
            .eael-product-price {
               ins {
                  text-decoration: none;
               }
            }
         }
      }
   }
}

//Kadence theme conflicts
.theme-kadence {
   .woocommerce {
      ul.products:not(.woo-archive-btn-button)
         li:where(:not(.woo-archive-btn-button))
         .button:not(.kb-button) {
         display: flex;
      }
   }
}
