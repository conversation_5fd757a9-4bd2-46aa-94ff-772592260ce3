table.eael-data-table thead .sorting_asc,
table.eael-data-table thead .sorting_desc,
table.eael-data-table thead .sorting {
	position: relative;
	z-index: 0;
	outline: 0;
	cursor: pointer;
}

table.eael-data-table thead .sorting_asc:after,
table.eael-data-table thead .sorting_desc:after,
table.eael-data-table thead .sorting:after {
	position: absolute;
	top: 50%;
	right: 10px;
	font-family: 'Font Awesome\ 5 Free';
	color: #fff;
	z-index: 1;
	transform: translateY(-50%);
}

.eael-data-table-wrap .eael-data-tables_wrapper {
	display: flex;
	flex-flow: row wrap;
	justify-content: flex-start;
	width: 100%;
}
.eael-data-table-wrap .eael-data-tables_filter {
	flex-grow: 1;
	flex-basis: 50%;
	text-align: right;
}
.eael-data-table-wrap .eael-data-tables_filter label {
	font-weight: 700;
}
.eael-data-table-wrap .eael-data-tables_filter label input[type="search"] {
	height: 40px;
	border: 1px solid rgba(0, 0, 0, 0.09);
	outline: 0;
	padding: 10px;
	margin-left: 10px;
}
.eael-data-table-wrap .eael-data-tables_length {
	flex-grow: 1;
	flex-basis: 50%;
}
.eael-data-table-wrap .eael-data-tables_length select {
	width: auto;
	max-width: 120px;
	height: 40px;
	border: 1px solid rgba(0, 0, 0, 0.09);
	outline: 0;
	margin-left: 10px;
	margin-right: 10px;
}
.eael-data-table-wrap .eael-data-tables_length label {
	font-weight: 700;
}
.eael-data-table-wrap .eael-data-tables_info {
	flex-grow: 1;
	flex-basis: 50%;
	font-weight: 700;
}
.eael-data-table-wrap .eael-data-tables_paginate {
	flex-grow: 1;
	flex-basis: 50%;
	text-align: right;
}
.eael-data-table-wrap .eael-data-tables_paginate .paginate_button {
	padding: 10px 15px;
	background: #f2f2f2;
	margin-right: 2px;
	cursor: pointer;
	transition: all 0.3s;
}
.eael-data-table-wrap .eael-data-tables_paginate .paginate_button:hover {
	color: #fff;
	background: #4a4893;
}
.eael-data-table-wrap .eael-data-tables_paginate .paginate_button.current {
	color: #fff;
	background: #4a4893;
}
.eael-data-table-wrap .eael-data-tables_paginate .paginate_button.disabled {
	cursor: no-drop;
	background: #f2f2f2;
	opacity: 0.5;
	color: #888;
}

.data-header-icon {
	margin-right: 10px;
	position: relative;
	top: 2px;
}

.eael-data-table {
	width: 100%;
	height: auto;
	margin: 0;
	border-collapse: separate;
	border: none;
}
.eael-data-table tr {
	border-style: none;
}
.eael-data-table thead tr {
	text-align: left;
}
.eael-data-table thead tr th {
	padding: 20px 15px;
	background: #4a4893;
	font-size: 16px;
	font-weight: 600;
	font-family: "Montserrat", "sans-serif";
	line-height: 1;
	color: #fff;
	border-color: #000;
	border-width: 2px;
	border-style: none;
	background-clip: padding-box;
}
.eael-data-table thead tr th:hover {
	border-color: #000;
}
.eael-data-table tbody tr.even {
	transition: background 0.4s ease-in-out;
}
.eael-data-table tbody tr.even:hover {
	background: rgba(242, 242, 242, 0.7);
}
.eael-data-table tbody tr.even:last-child {
	border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
.eael-data-table tbody tr.odd {
	background: rgba(242, 242, 242, 0.5);
	transition: background 0.2s ease-in-out;
}
.eael-data-table tbody tr.odd:hover {
	background: rgba(242, 242, 242, 0.7);
}
.eael-data-table tbody tr.odd:last-child {
	border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
.eael-data-table tbody tr td {
	padding: 20px 15px;
	font-size: 14px;
	font-family: "Montserrat", "sans-serif";
	line-height: 1;
	border-width: 2px;
	border-style: none;
	background-clip: padding-box;
}

.eael-data-table-th-img {
	display: inline-block;
	margin: 0 15px 0 0;
	line-height: 1;
}

.th-mobile-screen {
	display: none;
}

@media (max-width: 767px) {
	.eael-data-table-wrap {
		overflow-x: scroll !important;
		padding-bottom: 5px;
	}

	.th-mobile-screen {
		display: inline-block;
		padding: 0 0.6em;
		margin-right: 10px;
		text-align: center;
		flex-grow: 0;
		flex-shrink: 0;
		flex-basis: 100px;
	}
	.th-mobile-screen .eael-data-table-th-img {
		margin: 0 auto 15px auto;
		display: block;
	}
	.th-mobile-screen .data-header-icon {
		margin: 0 auto 15px auto;
		display: block;
	}

	.td-content-wrapper {
		display: flex;
		width: 100%;
	}

	.td-content {
		width: 100%;
		justify-content: center;
	}
	.td-content > p {
		width: 100%;
		justify-content: center;
	}

	.eael-data-table .td-content {
		align-items: center;
		display: flex;
	}
	.eael-dt-td-align-mobile-left .td-content {
		justify-content: flex-start;
	}
	.eael-dt-td-align-mobile-center .td-content {
		justify-content: center;
	}
	.eael-dt-td-align-mobile-right .td-content {
		justify-content: flex-end;
	}
}
table.eael-data-table thead .sorting:after {
	content: "";
}
table.eael-data-table thead .headerSortDown:after {
	content: "";
}
table.eael-data-table thead .headerSortUp:after {
	content: "";
}
table.eael-data-table thead .sorting_disabled.sorting:after {
	display: none;
}
table.eael-data-table .sorting.sorting-none:after {
	display: none;
}
table.eael-data-table .sorting_desc.sorting-none:after {
	display: none;
}
table.eael-data-table .sorting_asc.sorting-none:after {
	display: none;
}

.eael-table-align-left table.eael-data-table {
	float: left;
}

.eael-table-align-center table.eael-data-table {
	margin: 0 auto;
}

.eael-table-align-right table.eael-data-table {
	float: right;
}

.eael-hide-elements .eael-data-tables_info {
	display: none;
}

.eael-dt-th-align-left .eael-data-table thead tr th {
	text-align: left;
}

.eael-dt-th-align-right .eael-data-table thead tr th {
	text-align: right;
	padding-right: 30px;
}

.eael-dt-th-align-center .eael-data-table thead tr th {
	text-align: center;
}

.eael-dt-td-align-left .eael-data-table tbody tr td {
	text-align: left;
}

.eael-dt-td-align-center .eael-data-table tbody tr td {
	text-align: center;
}

.eael-dt-td-align-right .eael-data-table tbody tr td {
	text-align: right;
	padding-right: 30px;
}

.eael-dt-td-align-left .eael-data-table .th-mobile-screen {
	text-align: left;
}
.eael-dt-td-align-center .eael-data-table .th-mobile-screen {
	text-align: center;
}
.eael-dt-td-align-right .eael-data-table .th-mobile-screen {
	text-align: right;
}

@media (max-width: 1024px) {
	.eael-dt-td-align-tablet-left {
		.eael-data-table tbody tr td {
			text-align: left;
		}
	}
	.eael-dt-td-align-tablet-center {
		.eael-data-table tbody tr td {
			text-align: center;
		}
	}
	.eael-dt-td-align-tablet-right {
		.eael-data-table tbody tr td {
			text-align: right;
		}
	}

	.eael-dt-th-align-tablet-left {
		.eael-data-table {
			thead {
				tr th {
					text-align: left;
				}
			}
			.th-mobile-screen {
				text-align: left;
			}
		}
	}

	.eael-dt-th-align-tablet-right {
		.eael-data-table {
			thead {
				tr th {
					text-align: right;
					padding-right: 30px;
				}
			}
			.th-mobile-screen {
				text-align: right;
			}
		}
	}

	.eael-dt-th-align-tablet-center {
		.eael-data-table {
			thead {
				tr th {
					text-align: center;
				}
			}
			.th-mobile-screen {
				text-align: center;
			}
		}
	}
}

@media (max-width: 767px) {
	.eael-dt-td-align-mobile-left {
		.eael-data-table {
			tbody {
				tr {
					td {
						text-align: left;
					}
				}
			}
		}
	}

	.eael-dt-td-align-mobile-center {
		.eael-data-table {
			tbody {
				tr {
					td {
						text-align: center;
					}
				}
			}
		}
	}

	.eael-dt-td-align-mobile-right {
		.eael-data-table {
			tbody {
				tr {
					td {
						text-align: right;
					}
				}
			}
		}
	}

	.eael-dt-th-align-mobile-left {
		.eael-data-table {
			thead {
				tr th {
					text-align: left;
				}
			}
			.th-mobile-screen {
				text-align: left;
			}
		}
	}

	.eael-dt-th-align-mobile-right {
		.eael-data-table {
			thead {
				tr th {
					text-align: right;
					padding-right: 30px;
				}
			}
			.th-mobile-screen {
				text-align: right;
			}
		}
	}

	.eael-dt-th-align-mobile-center {
		.eael-data-table {
			thead {
				tr th {
					text-align: center;
				}
			}
			.th-mobile-screen {
				text-align: center;
			}
		}
	}
}
