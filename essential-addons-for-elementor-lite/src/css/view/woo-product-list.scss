$theme-preset1: #758F4D;
$theme-preset2: #BC5C5C;
$theme-preset3: #A66C46;
$spacing1: 5px;
$spacing2: 10px;
$spacing3: 15px;
$spacing4: 20px;
$spacing5: 25px;
$spacing6: 30px;

$spacer-sm: $spacing3;
$spacer-md: $spacing4;
$spacer-lg: $spacing5;

.eael-product-list-wrapper {
    .eael-direction-rtl {
        direction: rtl;
    }

    .eael-m-0 {
        margin: 0;
    }
    
    &.preset-1,
    &.preset-2,
    &.preset-3 {
        .eael-product-list-item {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            position: relative;

            &.image-alignment-right {
                flex-direction: row-reverse;

                .eael-product-list-content-wrap {
                    padding-left: 0;
                    padding-right: 70px;
                }
            }

            // Content Wrap
            .eael-product-list-content-wrap {
                width: 70%;
                padding-left: 70px;
            }
        }

        .eael-product-list-title a {
            color: #343434;
        }

        .eael-product-list-quick-view-button a {
            color: #515151;
        }

        .eael-product-list-buttons-on-hover li a {
            color: #343434;
        }

        .eael-product-list-notice.eael-product-list-notice-shiping-free {
            direction: ltr;
        }
    }

    &.preset-1,
    &.preset-2 {
        .eael-product-list-content-header {
            margin-bottom: $spacing3;
        }

        .eael-product-list-title {
            margin: 0 0 $spacer-md;
        }

        .eael-product-list-progress {
            margin-bottom: $spacer-md;
        }
    }

    &.preset-1,
    &.preset-3 {
        .eael-product-list-item {
            background: #fff;
        }
    }

    &.preset-1 {
        .eael-product-list-title a:hover {
            color: $theme-preset1;
        }

        .eael-product-list-add-to-cart-button a {
            background: $theme-preset1;
            border-radius: 8px;
        }
        
        .eael-product-list-quick-view-button a {
            &:hover {
                color: $theme-preset1;
            }
        }

        .eael-product-list-buttons-on-hover li a {
            &:hover {
                color: $theme-preset1;
            }
        }

        .eael-product-list-notice p {
            color: $theme-preset1;

            & i {
                color: $theme-preset1;
            }
        }
    }

    &.preset-2 {
        .eael-product-list-title a:hover {
            color: $theme-preset2;
        }
        
        .eael-product-list-item {
            background: #FAF8F8;
        }

        .eael-product-list-add-to-cart-button a {
            background: $theme-preset2;
            border-radius: 8px;
        }

        .eael-product-list-quick-view-button a {
            &:hover {
                color: $theme-preset2;
            }
        }

        .eael-product-list-buttons-on-hover li a {
            &:hover {
                color: $theme-preset2;
            }
        }

        .eael-product-list-notice p {
            color: $theme-preset2;

            & i {
                color: $theme-preset2;
            }
        }
    }

    &.preset-3 {
        .eael-product-list-title a:hover {
            color: $theme-preset3;
        }
        
        .eael-product-list-content-header {
            margin-bottom: $spacing4;
        }

        .eael-product-list-title {
            margin: 0 0 $spacing3;
        }

        .eael-product-list-add-to-cart-button a {
            background: $theme-preset3;
            border-radius: 100px;
        }

        .eael-product-list-quick-view-button a {
            &:hover {
                color: $theme-preset3;
            }
        }

        .eael-product-list-buttons-on-hover li a {
            &:hover {
                color: $theme-preset3;
            }
        }

        .eael-product-list-progress {
            margin-right: 30px;
            margin-bottom: 0;
            margin-left: 10px;
        }

        .eael-product-list-notice p {
            color: #515151;

            i {
                color: #515151;
            }
        }
    }

    .eael-product-list-container {
        padding: 60px;
    }

    .eael-product-list-item {
        padding: 64px;
    }

    .product.outofstock {
        .eael-product-list-buttons-on-hover {
            .eael-product-list-add-to-cart-button a {
                display: none;
            }
        }
    }

    // Badge
    .eael-product-list-badge-wrap.badge-preset-1 {
        height: 100px;
        width: 100px;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: -30px;

        &.badge-alignment-left {
            left: 0;
        }
        
        &.badge-alignment-right {
            right: 0;
        }
        
        .eael-product-list-badge-bg {
            height: 100%;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            svg path {
                fill:#DBEC73;
            }
        }

        p {
            color: #292929;
            font-size: 15px;
            line-height: 1.2;
            text-transform: capitalize;
            text-align: center;
            margin: 0;
            position: relative;

            span {
                font-size: 21px;
                font-weight: 600;
                display: block;
            }
        }
    }

    .eael-product-list-badge-wrap {
        z-index: 1;
    }
    
    .eael-product-list-badge-wrap.badge-preset-2.is-on-sale {
        height: 110px;
        width: 110px;
        background: transparent;
        padding: 10px;
        display: block;
        overflow: hidden;
        position: absolute;
        top: -10px;

        &.badge-alignment-left {
            left: -10px;

            &::before {
                right: 0;
            }
    
            &::after {
                left: 0;
                border-right: 10px solid #a76060;
                filter: brightness(0.7);
            }

            p {
                transform: rotate(-45deg);
            }
        }
        
        &.badge-alignment-right {
            right: -10px;

            &::before {
                left: 0;
            }
    
            &::after {
                right: 0;
                border-left: 10px solid #a76060;
                filter: brightness(0.7);
            }
            
            p {
                transform: rotate(45deg);
            }
        }

        &::before,
        &::after {
            content: "";
            width: 0;
            height: 0;
            border: 0 solid transparent;
            position: absolute;
        }

        &::before {
            top: 0;
            border-right-width: 10px;
            border-left-width: 10px;
            border-bottom: 10px solid #a76060;
            filter: brightness(0.7);
        }

        &::after {
            bottom: 0;
            border-top-width: 10px;
            border-bottom-width: 10px;
        }

        p {
            color: #fff;
            background: #A76060;
            font-size: 17px;
            font-weight: 700;
            line-height: 1;
            letter-spacing: 5px;
            text-transform: uppercase;
            text-align: center;
            text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
            padding: 10px;
            transform-origin: bottom;
            width: calc(100% + 100px);
            position: absolute;
            top: 9px;
            left: -50px;
        }
    }

    .eael-product-list-badge-wrap.badge-preset-2.stock-out,
    .eael-product-list-badge-wrap.badge-preset-3 {
        max-width: 150px;
        background: #FF4545;
        padding: 8px 24px;
        border-radius: 100px;
        display: block;
        overflow: hidden;
        position: absolute;
        top: 24px;

        &.badge-alignment-left {
            left: 24px;
        }
        
        &.badge-alignment-right {
            right: 24px;
        }

        p {
            color: #fff;
            text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);
            font-size: 15px;
            font-weight: 600;
            line-height: 1.2;
            margin: 0;
        }
    }

    // Image Wrap
    .eael-product-list-image-wrap {
        text-align: center;
        width: 30%;
        position: relative;

        img {
            max-width: 100%;
            width: 100%;
            max-height: 350px;
            object-fit: contain;
        }

        .eael-product-list-buttons-on-hover {
            position: absolute;
            left: 0;
            bottom: 0;
            padding: 0;
            margin: 0;
            list-style: none;
            opacity: 0;
            visibility: hidden;
            transform: translateY(0);
            transition: all 0.4s;
            width: 100%;
            display: flex;
            justify-content: center;
            
            li {
                padding: 0 4px;

                a {
                    border-radius: 4px;
                    box-shadow: 0px 7px 16px rgba(0, 0, 0, 0.10);
                    transition: all 0.4s;
                    position: relative;
                    width: 35px;
                    height: 35px;
                    margin: 3px;
                    padding: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                
                .add_to_cart_button.added {
                    display: none;
                }

                .added_to_cart {
                    font-size: 0;

                    &:after {
                        content: '\f217';
                        font-weight: 900;
                        font-family: 'Font Awesome 5 Free';
                        font-size: 16px;
                        text-rendering: auto;
                        -webkit-font-smoothing: antialiased;
                        vertical-align: middle;
                        margin: 0;
                        padding: 0;
                    }
                }
            }

            .eael-product-list-add-to-cart-button a {
                font-size: 0px;
                border: none;

                &:before {
                    content: "\f07a";
                    display: block;
                    font-family: "Font Awesome 5 Free";
                    font-size: 16px;
                    font-weight: 900;
                    transform: translate(-50%, -50%);
                    top: 50%;
                    left: 50%;
                    position: absolute;
                }

                &.product_type_variable {
                    &:before {
                        content: "\f00c";
                    }
                }
            }
        }
        
        &:hover .eael-product-list-buttons-on-hover {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50px);
        }
    }

    // Content Header
    .eael-product-list-content-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .eael-product-list-rating {
        display: flex;
        align-items: center;
        direction: ltr;
    }

    .eael-product-list-rating .star-rating {
        margin: 0;
        display: inline-block;
        float: none;
        height: 1em;
        width: 5.6em;
        line-height: 1em;

        &:before {
            content: '\f005 \f005 \f005 \f005 \f005';
            font-family: "Font Awesome 5 Free";
            font-weight: 400;
            opacity: 1;
        }

        span {
            display: inline-block;

            &:before {
                content: '\f005 \f005 \f005 \f005 \f005';
                font-family: "Font Awesome 5 Free";
                font-weight: 900;
            }
        }
    }

    .eael-product-list-review-count {
        font-size: 15px;
        margin-left: 5px;
    }

    .eael-product-list-notice p {
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 1.2;
        margin: 0;

        & i {
            margin-right: 4px;
        }
    }

    // Content Body
    .eael-product-list-title {
        width: 100%;
    }

    .eael-product-list-title,
    .eael-product-list-title a {
        font-size: 28px;
        font-style: normal;
        font-weight: 500;
        line-height: 1.2;
        text-transform: capitalize;
        text-decoration: none;
        transition: all 0.4s;
    }

    .eael-product-list-content-body {
        .eael-product-list-excerpt {
            font-size: 16px;
            font-weight: 400;
            margin: 0 0 $spacer-md;
        }

        .eael-product-list-price {
            display: flex;
            align-items: center;
            margin: 0 0 $spacer-md;
            gap: $spacing3;

            del {
                order: 1;
                font-size: 20px;
                font-weight: 400;
                line-height: 1.2;
                text-decoration: strikethrough;
            }

            ins,
            > .amount {
                font-size: 28px;
                font-weight: 600;
                line-height: 1.2;
                text-decoration: none;
            }
        }
    }

    // Content Footer
    .eael-product-list-content-footer {
        .eael-product-list-progress {
            .eael-product-list-progress-info {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-bottom: 5px;

                .eael-product-list-progress-count,
                .eael-product-list-progress-remaining {
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 1.4;
                    margin: 0;

                    & span {
                        font-weight: 500;
                    }
                }
            }

            .eael-product-list-progress-bar-outer {
                width: 100%;
                height: 3px;
                border-radius: 100px;

                .eael-product-list-progress-bar-inner {
                    height: 3px;
                    border-radius: 100px;
                }
            }
        }

        .eael-product-list-buttons {
            display: flex;
            align-items: center;

            a {
                display: inline-block;
            }

            .eael-product-list-add-to-cart-button a {
                font-size: 14px;
                font-weight: 600;
                line-height: 1.2;
                text-decoration: none;
                padding: 12px 16px 12px 40px;
                margin: 0 15px 0 0;
                transition: all 0.4s;
                position: relative;

                &:before {
                    content: "\f07a";
                    display: block;
                    font-family: "Font Awesome 5 Free";
                    font-size: 16px;
                    font-weight: 900;
                    transform: translate(-50%, -50%);
                    top: 50%;
                    left: 23px;
                    position: absolute;
                }
    
                &.product_type_variable {
                    &:before {
                        content: "\f00c";
                    }
                }
            }

            .eael-product-list-quick-view-button a {
                font-size: 16px;
                font-weight: 500;
                line-height: 1.2;
                text-decoration-line: underline;
                transition: all 0.4s;
                cursor: pointer;
            }
        }
    }

    // Load more
    .eael-load-more-button-wrap button {
        margin-top: 20px;
    }
}

// Popup
.eael-product-popup.woocommerce div.product form.cart div.quantity {
    margin-bottom: 0;
}

.eael-product-popup.woocommerce div.product .woocommerce-product-rating {
    display: flex;
    align-items: center;

    .star-rating {
        margin-top: 0;
        margin-right: 5px;
    }    
}

@media only screen and (max-width: 767px) {
    .eael-product-list-wrapper {
        &.preset-1,
        &.preset-2,
        &.preset-3 {
            .eael-product-list-item {
                .eael-product-list-image-wrap,
                .eael-product-list-content-wrap {
                    width: 100%;
                }
                
                .eael-product-list-content-wrap {
                    padding: 20px 0 0;
                }

                &.image-alignment-right {
                    flex-direction: column-reverse;
        
                    .eael-product-list-content-wrap {
                        padding: 0 0 20px;
                    }
                }
            }
        }

        .eael-product-list-container {
            padding: 15px;
        }

        .eael-product-list-item {
            padding: 15px;
        }

        .eael-product-list-content-footer {
            .eael-product-list-buttons {
                .eael-product-list-add-to-cart-button a.added_to_cart {
                    margin-top: 10px;
                }
            }
        }

        .eael-product-list-image-wrap {
            margin: auto;
        }
    }
}

@media only screen and (max-width: 991px) {
    .eael-product-list-wrapper {
        .eael-product-list-content-footer .eael-product-list-buttons {
            flex-wrap: wrap;
            gap: 15px;
        }
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .eael-product-list-wrapper {
        &.preset-1,
        &.preset-2,
        &.preset-3 {
            .eael-product-list-item {
                .eael-product-list-content-wrap {
                    padding-left: 20px;
                }
            }
        }
    }
}

// Theme Compatibility
.theme-twentytwentythree,
.theme-flexia {
    .eael-product-list-wrapper .eael-product-list-badge-wrap.badge-preset-2.is-on-sale p {
        top: -8px;
    }

    ins, mark {
        background: unset;
    }
}