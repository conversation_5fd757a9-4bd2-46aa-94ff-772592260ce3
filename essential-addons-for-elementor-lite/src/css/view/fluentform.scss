.eael-fluent-form-wrapper {
    label.ff-el-form-check-label {
        display: flex;
        align-items: center;
        
        input {
            margin-right: 5px;
        }
    }
}

.eael-fluentform-form-button-full-width .ff-btn-submit {
    width: 100%;
    display: block;
}

.eael-contact-form.eael-fluent-form-wrapper.error-message-hide .ff-el-is-error .text-danger {
    display: none;
}


.eael-fluentform-form-button-center .ff-el-group.ff-text-left .ff-btn-submit,
.eael-fluentform-form-button-center .ff-el-group.ff-text-right .ff-btn-submit,
.eael-fluentform-form-button-center .ff-el-group .ff-btn-submit {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.eael-fluentform-form-button-right .ff-el-group.ff-text-left .ff-btn-submit,
.eael-fluentform-form-button-right .ff-el-group.ff-text-right .ff-btn-submit,
.eael-fluentform-form-button-right .ff-el-group .ff-btn-submit {
    float: right;
}



.eael-fluentform-form-button-left .ff-el-group.ff-text-left .ff-btn-submit,
.eael-fluentform-form-button-left .ff-el-group.ff-text-right .ff-btn-submit,
.eael-fluentform-form-button-left .ff-el-group .ff-btn-submit {
    float: left;
}

.eael-contact-form.eael-fluent-form-wrapper.fluent-form-labels-hide {
    label {
        display: none !important;
    }
}

.eael-fluentform-section-break-content-left .ff-el-group.ff-el-section-break{ text-align: left; }
.eael-fluentform-section-break-content-center .ff-el-group.ff-el-section-break{ text-align: center; }
.eael-fluentform-section-break-content-right .ff-el-group.ff-el-section-break{ text-align: right; }

.ff-step-header .ff-el-progress-status,
.ff-el-progress { display: none; }

.eael-ff-step-header-yes .ff-step-header .ff-el-progress-status { display: block; }
.eael-ff-step-progressbar-yes .ff-el-progress { display: block; }

.ff-el-progress-bar {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
}