.eael-pricing {
    -webkit-display: flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
}

.eael-pricing .eael-pricing-item {
    width: 100%;
    height: auto;
    margin: 0;
}

.eael-pricing .eael-pricing-button {
    padding: 12px 25px;
    background: #00c853;
    font-size: 14px;
    font-weight: 600;
    color: #fff;
    text-transform: uppercase;
    text-decoration: none;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}

.eael-pricing .eael-pricing-button:hover {
    background: #03b048;
}

.eael-pricing .eael-pricing-item ul {
    padding: 0px;
    margin: 0px;
    list-style: none;
}

.eael-pricing .eael-pricing-item ul li.disable-item {
    text-decoration: line-through;
    opacity: 0.5;
}

.eael-pricing .eael-pricing-item ul li span.li-icon {
    color: #00c853;
    margin-right: 6px;
    margin-left: 6px;
}

.eael-pricing .eael-pricing-item ul li.disable-item span.li-icon {
    color: #ef5350;
}

/*--- Pricing Table: Style 1 ---*/
.eael-pricing.style-1 {
    position: relative;
    z-index: 0;
    text-align: center;
}

.eael-pricing.style-1 .eael-pricing-item {
    border: 1px solid rgba(9, 9, 9, 0.1);
    padding: 30px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -webkit-transition: 0.5s;
    transition: 0.5s;
}

//.eael-pricing.style-1 .eael-pricing-item:hover
.eael-pricing.style-1:hover
{
    -webkit-box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25),
        0 10px 10px rgba(0, 0, 0, 0.22);
    box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
    transition: 0.5s;
}

.eael-pricing.style-1 .eael-pricing-item.featured {
    position: relative;
}

.eael-pricing.style-1 .eael-pricing-item.ribbon-1:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 3px;
    background: #00c853;
    top: 0px;
    left: 0px;
    right: 0px;
    z-index: 1;
    -webkit-border-radius: 5px 5px 0px 0px;
    border-radius: 5px 5px 0px 0px;
}

.eael-pricing.style-1 .eael-pricing-item.ribbon-2:before {
    content: 'Featured';
    position: absolute;
    width: auto;
    background: #00c853;
    color: #fff;
    top: 35px;
    right: -15px;
    z-index: 10;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 5px 10px;
}

.eael-pricing.style-1 .eael-pricing-item.ribbon-2:after {
    content: '';
    position: absolute;
    top: 20px;
    right: -15px;
    width: 0;
    height: 0;
    border-bottom: 15px solid #00c853;
    border-right: 15px solid transparent;
    z-index: 9;
    opacity: 0.9;
}

.eael-pricing.style-1 .eael-pricing-item.ribbon-3:before {
    content: 'Featured';
    position: absolute;
    width: auto;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    top: 15px;
    right: 15px;
    z-index: 10;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 5px 15px;
}

.eael-pricing .eael-pricing-item .eael-pricing-image.ribbon-4:before,
.eael-pricing .eael-pricing-item.ribbon-4:before {
    content: 'Featured';
    position: absolute;
    width: auto;
    background: #00c853;
    color: #fff;
    top: 30px;
    right: -55px;
    z-index: 10;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    transform: rotate(45deg);
    width: 200px;
    padding: 7px 0;
    white-space: nowrap;
}
.eael-pricing
    .eael-pricing-item
    .eael-pricing-image.ribbon-left.ribbon-4:before,
.eael-pricing .eael-pricing-item.ribbon-left.ribbon-4:before {
    right: auto;
    left: -55px;
    transform: rotate(-45deg);
}

.eael-pricing.style-1 .eael-pricing-item .header {
    display: block;
    position: relative;
    z-index: 0;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.eael-pricing.style-1 .eael-pricing-item .header:after {
    content: '';
    position: absolute;
    width: 140px;
    height: 1px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    margin: 0 auto;
    z-index: 1;
    background: rgba(9, 9, 9, 0.1);
}

.eael-pricing.style-1 .eael-pricing-item .header .title {
    font-weight: 700;
    line-height: 30px;
    margin: 0px;
}

.eael-pricing.style-1 .eael-pricing-item .eael-pricing-tag {
    position: relative;
    z-index: 0;
    padding: 15px 0px;
    margin-bottom: 15px;
}

.eael-pricing.style-1 .eael-pricing-item .eael-pricing-tag:after {
    content: '';
    position: absolute;
    width: 140px;
    height: 1px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    margin: 0 auto;
    z-index: 1;
    background: rgba(9, 9, 9, 0.04);
}

.eael-pricing.style-1 .eael-pricing-item .price-tag {
    position: relative;
    // display: inline-block;
    font-size: 28px;
    font-weight: 500;
    line-height: 0px;
    margin: 0px auto;
}

.eael-pricing.style-1 .eael-pricing-item .price-tag .price-currency {
    font-size: 24px;
    font-weight: 700;
}

.eael-pricing.style-1 .eael-pricing-item .price-period {
    color: #999;
}

.eael-pricing.style-1 .eael-pricing-item .body ul {
    display: block;
    width: 100%;
    margin-bottom: 15px;
}

.eael-pricing.style-1 .eael-pricing-item .body ul li {
    display: block;
    width: 100%;
    height: auto;
    padding: 10px 0px;
    font-size: 14px;
    color: #6d6d6d;
    border-bottom: 1px solid rgba(9, 9, 9, 0.04);
}

.eael-pricing.style-1 .eael-pricing-item .body ul li:last-child {
    border: none;
}

.eael-pricing.style-1 .eael-pricing-item.featured-large {
    padding: 60px 0px;
}

/*--- Pricing Table : Style 2 ---*/
.eael-pricing.style-2 {
    position: relative;
    z-index: 0;
    text-align: center;
}

.eael-pricing.style-2 .eael-pricing-item {
    padding: 30px 0px;
    border-radius: 5px;
    margin: 0px;
    border: 1px solid rgba(9, 9, 9, 0.1);
}

.eael-pricing.style-2 .eael-pricing-item.featured {
    -webkit-box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25),
        0 10px 10px rgba(0, 0, 0, 0.22);
    box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
}

.eael-pricing.style-2 .eael-pricing-item.ribbon-1:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 3px;
    background: #00c853;
    top: 0px;
    left: 0px;
    right: 0px;
    z-index: 1;
    -webkit-border-radius: 5px 5px 0px 0px;
    border-radius: 5px 5px 0px 0px;
}

.eael-pricing.style-2 .eael-pricing-item.ribbon-2:before {
    content: 'Featured';
    position: absolute;
    width: auto;
    background: #00c853;
    color: #fff;
    top: 35px;
    right: -15px;
    z-index: 10;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 5px 10px;
}

.eael-pricing.style-2 .eael-pricing-item.ribbon-2:after {
    content: '';
    position: absolute;
    top: 20px;
    right: -15px;
    width: 0;
    height: 0;
    border-bottom: 15px solid #00c853;
    border-right: 15px solid transparent;
    z-index: 9;
    opacity: 0.9;
}

.eael-pricing.style-2 .eael-pricing-item.ribbon-3:before {
    content: 'Featured';
    position: absolute;
    width: auto;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    top: 15px;
    right: 15px;
    z-index: 10;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 5px 15px;
}

.eael-pricing.style-2 .eael-pricing-item .eael-pricing-icon .icon {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
    background: #00c853;
    border-radius: 50%;
    margin-bottom: 30px;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    overflow: hidden;
}

.eael-pricing.style-2 .eael-pricing-item .eael-pricing-icon,
.eael-pricing.style-2 .eael-pricing-item .eael-pricing-icon .icon {
	display: flex;
	align-items: center;
	justify-content: center;
}

.eael-pricing.style-2 .eael-pricing-item .eael-pricing-icon .icon i {
    font-size: 30px;
    color: #fff;
    -webkit-transition: 0.5s;
    transition: 0.5s;
}

.eael-pricing.style-2 .eael-pricing-item:hover .eael-pricing-icon .icon {
    background: #43a047;
}

.eael-pricing.style-2 .eael-pricing-item:hover .eael-pricing-icon .icon i {
    color: #fff;
}

.eael-pricing.style-2 .eael-pricing-item .header {
    background: #c8e6c9;
    padding: 25px 30px;
    margin-bottom: 15px;
    position: relative;
    z-index: 0;
}

.eael-pricing.style-2 .eael-pricing-item.featured .header:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    z-index: -1;
    background: rgba(255, 255, 255, 0.4);
}

.eael-pricing.style-2 .eael-pricing-item .header .title {
    font-size: 28px;
    font-weight: 700;
    line-height: 40px;
    margin: 0px;
}

.eael-pricing.style-2 .eael-pricing-item .header .subititle {
    font-size: 14px;
    font-weight: 600;
    color: #6d6d6d;
}

.eael-pricing.style-2 .eael-pricing-item .eael-pricing-tag {
    position: relative;
    z-index: 0;
    padding: 15px 0px;
    margin-bottom: 15px;
}

.eael-pricing.style-2 .eael-pricing-item .eael-pricing-tag:after {
    content: '';
    position: absolute;
    width: 140px;
    height: 1px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    margin: 0 auto;
    z-index: 1;
    background: rgba(9, 9, 9, 0.04);
}

.eael-pricing.style-2 .eael-pricing-item .price-tag {
    position: relative;
    // display: inline-block;
    font-size: 28px;
    font-weight: 500;
    line-height: 0px;
    margin: 0px auto;
}

.eael-pricing.style-2 .eael-pricing-item .price-tag .price-currency {
    font-size: 24px;
    font-weight: 700;
    color: #00c853;
}

.eael-pricing.style-2 .eael-pricing-item .price-period {
    color: #999;
}

.eael-pricing.style-2 .eael-pricing-item .body ul {
    display: block;
    width: 100%;
    margin-bottom: 15px;
}

.eael-pricing.style-2 .eael-pricing-item .body ul li {
    display: block;
    width: 100%;
    height: auto;
    padding: 10px 15px;
    font-size: 14px;
    color: #6d6d6d;
    border-bottom: 1px solid rgba(9, 9, 9, 0.04);
}

.eael-pricing.style-2 .eael-pricing-item .body ul li:last-child {
    border: none;
}
/*--- Media Query ---*/
@media only screen and (min-width: 768px) and (max-width: 992px) {
    .eael-pricing {
        display: block;
    }
    .eael-pricing .eael-pricing-item,
    .eael-pricing.style-2 .eael-pricing-item,
    .eael-pricing.style-4 .eael-pricing-item {
        width: 100%;
        margin: 0 auto 30px auto;
    }
}

@media only screen and (max-width: 480px) {
    .eael-pricing {
        display: block;
    }
    .eael-pricing .eael-pricing-item {
        width: 100%;
    }
    .eael-pricing .eael-pricing-item,
    .eael-pricing.style-2 .eael-pricing-item,
    .eael-pricing.style-4 .eael-pricing-item {
        margin: 0 auto 30px auto;
    }
}

/*--- Page Builder Related Style ---*/
.eael-pricing-content-align-center .eael-pricing {
    text-align: center;
}

.eael-pricing-content-align-left .eael-pricing {
    text-align: left;
}

.eael-pricing-content-align-right .eael-pricing {
    text-align: right;
}

.eael-pricing-content-align-center .eael-pricing.style-4 {
    text-align: center;
}

.eael-pricing-content-align-left .eael-pricing .eael-pricing-item.ribbon-4:before,
.eael-pricing-content-align-right .eael-pricing .eael-pricing-item.ribbon-4:before{
    text-align: center
}

.eael-pricing-content-align-left .eael-pricing.style-4 {
    text-align: left;
}

.eael-pricing-content-align-right .eael-pricing.style-4 {
    text-align: right;
}

.eael-pricing-content-align-left
    .eael-pricing.style-2
    .eael-pricing-item
    .price-tag {
    padding-left: 45px;
}

.eael-pricing-content-align-left
    .eael-pricing.style-2
    .eael-pricing-item
    .price-tag:before {
    left: 30px;
}

.eael-pricing-content-align-right
    .eael-pricing.style-2
    .eael-pricing-item
    .eael-pricing-tag {
    padding-right: 30px;
}

.eael-pricing-content-align-left .eael-pricing.style-2 .eael-pricing-item .header,
// .eael-pricing-content-align-left .eael-pricing.style-2 .eael-pricing-item .eael-pricing-icon,
.eael-pricing-content-align-left .eael-pricing.style-2 .eael-pricing-item .footer,
.eael-pricing-content-align-left .eael-pricing.style-4 .eael-pricing-item .header,
.eael-pricing-content-align-left .eael-pricing.style-4 .eael-pricing-item .footer {
    padding-left: 30px;
    padding-right: 30px;
}

.eael-pricing-content-align-right .eael-pricing.style-2 .eael-pricing-item .header,
// .eael-pricing-content-align-right .eael-pricing.style-2 .eael-pricing-item .eael-pricing-icon,
.eael-pricing-content-align-right .eael-pricing.style-2 .eael-pricing-item .footer,
.eael-pricing-content-align-right .eael-pricing.style-4 .eael-pricing-item .header,
.eael-pricing-content-align-right .eael-pricing.style-4 .eael-pricing-item .footer {
    padding-right: 30px;
    padding-left: 30px;
}

.eael-pricing-content-align-left
    .eael-pricing.style-2
    .eael-pricing-item
    .body
    ul
    li,
.eael-pricing-content-align-left
    .eael-pricing.style-4
    .eael-pricing-item
    .body
    ul
    li {
    padding-left: 30px;
}

.eael-pricing-content-align-right
    .eael-pricing.style-2
    .eael-pricing-item
    .body
    ul
    li,
.eael-pricing-content-align-right
    .eael-pricing.style-4
    .eael-pricing-item
    .body
    ul
    li {
    padding-right: 30px;
}

.eael-pricing-content-align-left
    .eael-pricing.style-3
    .eael-pricing-item
    .header:after {
    -webkit-transform: translateX(-80%);
    transform: translateX(-80%);
}

.eael-pricing-content-align-right
    .eael-pricing.style-3
    .eael-pricing-item
    .header:after {
    -webkit-transform: translateX(80%);
    transform: translateX(80%);
}

.eael-pricing-content-align-left
    .eael-pricing.style-3
    .eael-pricing-item:hover
    .header:after,
.eael-pricing-content-align-right
    .eael-pricing.style-3
    .eael-pricing-item:hover
    .header:after {
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
}

.eael-pricing-content-align-left
    .eael-pricing.style-1
    .eael-pricing-item
    .header:after,
.eael-pricing-content-align-right
    .eael-pricing.style-1
    .eael-pricing-item
    .header:after,
.eael-pricing-content-align-left
    .eael-pricing.style-1
    .eael-pricing-item
    .eael-pricing-tag:after,
.eael-pricing-content-align-right
    .eael-pricing.style-1
    .eael-pricing-item
    .eael-pricing-tag:after,
.eael-pricing-content-align-left
    .eael-pricing.style-2
    .eael-pricing-item
    .eael-pricing-tag:after,
.eael-pricing-content-align-right
    .eael-pricing.style-2
    .eael-pricing-item
    .eael-pricing-tag:after {
    margin: 0;
    width: 100%;
}

/*--- Button Alignment ---*/
.eael-pricing-button-align-right .eael-pricing.style-1 .footer,
.eael-pricing-button-align-right .eael-pricing.style-2 .footer,
.eael-pricing-button-align-right .eael-pricing.style-3 .footer,
.eael-pricing-button-align-right .eael-pricing.style-4 .footer {
    text-align: right;
}

.eael-pricing-button-align-center .eael-pricing.style-1 .footer,
.eael-pricing-button-align-center .eael-pricing.style-2 .footer,
.eael-pricing-button-align-center .eael-pricing.style-3 .footer,
.eael-pricing-button-align-center .eael-pricing.style-4 .footer {
    text-align: center;
}

.eael-pricing-button-align-left .eael-pricing.style-1 .footer,
.eael-pricing-button-align-left .eael-pricing.style-2 .footer,
.eael-pricing-button-align-left .eael-pricing.style-3 .footer,
.eael-pricing-button-align-left .eael-pricing.style-4 .footer {
    text-align: left;
}

.eael-pricing-content-align-center.eael-pricing-button-align-right
    .eael-pricing.style-2
    .footer,
.eael-pricing-content-align-center.eael-pricing-button-align-right
    .eael-pricing.style-4
    .footer {
    padding-right: 30px;
}

.eael-pricing-content-align-center.eael-pricing-button-align-left
    .eael-pricing.style-2
    .footer,
.eael-pricing-content-align-center.eael-pricing-button-align-left
    .eael-pricing.style-4
    .footer {
    padding-left: 30px;
}

/*--- Only In Pro Alert ---*/
.only-in-pro {
    width: 100%;
    -webkit-display: flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
    padding: 15px;
    min-width: 200px;
    background: #ef5350;
    color: #fff;
    text-align: center;
}

.only-in-pro .title {
    font-family: 'Roboto', sans-serif;
    font-size: 24px;
    line-height: 40px;
    margin: 0px;
}

/*--- Pricing Table Tooltip ---*/
div.tooltipster-sidetip.tooltipster-base.tooltipster-right .tooltipster-arrow {
    position: absolute;
    top: 50%;
}

div.tooltipster-sidetip.tooltipster-top div.tooltipster-box {
    margin-bottom: 0px !important;
}

div.tooltipster-sidetip.tooltipster-bottom div.tooltipster-box {
    margin-top: 0px !important;
}

.tooltipster-base.tooltipster-sidetip.tooltipster-top .tooltipster-arrow {
    top: auto;
    bottom: -8px;
}

div.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow {
    top: -8px;
    bottom: auto;
}

@media only screen and (max-width: 480px) {
    .eael-pricing.style-1 .eael-pricing-item .price-tag {
        display: block;
    }
}
