.eael-post-timeline {
	margin-bottom: 0;
	min-height: 100%;
	overflow: hidden;
	position: relative;
}

.eael-timeline-column {
	width: 50%;
	margin-left: 0;
	float: left;
	margin-top: 0 !important;
}

.eael-timeline-post {
	position: relative;
}

.eael-timeline-post:after {
	background-color: rgba(83, 85, 86, 0.2);
	content: "";
	width: 2px;
	height: 245px;
	position: absolute;
	right: 0;
	top: 70px;
}

.eael-timeline-post:nth-child(2n):after {
	display: none;
}

.eael-timeline-bullet {
	background-color: #9fa9af;
	border: 5px solid #fff;
	border-radius: 50%;
	box-shadow: 0 1px 0 1px rgba(0, 0, 0, 0.1);
	content: "";
	height: 20px;
	position: absolute;
	right: -9px;
	top: 60px;
	width: 20px;
	z-index: 3;
	cursor: pointer;
}

.eael-timeline-post:nth-child(2n) .eael-timeline-bullet {
	background-color: #9fa9af;
	border: 5px solid #fff;
	border-radius: 50%;
	bottom: 36px;
	content: "";
	height: 20px;
	left: -11px;
	position: absolute;
	top: 300px;
	width: 20px;
	z-index: 3;
}

.eael-timeline-post-inner {
	background: linear-gradient(45deg, #3f3f46 0%, #05abe0 100%) repeat scroll 0 0 rgba(0, 0, 0, 0);
	border: 8px solid #e5eaed;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.15);
	float: right;
	margin: 30px 40px 30px auto;
	position: relative;
	height: 320px;
	width: calc(100% - 40px);
}

.eael-timeline-post:nth-child(even) .eael-timeline-post-inner {
	float: left;
	margin-left: 40px;
}

.eael-timeline-post-inner:after {
	border-color: transparent transparent transparent #e5eaed;
	border-style: solid;
	border-width: 15px;
	content: "";
	height: 0;
	position: absolute;
	right: -36px;
	top: 17px;
	width: 0;
}

.eael-timeline-post:nth-child(2n) .eael-timeline-post-inner:after {
	border-color: transparent #e5eaed transparent transparent;
	border-style: solid;
	border-width: 15px;
	content: "";
	height: 0;
	left: -36px;
	position: absolute;
	top: 257px;
	width: 0;
}

.eael-timeline-post:nth-child(2n) .eael-timeline-post-inner::after {
	border-left-color: transparent !important;
}

.eael-timeline-post p {
	margin: 1.6rem 0 0 0;
	font-size: 0.9em;
	line-height: 1.6em;
}

.eael-timeline-post-image {
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
	display: block;
	height: 100%;
	overflow: hidden;
	position: relative;
	opacity: 1;
	-webkit-transition: all 0.3s;
	-moz-transition: all 0.3s;
	transition: all 0.3s;
}

.eael-timeline-post-title {
	bottom: 40px;
	position: absolute;
	width: 100%;
}

.eael-timeline-post-title .eael-timeline-post-title-text {
	color: #fff;
	font-size: 20px;
	font-weight: bold;
	letter-spacing: 1px;
	line-height: 24px;
	padding: 0 25px;
	text-align: left;
	text-transform: uppercase;
	margin-bottom: 15px;
	display: block;
}

.eael-timeline-post-excerpt {
	opacity: 0;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	-webkit-transition: all 0.3s;
	-moz-transition: all 0.3s;
	transition: all 0.3s;
}

.eael-timeline-post-excerpt p {
	color: #fff;
	font-size: 14px;
	padding: 25px;
}

.eael-timeline-post-inner:hover .eael-timeline-post-excerpt {
	opacity: 1;
	top: 10px;
}

.eael-timeline-post-inner:hover .eael-timeline-post-image {
	opacity: 0.3;
}

.eael-timeline-post time {
	opacity: 0;
	background-color: rgba(0, 0, 0, 0.7);
	color: #fff;
	font-size: 10px;
	border-radius: 20px;
	position: absolute;
	right: -97px;
	width: 100px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	top: 50px;
	z-index: 4;
	-webkit-transition: all .5s;
	-moz-transition: all .5s;
	transition: all .5s;
}

.eael-timeline-post:nth-child(2n) time {
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 20px;
	color: #fff;
	font-size: 10px;
	height: 30px;
	left: -99px;
	line-height: 30px;
	position: absolute;
	text-align: center;
	top: 290px;
	width: 100px;
	z-index: 4;
}

.eael-timeline-post time:before {
	border-bottom: 5px solid rgba(0, 0, 0, 0.7);
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	content: "";
	height: 0;
	left: 45px;
	position: absolute;
	top: -5px;
	width: 0;
}

.eael-timeline-post:hover time {
	opacity: 1;
}

.eael-timeline-post::after {
	height: 100%;
}

.eael-post-timeline .eael-timeline-post:nth-last-child(2)::after {
	height: 245px;
}

.eael-post-timeline .eael-timeline-post:last-child::after {
	display: none;
}

.eael-load-more-button-wrap {
	display: flex;
}

/*--- Responsive Style for Post Timeline ---*/

@media only screen and (max-width: 1366px) {
	.eael-timeline-post-title .eael-timeline-post-title-text {
		font-size: 0.8em;
	}
	.eael-timeline-post-excerpt p {
		font-size: 13px;
	}
}

@media only screen and (max-width: 1169px) {
	.eael-timeline-post-inner {
		height: 320px;
	}
}

@media only screen and (max-width: 992px) {
	.eael-post-timeline {
		margin-left: 0;
	}
	.eael-timeline-bullet,
	.eael-timeline-post:after,
	.eael-timeline-post:before,
	.eael-timeline-post-inner:after {
		display: none;
	}
	.eael-timeline-post {
		display: inline-block;
		float: left !important;
		width: 50% !important;
		margin: 15px auto;
	}
	.eael-timeline-post-inner {
		height: 320px;
		padding-bottom: 30px;
	}
	.eael-timeline-post-title {
		bottom: 50px;
	}
	.eael-timeline-post-title .eael-timeline-post-title-text {
		font-size: 0.8em;
		line-height: 1.2em;
	}
	.eael-timeline-post .eael-timeline-post-inner {
		margin: 0 10px auto 0;
		width: 90%;
	}
	.eael-timeline-post:nth-child(2n) .eael-timeline-post-inner {
		margin: 0 auto 0 10px;
		width: 90%;
	}
	.eael-timeline-post-excerpt {
		opacity: 0 !important;
	}
	.eael-timeline-post-image {
		opacity: 0.3;
	}
	.eael-timeline-post time,
	.eael-timeline-post:nth-child(2n) time {
		background-color: #fff;
		border-radius: 0;
		color: #444;
		font-size: 12px;
		text-transform: uppercase;
		left: 0;
		opacity: 1;
		padding-top: 3px;
		top: 275px;
		width: 100%;
	}
	time:before {
		display: none;
	}
}

@media only screen and (max-width: 767px) {
	.eael-timeline-post {
		display: block;
		float: none !important;
		margin: 20px auto;
		width: 100% !important;
	}
	.eael-timeline-post .eael-timeline-post-inner,
	.eael-timeline-post:nth-child(2n) .eael-timeline-post-inner {
		display: block;
		float: none;
		margin: 0 auto;
	}
}

@media only screen and (max-width: 479px) {
	.eael-timeline-post .eael-timeline-post-inner,
	.eael-timeline-post:nth-child(2n) .eael-timeline-post-inner {
		height: 250px;
		margin: 0 auto;
		width: 95%;
	}
	.eael-timeline-post time,
	.eael-timeline-post:nth-child(2n) time {
		top: 205px;
	}
}


// RTL
.rtl {
	.eael-timeline-post {
		direction: ltr;
	}
}

.eael-post-timeline.timeline-layout-card {
	margin: -20px;
	overflow: unset;

	.eael-timeline-post {
		width: 100%;
		padding: 20px;

		@media only screen and (min-width: 992px) {
			&:nth-child(odd) .eael-timeline-post-inner::after {
				border-right: none;
				left: auto !important;
			}
		}

		@media only screen and (max-width: 992px) {
			width: 100% !important;
			margin: 0;

			.eael-timeline-bullet,
			&:after, &:before,
			.eael-timeline-post-inner:after {
				display: block;
			}

			&:after {
				left: 7% !important;
				transform: translateX(-7%) !important;
				//border-left: none;
			}
		}

		&:last-child {
			&::after {
				display: none !important;
			}
		}

		&:after {
			left: 50%;
			transform: translateX(-50%);
			right: auto;
			top: 40px;
		}

		.eael-timeline-bullet {
			left: 50%;
			transform: translateX(-50%);
			right: auto;
			top: 40px;

			@media only screen and (max-width: 992px) {
				left: 7%;
				transform: translateX(-50%);
			}
		}

		&:nth-last-child(2)::after {
			height: 100%;
		}

		&:nth-child(2n) {
			&:after {
				display: block;
			}

			.eael-timeline-post-inner:after {
				top: 15px;
				left: -12px;
				border-left: none;
			}
		}


		@media only screen and (min-width: 992px) {
			&:nth-child(2n) {
				.eael-timeline-post-inner {
					float: right;
				}

				time {
					left: auto;
					right: calc(100% + 85px);
					text-align: right;
				}
			}
		}

		.eael-timeline-clear {
			clear: right;
		}
	}

	.eael-timeline-post-inner {
		width: 46%;
		height: auto;
		float: none;
		margin: 0;
		background: #2315ab;
		border: none;
		box-shadow: 0px 0px 20px 0px rgb(0 0 0 / 8%);

		@media only screen and (max-width: 992px) {
			width: 90%;
			float: right;
			padding-bottom: 0;

			&:after {
				display: block;
				top: 15px;
				left: -12px;
				border-left: none;
			}
		}

		&:hover {
			.eael-timeline-post-excerpt {
				top: 0;
			}
		}

		&:after {
			top: 15px;
			right: -12px;
			border-left-color: #2315ab;
			border-right-color: #2315ab;
			border-width: 12px;
			//border-right: none;
		}
	}

	.eael-timeline-post-image {
		opacity: 1;
		height: 200px;
		background-repeat: no-repeat;
		background-size: cover;
		background-position: center center;
	}

	.eael-timeline-content {
		padding: 30px;
	}

	.eael-timeline-post-title {
		bottom: 0;

		* {
			margin-top: 0;
			padding: 0;
		}
	}

	.eael-timeline-post-excerpt {
		p {
			padding: 0;
			margin-top: 0;
		}
	}

	.eael-timeline-post-excerpt,
	.eael-timeline-post-title {
		position: relative;
		opacity: 1 !important;

		h2, p {
			//color: #000 !important;
		}
	}

	time {
		position: absolute;
		left: calc(100% + 85px);
		top: 10px;
		font-size: 1em;
		padding: 5px 10px;
		text-align: left;
		opacity: 1;
		height: auto;
		width: max-content;
		border-radius: 5px;

		&:before {
			content: none;
		}

		@media only screen and (max-width: 992px) {
			position: relative;
			left: 0;
			top: 0;
			margin: 20px;
			display: inline-block;
			font-size: 12px;
			padding: 2px 10px;
		}
	}

	&.eael-post-timeline-arrow-middle {
		.eael-timeline-post-inner:after,
		.eael-timeline-post:nth-child(2n) .eael-timeline-post-inner:after {
			top: 50%;
			transform: translateY(-50%);
		}

		.eael-timeline-post .eael-timeline-bullet {
			top: 50%;
			transform: translate(-50%, -50%);
		}

		.eael-timeline-post {

			&:after {
				top: 0;
			}

			&:first-child:after {
				top: 50%;
				height: 50%;
			}

			&:last-child:after {
				display: block !important;
				height: 50%;
			}
		}

		time {
			top: 50%;
			transform: translateY(-50%);
		}
	}

	&.eael-post-timeline-arrow-bottom {
		.eael-timeline-post-inner:after,
		.eael-timeline-post:nth-child(2n) .eael-timeline-post-inner:after {
			top: auto;
			bottom: 20px;
		}

		.eael-timeline-post .eael-timeline-bullet {
			top: auto;
			bottom: 40px;
		}

		.eael-timeline-post {

			&:after {
				top: 0;
			}

			&:first-child:after {
				height: 40px;
				top: auto;
				bottom: 0;
			}

			&:last-child:after {
				display: block !important;
				height: calc(100% - 40px);
			}
		}

		time {
			bottom: 10px;
			top: auto;
		}
	}

}

