.eael-simple-menu-container {
    min-height: 50px;
}

.elementor-widget-eael-simple-menu.eael-hamburger--mobile.eael-hamburger--responsive,
.elementor-widget-eael-simple-menu.eael-hamburger--mobile_extra.eael-hamburger--responsive,
.elementor-widget-eael-simple-menu.eael-hamburger--tablet.eael-hamburger--responsive,
.elementor-widget-eael-simple-menu.eael-hamburger--tablet_extra.eael-hamburger--responsive,
.elementor-widget-eael-simple-menu.eael-hamburger--laptop.eael-hamburger--responsive,
.elementor-widget-eael-simple-menu.eael-hamburger--desktop.eael-hamburger--responsive,
.elementor-widget-eael-simple-menu.eael-hamburger--widescreen.eael-hamburger--responsive {
    .eael-simple-menu-container{
        min-height: 50px;
        overflow: visible;

        .eael-simple-menu {
            &.eael-simple-menu-horizontal,
            &.eael-simple-menu-vertical {
                position: absolute;
                top: 50px;
                background-color: #54595f;
                width: 100%;
                z-index: 9;
                display: none;

                li {
                    float: none;
                    display: block;

                    a {
                        display: block;
                        text-align: inherit;

                        span {
                            &.eael-simple-menu-indicator{
                                display: none;
                            }
                            display: none;
                        }
                    }

                    ul {
                        position: relative;
                        top: unset;
                        left: unset;
                        width: 100%;
                        box-shadow: none;
                        visibility: visible;
                        opacity: 1;
                        display: none;

                        li {
                            position: relative;
                            display: block;

                            .eael-simple-menu-indicator {
                                border: 1px solid #00000080;
                            }

                            ul {
                                position: relative;
                                width: 100%;
                                top: unset;
                                left: unset;

                                li {
                                    &:last-child {
                                        > a {
                                            border-bottom-width: 1px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }

        &.eael-simple-menu-align-center {
            .eael-simple-menu {
                > li {
                    > a {
                        text-align: center;
                    }
                }
            }
        }

        &.eael-simple-menu-align-right {
            .eael-simple-menu {
                &.eael-simple-menu-horizontal {
                    > li {
                        > a {
                            text-align: right;
                        }
                    }
                }
            }
        }

        &.preset-1 {
            .eael-simple-menu {
                &.eael-simple-menu-horizontal,
                &.eael-simple-menu-vertical {
                    background-color: #4E36A3;
                }
            }
        }

        &.preset-3 {
            .eael-simple-menu {
                &.eael-simple-menu-horizontal,
                &.eael-simple-menu-vertical {
                    background-color: #15DBD5;
                }
            }
        }

        &.preset-2 {
            .eael-simple-menu {
                &.eael-simple-menu-horizontal,
                &.eael-simple-menu-vertical {
                    background-color: #1BC1FF;

                    li.current-menu-ancestor>a.eael-item-active,
                    li:hover > a,
                    li.current-menu-item > a.eael-item-active {
                        color: #fff;
                    }
                }
            }
        }
    }
}

.eael_simple_menu_hamburger_disable_selected_menu_hide.eael-hamburger--mobile.eael-hamburger--responsive,
.eael_simple_menu_hamburger_disable_selected_menu_hide.eael-hamburger--mobile_extra.eael-hamburger--responsive,
.eael_simple_menu_hamburger_disable_selected_menu_hide.eael-hamburger--tablet.eael-hamburger--responsive,
.eael_simple_menu_hamburger_disable_selected_menu_hide.eael-hamburger--tablet_extra.eael-hamburger--responsive,
.eael_simple_menu_hamburger_disable_selected_menu_hide.eael-hamburger--laptop.eael-hamburger--responsive,
.eael_simple_menu_hamburger_disable_selected_menu_hide.eael-hamburger--desktop.eael-hamburger--responsive,
.eael_simple_menu_hamburger_disable_selected_menu_hide.eael-hamburger--widescreen.eael-hamburger--responsive {
    .eael-simple-menu-toggle-text {
        display: none !important;
    }
}

.eael-simple-menu-hamburger-align-left.eael-hamburger--mobile.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-left.eael-hamburger--mobile_extra.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-left.eael-hamburger--tablet.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-left.eael-hamburger--tablet_extra.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-left.eael-hamburger--laptop.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-left.eael-hamburger--desktop.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-left.eael-hamburger--widescreen.eael-hamburger--responsive {
    .eael-simple-menu-toggle {
        top: 0;
        left: 0;
        right: auto;
    }
}

.eael-simple-menu-hamburger-align-center.eael-hamburger--mobile.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-center.eael-hamburger--mobile_extra.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-center.eael-hamburger--tablet.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-center.eael-hamburger--tablet_extra.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-center.eael-hamburger--laptop.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-center.eael-hamburger--desktop.eael-hamburger--responsive,
.eael-simple-menu-hamburger-align-center.eael-hamburger--widescreen.eael-hamburger--responsive {
    .eael-simple-menu-toggle {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
    }
}

.elementor-widget-eael-simple-menu.eael-hamburger--none, 
.elementor-widget-eael-simple-menu.eael-hamburger--not-responsive {
    .eael-simple-menu-container {
        .eael-simple-menu-toggle {
            display: none;
        }

        .eael-simple-menu-toggle-text {
            display: none;
        }

        .eael-simple-menu {
            &.eael-simple-menu-horizontal {
                &.eael-simple-menu-dropdown-animate-fade {
                    li {
                        ul {
                            transition: all 300ms;
                        }
                    }
                }

                &.eael-simple-menu-dropdown-animate-to-top {
                    li {
                        ul {
                            transform: translateY(20px);
                            transition: all 300ms;
                        }

                        &:hover {
                            > ul {
                                transform: translateY(0);
                            }
                        }
                    }
                }

                &.eael-simple-menu-dropdown-animate-zoom-in {
                    li {
                        ul {
                            transform: scale(0.8);
                            transition: all 300ms;
                        }

                        &:hover {
                            > ul {
                                transform: scale(1);
                            }
                        }
                    }
                }

                &.eael-simple-menu-dropdown-animate-zoom-out {
                    li {
                        ul {
                            transform: scale(1.2);
                            transition: all 300ms;
                        }

                        &:hover {
                            > ul {
                                transform: scale(1);
                            }
                        }
                    }
                }

                // hide indicator
                .eael-simple-menu-indicator {
                    display: none;
                }
            }
        }

        &.eael-simple-menu-align-center {
            text-align: center;

            .eael-simple-menu {
                &.eael-simple-menu-horizontal {
                    display: inline-flex;
                }
            }
        }

        &.eael-simple-menu-align-right {
            text-align: right;

            .eael-simple-menu {
                &.eael-simple-menu-horizontal {
                    display: inline-flex;
                }
            }
        }
    }
}

.eael-simple-menu-container {
    background-color: #54595f;

    .eael-simple-menu-toggle {
        position: absolute;
        top: 0;
        right: 0;
        //min-height: 50px;
        height: 100%;
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 15px;
        padding-right: 15px;
        background-color: #000000;
        color: #ffffff;
        border: none;
        border-radius: 0;
        outline: none;
        display: block;
        border-color:#333;

        .eael-simple-menu-toggle-text {
            display: none;
        }
    }

    .eael-simple-menu-toggle-text {
        position: absolute;
        top: 0;
        left: 20px;
        font-size: 14px;
        line-height: 50px !important;
        letter-spacing: 0 !important;
        color: #ffffff;
    }

    .eael-simple-menu {
        margin: 0;
        padding: 0;
        list-style: none;

        &:after {
            content: "";
            display: table;
            clear: both;
        }

        a {
            text-decoration: none;
            outline: none;
            box-shadow: none;
        }

        li {
            a {
                font-size: 14px;
                font-weight: 400;
                line-height: 50px;
                text-align: center;
                color: #ffffff;
                padding-left: 20px;
                padding-right: 20px;
                transition: all 100ms;
            }

            ul {
                margin: 0;
                padding: 0;
                list-style: none;

                li {
                    a {
                        font-size: 13px;
                        font-weight: 400;
                        line-height: 40px;
                        text-align: left;
                        color: #000;
                        padding-left: 20px;
                        padding-right: 20px;
                    }

                    &:hover,
                    &.current-menu-item {
                        > a {
                            color: #ee355f;
                            background-color: #ffffff;
                        }
                    }
                }
            }

            &:hover,
            &.current-menu-item {
                > a {
                    color: #ffffff;
                    //background-color: #ee355f;
                }
            }
        }

        .eael-simple-menu-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ffffff80;
            border-radius: 2px;
            text-align: center;
            //transform: translateY(-50%);
            cursor: pointer;

            &:before, svg, i {
                display: block;
                font-weight: 900;
                line-height: 26px;
                color: #ffffff;
                transition: transform 300ms;
            }

            &.eael-simple-menu-indicator-open {
                &:before, svg, i {
                    transform: rotate(180deg);
                }
            }
        }

        &.eael-simple-menu-horizontal {
            li {
                position: relative;
                float: left;
                display: inline-block;
                padding-top: 0;
                padding-bottom: 0;

                a {
                    display: inline-block;

                    // indicator
                    span {
                        position: relative;
                        margin-left: 5px;
                        padding-right: 4px;
                        border: 1px solid transparent;
                    }
                }

                ul {
                    position: absolute;
                    top: 100%;
                    left: auto;
                    width: 220px;
                    visibility: hidden;
                    opacity: 0;
                    background-color: #ffffff;
                    box-shadow: 1px 1px 5px #0000001a;
                    z-index: 1;

                    li {
                        position: relative;
                        float: none;
                        display: block;

                        a {
                            display: block;

                            // indicator
                            span {
                                transform: rotate(-90deg);
                            }
                        }

                        &:last-child {
                            > a {
                                border-bottom-width: 0;
                            }
                        }

                        ul {
                            top: 0;
                            left: 100%;
                        }
                    }
                }

                &:hover {
                    > ul {
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }
        }

        // Vertical Menu
        &.eael-simple-menu-vertical {
            li {
                position: relative;

                a {
                    display: block;
                    text-align: left;
                }

                ul {
                    display: none;

                    li {
                        position: relative;
                        display: block;

                        a {
                            border-bottom-style: solid;
                            border-bottom-width: 1px;
                            border-bottom-color: transparent;
                        }

                        &:last-child {
                            > a {
                                border-bottom-width: 0;
                            }
                        }

                        .eael-simple-menu-indicator {
                            border: 1px solid #00000080;

                            &:before, svg, i {
                                color: #000000;
                                fill: #000000;
                            }
                        }

                        ul {
                            li {
                                &:last-child {
                                    > a {
                                        border-bottom-width: 1px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // alignment
    &.eael-simple-menu-align-center {
        .eael-simple-menu {
            &.eael-simple-menu-vertical {
                > li {
                    > a {
                        text-align: center;
                    }
                }
            }
        }
    }

    &.eael-simple-menu-align-right {
        .eael-simple-menu {
            &.eael-simple-menu-horizontal {
                > li {
                    > .eael-simple-menu-indicator {
                        right: initial;
                        left: 10px;
                    }

                    &:last-child {
                        a {
                            border-right: none;
                        }
                    }
                }
            }

            &.eael-simple-menu-vertical {
                > li {
                    > a {
                        text-align: right;
                    }

                    > .eael-simple-menu-indicator {
                        right: initial;
                        left: 10px;
                    }
                }
            }
        }
    }

    &.eael-simple-menu-dropdown-align-center {
        .eael-simple-menu {
            li ul li a {
                text-align: center;
            }
        }
    }

    &.eael-simple-menu-dropdown-align-right {
        .eael-simple-menu {
            li ul li {
                a {
                    text-align: right;
                }

                > .eael-simple-menu-indicator {
                    right: initial;
                    left: 10px;
                }
            }
        }
    }


    &.preset-1 {
        background-color: #4E36A3;

        .eael-simple-menu {
            li > a {
                color: #fff;
            }
            
            li.current-menu-ancestor>a.eael-item-active,
            li:hover > a,
            li.current-menu-item > a.eael-item-active {
                background-color: #743EFF;
            }

            li ul {
                background-color: #743EFF;

                li.current-menu-ancestor>a.eael-item-active,
                li:hover > a,
                li.current-menu-item > a.eael-item-active {
                    background-color: #4E36A3;
                }
            }
        }

    }

    &.preset-3 {
        background-color: #15DBD5;

        .eael-simple-menu {
            li > a {
                color: #2A1A6C;
            }

            li.current-menu-ancestor>a.eael-item-active,
            li:hover > a,
            li.current-menu-item > a.eael-item-active {
                color: #fff;
                background-color: #F72C8A;
            }

            li ul {
                background-color: #F72C8A;

                li > a {
                    color: #fff;
                    border-bottom: 1px solid #F72C8A;
                }

                li.current-menu-ancestor>a.eael-item-active,
                li:hover > a,
                li.current-menu-item > a.eael-item-active {
                    background-color: #f30875;
                }
            }
        }

    }

    &.preset-2 {
        background-color: #fff;
        border: 1px solid #1BC1FF;
        //text-align: right;
        
        .eael-simple-menu {
            li > a {
                color: #5D5E61;
            }
            &.eael-simple-menu-horizontal:not(.eael-simple-menu-responsive)>li:first-child>a {
                border-left: 1px solid #1BC1FF;
            }

            &.eael-simple-menu-horizontal:not(.eael-advanced-menu-responsive)>li>a {
                border-right: 1px solid #1BC1FF;
            }

            li.current-menu-ancestor>a.eael-item-active,
            li:hover > a,
            li.current-menu-item > a.eael-item-active {
                color: #1BC1FF;
            }

            li ul {
                background-color: #fff;
                border: 1px solid #1BC1FF;

                li > a {
                    color: #5f5d5d;
                    border-bottom: 1px solid #1BC1FF;
                }

                li.current-menu-ancestor>a.eael-item-active,
                li:hover > a,
                li.current-menu-item > a.eael-item-active {
                    color: #1BC1FF;
                }
            }
        }

    }
}

.eael-simple-menu-hamburger-align-left {
    .eael-simple-menu-toggle-text {
        display: none;
    }

    .eael-simple-menu-toggle {
        .eael-simple-menu-toggle-text {
            display: block;
            left: calc(100% + 20px);
            white-space: nowrap;
        }
    }
}

.eael-simple-menu--stretch {
    .eael-simple-menu.eael-simple-menu-responsive {
        left: 50%;
        transform: translateX(-50%);
        transition: max-height .3s,transform .3s,-webkit-transform .3s;
        transform-origin: top;
        overflow: auto;
        z-index: 9999;
        max-height: 100vh;
    }
}

.eael-simple-menu-container {
    .eael-simple-menu-toggle {
        display: none;
        svg {
            width: 35px;
            height: auto;
        }
    }
}

.eael-simple-menu-container .eael-simple-menu-responsive {
    &.eael-hamburger-center {
        li {
            .eael-simple-menu-indicator {
                left: initial !important;
                right: 10px !important;
            }
            a {
                text-align: center !important;
            }
        }
    }
    &.eael-hamburger-left {
        li {
            .eael-simple-menu-indicator {
                left: initial !important;
                right: 10px !important;
            }
            a {
                text-align: left !important;
            }
        }

    }
    &.eael-hamburger-right {
        li {
            .eael-simple-menu-indicator {
                right: initial !important;
                left: 10px !important;
            }
            a {
                text-align: right !important;
            }
        }

    }
}
