.eael-img-accordion {
	display: flex;
	height: 50vh;
	overflow: hidden;

	&.accordion-direction-vertical {
		flex-direction: column;
	}
}

.eael-img-accordion .eael-image-accordion-hover {
	position: relative;
	flex: 1;
	text-align: center;
	text-decoration: none;
	color: #fff;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	transition: flex 0.4s;
	overflow: hidden;
	cursor: pointer;
}

.eael-grow-accordion {
	flex: 3;
}

.eael-img-accordion {
	.overlay {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 10px;
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		transition: background-color 0.4s;
		opacity: 1;
		visibility: visible;
		background: transparent;
	}

	&-horizontal-align-left .overlay {
		justify-content: flex-start;
		text-align: left;
	}

	&-horizontal-align-center .overlay {
		justify-content: center;
		text-align: center;
	}

	&-horizontal-align-right .overlay {
		justify-content: flex-end;
		text-align: right;
	}

	&-vertical-align-top .overlay {
		align-items: flex-start;
	}

	&-vertical-align-center .overlay {
		align-items: center;
	}

	&-vertical-align-bottom .overlay {
		align-items: flex-end;
	}
}

.eael-img-accordion .overlay .overlay-inner {
	z-index: 1;
}

.eael-img-accordion .eael-image-accordion-hover:before {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 0;
	top: 0px;
	left: 0px;
	bottom: 0px;
	right: 0px;
	transition: all 0.3s ease-in-out;
}

.eael-img-accordion .eael-image-accordion-hover:before {
	background-color: rgba(0, 0, 0, .3);
}

.eael-img-accordion .eael-image-accordion-hover{
	&.overlay-active,
	&:hover{
		&::before {
			background-color: rgba(0, 0, 0, .5);
		}
	}
}

.eael-img-accordion .overlay-inner * {
	visibility: hidden;
	opacity: 0;
	transform-style: preserve-3d;
}

.eael-img-accordion .overlay h2 {
	color: #fff;
	transform: translate3d(0, -60px, 0);
}

.eael-img-accordion .overlay p {
	color: #fff;
	margin-bottom: 0;
	transform: translate3d(0, 60px, 0);
}

.eael-img-accordion .eael-image-accordion-hover.overlay-active .overlay-inner *,
.eael-img-accordion .overlay-inner-show * {
	opacity: 1;
	visibility: visible;
	transform: none !important;
	transition: all 0.3s 0.3s;
}

@media screen and (max-width: 800px) {
	.eael-img-accordion {
		flex-direction: column;
	}

	.eael-img-accordion .eael-image-accordion-hover:hover {
		flex: 1;
	}

	.eael-img-accordion .eael-image-accordion-hover:hover .overlay {
		background-color: transparent;
	}
}

// rtl
.rtl {
	.eael-img-accordion {
		direction: ltr;
	}
}
