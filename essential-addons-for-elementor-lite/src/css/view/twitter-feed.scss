.eael-twitter-feed {
    &::before,
    &::after {
        content: '';
        clear: both;
    }
}

.eael-twitter-feed-masonry {
    .eael-twitter-feed-item {
        float: left;
    }

    &.eael-twitter-feed-col-2 {
        .eael-twitter-feed-item {
            @media only screen and (max-width: 480px) {
                width: 100% !important;
            }
        }
    }

    &.eael-twitter-feed-col-3 {
        .eael-twitter-feed-item {
            @media only screen and (max-width: 480px) {
                width: 100% !important;
            }
        }
    }

    &.eael-twitter-feed-col-4 {
        .eael-twitter-feed-item {
            @media only screen and (max-width: 480px) {
                width: 100% !important;
            }
        }
    }
}

// Item
.eael-twitter-feed-item {
    .page-1 {
        display: block;
    }

    .eael-twitter-feed-item-inner {
        background: #fff;
        border: 1px solid rgba(0, 0, 0, 0.1);
        -webkit-transition: all 0.3s ease;
        -moz-transition: all 0.3s ease;
        transition: all 0.3s ease;
        &.has-media {
            overflow: hidden;
            .eael-twitter-feed-entry-contentwrap {
                overflow: hidden;
            }
        }
        .eael-twitter-feed-item-avatar.avatar-circle {
            img {
                border-radius: 50%;
            }
        }
    }

    a {
        color: #0088cc;
        text-decoration: none;

        &:hover {
            outline: 0;
            color: #005580;
        }

        &:focus {
            outline: none;
        }
    }

    .eael-twitter-feed-item-header {
        padding: 20px 15px 0;
        margin-bottom: 10px;

        .eael-twitter-feed-item-avatar {
            float: left;
            width: 38px;
            margin-right: 10px;

            img {
                max-width: initial;
                height: auto;
                object-fit: cover;
            }

            &.avatar-circle {
                img {
                    border-radius: 50%;
                }
            }
        }

        .eael-twitter-feed-item-meta {
            float: left;

            .eael-twitter-feed-item-icon {
                color: #1da1f2;
                margin-right: 5px;
            }
            .eael-twitter-feed-item-author {
                color: #444;
                line-height: 1.5em;
                font-weight: 700;
            }
        }

        .eael-twitter-feed-item-date {
            float: right;
            margin-right: 10px;
            font-size: 90%;
            color: #999999;
        }
    }

    .eael-twitter-feed-item-content {
        padding: 0 15px 15px;

        p {
            color: #666666;
            margin: 0 0 5px;
            min-height: 20px;
            text-overflow: ellipsis;
            -webkit-line-clamp: 5;
            -webkit-box-orient: vertical;
        }
    }
    &-style-three {
        .eael-twitter-feed-item-inner {
            display: flex;
            padding: 20px 15px;
            .eael-twitter-feed-item-content,
            .eael-twitter-feed-item-header {
                padding: 0;
            }
        }
    }
    &-style-two {
        .eael-twitter-feed-item-icon {
            float: right;
            margin-top: 5px;
        }
    }
}

.eael-twitter-feed.swiper-container {
    .swiper-button-next,
    .swiper-button-prev {
        background-image: none;
        outline: none;
    }
}
// carousel
.eael-twitter-feed-carousel {
    &.swiper-container-flip {
        .swiper-slide:not(.swiper-slide-active) {
            opacity: 0 !important;
        }
    }
    .swiper-button-next,
    .swiper-button-prev {
        display: flex;
        justify-content: center;
        align-items: center;
        -webkit-transition: all 0.3s ease;
        -moz-transition: all 0.3s ease;
        transition: all 0.3s ease;
    }

    &.swiper-container {
        width: 100%;

        .swiper-button-prev:after,
        .swiper-button-next:after {
            content: none;
        }
    }
}

.eael-d-block {
    display: block;
}

.eael-d-none {
    display: none;
}