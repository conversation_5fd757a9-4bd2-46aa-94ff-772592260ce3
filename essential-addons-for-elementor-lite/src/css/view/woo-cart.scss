$text-color-1: #202B46;
$text-color-2: #747C92;
$text-color-3: #737373;
$qty-button: #97A0B6;
$icon-color: #BEC6D7;
$background: #f9fbff;
$btn-bg: #6557FF;
$btn2-bg: #312F4B;
$transparent: transparent;
$white: #fff;
$black: #000;
$thumb-bg-1: #E5F6FC;

.eael-woo-cart .eael-woo-cart-wrapper {
    background: $background;

    .sr-only {
        border: 0;
        clip: rect(0,0,0,0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }

    p {
        margin: 0;
    }

    .eael-woo-cart-product-remove {
        display: none;
    }

    .woocommerce-notices-wrapper {
        .woocommerce-error,
        .woocommerce-info,
        ~ .woocommerce-info,
        .woocommerce-message {
            background: $white;
            color: $text-color-1;
            box-shadow: 0 8px 18px 0 rgba(0, 1, 35, 0.05);
            border-radius: 5px;
            margin-bottom: 30px;

            &::before {
                position: relative;
                top: 0;
                left: 0;
                vertical-align: top;
            }

            li {
                display: inline-block;
                width: calc(100% - 30px);
            }
        }

        .woocommerce-error {
            border-color: #b81c23;
            list-style: none;

            &::before {
                color: #b81c23;
            }
        }

        .woocommerce-info {
            border-color: #1e85be;

            &::before {
                color: #1e85be;
            }
        }

        .woocommerce-message {
            border-color: #8fae1b;

            &::before {
                color: #8fae1b;
            }
        }

        ~ .return-to-shop a {
            font-size: 16px;
            font-weight: 500;
            color: $text-color-1;
            margin-top: 25px;
            display: inline-block;
        }
    }

    &.eael-woo-cart-empty {
        .woocommerce-notices-wrapper {
            min-height: 1px;
        }
    }

    form.eael-woo-cart-form {
        a {
            text-decoration: none;
            outline: none;
        }

        input {
            outline: none;
            box-shadow: none;
        }

        .eael-cart-clear-btn {
            .button {
                // margin-bottom: 15px;
                text-align: center;
            }
        }

        .eael-woo-cart-table {
            border-collapse: separate;
            border-spacing: 0 13px;
            border: none;
            margin: 0;

            th,
            td {
                border: none;
                color: $text-color-1;
                padding: 15px;
                text-transform: initial;

                a {
                    color: $text-color-1;
                }
            }

            dl {
                display: block;
                margin: 0 0 0 -5px;
                font-size: 80%;
                font-weight: 400;
                color: $text-color-3;
                text-decoration: inherit;

                dt {
                    margin: 0 0 0 5px;
                    text-transform: capitalize;
                    display: inline-block;
                    float: none;
                    font-weight: 400;
                    text-decoration: inherit;

                    &::before {
                        content: "|";
                        margin: 0 5px 0 -5px;
                    }

                    &:first-child::before {
                        display: none;
                    }
                }

                dd {
                    margin: 0 0 0 5px;
                    display: inline-block;
                    padding-left: 0;
                    text-decoration: inherit;
                }
            }

            .product-quantity {
                .quantity {
                    display: flex;
                    float: none;
                    align-items: center;
                    justify-content: center;
                    font-size: inherit;

                    input::-webkit-outer-spin-button,
                    input::-webkit-inner-spin-button {
                        -webkit-appearance: none;
                    }

                    input[type=number] {
                        -moz-appearance: textfield;
                        width: 50px;
                        background: #F9FBFF;
                        border: none;
                        border-radius: 3px;
                        padding: 10px;
                        line-height: 30px;
                        text-align: center;
                        font-size: 20px;
                        color: $text-color-1;
                        text-decoration: none !important;
                    }

                    .eael-cart-qty-minus,
                    .eael-cart-qty-plus {
                        line-height: 30px;
                        padding: 10px 15px;
                        color: $qty-button;
                        font-size: 20px;
                        cursor: pointer;
                        text-decoration: none !important;
                    }
                }
            }

            thead {
                background: $transparent;

                tr {
                    th {
                        border: none;
                        font-size: 18px;
                        font-weight: 500;
                        line-height: 30px;
                        background: transparent;
                        text-align: center;

                        &.product-thumbnail {
                            text-align: left;
                            display: table-cell;
                        }

                        &.product-name {
                            text-align: left;
                        }
                    }
                }
            }

            tbody {
                tr {
                    position: relative;
                    z-index: 2;

                    &::after {
                        position: absolute;
                        content: "";
                        background: $white;
                        top: 0;
                        left: 0;
                        height: 100%;
                        width: 100%;
                        z-index: -1;
                        box-shadow: 0 8px 18px 0 rgba(0, 1, 35, 0.05);
                        border-radius: 5px;
                    }

                    td {
                        border: none;
                        background: transparent;
                        text-align: center;
                        vertical-align: middle;
                        font-size: 20px;
                        font-weight: 400;

                        &.product-remove {
                            color: $icon-color;

                            a {
                                color: $icon-color !important;
                                display: block;
                                width: auto;
                                height: auto;
                                line-height: inherit;
                                font-weight: inherit;
                                font-size: inherit;
                                text-indent: initial;
                                border: none;

                                &::before {
                                    content: none;
                                }

                                &:hover {
                                    background: transparent;
                                    color: inherit !important;
                                }
                            }
                        }

                        &.product-thumbnail {
                            font-size: 0;
                            text-align: left;
                            display: table-cell;

                            a {
                                display: inline-block;

                                img {
                                    display: block;
                                }
                            }

                            img {
                                width: 62px !important;
                                max-width: 100%;
                                height: auto;
                                border-radius: 50%;
                                display: inline-block;
                            }
                        }

                        &.product-name {
                            text-align: left;
                        }

                        &.product-price {
                            color: $text-color-2;
                        }

                        &.product-subtotal {
                            font-weight: 500;
                        }
                    }
                }
            }

            tbody,
            .eael-woo-cart-tbody {
                .product-quantity {
                    text-decoration: none !important;
                }
            }
        }

        .woocommerce-Price-amount,
        .cart-collaterals .cart_totals .order-total td span.woocommerce-Price-amount.amount {
            font-weight: inherit;
            color: inherit;
            font-size: inherit;
        }
    }

    .eael-cart-coupon-and-collaterals,
    .eael-cart-clear-btn {
        &::before,
        &::after {
            content: '';
            display: block;
            clear: both;
        }

        button,
        .button,
        button:not(:hover):not(:active),
        .button:not(:hover):not(:active) {
            background: $btn-bg;
            box-shadow: 0 8px 18px rgba(0, 0, 0, 0.05);
            border-radius: 5px;
            color: $white;
            border: none;
            font-size: 18px;
            line-height: 30px;
            font-weight: 500;
            padding: 10px 40px;
            letter-spacing: initial;
            text-transform: initial;
            text-decoration: none;
            overflow: initial;

            &:hover {
                color: $white;
                background: $btn-bg;
                border-color: inherit;
            }
        }

        .eael-cart-coupon-wrapper {
            width: 50%;
            float: left;
            min-height: 1px;

            .coupon {
                border: 1px solid #D7DFEF;
                border-radius: 5px;
                max-width: 400px;
                padding: 7px;
                display: flex;

                input {
                    background: $transparent;
                    border: none;
                    box-shadow: none;
                    font-size: 18px;
                    line-height: 30px;
                    width: 100%;
                }

                button {
                    white-space: nowrap;
                    box-shadow: 0 8px 18px rgba(56, 51, 117, 0.1);
                }
            }

            .eael-woo-cart-back-to-shop {
                font-size: 16px;
                font-weight: 500;
                color: $text-color-1;
                margin-top: 25px;
                display: inline-block;
            }
        }

        .cart-collaterals {
            width: 50%;
            float: left;
            text-align: right;

            .eael-cart-update-btn {
                display: inline-block;
                text-align: center;
                max-width: 400px;
                width: 100%;

                button {
                    width: 100%;
                    margin-bottom: 13px;
                    display: inline-block;

                    &[disabled] {
                        opacity: .5;
                        cursor: not-allowed;
                    }
                }
            }

            .cart_totals {
                max-width: 400px;
                width: 100%;
                padding: 0 0 10px 0;
                margin: 0;
                float: right;
                border: none;

                table {
                    border-collapse: separate;
                    border-spacing: 0 13px;
                    margin: -13px 0;
                    border: none;

                    td,
                    th {
                        background: $transparent;
                        border: none;
                        font-size: 18px;
                        padding: 18px;
                    }

                    tr {
                        position: relative;
                        z-index: 2;

                        &::after {
                            position: absolute;
                            content: "";
                            background: $white;
                            top: 0;
                            left: 0;
                            height: 100%;
                            width: 100%;
                            z-index: -1;
                            box-shadow: 0 8px 18px 0 rgba(0, 1, 35, 0.05);
                            border-radius: 5px;
                        }

                        th {
                            color: $text-color-3;
                            font-weight: 500;
                            text-align: left;
                            width: 40%;
                        }

                        &.shipping td {
                            color: $text-color-3;

                            a {
                                display: inline-block;
                                margin-top: 9px;
                            }

                            .select2-container .select2-selection--single {
                                padding: 0;
                                background: $transparent;
                            }

                            input {
                                background: $transparent;
                                vertical-align: middle;
                                margin-right: 5px;
                            }

                            ul {
                                list-style: none;
                                padding: 0;
                                margin: 0 0 10px;
                            }
                        }

                        td {
                            border-spacing: 0;
                            text-align: right;
                            color: $text-color-1;

                            a {
                                color: $text-color-1;
                            }

                            .button {
                                width: 100%;
                            }
                        }

                        &.cart-subtotal,
                        &.order-total {
                            td {
                                font-size: 20px;
                                font-weight: 500;
                                color: $text-color-1;

                                strong {
                                    font-weight: 500;
                                }
                            }
                        }
                    }

                    + .wc-proceed-to-checkout {
                        margin-top: 13px;
                    }
                }

                .wc-proceed-to-checkout {
                    padding: 0;
                    text-align: center;

                    .button,
                    .button:not(:hover):not(:active) {
                        font-size: 20px;
                        padding: 18px 0;
                        margin: 0;
                        display: inline-block;
                        text-align: center;
                        width: 100%;
                    }
                }

                h2 {
                    display: none;
                }
            }
        }
    }

    &.eael-woo-style-2 {
        background: $white;

        .woocommerce-notices-wrapper {
            &:not(:empty) {
                padding: 50px 50px 0;
            }

            .woocommerce-error,
            .woocommerce-info,
            ~ .woocommerce-info,
            .woocommerce-message {
                border-radius: 0;
            }

            ~ .woocommerce-info {
                margin: 50px 50px 0;
            }

            ~ .return-to-shop a {
                margin-left: 50px;
                margin-right: 50px;
                margin-bottom: 50px;
            }
        }

        &.has-table-left-content.has-table-right-content {
            background: linear-gradient(to right, #FAFAFA 45%, #FFF 0%);
            background-color: #FAFAFA;
        }

        &:not(.has-table-right-content) {
            background: #FAFAFA;
        }

        form.eael-woo-cart-form {
            .eael-woo-cart-table {
                min-height: 1px;
                margin-bottom: 13px;

                .eael-woo-cart-tr {
                    display: flex;

                    .eael-woo-cart-tr-left {
                        width: 45%;
                        padding-left: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        > .product-thumbnail {
                            width: 130px;
                        }

                        > .product-name {
                            width: calc(100% - 130px - 28px);
                            font-weight: 500;

                            .eael-woo-cart-sku {
                                font-size: 89%;
                                font-weight: 400;
                                color: $text-color-3;
                            }

                            dl {
                                font-size: 89%;
                            }
                        }
                    }

                    .eael-woo-cart-tr-right {
                        width: 55%;
                        padding-right: 50px;
                        padding-left: 30px;
                        display: flex;
                        align-items: center;
                        position: relative;

                        &::after {
                            position: absolute;
                            content: '';
                            width: calc(100% - 80px);
                            left: 30px;
                            top: 100%;
                            height: 1px;
                            background: #EAEEF4;
                        }

                        > .eael-woo-cart-td {
                            flex: 1 1 20%;
                            text-align: center;

                            &.product-quantity {
                                flex: 1 1 35%;

                                .eael-cart-qty-minus,
                                .eael-cart-qty-plus,
                                input[type=number] {
                                    font-size: 100%;
                                }

                                input[type=number] {
                                    background: transparent;
                                }
                            }

                            &.product-remove {
                                color: $icon-color;
                                flex: 1 1 0%;

                                a {
                                    color: $icon-color !important;
                                    display: block;
                                    width: auto;
                                    height: auto;
                                    line-height: inherit;
                                    font-weight: inherit;
                                    font-size: inherit;
                                    text-indent: initial;
                                    border: none;

                                    &::before {
                                        content: none;
                                    }

                                    &:hover {
                                        background: transparent;
                                        color: inherit !important;
                                    }
                                }
                            }
                        }
                    }

                    .eael-woo-cart-td {
                        color: $text-color-1;
                        font-size: 18px;

                        a {
                            color: $text-color-1;
                            &.remove {
                                color: $text-color-1 !important;
                                position: unset;
                                font-size: 20px;
                                height: 20px;
                                width: 20px;
                                display: inline-block;
                                opacity: .5;
                                &:hover {
                                    background-color: transparent;
                                    opacity: 1;
                                }
                            }
                        }
                    }
                }

                .eael-woo-cart-thead {
                    .eael-woo-cart-tr {
                        .eael-woo-cart-td {
                            color: $text-color-3;
                            font-size: 16px;
                            font-weight: 500;
                            padding: 45px 0 55px 0;
                            text-transform: uppercase;
                        }
                    }
                }

                .eael-woo-cart-tbody {
                    .eael-woo-cart-tr {
                        .eael-woo-cart-tr-left {
                            padding-top: 10px;
                            padding-bottom: 10px;

                            > .product-thumbnail {
                                height: 130px;
                                border-radius: 6px;
                                overflow: hidden;
                                background: $thumb-bg-1;
                                display: flex;
                                justify-content: center;
                                align-items: center;

                                > a {
                                    font-size: 0;
                                }
                            }
                        }

                        .eael-woo-cart-tr-right {
                            > .eael-woo-cart-td {
                                &.product-subtotal {
                                    font-weight: 600;
                                }
                            }
                        }

                        &:first-child {
                            .eael-woo-cart-tr-left {
                                padding-top: 0;
                            }
                        }
                    }
                }
            }
        }

        .eael-cart-coupon-and-collaterals,
        .eael-cart-clear-btn {
            button,
            .button,
            button:not(:hover):not(:active),
            .button:not(:hover):not(:active) {
                background: $btn2-bg;
                font-size: 15px;
                font-weight: 600;
                padding: 7px 40px;
            }

            .eael-cart-coupon-wrapper {
                width: 45%;
                padding-left: 50px;

                .coupon {
                    padding: 3px;
                    border-color: #CCD4E9;

                    input {
                        font-size: 15px;
                    }
                }
            }

            .cart-collaterals {
                width: 55%;
                padding-right: 50px;

                .cart_totals {
                    padding-bottom: 30px;

                    table {
                        border-spacing: initial;
                        border-collapse: collapse;

                        tr {
                            border-bottom: 1px solid #EFF2F7;

                            &:last-child {
                                border-bottom: none;
                            }

                            &::after {
                                content: none;
                            }

                            th,
                            td {
                                padding: 20px 0;
                            }
                        }
                    }
                }
            }
        }

        &:not(.has-table-left-content) {
            form.eael-woo-cart-form .eael-woo-cart-table .eael-woo-cart-tr .eael-woo-cart-tr-right {
                width: 100%;
            }
        }

        &:not(.has-table-right-content) {
            form.eael-woo-cart-form .eael-woo-cart-table .eael-woo-cart-tr .eael-woo-cart-tr-left {
                width: 100%;
            }
        }
    }
}

// OceanWP Theme Compatibility
body.oceanwp-theme.eael-woo-cart .eael-woo-cart-wrapper {
    .eael-cart-coupon-and-collaterals .cart-collaterals .cart_totals table tr.shipping td a {
        font-size: inherit;
        line-height: inherit;
        font-weight: inherit;
        padding: 0;
        border: none;
        text-transform: inherit;
        letter-spacing: inherit;
    }

    .quantity .minus,
    .quantity .plus {
        display: none;
    }

    form.eael-woo-cart-form .eael-woo-cart-table {
        thead {
            display: table-header-group;
        }

        tbody tr td {
            display: table-cell;
            width: inherit;

            &:before {
                content: none;
            }
        }
    }
}

// Storefront Theme Compatibility
body.theme-storefront.eael-woo-cart .eael-woo-cart-wrapper {
    form.eael-woo-cart-form .shipping input {
        background: transparent;
        border: 1px solid #aaa;
        border-radius: 4px;
    }
}

// Neve Theme Compatibility
body.theme-neve.eael-woo-cart .eael-woo-cart-wrapper {
    a:hover {
        opacity: 1;
    }

    form.eael-woo-cart-form .product-thumbnail img {
        min-width: auto !important;
    }
}

// Twenty Twenty Theme Compatibility
body.theme-twentytwenty.eael-woo-cart .eael-woo-cart-wrapper {
    section {
        padding: 0;
    }

    a.button:hover {
        text-decoration: none !important;
    }
}

// Twenty Twenty-One Theme Compatibility
body.theme-twentytwentyone.eael-woo-cart .eael-woo-cart-wrapper {
    a.button:hover {
        text-decoration: none !important;
    }

    ul.woocommerce-error {
        padding: 0;
    }
}

//WoodMart Theme Compatibility
body.theme-woodmart.eael-woo-cart .eael-woo-cart-wrapper {
    form.eael-woo-cart-form .eael-woo-cart-table .product-quantity .quantity input.minus,
    form.eael-woo-cart-form .eael-woo-cart-table .product-quantity .quantity input.plus {
        display: none;
    }

    .cart-totals-inner {
        padding: 0;
        border: none;
    }
}

//Blocksy Theme Compatibility
.theme-blocksy{
    .eael-woo-cart-table{
        .product-quantity{
            .ct-increase,
            .ct-decrease{
                display: none;
            }
            .quantity input[type=number]{
                padding: 10px !important;
            }
        }
    }
}

// Responsive Tablet
@media (max-width: 1024px) {
    .eael-woo-cart .eael-woo-cart-wrapper {
        .eael-cart-coupon-and-collaterals {
            .cart-collaterals .cart_totals table tr {
                display: table-row;

                th {
                    display: table-cell;
                }

                td {
                    &::before {
                        content: none;
                    }
                }
            }
        }
    }

    .woocommerce-page #content table.cart.eael-woo-cart-table .product-thumbnail,
    .woocommerce-page table.cart.eael-woo-cart-table .product-thumbnail {
        display: table-cell;
    }
}

// Responsive Mobile
@media (max-width: 767px) {
    .eael-woo-cart .eael-woo-cart-wrapper {
        .eael-woo-cart-table-warp {
            width: 100%;
            overflow-x: auto;

            table {
                width: 700px;
            }
        }

        .eael-cart-coupon-and-collaterals,
        .eael-cart-clear-btn {
            .eael-cart-coupon-wrapper,
            .cart-collaterals {
                width: 100%;
                float: none;
            }

            .eael-cart-coupon-wrapper .eael-woo-cart-back-to-shop {
                margin-bottom: 25px;
            }

            .coupon input {
                padding-left: 5px;
            }

            button,
            .button,
            button:not(:hover):not(:active),
            .button:not(:hover):not(:active) {
                padding: 10px 20px;
            }
        }

        &.eael-woo-style-2 {
            &.has-table-left-content.has-table-right-content {
                background-image: none !important;
            }

            form.eael-woo-cart-form .eael-woo-cart-table {
                .eael-woo-cart-thead {
                    display: none;
                }

                .eael-woo-cart-tr {
                    display: block;

                    .product-thumbnail{
                        .eael-woo-cart-product-remove {
                            a {
                                position: absolute !important;
                                left: 0;
                                top: 0;
                            }
                        }
                    }

                    .eael-woo-cart-tr-left,
                    .eael-woo-cart-tr-right {
                        width: 100% !important;
                        display: block;
                        overflow: hidden;
                        padding: 15px;

                        .eael-woo-cart-td {
                            display: block;
                            width: 100%;
                            text-align: left;

                            &.product-thumbnail {
                                width: 270px;
                                height: 270px;
                                margin: 0 auto;
                                position: relative;

                                .eael-woo-cart-product-remove {
                                    display: block;
                                    position: absolute;
                                    top: 0;
                                    right: 0;
                                    height: 30px;
                                    width: 30px;
                                    text-align: center;
                                    line-height: 30px;
                                    border-radius: 50%;
                                    background: $white;
                                    border: 1px solid $black;
                                }
                            }

                            &.product-name {
                                width: 100% !important;
                            }

                            &.product-price,
                            &.product-quantity {
                                width: 48%;
                                display: inline-block;
                                vertical-align: middle;
                            }

                            &.product-quantity {
                                .quantity {
                                    justify-content: flex-start;
                                }
                            }

                            &.product-subtotal {
                                float: left;
                                padding-left: 50%;
                                position: relative;

                                &::before {
                                    content: attr(data-title);
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                }
                            }

                            &.product-remove {
                                display: none;
                            }
                        }
                    }

                    .eael-woo-cart-tr-right {
                        border-bottom: 1px solid #eaeef4;
                    }
                }
            }

            .eael-cart-coupon-and-collaterals {
                .eael-cart-coupon-wrapper,
                .cart-collaterals {
                    width: 100% !important;
                    padding-left: 15px;
                    padding-right: 15px;
                }
            }

            .eael-cart-clear-btn {
                width: 100% !important;
                padding-left: 15px;
                padding-right: 15px;
            }
        }
    }

    .theme-dt-the7{
        &.eael-woo-cart {
            .eael-woo-cart-wrapper {
                .eael-woo-cart-table-warp {
                    table.eael-woo-cart-table{
                        width: auto;
                        td{
                            width: auto !important;
                        }
                    }
                }
            }
        }
    }
}