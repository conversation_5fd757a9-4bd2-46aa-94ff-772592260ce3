.eael-wpforms {
	&.eael-contact-form{
		width: 100%;

		&:not(.eael-wpforms-align-default) {
			display: flex;
		}
		&.eael-wpforms-align-left {
			justify-content: start;
		}
		&.eael-wpforms-align-center {
			justify-content: center;
		}
		&.eael-wpforms-align-right {
			justify-content: end;
		}
		&.eael-wpforms-align-left,
		&.eael-wpforms-align-center,
		&.eael-wpforms-align-right {
			.wpforms-container-full {
				margin: 0;
			}
		}
	}
	.wpforms-container {
		.wpforms-form {
			input[type="submit"],
			button[type="submit"],
			.wpforms-page-button {
				border: 0;
				&:hover{
					border: 0;
				    background: none;
                }
			}

			input[type="checkbox"],
			input[type="radio"] {
				padding: 3px;
			}

			.wpforms-field-label {
				display: none;
			}

			.wpforms-field-name {
				.wpforms-field-row {
					max-width: 100%;
				}
			}
		}

		.wpforms-field {
			input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]),
			.wpforms-field textarea,
			.wpforms-field select {
				max-width: 100% !important;
                min-height: 43px;
			}
		}
	}
}

.eael-wpforms-labels-yes .wpforms-container .wpforms-form .wpforms-field-label {
	display: block;
}

.eael-wpforms-form-button-full-width .wpforms-submit-container .wpforms-submit {
	width: 100%;
}
