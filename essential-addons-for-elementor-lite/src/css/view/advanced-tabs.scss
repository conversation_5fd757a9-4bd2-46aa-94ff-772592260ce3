.eael-advance-tabs {
	display: block;
}

.eael-advance-tabs .eael-tabs-nav > ul {
	position: relative;
	padding: 0px;
	margin: 0px;
	list-style-type: none;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	z-index: 1;
}

.eael-advance-tabs .eael-tabs-nav > ul li {
	position: relative;
	padding: 1em 1.5em;
	flex: 1 1 auto;
	cursor: pointer;
	display: flex;
	justify-content: center;
	align-items: center;
	text-align: center;
	background-color: #f1f1f1;
}

.eael-advance-tabs .eael-tabs-nav > ul li .eael-tab-title {
	margin: unset;
	font-size: unset;
	line-height: unset;
	font-family: unset;
	font-weight: unset;
	color: unset;
}

.eael-advance-tabs .eael-tabs-nav > ul li:after {
	content: "";
	position: absolute;
	bottom: -10px;
	left: 0px;
	right: 0px;
	margin: 0 auto;
	z-index: 1;
	width: 0px;
	height: 0px;
	border-left: 10px solid transparent;
	border-right: 10px solid transparent;
	border-top: 10px solid #444;
	border-bottom: 0px;
	display: none;
}
.eael-advance-tabs .eael-tabs-nav > ul li:hover,
.eael-advance-tabs .eael-tabs-nav > ul li.active {
	background-color: #444;
}
.eael-advance-tabs  {
	.eael-tabs-nav > {
		ul {
			li {
				&:focus-visible{
					outline: 1px solid #a9d204 !important;
				}
			}
		}
	}
}
.eael-advance-tabs.active-caret-on .eael-tabs-nav > ul li.active:after {
	display: none;
}

.eael-advance-tabs .eael-tabs-nav > ul li.active:after {
	display: block;
}

.eael-tabs-content {
	display: flex;
	flex: 1 1 auto;
	overflow: hidden;
}

.eael-advance-tabs .eael-tabs-content > div {
	display: none;
	opacity: 0;
}

.eael-advance-tabs .eael-tabs-content > div.active {
	display: block;
	width: 100%;
	padding: 1em;
	opacity: 1;
	animation: fadeIn linear 0.3s;
}

/*--- Inline Icon ---*/
.eael-tab-inline-icon li a .fa {
	margin-right: 10px;
	line-height: 1;
}

.eael-tab-top-icon li {
	display: flex;
	flex-wrap: wrap;
	text-align: center;
}

.eael-tab-top-icon li a {
	display: flex;
	flex-wrap: wrap;
	text-align: center;
}

.eael-tab-top-icon li .fa,
.eael-tab-top-icon li .eael-tab-title {
	flex: 1 1 100%;
	line-height: 1;
}

.eael-tab-top-icon li a .eael-tab-title {
	margin-top: 10px;
}

/*--- Vertical Tabs ---*/
.eael-advance-tabs.eael-tabs-vertical {
	display: flex;
}

.eael-advance-tabs.eael-tabs-vertical > .eael-tabs-nav {
	flex: 0 0 auto;
}

.eael-advance-tabs.eael-tabs-vertical > .eael-tabs-nav > ul {
	flex-flow: column wrap;
}

.eael-advance-tabs.eael-tabs-vertical > .eael-tabs-nav > ul > li {
	width: 100%;
	justify-content: start;
}

.eael-advance-tabs.eael-tabs-vertical > .eael-tabs-nav > ul li::after {
	bottom: auto !important;
	right: -10px;
	top: calc(50% - 10px);
	left: auto !important;
	border-left: 10px solid #444;
	border-right: 0;
	border-top: 10px solid transparent;
	border-bottom: 10px solid transparent;
}

@media only screen and (max-width: 767px) {
	.eael-advance-tabs .eael-tabs-nav > ul {
		flex-wrap: wrap;
		flex-flow: row wrap;
	}

	.eael-advance-tabs .eael-tabs-nav > ul li {
		flex: 1 1 auto;
	}

	.responsive-vertical-layout {
		&.eael-advance-tabs {
			.eael-tabs-nav > ul li {
				flex: 1 1 100%;
			}
		}
	}

	.eael-advance-tabs.eael-tabs-vertical {
		flex-wrap: wrap;
	}

	.eael-advance-tabs.eael-tabs-vertical div.eael-tabs-nav {
		flex: 1 100%;
	}

	.eael-advance-tabs.eael-tabs-vertical ul {
		flex: 1 100%;
	}

	.eael-advance-tabs.eael-tabs-vertical ul li {
		flex: 1 100%;
	}

	div.eael-advance-tabs {
		&.eael-tabs-vertical {
			div.eael-tabs-nav {
				& > ul li::after {
					border-left-color: transparent !important;
					bottom: -20px !important;
					left: 0 !important;
					right: 0 !important;
					top: auto !important;
					border-left: 10px solid transparent;
					border-right: 10px solid transparent;
					border-top-color: #444 !important;
				}
			}
		}
	}
}

// rtl
.rtl {
	.eael-advance-tabs.eael-tabs-vertical > .eael-tabs-nav > ul li::after {
		border-right: 10px solid #444;
		border-left: 0;
	}
}
