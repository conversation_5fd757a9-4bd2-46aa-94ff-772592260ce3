.eael-caldera-form-align-left,
.eael-caldera-form-btn-align-left {
	text-align: left;
}

.eael-caldera-form-align-right,
.eael-caldera-form-btn-align-right {
	text-align: right;
}

.eael-caldera-form-align-center,
.eael-caldera-form-btn-align-center {
	text-align: center;
}

.eael-caldera-form .control-label {
	display: none;
}

.eael-caldera-form-labels-yes .control-label {
	display: block;
}

.eael-caldera-form-button-center .form-group input[type=button],
.eael-caldera-form-button-center .form-group input[type=submit] {
	display: block;
	margin: 0 auto;
}

.eael-caldera-form-button-right .form-group input[type=button],
.eael-caldera-form-button-right .form-group input[type=submit] {
	float: right;
}

.eael-caldera-form .intl-tel-input {
	display: inherit;
}

.eael-custom-radio-checkbox .caldera-grid input[type=checkbox],
.eael-custom-radio-checkbox .caldera-grid input[type=radio] {
	border-style: solid;
	border-width: 0;
	padding: 3px;
	-webkit-appearance: none;
}

.eael-caldera-form-button-full-width .form-group input[type=submit],
.eael-caldera-form-button-full-width .form-group input[type=button] {
	width: 100%;
}

.rtl {
	.eael-caldera-form-button-left .form-group input[type=button],
	.eael-caldera-form-button-left .form-group input[type=submit] {
		float: left;
	}
}