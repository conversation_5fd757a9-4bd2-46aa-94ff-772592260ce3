@import url('https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --page-background: #F5F6FC;
  --base-background: #FFFFFF;
  --background-1: #F9EFFB;
  --background-2: #F6EEFF;
  --background-3: #EFF8FF;
  --background-4: #E0FCF5;
  --background-5: #12B76A;
  --background-6: #FFAD4C;
  --background-7: #5262EE;
  --background-8: #FDFDFF;
  --background-9: #F1F2FA;
  --background-10: #FEFAFF;
  --background-11: #FBFCFF;
  --background-12: #FFFEFC;
  --background-13: #F9FAFB;
  --background-14: #F5F1FF;
  --background-15: #FFFCF5;
  --background-16: #FFF1F3;
  --background-17: #FFF6ED;
  --background-18: #FD853A;
  --toggle-color: #EAECF0;
  --label-color: #EAECF0;
  --label-color-fill: #7F56D9;
  --base-color-50: #750EF4;
  --base-color-75: #6F0AF2;
  --base-color-100: #FF9437;
  --base-color-200: #C01048;
  --text-color-25: #1D2939;
  --text-color-50: #344054;
  --text-color-100: #475467;
  --text-color-150: #937998;
  --text-color-200: #1570EF;
  --text-color-300: #959AA1;
  --text-color-500: #D0D5DD;
  --text-icon-100: #98A2B3;
  --text-icon-200: #2E90FA;
  --text-icon-300: #00C567;
  --text-icon-400: #AFB6C0;
  --text-icon-500: #667085;
  --text-icon-600: #FD853A;
  --text-icon-active: #E1C9FF;
  --border-color-base: #F2F4F7;
  --border-color-2: #F9F3F1;
  --border-color-3: #ED7206;
  --border-color-4: #EBEEF2;
  --shadow-color-1: #FFFFFF00;
  --shadow-color-6: #1018280F;
  --shadow-color-8: #00012314;
  --shadow-color-10: #1018281A;
  --shadow-color-12: #1B212C1F;
  --modal-bg-base: #F7FAFF;
  --modal-bg-2: #FFF7F7;
  --modal-bg-3: #F7F8FF;
  --modal-bg-4: #F9F9F9;
  --modal-bg-5: #F3F7FF;
  --gardient-text: #750EF4;
  --gardient-1: #fefbff;
  --gardient-2: #f4e4f6;
  --gardient-3: #dea8ff;
  --gardient-4: #fefaff;
  --gardient-5: #FBEEFF;
  --gardient-6: #FEFEFE;
  --gardient-7: #8725FF;
  --gardient-8: #EED6FF;
  --modal-overlay: #1D232733;
  --elements-1: #FAF5FF;
  --elements-2: #F1FFF2;
  --elements-3: #F6F9FF;
  --mode-switcher-icon: "\e905";
  --enhance-bg: url('../images/pro-elements-bg.png');
  --whats-new-bg: url('../images/img-4.png');
  --features-bg: url('../images/pro-bg.png');
}

body.eael_dash_dark_mode {
  --page-background: #09090A;
  --base-background: #18191B;
  --background-1: #302837;
  --background-2: #2C213A;
  --background-3: #182741;
  --background-4: #182B23;
  --background-5: #12B76A;
  --background-6: #FFAD4C;
  --background-7: #5262EE;
  --background-8: #18191B;
  --background-9: #101112;
  --background-10: #17111D;
  --background-11: #101112;
  --background-12: #18191B;
  --background-13: #09090A;
  --background-14: #09090A;
  --background-15: #09090A;
  --background-16: #FFF1F3;
  --background-17: #8E511636;
  --toggle-color: #2D2F34;
  --label-color: #1F2125;
  --label-color-fill: #7F56D9;
  --base-color-50: #750EF4;
  --base-color-75: #6F0AF2;
  --base-color-100: #FF9437;
  --base-color-200: #C01048;
  --text-color-25: #FFFFFF;
  --text-color-50: #D0D5DD;
  --text-color-100: #B1B4B9;
  --text-color-150: #8D809C;
  --text-color-200: #1570EF;
  --text-color-300: #8D809C;
  --text-color-500: #1F2125;
  --text-icon-100: #667085;
  --text-icon-200: #2E90FA;
  --text-icon-300: #00C567;
  --text-icon-400: #AFB6C0;
  --text-icon-500: #667085;
  --text-icon-600: #FD853A;
  --text-icon-active: #E1C9FF;
  --border-color-base: #1F2125;
  --border-color-2: #2A2D33;
  --border-color-3: #ED7206;
  --border-color-4: #18191B;
  --shadow-color-1: #18191B00;
  --shadow-color-6: #1018280F;
  --shadow-color-8: #00012314;
  --shadow-color-10: #1018281A;
  --shadow-color-12: #1B212C1F;
  --modal-bg-base: #09090A;
  --modal-bg-2: #09090A;
  --modal-bg-3: #09090A;
  --modal-bg-4: #09090A;
  --modal-bg-5: #09090A;
  --gardient-text: #FFFFFF;
  --gardient-1: #17111D;
  --gardient-2: #693787;
  --gardient-3: #693787;
  --gardient-4: #17111D;
  --gardient-5: #9743FF;
  --gardient-6: #151618;
  --gardient-7: #B477FF;
  --gardient-8: #4B00A7;
  --modal-overlay: #09090ACC;
  --elements-1: #2C213A;
  --elements-2: #182B23;
  --elements-3: #1B1E2F;
  --mode-switcher-icon: "\e903";
  --enhance-bg: url('../images/pro-elements-bg-dark.png');
  --whats-new-bg: url('../images/img-4-dark.png');
  --features-bg: url('../images/pro-bg-dark.png');
}

/* from old dashboard start */
a, a:hover {
  color: inherit;
}

label {
  margin-bottom: 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.3;
}

ul,
ol {
  padding: 0;
  margin: 0;
  padding-left: 18px;
}

img,
video {
  max-width: 100%;
}

a,
span {
  display: inline-block;
}

a:focus {
  box-shadow: none;
  outline: 0;
}

.eael-admin-promotion-message {
  background: #5E2EFF;
  color: white;
  padding: 15px 10px;
  position: relative;
}

.eael-admin-promotion-message p {
  font-size:14px;
  text-align:center;
}

.eael-admin-promotion-message p a {
  font-weight: bold;
}

.eael-admin-promotion-message p i {
  font-size: 15px;
  margin-right: 5px;
}

.eael-admin-promotion-message p a:focus {
  color:inherit;
  outline:none;
  box-shadow:none;
}
.eael-admin-promotion-close {
  top: 7px !important;
}
.eael-lock-style {
  position: absolute;
  right: 21px;
  color: #E8AA17;
  font-size: 12px;
}

.eael-admin-promotion-message i:before {
  color:white;
}
.toplevel_page_eael-settings #wpcontent {
  padding-left: 0;
}

ul#adminmenu>li.current>a.current:after {
  border-right-color: var(--page-background);
}
/* from old dashboard end */

body,
#ea__dashboard--wrapper {
  font-size: 16px;
  font-family: "Rubik", sans-serif;
  background: var(--page-background);
}

.eael_btn_loader {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  position: relative;
  animation: ea_rotate 1s linear infinite
}
.eael_btn_loader::before {
  content: "";
  box-sizing: border-box;
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 2px solid #FFF;
  animation: eaPrixClipFix 2s linear infinite ;
}

@keyframes ea_rotate {
  100%   {transform: rotate(360deg)}
}

@keyframes eaPrixClipFix {
  0%   {clip-path:polygon(50% 50%,0 0,0 0,0 0,0 0,0 0)}
  25%  {clip-path:polygon(50% 50%,0 0,100% 0,100% 0,100% 0,100% 0)}
  50%  {clip-path:polygon(50% 50%,0 0,100% 0,100% 100%,100% 100%,100% 100%)}
  75%  {clip-path:polygon(50% 50%,0 0,100% 0,100% 100%,0 100%,0 100%)}
  100% {clip-path:polygon(50% 50%,0 0,100% 0,100% 100%,0 100%,0 0)}
}

#ea__dashboard--wrapper {
  font-size: 16px;
  font-family: "Rubik", sans-serif;
  background: var(--page-background);
}

a {
  text-decoration: none;
  display: inline-flex;
}

img {
  max-width: 100%;
}

input:focus {
  outline: 0 !important;
  box-shadow: none !important;
}

.relative {
  position: relative;
}

.hidden {
  overflow: hidden;
}

.rotate-180 {
  transform: rotate(180deg);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.flex-end {
  justify-content: flex-end;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.min-h-full {
  min-height: 100vh;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.gap-5 {
  gap: 20px;
}

.gap-6 {
  gap: 24px;
}

.gap-10 {
  gap: 40px;
}

.mb-1 {
  margin-bottom: 4px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-3 {
  margin-bottom: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-5 {
  margin-bottom: 20px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mb-7 {
  margin-bottom: 28px;
}

.mb-8 {
  margin-bottom: 32px;
}

.mb-10 {
  margin-bottom: 40px;
}

.pointer {
  cursor: pointer;
}

.d-none {
  display: none;
}

.min-h-538 {
  min-height: 538px;
}

#ea__dashboard--wrapper .min-w-220 {
  min-width: 220px;
}

#ea__dashboard--wrapper .max-w-220 {
  max-width: 220px;
}

#ea__dashboard--wrapper .min-w-230 {
  min-width: 230px;
}

#ea__dashboard--wrapper .max-w-230 {
  max-width: 230px;
}

#ea__dashboard--wrapper .max-w-454 {
  max-width: 454px;
}

#ea__dashboard--wrapper .underline {
  text-decoration: underline;
}

#ea__dashboard--wrapper .primary-btn {
  font-size: 14px;
  font-weight: 450;
  line-height: 1.6em;
  font-family: "Rubik", sans-serif;
  color: var(--text-color-50);
  display: inline-flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  padding: 8px 20px;
  background: transparent;
  border-radius: 8px;
  border: 1px solid var(--text-color-100);
  transition: all .35s ease-in-out;
  cursor: pointer;
  position: relative;

}

#ea__dashboard--wrapper .primary-btn:hover {
  color: #ffffff;
  background: var(--base-color-75);
  border: 1px solid var(--base-color-75);

}

#ea__dashboard--wrapper .primary-btn.install-btn {
  color: #ffffff;
  border: 1px solid var(--base-color-75);
  background: conic-gradient(from 195.22deg at 68.31% 39.29%, rgba(143, 32, 251, 0) 0deg, #8F20FB 360deg), linear-gradient(0deg, #6F0AF2, #6F0AF2);
}

#ea__dashboard--wrapper .primary-btn.install-btn:hover {
  background: conic-gradient(from 226deg at 68.31% 39.29%, rgba(143, 32, 251, 0) 0deg, #8F20FB 360deg), linear-gradient(0deg, #6F0AF2, #6F0AF2);
}

#ea__dashboard--wrapper .primary-btn.install-btn[disabled] {
  cursor: not-allowed;
}

#ea__dashboard--wrapper button.primary-btn.changelog-btn i {
  font-size: 9px;
}

#ea__dashboard--wrapper button.primary-btn.install-btn i {
  font-size: 12px;
}

#ea__dashboard--wrapper button.upgrade-button {
  font-size: 14px;
  font-weight: 450;
  line-height: 1.5rem;
  font-family: "Rubik", sans-serif;
  color: #FFFFFF;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  padding: 10px 22px;
  background: var(--base-color-100);
  border-radius: 8px;
  border-bottom: 1px solid var(--border-color-3);
  transition: all .35s ease-in-out;
  cursor: pointer;
  position: relative;
  width: 100%;
}

#ea__dashboard--wrapper button.upgrade-button:hover {
  color: #FFFFFF;
  background: #FF8114;
  border-color: #CB5F00;
}

#ea__dashboard--wrapper button.upgrade-button i {
  font-size: 16px;
}

#ea__dashboard--wrapper button {
  font-size: 14px;
  font-weight: 450;
  line-height: 1.3em;
  font-family: "Rubik", sans-serif;
  background: transparent;
  border: none;
  color: var(--text-color-50);
  cursor: pointer;
  transition: all .35s ease-in-out;
  display: flex;
  align-items: center;
  gap: 4px;
}

#ea__dashboard--wrapper button:hover {
  color: var(--base-color-50);
}

#ea__dashboard--wrapper h3.ea__content-title {
  font-size: 18px;
  line-height: 1.2em;
  font-weight: 500;
  color: var(--text-color-25);
}

#ea__dashboard--wrapper h3.ea__content-title.title {
  margin-bottom: 16px;
}

#ea__dashboard--wrapper .eael-toggle-label {
  font-size: 14px;
  line-height: 1.2em;
  font-weight: 400;
  color: var(--text-color-25);
}

.ea__input-search-wrapper {
  position: relative;
  display: inline-flex;
}

#ea__dashboard--wrapper input.input-name {
  font-size: 14px;
  color: var(--text-color-100);
  line-height: 1.2em;
  border: none;
  border-right: 1px solid var(--label-color);
  border-radius: 0;
  padding-left: 16px;
  padding-right: 36px;
  width: 220px;
  background: transparent;
}

#ea__dashboard--wrapper input.input-name::placeholder {
  opacity: .7;
}

.ea__input-search-wrapper:after {
  content: '\e930';
  font-family: 'ea-dash-icon';
  position: absolute;
  top: 50%;
  right: 18px;
  transform: translateY(-50%);
  text-align: center;
  font-size: 14px;
  color: var(--text-icon-500);
}

#ea__dashboard--wrapper input.input-name:focus-visible {
  outline: 0;
}

#ea__dashboard--wrapper ::placeholder {
  color: var(--text-icon-500);
}

input.input-api[disabled]::placeholder {
  color: var(--text-icon-100) !important;
}

input.input-api::placeholder {
  color: var(--text-icon-100) !important;
}

input.input-api.verify::placeholder {
  color: var(--text-icon-500) !important;
}

input::placeholder {
  color: var(--text-icon-500) !important;
}

#ea__dashboard--wrapper .select-option-wrapper {
  position: relative;
}

#ea__dashboard--wrapper .form-select {
  color: var(--text-color-100);
  font-size: 14px;
  line-height: 1.5em;
  min-width: 140px;
  max-width: 146px;
  height: 44px;
  border: none;
  padding-left: 16px;
  padding-right: 35px;
  background: transparent;
  cursor: pointer;
  appearance: none;
  white-space: nowrap;
  text-overflow: ellipsis;
}

#ea__dashboard--wrapper .form-select:focus-visible,
#ea__dashboard--wrapper .form-select:focus {
  outline: 0;
  box-shadow: none;
}

#ea__dashboard--wrapper .select-option-wrapper:after,
#ea__dashboard--wrapper .select-option-external:after {
  content: '\e90a';
  font-family: 'ea-dash-icon';
  position: absolute;
  top: 50%;
  right: 14px;
  transform: translateY(-50%);
  text-align: center;
  font-size: 10px;
  color: var(--text-icon-500);
  cursor: pointer;
  pointer-events: none;
}

#ea__dashboard--wrapper .select-wrapper {
  margin-bottom: 30px;
}

/* Toggle css */

#ea__dashboard--wrapper .toggle-wrap {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 20px;
}

#ea__dashboard--wrapper .toggle-wrap input {
  opacity: 0;
  width: 0;
  height: 0;
}

#ea__dashboard--wrapper .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--toggle-color);
  border-radius: 12px;
  transition: ease-in-out .35s;
}

#ea__dashboard--wrapper .slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: var(--base-background);
  border-radius: 50%;
  transition: ease-in-out .5s;
  box-shadow: 0px 1px 2px 0px var(--shadow-color-6), 0px 1px 3px 0px var(--shadow-color-10);
}

#ea__dashboard--wrapper input:checked+.slider {
  background-color: var(--label-color-fill);
}

#ea__dashboard--wrapper input:checked+.slider:before {
  left: 18px;
  background-color: #FFFFFF;
}

#ea__dashboard--wrapper .slider.pro:before {
  content: '\e901';
  font-family: "ea-dash-icon";
  font-size: 10px;
  color: var(--text-color-100);
  display: flex;
  align-items: center;
  justify-content: center;
}

#ea__dashboard--wrapper .slider.ea-loader::before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background: transparent;
  box-shadow: none;
  box-sizing: border-box;
  border: 2px solid rgba(71, 75, 87, .2);
  border-top-color: var(--text-color-25);
  border-radius: 50%;
  transition: ease-in-out .1s;
  animation: ea_loading 1s infinite;
}

@keyframes ea_loading {
  from{
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

#ea__dashboard--wrapper input:checked+.slider.ea-loader {
  background-color: var(--label-color-fill);
}

#ea__dashboard--wrapper input:checked+.slider.ea-loader:before {
  left: 18px;
  background-color: var(--base-background);
  border-color: rgb(165 137 226);
  border-top-color: #421E90;
}

/*----- checkbox -------- */

.checkbox-item.checkbox-selected {
  position: relative;
  cursor: pointer;
  display: inline-flex;
  margin-bottom: 16px;
}

/* Hide the browser's default checkbox */
.checkbox-item.checkbox-selected input {
  display: none;
}

.e-notice__dismiss.eael-admin-promotion-close {
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: .8125rem;
  background: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50% !important;
  inset-inline-end: 10px;
  transform: translateY(-50%);
  border: none;
  margin: 0;
  padding: 0;
  cursor: pointer;
  font-style: normal;
}

.eael-admin-promotion-message p {
  font-size: 14px;
  text-align: center;
  padding-right: 20px;
}

/*-------- general-nav ------------*/

.ea__sidebar-nav-list {
  padding: 20px;
  background: var(--background-9);
  border: 1px solid var(--border-color-4);
  border-radius: 8px;
  min-width: 220px;
  max-width: 220px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.ea__sidebar-nav {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 20px;
  background: transparent;
  border-radius: 8px;
  transition: ease-in-out .25s;
}

.ea__nav-icon {
  display: inline-flex;
  font-size: 12px;
  color: var(--text-icon-100);
}

.ea__nav-icon i {
  font-weight: 700;
}

span.ea__nav-icon svg {
  width: 24px;
  height: 24px;
}

.ea__sidebar-nav .ea__nav-text {
  color: var(--text-color-50);
  font-size: 16px;
  font-weight: 450;
  transition: ease-in-out .25s;
}

.ea__sidebar-nav:hover {
  background: var(--page-background);
}

.ea__sidebar-nav.active {
  background: var(--base-color-50);
}

.ea__sidebar-nav.active .ea__nav-text {
  color: #ffffff;
}

.ea__sidebar-nav.active span.ea__nav-icon {
  color: var(--text-icon-active);
}

.ea__main-content-wrapper {
  flex: 1;
}

.ea__general-content-item {
  background: var(--base-background);
  border-radius: 8px;
  padding: 24px;
  border: 1px solid var(--border-color-base);
  margin-bottom: 16px;
  background-image: var(--whats-new-bg);
  background-repeat: repeat-y;
  background-position-x: right;
}

.ea__general-content-item.license-unlock {
  background-image: none;
}

.ea__general-content-item.license-unlock a {
  text-decoration: underline;
}

.ea__general-content-item.license-unlock .ea__others-icon {
  margin-bottom: 0;
}

.ea__general-content-item h3 {
  font-size: 22px;
  line-height: 1.2em;
  font-weight: 500;
  color: var(--text-color-25);
  margin-bottom: 18px;
}

.ea__general-content-item .ea__content-details {
  font-size: 16px;
  font-weight: 400;
  color: var(--text-color-100);
  line-height: 1.4em;
}

.ea__content-details .title--ex {
  font-weight: 500;
  color: var(--text-color-300);
  padding-right: 4px;
}

.ea__content-details .title--ex:empty {
  display: none;
}

.ea__general-content-item h2 {
  font-size: 26px;
  line-height: 1.2em;
  font-weight: 500;
  color: var(--text-color-25);
  margin-bottom: 12px;
}

.ea__section-wrapper {
  max-width: 1144px;
  margin: 0 auto;
}

.ea__section-header {
  background: var(--base-background);
  width: auto;
  position: sticky;
  left: 0;
  right: 0;
  top: 31px;
  z-index: 999;
}

.ea__header-content {
  max-width: 1144px;
  margin: 0 auto;
  padding: 15px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ea__header-content img {
  height: 40px;
}

span.dark-icon {
  width: 36px;
  height: 36px;
  background: var(--page-background);
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--text-color-100);
}

.ea__section-wrapper.ea__main-wrapper {
  padding-top: 50px;
  padding-bottom: 16px;
}

.ea__sidebar-info {
  max-width: 232px;
  min-width: 232px;
}

.ea__sidebar-sticky {
  position: sticky;
  top: 92px;
}

.ea__sidebar-content {
  padding: 24px;
  background: var(--base-background);
  border-radius: 8px;
  border: 1px solid var(--border-color-base);
  margin-bottom: 16px;
  background-image: var(--features-bg);
  background-repeat: no-repeat;
  background-position: top;
}

.ea__content-details span.check-icon {
  width: 18px;
  height: 18px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: var(--background-1);
  font-size: 9px;
  font-weight: 600;
  color: var(--text-color-150);
  border-radius: 4px;
}

.templates-content {
  padding: 24px 0 24px 24px;
  max-width: 290px;
}

.video-promo-wrapper .templates-content {
  padding: 0;
  padding-left: 8px;
  padding-right: 8px;
}

.ea__general-content-item.templates {
  padding: 0;
  background-image: none;
  background-color: var(--background-10);
  border: 1px solid var(--border-color-base);
  border-radius: 8px;
}

.ea__templates-content-wrapper {
  background: linear-gradient(282.7deg, var(--gardient-3) -7.9%, var(--gardient-4) 35.7%);
  border-radius: 8px;
  gap: 50px;
}

.ea__connect-others-wrapper .ea__connect-others p a {
  text-decoration: underline;
  transition: ease-in-out .35s;
}

.ea__connect-others-wrapper .ea__connect-others p a:hover {
  color: var(--base-color-50);
}

.templates-img {
  display: inline-flex;
  padding: 12px 12px 12px 0;
  flex: 1;
}

.video-promo-wrapper .templates-img {
  padding: 0;
  position: relative;
}

.video-promo-wrapper .templates-img span {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3A04A966;
}

.video-promo-wrapper .templates-img span i {
  /*position: absolute;*/
  width: 32px;
  height: 32px;
  font-size: 12px;
  color: #750EF4;
  background: #FFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.templates-img a {
  flex: 1;
}

.templates-img img {
  border-radius: 0 8px 8px 0;
  width: 100%;
  min-width: 200px;
  min-height: 280px;

}

.ea__connect-others {
  padding: 24px;
  background: var(--base-background);
  border: 1px solid var(--border-color-base);
  border-radius: 8px;
}

.ea__connect-others:not(:last-child) {
  margin-bottom: 16px;
}

.ea__tools-content-wrapper .ea__connect-others {
  margin-bottom: 0;
}

.ea__connect-others-wrapper .ea__connect-others {
  margin-bottom: 0;
  width: 100%;
}

.ea__others-icon {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  margin-bottom: 16px;
}

.ea__tools-content-wrapper .ea__others-icon {
  margin-bottom: 0;
}

.ea__others-icon.eaicon-unlock {
  color: var(--base-color-200);
  background: var(--background-16);
}

.ea__others-icon.eaicon-1 {
  color: var(--base-color-50);
  background: var(--background-2);
}

.ea__others-icon.eaicon-2 {
  color: var(--text-icon-200);
  background: var(--background-3);
}

.ea__others-icon.eaicon-3 {
  color: var(--text-icon-300);
  background: var(--background-4);
}

.ea__others-icon.eaicon-4 {
  color: var(--text-icon-600);
  background: var(--background-17);
}

.ea__connect-others h5 {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.2em;
  color: var(--text-color-25);
  margin-bottom: 10px;
}

.ea__connect-others p {
  font-size: 14px;
  line-height: 1.5em;
  color: var(--text-color-100);
}

.ea__sidebar-content h5 {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.1em;
  color: var(--text-color-25);
  padding-top: 46px;
  margin-bottom: 8px;
}

.ea__sidebar-content p {
  font-size: 14px;
  line-height: 1.5em;
  color: var(--text-color-100);
  margin-bottom: 36px;
}

.review-wrap {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border-color-2);
}

.review-wrap h6 {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.1em;
  color: var(--text-color-25);
}

.review-wrap i {
  font-size: 14px;
  line-height: 1.1em;
  color: var(--base-color-100);
}

.review-wrap .reating-details {
  font-size: 14px;
  color: var(--text-color-25);
  line-height: 1.1em;
  padding-top: 6px;
  padding-left: 22px;
}

.ea__pro-elements-content .review-wrap {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}

.ea__pro-elements-content .review-wrap h6 {
  font-size: 18px;
}

.ea__pro-elements-content .review-wrap i {
  font-size: 16px;
}

.ea__pro-elements-content .review-wrap .reating-details {
  font-size: 18px;
  padding-top: 0;
  padding-left: 0;
  margin-left: -4px;
}

.ea__pro-elements-content .review-wrap button.upgrade-button i {
  color: #FFFFFF;
}

.ea__connect-others button i {
  font-size: 9px;
  font-weight: 600;
}

.ea__elements-wrapper {
  background: var(--elements-1);
  border-radius: 4px;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ea__elements-wrapper:not(:last-child) {
  margin-bottom: 10px;
}

.ea__elements-wrapper.elements-1 {
  background:  var(--elements-1);
}

.ea__elements-wrapper.elements-2 {
  background:  var(--elements-2);
}

.ea__elements-wrapper.elements-3 {
  background:  var(--elements-3);
}

.ea__elements-wrapper i {
  font-size: 10px;
  width: 20px;
  height: 20px;
  background: var(--base-background);
  border-radius: 4px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.ea__elements-wrapper.elements-1 i {
  color: var(--base-color-50);
}

.ea__elements-wrapper.elements-2 i {
  color: #00C567;
}

.ea__elements-wrapper.elements-3 i {
  color: #717BBC;
}

.ea__elements-wrapper h4 {
  font-size: 16px;
  line-height: 1.2em;
  font-weight: 500;
  color: var(--text-color-25);
}

.ea__elements-wrapper span {
  font-size: 14px;
  line-height: 1.4em;
  color: var(--text-color-100);
}

/*-------- elements-nav ------------*/

.ea__sidebar-nav-list.ea__elements-nav {
  border-style: solid;
  border-color: var(--border-color-4);
  border-width: 1px;
  border-radius: 8px 0 0 8px;
}

.nav-sticky {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: sticky;
  top: 116px;
  left: 0;
  right: 0;
}

.ea__elements-nav-content {
  flex: 1;
}

.ea__elements-nav-content.elements-contents {
  background: var(--background-9);
  padding-bottom: 16px;
  border-radius: 0 8px 8px 0;
}

.ea__elements-nav-content.elements-contents .ea__content-elements-wrapper {
  background: transparent;
}

.ea__content-header.sticky {
  position: sticky;
  left: 0;
  right: 0;
  top: 100px;
  z-index: 99;
}

.ea__section-overlay {
  position: absolute;
  min-height: 75px;
  background: linear-gradient(360deg,var(--base-background) 78%,var(--shadow-color-1) 100%);
  bottom: 0;
  left: 16px;
  right: 16px;
  border-radius: 8px;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.ea__elements-button-wrap {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px 16px;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
}

#ea__dashboard--wrapper .ea__elements-button-wrap .primary-btn.install-btn {
  z-index: 9999;
  margin-right: 12px;
}

.ea__content-header.sticky .ea__content-info {
  border-style: solid;
  border-width: 1px 1px 0 1px;
  border-color: var(--label-color);
}

.ea__content-info {
  padding: 24px;
  background: var(--base-background);
  border-radius: 0 8px 0 0;
}

.search--widget {
  border: 1px solid var(--label-color);
  border-radius: 8px;
}

.ea__widget-elements h4 {
  font-size: 18px;
  font-weight: 500;
  line-height: 1em;
  color: var(--text-color-25);
  padding-right: 50px;
}

.toggle-wrapper h5 {
  font-size: 14px;
  font-weight: 450;
  line-height: 1em;
  color: var(--text-color-25);
}

.ea__content-icon {
  background: var(--page-background);
}

.ea__icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 25px 0;
  flex-grow: 1;
  border-style: solid;
  border-color: var(--label-color);
  border-width: 1px 0px 1px 1px;
  font-size: 20px;
  color: var(--text-icon-100);
  cursor: pointer;
  position: relative;
}

.ea__icon-wrapper.active {
  background: linear-gradient(180deg, var(--base-background) 15%, var(--page-background) 100%);
  border-color: var(--page-background);
  border-width: 0 0 1px 1px;
}

.ea__icon-wrapper.active:after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 2px;
  background: var(--base-color-50);
}

.ea__icon-wrapper.active i {
  color: var(--base-color-50);
}

.ea__icon-wrapper:nth-last-child(1) {
  border-right: 1px solid var(--label-color);
}

.ea__icon-wrapper i {
  font-size: 20px;
  color: var(--text-icon-100);
  transition: all .30s ease-in-out;
}

.ea__icon-wrapper i:hover span.ea__tooltip {
  opacity: 1;
  visibility: visible;
}

span.ea__tooltip:after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translate(-50%);
  border-top: 8px solid var(--base-background);
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
}

span.ea__tooltip {
  position: absolute;
  left: 50%;
  top: -20px;
  transform: translate(-50%);
  min-width: 166px;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.2em;
  color: var(--text-color-25);
  font-family: "Rubik", sans-serif;
  text-align: center;
  padding: 8px 4px;
  box-shadow: 0px 8px 16px -2px var(--shadow-color-12);
  background: var(--base-background);
  border-radius: 4px;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
}

.ea__content-elements-wrapper {
  padding: 16px 16px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: var(--background-9);
}

.ea__contents {
  flex: 1;
  padding: 16px;
  background: var(--base-background);
  border: 1px solid var(--border-color-base);
  border-radius: 8px;
}

.ea__not-found-wrapper {
  max-width: 260px;
  margin: auto;
  padding: 100px 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

.ea__not-found-wrapper img {
  max-width: 172px;
}

.ea__not-found-wrapper h5 {
  font-size: 20px;
  font-weight: 500;
  color: var(--text-color-100);
  line-height: 1.2em;
  text-align: center;
}

.ea__content-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.ea__content-items {
  background: var(--base-background);
  padding: 16px;
  border: 1px solid var(--border-color-base);
  border-radius: 8px;
  transition: all .25s ease-in-out;
}

.ea__content-items:hover {
  box-shadow: 0px 7px 18px 0px var(--shadow-color-8);
}

.ea__content-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.ea__content-footer {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  align-items: center;
  margin-top: 10px;
}

.ea__content-footer.ea-no-label {
  justify-content: end;
}

.content-btn {
  font-size: 11px;
  line-height: 11px;
  font-weight: 500;
  color: var(--base-background);
  text-transform: uppercase;
  padding: 0 4px 0;
  border-radius: 4px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-btn.updated {
  background: var(--background-6);
}

.content-btn.new {
  background: var(--background-5);
}

.content-btn.popular {
  background: var(--background-7);
}

.content-btn.beta {
  background: var(--background-18);
}

.content-icons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.content-icons i {
  font-size: 11px;
  color: var(--text-icon-400);
  cursor: pointer;
}

.ea__extension-main-wrap .ea__section-wrapper.ea__main-wrapper {
  padding-bottom: 16px;
}

.ea__tools-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ea__connect-others label {
  font-size: 16px;
  line-height: 1.5em;
  font-weight: 500;
  color: var(--text-color-25);
  min-width: 153px;
}

.select-option-external {
  position: relative;
  border: 1px solid var(--text-color-100);
  border-radius: 8px;
}

#ea__dashboard--wrapper .select-option-external .form-select {
  min-width: 100%;
}

.select-post-type-options{
  margin-top: 20px;
}
.select-post-type-options li label:hover{
  cursor: pointer;
}

span.select-details {
  font-size: 14px;
  line-height: 1.2em;
  font-style: italic;
  color: var(--text-icon-100);
  margin-top: 12px;
  display: inline-block;
}

.ea__integration-content-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.ea__integration-item {
  display: flex;
  flex-direction: column;
}

.ea__integration-header {
  padding: 20px 24px;
  background: var(--background-11);
  border-radius: 8px 8px 0 0;
  border: 1px solid var(--border-color-base);
}

.ea__integration-header h5 {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.2em;
  color: var(--text-color-25);
}

.ea__integration-footer {
  padding: 24px;
  background: var(--base-background);
  border-radius: 0 0 8px 8px;
  border-color: var(--border-color-base);
  border-style: solid;
  border-width: 0 1px 1px 1px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.ea__integration-footer p {
  font-size: 14px;
  line-height: 1.5em;
  color: var(--text-color-100);
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color-base);
  flex: 1;
}

.integration-settings {
  padding-top: 16px;
}

/* --------- Slide css-------- */

.ea__slider-connect {
  width: 100%;
  overflow: hidden;
  position: relative;
  background: var(--base-background);
  border-radius: 8px;
  padding: 24px 24px 40px;
}

.ea__slider-connect .swiper-wrapper {
  max-width: 860px;
}

.swiper-slide {
  display: flex;
}

.ea__premium-item {
  border: 1px solid var(--border-color-base);
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
}

.ea__premium-item-footer {
  padding: 16px;
}

.ea__premium-item-footer h5 {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.2em;
  color: var(--text-color-25);
  margin-bottom: 10px;
}

.ea__premium-item-footer p {
  font-size: 14px;
  line-height: 1.5em;
  color: var(--text-color-100);
}

.ea__slider-connect .swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: -4px;
}

.ea__slider-connect .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet {
  width: 12px;
  height: 3px;
  border-radius: 2px;
}

.ea__slider-connect .swiper-pagination-bullets.swiper-pagination-horizontal .swiper-pagination-bullet-active {
  background: #5E2EFF;
}

.features-widget-wrapper {
  padding-left: 24px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
}

.features-widget-item {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FFF;
  width: 50px;
  height: 50px;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
}

.features-widget-item span.eael-tooltip {
  position: absolute;
  left: 50%;
  top: -20px;
  min-width: 125px;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: 400;
  line-height: 1.2em;
  color: #1D2939;
  font-family: "Rubik", sans-serif;
  text-align: center;
  padding: 8px 4px;
  box-shadow: 0 7px 22px 0 #00012329;
  background: #FFF;
  border-radius: 4px;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
}

.features-widget-item:hover span.eael-tooltip {
  opacity: 1;
  visibility: visible;
}

span.eael-tooltip:after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translate(-50%);
  border-top: 6px solid #FFF;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
}

.ea__feature-list-item p {
  font-size: 16px;
  line-height: 1em;
  color: var(--text-color-25);
}

#ea__dashboard--wrapper .primary-btn i {
  font-size: 9px;
}

.ea__pro-features {
  border: 1px solid var(--border-color-base);
  border-radius: 8px;
  background: linear-gradient(89.99deg, var(--gardient-1) 63.58%, var(--gardient-2) 115.99%);
  padding: 24px;
}

.ea__features-content {
  max-width: 550px;
  padding-right: 16px;
}

.ea__features-content h2 {
  font-size: 26px;
  line-height: 1.2em;
  font-weight: 500;
  color: var(--text-color-25);
  margin-bottom: 12px;
}

.ea__features-content p {
  font-size: 16px;
  line-height: 1.5em;
  font-weight: 400;
  color: var(--text-color-100);
}

.ea__features-content .ea__feature-list-item i {
  font-size: 14px;
  color: var(--base-color-50);
  line-height: 1.6em;
}

.ea__features-content .ea__feature-list-item p {
  color: var(--text-color-25);
}

.ea__premium-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ea__premium-content-wrapper .ea__connect-others p {
  margin-bottom: 0;
}

.ea__tools-content-wrapper .ea__connect-others p {
  margin-bottom: 0;
}

.ea__pro-elements-content {
  padding: 48px;
  background: var(--background-12);
  border-radius: 8px;
  background-image: var(--enhance-bg);
  background-repeat: no-repeat;
  background-position: top;
  background-size: cover;
}

.ea__pro-elements-content .ea__active-user-wrapper {
  /*background: linear-gradient(90deg, #FBEEFF 0%, #FEFEFE 100%);*/
  /*border-radius: 8px;*/
  margin-bottom: 8px;
  position: relative;
  z-index: 0;
  display: inline-block;
}

.ea__pro-elements-content .ea__active-users {
  font-size: 16px;
  line-height: 1em;
  font-weight: 400;
  color: var(--gardient-text);
  background: linear-gradient(90deg, var(--gardient-5) 0%, var(--gardient-6) 100%);
  padding: 6px 8px;
  border-radius: 7px;
}
.ea__pro-elements-content .ea__active-user-wrapper::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  z-index: -1;
  border-radius: 8px;
  background: linear-gradient(90deg, var(--gardient-7) 0%, var(--gardient-8) 100%);
}

.ea__pro-elements-content h3 {
  font-size: 32px;
  line-height: 1.37em;
  font-weight: 400;
  color: var(--text-color-25);
  padding-bottom: 60px;
}

.ea__pro-elements-content h3 span.Advance-color {
  font-weight: 600;
  color: var(--base-color-100);
}

.ea__pro-elements-content h3 b {
  font-weight: 600;
}

/*--------- Modal ---------*/

section.ea__modal-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--modal-overlay);
  z-index: 99999;
  font-family: "Rubik", sans-serif;
}

.ea__modal-content-wrapper {
  max-width: 663px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--base-background);
  border-radius: 8px;
  border: 1px solid var(--label-color);
}

.ea__modal-content-wrapper.go-premium-wrapper {
  max-width: 444px;
}

.ea__modal-content-wrapper.go-premium-wrapper .ea__modal-body {
  width: 444px;
}

.ea__modal-header {
  padding: 24px 40px;
  border-bottom: 1px solid var(--label-color);
}

.ea__modal-header h5 {
  font-size: 16px;
  line-height: 1.1em;
  font-weight: 400;
  color: var(--text-color-100);
}

.ea__modal-body {
  padding: 40px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: calc(90vh - 170px);
  overflow: auto;
  width: 653px;
}

.ea__modal-body h4 {
  font-size: 18px;
  line-height: 1.2em;
  font-weight: 450;
  color: var(--text-color-25);
}

.ea__modal-body .go-premium-wrapper h3 {
  font-size: 22px;
  line-height: 1.2em;
  font-weight: 450;
  color: var(--text-color-25);;
}


.ea__modal-body p {
  font-size: 14px;
  line-height: 1.5em;
  color: var(--text-color-100);
  margin-bottom: 16px;
}

.ea__modal-body p.pro--content {
  font-size: 16px;
  text-align: center;
  margin-bottom: 8px;
}

.ea__modal-body .ea__feature-list-item p {
  font-size: 16px;
  color: var(--text-color-25);
  margin-bottom: 0;
}

.ea__modal-body .ea__feature-list-item span {
  line-height: 1.7em;
}

.ea__modal-body label {
  font-size: 14px;
  line-height: 1.3em;
  color: var(--text-color-100);
  display: block;
  margin-bottom: 6px;
}

.ea__modal-body .ea__api-key-according label {
  min-width: 153px;
  margin-bottom: 0;
}

.ea__modal-body .ea__api-key-according label.toggle-wrap {
  min-width: auto;
}

.ea__modal-body p.info--text {
  font-size: 12px;
  color: var(--text-icon-500);
  margin-left: 18px;
  position: relative;
}

.ea__modal-body p.info--text:before {
  position: absolute;
  content: '\e924';
  font-family: 'ea-dash-icon';
  left: -18px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  font-size: 7px;
  color: var(--text-icon-500);
  border: 1px solid var(--text-icon-500);
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.ea__modal-body .ea__hide-badge {
  margin-top: 12px;
}

.ea__modal-body .ea__hide-badge input[type=checkbox] {
  border: 1.5px solid var(--text-color-25);
  border-radius: 4px;
  box-shadow: none;
  margin: 0;
}

.ea__modal-body .ea__hide-badge label {
  font-size: 15px;
  color: var(--text-color-25);
  display: flex;
  align-items: center;
  gap: 4px;
}

.ea__modal-body .ea__hide-badge label i {
  width: 14px;
  height: 14px;
  font-size: 7px;
  color: var(--text-icon-500);
  border: 1px solid var(--text-icon-500);
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

#ea__dashboard--wrapper .ea__modal-body input.input-name {
  padding: 10px 14px;
  background: var(--background-8);
  border-radius: 8px;
  border: 1px solid var(--label-color);
  width: 100%;
}

.ea__modal-body .ea__api-link {
  font-size: 14px;
  line-height: 1.2em;
  color: var(--text-color-200);
  text-decoration: underline;
  margin-top: 6px;
}

.ea__modal-body .regenerated-wrapper {
  width: 573px;
}

.ea__modal-body .regenerated-wrapper h4 {
  margin-bottom: 16px;
}

.ea__modal-body .regenerated-wrapper p {
  font-size: 16px;
}

#ea__dashboard--wrapper .ea__modal-footer button.upgrade-button {
  width: auto;
}

.ea__modal-footer .ea__api-link {
  font-size: 14px;
  line-height: 1.2em;
  color: var(--text-color-200);
  text-decoration: underline;
}

#ea__dashboard--wrapper .ea__modal-body .select-option-wrapper {
  width: 100%;
  position: relative;
}

#ea__dashboard--wrapper .ea__modal-body .form-select {
  color: var(--text-color-100);
  font-size: 14px;
  line-height: 1.5em;
  width: 100%;
  min-width: 100%;
  height: 44px;
  border: 1px solid var(--label-color);
  border-radius: 8px;
  padding: 0 16px;
  background: transparent;
  cursor: pointer;
  appearance: none;
  position: relative;
}

.ea__api-key-according {
  background: var(--modal-bg-base);
  border-radius: 8px;
  padding-left: 16px;
  padding-right: 16px;
  width: 100%;
}

.ea__api-key-according:nth-child(3) {
  background: var(--modal-bg-2);
}

.ea__api-key-according:nth-child(4) {
  background: var(--modal-bg-3);
}

.ea__api-key-according:nth-child(5) {
  background: var(--modal-bg-4);
}

.ea__api-key-according:nth-child(6) {
  background: var(--modal-bg-5);
}

.ea__api-key-according span.ea__tooltip {
  position: absolute;
  left: 50%;
  top: -40px;
  transform: translate(-50%);
  min-width: 175px;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  color: var(--text-color-25);
  font-family: "Rubik", sans-serif;
  text-align: center;
  padding: 8px 12px;
  box-shadow: 0px 4px 14px 0px #00012314;
  background: var(--base-background);
  border-radius: 4px;
  opacity: 0;
  visibility: hidden;
}

.ea__according-title {
  padding-top: 16px;
  padding-bottom: 16px;
}

.ea__according-title i.ea-dash-icon.ea-info {
  width: 22px;
  height: 22px;
  background: var(--base-background);
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: 8px;
  border: 4px solid #E8F1F9;
  margin-left: 14px;
  cursor: pointer;
}

.ea__according-title i {
  font-size: 12px;
  color: #000;
  transition: all .35s ease-in-out;
}

.ea__according-content {
  border-top: 1px solid var(--label-color);
  transition: all .35s ease-in-out;
  opacity: 0;
  visibility: hidden;
  max-height: 0;
}

.ea__according-content.accordion-show {
  padding-top: 16px;
  padding-bottom: 16px;
  opacity: 1;
  visibility: visible;
  max-height: 500px;
}

.ea__modal-footer {
  padding: 16px 40px;
  border-top: 1px solid var(--label-color);
}

#ea__dashboard--wrapper button.ea__modal-btn {
  font-size: 14px;
  font-weight: 450;
  line-height: 1.6em;
  font-family: "Rubik", sans-serif;
  padding: 8px 20px;
  border-radius: 8px;
  transition: all .35s ease-in-out;
  cursor: pointer;
  position: relative;
  color: #FFFFFF;
  background: var(--base-color-75);
  border: 1px solid var(--base-color-75);
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.ea__modal-close-btn {
  position: absolute;
  top: 0;
  right: -55px;
  width: 40px;
  height: 40px;
  background: var(--border-color-base);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color-100);
  cursor: pointer;
}

.ea__according-content .ea__hide-badge i:hover span.ea__tooltip {
  opacity: 1;
  visibility: visible;
}

.ea__according-content .ea__hide-badge i {
  position: relative;
  transition: all .35s ease-in-out;
}

.ea__according-content .ea__hide-badge i span.ea__tooltip {
  width: 330px;
  top: -60px;
}

.ea__according-content .ea__hide-badge i span.ea__tooltip:after {
  top: 50px;
}

.ea__according-content .ea__hide-badge i span .color--exx {
  color: #1570EF;
}

/*-------- Onboard ------------*/

span.title-color-1 {
  color: #FF7B8E;
}

.ea__templately-details .ea__content-details {
  padding: 16px;
  border-radius: 4px;
}

.ea__templately-details .ea__content-details:nth-child(1) {
  background: #F7F4FF;
}

.ea__templately-details .ea__content-details:nth-child(2) {
  background: #FFFAFA;
}

.ea__templately-details .ea__content-details:nth-child(3) {
  background: #FFF8F0;
}

.ea__templately-details .ea__content-details:nth-child(4) {
  background: #F1FAFF;
}

.ea__general-content-item h4 {
  font-size: 22px;
  font-weight: 500;
  line-height: 1.1em;
  color: var(--text-color-25);
  margin-bottom: 8px;
}

.ea__general-content-item p {
  font-size: 16px;
  font-weight: 400;
  color: var(--text-color-100);
  line-height: 1.5em;
}

.ea__general-content-item.video-promo {
  padding: 0;
  background: var(--background-10);
  border: 1px solid var(--border-color-base);
}

.ea__general-content-item.video-promo h2 {
  font-size: 24px;
  margin-bottom: 16px;
}

.ea__general-content-item.video-promo p {
  font-size: 14px;
}

.video-promo-wrapper {
  padding: 16px;
  background: linear-gradient(282.7deg, var(--gardient-3) -7.9%, var(--gardient-4) 35.7%);
  border-radius: 8px;
}

.ea__general-content-item.video-promo .templates-img {
  width: 100%;
}

.ea__general-content-item.video-promo .templates-img img {
  border-radius: 0;
  padding-left: 0;
  height: 100%;
}

/*-------- license-item ------------*/

.ea__license-wrapper {}

.ea__license-wrapper .ea__license-content {
  display: inline-flex;
  align-items: center;
  margin: 16px 0 16px 56px;
  cursor: pointer;
}

.ea__license-wrapper .ea__license-content h5 {
  color: var(--text-icon-100);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.4em;
  text-decoration: underline;
  margin-right: 12px;
}

.ea__license-wrapper .ea__license-content i {
  width: 24px;
  height: 24px;
  background: var(--border-color-base);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: 600;
  transition: all .35s ease-in-out;
}

.ea__license-options-wrapper {
  padding-top: 24px;
  border-top: 1px solid var(--label-color);
}

.ea__license-step {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding-bottom: 24px;
}

.ea__license-step-items {
  position: relative;
}

.ea__license-step-items:not(:last-child):after {
  content: '';
  position: absolute;
  bottom: -19px;
  left: 24px;
  width: 1px;
  height: 19px;
  background: var(--text-color-500);
}

.ea__license-step-items:not(:last-child):before {
  content: '';
  position: absolute;
  bottom: -19px;
  left: 22px;
  width: 5px;
  height: 5px;
  border-style: solid;
  border-width: 0 1px 1px 0;
  border-color: var(--text-color-500);
  transform: rotate(45deg);
}

span.step-count {
  width: 48px;
  padding: 12px 0;
  background: var(--background-2);
  border-radius: 8px 0 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--base-color-50);
}

.ea__license-step p {
  background: var(--background-13);
  border-radius: 0 8px 8px 0;
  flex: 1;
  padding: 12px 16px;
}

.step-details-ex {
  color: var(--base-color-50);
}

.step-details-ex:hover {
  color: var(--base-color-50);
}

.ea__license-key {
  background: var(--background-14);
  padding: 24px;
  border-radius: 8px;
}

.ea__license-key.ea__invalid-license {
  background: #FEF3F2;
}

.ea__license-key.ea__invalid-license .license-key-items {
  border: 1px solid #F97066;
}

.invalid-text {
  margin-top: 8px;
  gap: 6px;
}

.invalid-text span {
  font-size: 12px;
  line-height: 1em;
  font-weight: 400 !important;
  color: #7A271A;
}

.license-key-items {
  background: var(--base-background);
  border-radius: 8px;
  border: 1px solid var(--border-color-base);
  padding: 4px 6px 4px 0;
  position: relative;
}

.license-key-items i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: var(--base-color-50);
}

input.input-api {
  font-size: 16px;
  color: var(--text-icon-500);
  border: none;
  background: transparent;
  outline: 0;
  padding-left: 54px;
  padding-right: 15px;
  flex: 1;
}

input.input-api:disabled {
  box-shadow: none;
  color: var(--text-icon-100) !important;
}

.ea__license-verify {
  padding: 24px;
  background: var(--background-15);
  border-radius: 8px;
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.ea__license-verify.warning {
  background: #FEF3F2;
}

.ea__license-verify.warning .license-key-items {
  border: 1px solid #F97066;
}

.ea__general-content-item .ea__license-verify .resend-content {
  font-size: 16px;
  font-weight: 400;
  color: var(--text-icon-500);
  line-height: 1.5em;
}

.ea__license-verify input.input-api {
  padding-left: 15px;
}

#ea__dashboard--wrapper .primary-btn.verify-btn {
  padding: 8px 14px;
  border: 1px solid #D0D5DD;
  box-shadow: 0px 1px 2px 0px #1018280D;
}

#ea__dashboard--wrapper .primary-btn.verify-btn:hover,
#ea__dashboard--wrapper .primary-btn.verify-btn.active {
  border: 1px solid var(--base-color-50);
  background: var(--base-color-50);
  color: var(--base-background);
}

#ea__dashboard--wrapper .primary-btn.install-btn.deactivated {
  color: var(--base-background);
  border: 1px solid #D92D20;
  background: #D92D20;
}

.resend-text {
  color: #1570EF;
  font-weight: 500;
  text-decoration: underline;
  cursor: pointer;
}

.resend-content .info-icon-wrap {
  margin-left: 4px;
  display: inline-flex;
  position: relative;
  cursor: pointer;
  vertical-align: top;
}

.resend-content .info-icon-wrap i {
  background: var(--base-color-50);
  border: 4px solid var(--background-2);
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: var(--base-background);
  font-weight: 700;
}

.tooltip-api {
  position: absolute;
  top: 30px;
  right: -47px;
  background: var(--base-background);
  width: 450px;
  height: 52px;
  padding: 8px 12px;
  box-shadow: 0px 4px 14px 0px #00012314;
  border-radius: 8px;
  font-size: 12px;
  color: var(--text-color-50);
  font-family: "Rubik", sans-serif;
  font-weight: 400 !important;
  line-height: 1.5em;
  transition: all .25s ease-in-out;
  opacity: 0;
  cursor: auto;
}

.resend-content i:hover .tooltip-api {
  opacity: 1;
}

.resend-content .tooltip-api:after {
  content: '';
  position: absolute;
  right: 0;
  top: -7px;
  transform: translateX(-50px);
  border-bottom: 8px solid var(--base-background);
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
  border-radius: 2px;
}

.color-ex {
  color: #1570EF;
  font-weight: 400 !important;
}

.ea__active-license .ea__others-icon.eaicon-active {
  color: #00C567;
  background: #E0FCF5;
}

.ea__active-license .activated-btn {
  background: #ECFDF3;
  padding: 5px 10px;
  font-size: 14px;
  line-height: 1.4em;
  font-weight: 450;
  color: #039855;
  border-radius: 8px;
  margin-top: 16px;
  margin-bottom: 24px;
  display: inline-flex;
  gap: 6px;
  align-items: center;
}

.ea__active-license .activated-btn i {
  font-size: 12px;
}

/*-------- Toaster-item ------------*/

.ea__toaster-wrapper {
  width: 360px;
  margin: auto;
  position: fixed;
  bottom: 40px;
  right: 40px;
}

.toaster-content {
  background: var(--base-background);
  padding: 16px;
  border-radius: 2px;
  -webkit-box-shadow: 0 20px 36px 0 #00012314;
  box-shadow: 0 20px 36px 0 #00012314;
  position: relative;
  overflow: hidden;
}

.toaster-content::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  border-radius: 0 0 3px 3px;
  background-image: linear-gradient(90deg, var(--base-background), #52CB98 50%, var(--base-background));
  animation: slide 3s 1;
  animation-fill-mode: forwards;
}

@keyframes slide {
  from{
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

.toaster-content.ea__success::after {
  background-image: linear-gradient(90deg, var(--base-background), #52CB98 50%, var(--base-background));
}

.toaster-content.ea__warning::after {
  background-image: linear-gradient(90deg, var(--base-background), #FFB545 50%, var(--base-background));
}

.toaster-content.ea__error::after {
  background-image: linear-gradient(90deg, var(--base-background), #FF3E3E 50%, var(--base-background));
}

.toaster-content h5 {
  font-size: 14px;
  line-height: 16px;
  font-weight: 450;
  color: var(--text-color-25);
}
.toaster-content i {
  color: #A89E9E;
  font-size: 10px;
  cursor: pointer;
}

/*-------- Responsive-item ------------*/
@media only screen and (max-width: 1400px) {
  .ea__section-wrapper.ea__main-wrapper {
    padding-left: 20px;
    padding-right: 20px;
  }

  .ea__content-header.sticky {
    top: 94px;
  }

  .ea__header-content {
    padding: 12px 16px;
  }

  .ea__slider-connect .swiper-wrapper {
    max-width: 820px;
  }
}

@media only screen and (max-width: 1280px) {

  .mb-6 {
    margin-bottom: 18px;
  }

  .gap-4 {
    gap: 12px;
  }

  #ea__dashboard--wrapper button {
    font-size: 12px;
  }

  #ea__dashboard--wrapper button.upgrade-button {
    font-size: 12px;
    gap: 6px;
    padding: 8px 14px;
  }

  #ea__dashboard--wrapper .primary-btn {
    font-size: 13px;
    padding: 7px 16px;
  }

  #ea__dashboard--wrapper .toggle-wrap {
    width: 30px;
    min-width: 30px;
    height: 16px;
  }

  #ea__dashboard--wrapper input:checked+.slider:before {
    left: 16px;
  }

  #ea__dashboard--wrapper .slider:before {
    height: 12px;
    width: 12px;
  }

  #ea__dashboard--wrapper .slider.pro:before {
    font-size: 7px;
  }

  #ea__dashboard--wrapper .slider.ea-loader::before {
    height: 12px;
    width: 12px;
  }

  #ea__dashboard--wrapper input:checked+.slider.ea-loader:before {
    transform: translateX(14px);
  }

  #ea__dashboard--wrapper .max-w-454 {
    max-width: 386px;
  }

  .content-btn {
    font-size: 9px;
  }

  .ea__header-content img {
    height: 36px;
  }

  span.dark-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }

  .nav-sticky {
    top: 100px;
  }

  .ea__elements-nav-content.go-premium-wrapper {
    max-width: calc(100% - 172px);
  }

  .ea__sidebar-nav-list {
    padding: 12px 16px;
    min-width: 160px;
    max-width: 160px;
  }

  .ea__sidebar-nav {
    gap: 6px;
    padding: 6px 12px;
  }

  .ea__sidebar-nav .ea__nav-text {
    font-size: 13px;
  }

  .ea__section-wrapper.ea__main-wrapper {
    padding-top: 40px;
    padding-bottom: 10px;
  }

  .ea__content-header.sticky {
    top: 88px;
  }

  .ea__sidebar-info {
    max-width: 196px;
    min-width: 196px;
  }

  .ea__sidebar-sticky {
    top: 80px;
  }

  .ea__general-content-item {
    padding: 16px;
    margin-bottom: 12px;
  }

  .ea__general-content-item h3 {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .ea__content-details span.check-icon {
    width: 14px;
    height: 14px;
    font-size: 8px;
  }

  .ea__general-content-item .ea__content-details {
    font-size: 14px;
  }

  .ea__templates-content-wrapper {
    gap: 28px;
  }

  .templates-content {
    padding: 16px 0 16px 16px;
    min-width: 230px;
  }

  .video-promo-wrapper .templates-content {
    padding: 0;
  }

  .ea__pro-elements-content {
    padding: 30px;
  }

  .ea__pro-features {
    padding: 16px;
  }

  .ea__general-content-item h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .review-wrap {
    padding-bottom: 14px;
    margin-bottom: 14px;
  }

  .ea__sidebar-content {
    padding: 16px;
    background-position: unset;
    background-position-y: -20px;
  }

  .ea__connect-others {
    padding: 16px;
  }

  .ea__connect-others h5 {
    font-size: 14px;
    margin-bottom: 7px;
  }

  .ea__others-icon {
    font-size: 14px;
    width: 32px;
    height: 32px;
    min-width: 32px;
    margin-bottom: 12px;
  }

  .ea__sidebar-content p {
    font-size: 13px;
    margin-bottom: 22px;
  }

  .review-wrap h6 {
    font-size: 14px;
  }

  .review-wrap i {
    font-size: 12px;
  }

  .review-wrap .reating-details {
    font-size: 12px;
  }

  .ea__sidebar-content h5 {
    font-size: 14px;
    padding-top: 28px;
    margin-bottom: 8px;
  }

  .ea__connect-others p {
    font-size: 13px;
    margin-bottom: 14px;
  }

  .templates-img img {
    border-radius: 8px;
  }

  .ea__content-info {
    padding: 18px;
  }

  .ea__widget-elements h4 {
    font-size: 14px;
    padding-right: 20px;
  }

  #ea__dashboard--wrapper input.input-name {
    font-size: 13px;
    padding-left: 10px;
    padding-right: 28px;
    width: 160px;
  }

  .ea__input-search-wrapper:after {
    right: 12px;
    font-size: 12px;
  }

  #ea__dashboard--wrapper .form-select {
    color: var(--text-color-100);
    font-size: 13px;
    min-width: 130px;
    max-width: 130px;
    height: 40px;
    padding-left: 10px;
  }

  .form-select--address {
    font-size: 12px;
    width: 110px;
    height: 38px;
    padding: 0px 10px;
  }

  .toggle-wrapper h5 {
    font-size: 12px;
  }

  .ea__icon-wrapper {
    padding: 16px 0;
    font-size: 16px;
  }

  .ea__icon-wrapper i {
    font-size: 15px;
  }

  #ea__dashboard--wrapper h3.ea__content-title {
    font-size: 15px;
  }

  #ea__dashboard--wrapper h3.ea__content-title.title {
    margin-bottom: 12px;
  }

  .ea__integration-header {
    padding: 16px 18px;
  }

  .ea__integration-footer {
    padding: 18px;
  }

  .ea__integration-footer p {
    font-size: 13px;
  }

  #ea__dashboard--wrapper .eael-toggle-label {
    font-size: 12px;
  }

  .ea__integration-header h5 {
    font-size: 13px;
  }

  .ea__content-items {
    padding: 10px;
  }

  .ea__content-head h5 {
    font-size: 12px;
  }

  .ea__slider-connect {
    padding: 16px 16px 30px;
  }

  .ea__general-content-item h4 {
    font-size: 18px;
    margin-bottom: 6px;
  }

  .ea__general-content-item p {
    font-size: 14px;
  }

  .ea__general-content-item.video-promo h2 {
    font-size: 20px;
    margin-bottom: 12px;
  }

  .ea__license-wrapper .ea__license-content {
    margin: 12px 0 12px 44px;
  }

  .ea__license-wrapper .ea__license-content h5 {
    font-size: 14px;
    margin-right: 8px;
  }

  .ea__license-wrapper .ea__license-content i {
    width: 20px;
    height: 20px;
    font-size: 6px;
  }

  .ea__license-options-wrapper {
    padding-top: 20px;
  }

  .ea__license-step {
    gap: 16px;
    padding-bottom: 18px;
  }

  span.step-count {
    font-size: 14px;
    width: 41px;
  }

  .ea__license-step p {
    padding: 10px 12px;
  }

  .ea__license-step-items:not(:last-child):after {
    bottom: -14px;
    left: 21px;
    height: 14px;
  }

  .ea__license-step-items:not(:last-child):before {
    bottom: -14px;
    left: 19px;
  }

  .ea__license-key {
    padding: 18px;
  }

  .license-key-items i {
    left: 12px;
    font-size: 14px;
  }

  input.input-api {
    font-size: 14px;
    padding-left: 38px;
    padding-right: 12px;
  }

  .ea__license-verify {
    padding: 18px;
    margin-top: 18px;
    gap: 15px;
  }

  .ea__license-verify input.input-api {
    padding-left: 12px;
  }

  .resend-content i {
    width: 20px;
    height: 20px;
  }

  .tooltip-api {
    position: absolute;
    top: 26px;
    width: 370px;
    height: 42px;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 10px;
  }

  .ea__elements-wrapper {
    padding: 8px;
    gap: 6px;
  }

  .ea__elements-wrapper h4 {
    font-size: 14px;
  }

  .ea__elements-wrapper:not(:last-child) {
    margin-bottom: 8px;
  }

  .ea__elements-wrapper span {
    font-size: 13px;
  }

  .ea__pro-elements-content .review-wrap i {
    font-size: 14px;
  }

  .ea__pro-elements-content .review-wrap h6 {
    font-size: 14px;
  }

  .ea__pro-elements-content .review-wrap .reating-details {
    font-size: 14px;
  }

  /*.ea__icon-wrapper:last-child span.ea__tooltip {*/
  /*  left: -30%;*/
  /*}*/

  /*.ea__icon-wrapper:last-child span.ea__tooltip:after {*/
  /*  left: 82%;*/
  /*}*/
}

@media only screen and (max-width: 1024px) {

  #ea__dashboard--wrapper .max-w-454 {
    max-width: 290px;
  }

  /*.search--widget {*/
  /*  display: none;*/
  /*}*/

  .ea__content-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }

  .ea__connect-others-wrapper {
    flex-wrap: wrap;
  }

  .ea__general-content-item.templates {
    flex-wrap: wrap;
  }

  .ea__templates-content-wrapper {
    gap: 0;
    flex-wrap: wrap;
  }

  .ea__general-content-item.templates .templates-img img {
    border-radius: 8px;
    padding-left: 0;
    width: 100%;
  }

  .ea__pro-elements-content h3 {
    font-size: 26px;
    padding-bottom: 38px;
  }

  .review-wrap-ex .ea-link-2 {
    font-size: 13px;
  }

  .review-wrap-ex h6 {
    font-size: 13px;
    margin-bottom: 5px;
  }

  .ea__features-content h2 {
    font-size: 22px;
    margin-bottom: 8px;
  }

  .ea__features-content p {
    font-size: 13px;
  }

  .ea__pro-elements-content {
    padding: 28px;
  }

  .review-wrap-ex .icons i {
    font-size: 12px;
  }

  .review-wrap-ex .reating-details {
    font-size: 11px;
  }

  /*.ea__features-content {*/
  /*  max-width: 375px;*/
  /*}*/

  .ea__premium-item-footer h5 {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .ea__premium-item-footer p {
    font-size: 13px;
  }

  .ea__connect-others p {
    margin-bottom: 10px;
  }

  .ea__active-license .activated-btn {
    padding: 4px 8px;
    font-size: 12px;
    margin-top: 12px;
    margin-bottom: 18px;
    gap: 4px;
  }

  .ea__active-license .activated-btn i {
    font-size: 10px;
  }

  .video-promo-wrapper {
    flex-wrap: wrap;
  }

  .ea__general-content-item.video-promo p {
    font-size: 12px;
  }

  .ea__general-content-item.video-promo .templates-img a {
    flex: 1;
  }

  .ea__general-content-item.video-promo .templates-img a img {
    width: 100%;
  }

  .video-promo-wrapper {
    flex-direction: column;
    gap: 30px;
  }

  .templates-content {
    max-width: 100%;
  }

  .ea__general-content-item.video-promo .templates-img {
    width: 100%;
    display: inline-flex;
  }

  .ea__modal-header {
    padding: 18px 28px;
  }

  .ea__modal-body {
    padding: 28px;
    gap: 18px;
    max-height: calc(90vh - 130px);
    width: 460px;
  }

  .ea__modal-content-wrapper.go-premium-wrapper .ea__modal-body {
    width: 380px;
  }

  .ea__modal-body .regenerated-wrapper {
    width: 460px;
  }

  .ea__modal-body .ea__api-key-according label {
    min-width: 132px;
  }

  .go-premium-wrapper .mb-6 {
    margin-bottom: 18px;
  }

  .ea__modal-body .go-premium-wrapper h3 {
    font-size: 20px;
  }

  .ea__modal-body p.pro--content {
    font-size: 14px;
    margin-bottom: 0;
  }

  .ea__modal-body .ea__feature-list-item {
    margin-bottom: 12px;
  }

  .ea__modal-body .ea__feature-list-item p {
    font-size: 14px;
  }

  .ea__modal-footer {
    padding: 12px 28px;
  }
  .ea__modal-close-btn {
    right: -42px;
    width: 34px;
    height: 34px;
    font-size: 10px;
  }
}

@media only screen and (max-width: 800px) {
  #ea__dashboard--wrapper .max-w-454 {
    max-width: 270px;
  }
}

@media only screen and (max-width: 782px) {
  .ea__section-header {
    top: 44px;
  }
}

@media only screen and (max-width: 767px) {

  .mb-10 {
    margin-bottom: 24px;
  }

  .mb-7 {
    margin-bottom: 20px;
  }

  #ea__dashboard--wrapper .max-w-454 {
    max-width: 340px;
  }

  section.ea__section-wrapper.ea__main-wrapper {
    flex-direction: column;
  }

  .ea__sidebar-nav-list {
    max-width: 100%;
    min-height: auto;
    gap: 0;
  }

  .nav-sticky {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .ea__main-content-wrapper {
    flex-wrap: wrap;
  }

  .ea__elements-nav-content.go-premium-wrapper {
    max-width: 100%;
  }

  .ea__main-content-wrapper .ea__general-content--wrapper {
    flex: 1;
  }

  .ea__connect-others-wrapper {
    display: grid;
  }

  .ea__pro-features {
    flex-wrap: wrap;
    gap: 30px;
  }

  .ea__templates-content-wrapper {
    gap: 30px;
  }

  .templates-content {
    padding: 16px 16px 0 16px;
    max-width: 244px;
  }

  .video-promo-wrapper .templates-content {
    padding: 0;
    max-width: 100%;
  }

  .templates-img {
    padding: 0 12px 12px 12px;
  }

  .video-promo-wrapper .templates-img {
    padding: 0;
  }

  .features-widget-wrapper {
    padding-left: 0;
  }

  .ea__sidebar-info {
    max-width: 100%;
    min-width: 100%;
  }

  .ea__sidebar-content {
    padding: 16px;
    background-position: unset;
    background-position-y: -130px;
    background-size: cover;
  }

  .ea__pro-elements-content h3 {
    font-size: 22px;
    padding-bottom: 28px;
  }

  .ea__next-step-wrapper p {
    margin-bottom: 18px;
  }

  .ea__next-step-wrapper {
    padding: 18px;
  }

  .skip-item {
    font-size: 12px;
    margin-top: 10px;
  }

  .select--wrapper .check-mark {
    height: 18px;
    margin-bottom: 12px;
  }

  .select--wrapper .check-mark:after {
    width: 3px;
    height: 7px;
    border-width: 0 1px 1px 0;
  }

  .ea__tools-content-wrapper .ea__connect-others .ea__connect--info {
    min-width: 350px;
  }

  .ea__tools-content-wrapper .ea__connect-others {
    flex-wrap: wrap;
    gap: 20px;
  }

  .ea__integration-content-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }

  .ea__modal-body {
    width: 380px;
  }

  .ea__modal-content-wrapper.go-premium-wrapper .ea__modal-body {
    width: 320px;
  }

  .ea__modal-body .regenerated-wrapper {
    width: 380px;
  }

}

@media only screen and (max-width: 600px) {
  .ea__section-header {
    top: 0;
  }
  .ea__content-header.sticky {
    top: 59px;
  }
}

@media only screen and (max-width: 580px) {

  .ea__content-info {
    flex-wrap: wrap;
    gap: 16px;
  }

  .ea__widget-elements {
    flex-wrap: wrap;
    gap: 12px;
  }

  .ea__content-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }

  .review-wrap {
    flex-wrap: wrap;
    gap: 16px;
  }

  .ea__pro-elements-content h3 {
    font-size: 16px;
    padding-bottom: 24px;
  }

  .ea__integration-content-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }

  .ea__modal-body {
    width: 320px;
  }

  .ea__modal-content-wrapper.go-premium-wrapper .ea__modal-body {
    width: 310px;
  }

  .ea__modal-body .regenerated-wrapper {
    width: 320px;
  }
}

@media only screen and (min-height: 840px) {
  .ea__elements-nav-content.elements-contents {
    padding-bottom: calc(100vh - 835px);
  }
}