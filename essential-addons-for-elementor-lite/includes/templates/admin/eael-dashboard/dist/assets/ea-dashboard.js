(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const s of l.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(i){if(i.ep)return;i.ep=!0;const l=n(i);fetch(i.href,l)}})();function xd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Vo={exports:{}},zi={},Bo={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yr=Symbol.for("react.element"),wd=Symbol.for("react.portal"),Sd=Symbol.for("react.fragment"),_d=Symbol.for("react.strict_mode"),Ed=Symbol.for("react.profiler"),Td=Symbol.for("react.provider"),Cd=Symbol.for("react.context"),kd=Symbol.for("react.forward_ref"),jd=Symbol.for("react.suspense"),Nd=Symbol.for("react.memo"),Ld=Symbol.for("react.lazy"),_a=Symbol.iterator;function Pd(e){return e===null||typeof e!="object"?null:(e=_a&&e[_a]||e["@@iterator"],typeof e=="function"?e:null)}var $o={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ho=Object.assign,Go={};function Nn(e,t,n){this.props=e,this.context=t,this.refs=Go,this.updater=n||$o}Nn.prototype.isReactComponent={};Nn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Nn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Uo(){}Uo.prototype=Nn.prototype;function Cs(e,t,n){this.props=e,this.context=t,this.refs=Go,this.updater=n||$o}var ks=Cs.prototype=new Uo;ks.constructor=Cs;Ho(ks,Nn.prototype);ks.isPureReactComponent=!0;var Ea=Array.isArray,Wo=Object.prototype.hasOwnProperty,js={current:null},Yo={key:!0,ref:!0,__self:!0,__source:!0};function Qo(e,t,n){var r,i={},l=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(l=""+t.key),t)Wo.call(t,r)&&!Yo.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var o=Array(a),c=0;c<a;c++)o[c]=arguments[c+2];i.children=o}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:yr,type:e,key:l,ref:s,props:i,_owner:js.current}}function Od(e,t){return{$$typeof:yr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ns(e){return typeof e=="object"&&e!==null&&e.$$typeof===yr}function Md(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ta=/\/+/g;function Xi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Md(""+e.key):t.toString(36)}function Gr(e,t,n,r,i){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case yr:case wd:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Xi(s,0):r,Ea(i)?(n="",e!=null&&(n=e.replace(Ta,"$&/")+"/"),Gr(i,t,n,"",function(c){return c})):i!=null&&(Ns(i)&&(i=Od(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Ta,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Ea(e))for(var a=0;a<e.length;a++){l=e[a];var o=r+Xi(l,a);s+=Gr(l,t,n,o,i)}else if(o=Pd(e),typeof o=="function")for(e=o.call(e),a=0;!(l=e.next()).done;)l=l.value,o=r+Xi(l,a++),s+=Gr(l,t,n,o,i);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Cr(e,t,n){if(e==null)return e;var r=[],i=0;return Gr(e,r,"","",function(l){return t.call(n,l,i++)}),r}function zd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var xe={current:null},Ur={transition:null},Dd={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:Ur,ReactCurrentOwner:js};function Ko(){throw Error("act(...) is not supported in production builds of React.")}F.Children={map:Cr,forEach:function(e,t,n){Cr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Cr(e,function(){t++}),t},toArray:function(e){return Cr(e,function(t){return t})||[]},only:function(e){if(!Ns(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};F.Component=Nn;F.Fragment=Sd;F.Profiler=Ed;F.PureComponent=Cs;F.StrictMode=_d;F.Suspense=jd;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Dd;F.act=Ko;F.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ho({},e.props),i=e.key,l=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,s=js.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(o in t)Wo.call(t,o)&&!Yo.hasOwnProperty(o)&&(r[o]=t[o]===void 0&&a!==void 0?a[o]:t[o])}var o=arguments.length-2;if(o===1)r.children=n;else if(1<o){a=Array(o);for(var c=0;c<o;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:yr,type:e.type,key:i,ref:l,props:r,_owner:s}};F.createContext=function(e){return e={$$typeof:Cd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Td,_context:e},e.Consumer=e};F.createElement=Qo;F.createFactory=function(e){var t=Qo.bind(null,e);return t.type=e,t};F.createRef=function(){return{current:null}};F.forwardRef=function(e){return{$$typeof:kd,render:e}};F.isValidElement=Ns;F.lazy=function(e){return{$$typeof:Ld,_payload:{_status:-1,_result:e},_init:zd}};F.memo=function(e,t){return{$$typeof:Nd,type:e,compare:t===void 0?null:t}};F.startTransition=function(e){var t=Ur.transition;Ur.transition={};try{e()}finally{Ur.transition=t}};F.unstable_act=Ko;F.useCallback=function(e,t){return xe.current.useCallback(e,t)};F.useContext=function(e){return xe.current.useContext(e)};F.useDebugValue=function(){};F.useDeferredValue=function(e){return xe.current.useDeferredValue(e)};F.useEffect=function(e,t){return xe.current.useEffect(e,t)};F.useId=function(){return xe.current.useId()};F.useImperativeHandle=function(e,t,n){return xe.current.useImperativeHandle(e,t,n)};F.useInsertionEffect=function(e,t){return xe.current.useInsertionEffect(e,t)};F.useLayoutEffect=function(e,t){return xe.current.useLayoutEffect(e,t)};F.useMemo=function(e,t){return xe.current.useMemo(e,t)};F.useReducer=function(e,t,n){return xe.current.useReducer(e,t,n)};F.useRef=function(e){return xe.current.useRef(e)};F.useState=function(e){return xe.current.useState(e)};F.useSyncExternalStore=function(e,t,n){return xe.current.useSyncExternalStore(e,t,n)};F.useTransition=function(){return xe.current.useTransition()};F.version="18.3.1";Bo.exports=F;var R=Bo.exports;const ne=xd(R);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Id=R,Ad=Symbol.for("react.element"),Rd=Symbol.for("react.fragment"),Fd=Object.prototype.hasOwnProperty,bd=Id.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Vd={key:!0,ref:!0,__self:!0,__source:!0};function Xo(e,t,n){var r,i={},l=null,s=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Fd.call(t,r)&&!Vd.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Ad,type:e,key:l,ref:s,props:i,_owner:bd.current}}zi.Fragment=Rd;zi.jsx=Xo;zi.jsxs=Xo;Vo.exports=zi;var u=Vo.exports,Ll={},qo={exports:{}},Me={},Zo={exports:{}},Jo={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(k,O){var z=k.length;k.push(O);e:for(;0<z;){var $=z-1>>>1,X=k[$];if(0<i(X,O))k[$]=O,k[z]=X,z=$;else break e}}function n(k){return k.length===0?null:k[0]}function r(k){if(k.length===0)return null;var O=k[0],z=k.pop();if(z!==O){k[0]=z;e:for(var $=0,X=k.length,Er=X>>>1;$<Er;){var zt=2*($+1)-1,Ki=k[zt],Dt=zt+1,Tr=k[Dt];if(0>i(Ki,z))Dt<X&&0>i(Tr,Ki)?(k[$]=Tr,k[Dt]=z,$=Dt):(k[$]=Ki,k[zt]=z,$=zt);else if(Dt<X&&0>i(Tr,z))k[$]=Tr,k[Dt]=z,$=Dt;else break e}}return O}function i(k,O){var z=k.sortIndex-O.sortIndex;return z!==0?z:k.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var o=[],c=[],f=1,p=null,v=3,y=!1,g=!1,w=!1,N=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(k){for(var O=n(c);O!==null;){if(O.callback===null)r(c);else if(O.startTime<=k)r(c),O.sortIndex=O.expirationTime,t(o,O);else break;O=n(c)}}function x(k){if(w=!1,m(k),!g)if(n(o)!==null)g=!0,A(S);else{var O=n(c);O!==null&&Q(x,O.startTime-k)}}function S(k,O){g=!1,w&&(w=!1,h(T),T=-1),y=!0;var z=v;try{for(m(O),p=n(o);p!==null&&(!(p.expirationTime>O)||k&&!P());){var $=p.callback;if(typeof $=="function"){p.callback=null,v=p.priorityLevel;var X=$(p.expirationTime<=O);O=e.unstable_now(),typeof X=="function"?p.callback=X:p===n(o)&&r(o),m(O)}else r(o);p=n(o)}if(p!==null)var Er=!0;else{var zt=n(c);zt!==null&&Q(x,zt.startTime-O),Er=!1}return Er}finally{p=null,v=z,y=!1}}var _=!1,C=null,T=-1,j=5,E=-1;function P(){return!(e.unstable_now()-E<j)}function D(){if(C!==null){var k=e.unstable_now();E=k;var O=!0;try{O=C(!0,k)}finally{O?I():(_=!1,C=null)}}else _=!1}var I;if(typeof d=="function")I=function(){d(D)};else if(typeof MessageChannel<"u"){var b=new MessageChannel,B=b.port2;b.port1.onmessage=D,I=function(){B.postMessage(null)}}else I=function(){N(D,0)};function A(k){C=k,_||(_=!0,I())}function Q(k,O){T=N(function(){k(e.unstable_now())},O)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(k){k.callback=null},e.unstable_continueExecution=function(){g||y||(g=!0,A(S))},e.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<k?Math.floor(1e3/k):5},e.unstable_getCurrentPriorityLevel=function(){return v},e.unstable_getFirstCallbackNode=function(){return n(o)},e.unstable_next=function(k){switch(v){case 1:case 2:case 3:var O=3;break;default:O=v}var z=v;v=O;try{return k()}finally{v=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(k,O){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var z=v;v=k;try{return O()}finally{v=z}},e.unstable_scheduleCallback=function(k,O,z){var $=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?$+z:$):z=$,k){case 1:var X=-1;break;case 2:X=250;break;case 5:X=**********;break;case 4:X=1e4;break;default:X=5e3}return X=z+X,k={id:f++,callback:O,priorityLevel:k,startTime:z,expirationTime:X,sortIndex:-1},z>$?(k.sortIndex=z,t(c,k),n(o)===null&&k===n(c)&&(w?(h(T),T=-1):w=!0,Q(x,z-$))):(k.sortIndex=X,t(o,k),g||y||(g=!0,A(S))),k},e.unstable_shouldYield=P,e.unstable_wrapCallback=function(k){var O=v;return function(){var z=v;v=O;try{return k.apply(this,arguments)}finally{v=z}}}})(Jo);Zo.exports=Jo;var Bd=Zo.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $d=R,Oe=Bd;function L(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var eu=new Set,tr={};function Qt(e,t){xn(e,t),xn(e+"Capture",t)}function xn(e,t){for(tr[e]=t,e=0;e<t.length;e++)eu.add(t[e])}var at=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Pl=Object.prototype.hasOwnProperty,Hd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ca={},ka={};function Gd(e){return Pl.call(ka,e)?!0:Pl.call(Ca,e)?!1:Hd.test(e)?ka[e]=!0:(Ca[e]=!0,!1)}function Ud(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Wd(e,t,n,r){if(t===null||typeof t>"u"||Ud(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function we(e,t,n,r,i,l,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=s}var de={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){de[e]=new we(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];de[t]=new we(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){de[e]=new we(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){de[e]=new we(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){de[e]=new we(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){de[e]=new we(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){de[e]=new we(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){de[e]=new we(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){de[e]=new we(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ls=/[\-:]([a-z])/g;function Ps(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ls,Ps);de[t]=new we(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ls,Ps);de[t]=new we(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ls,Ps);de[t]=new we(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){de[e]=new we(e,1,!1,e.toLowerCase(),null,!1,!1)});de.xlinkHref=new we("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){de[e]=new we(e,1,!1,e.toLowerCase(),null,!0,!0)});function Os(e,t,n,r){var i=de.hasOwnProperty(t)?de[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Wd(t,n,i,r)&&(n=null),r||i===null?Gd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var dt=$d.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,kr=Symbol.for("react.element"),Zt=Symbol.for("react.portal"),Jt=Symbol.for("react.fragment"),Ms=Symbol.for("react.strict_mode"),Ol=Symbol.for("react.profiler"),tu=Symbol.for("react.provider"),nu=Symbol.for("react.context"),zs=Symbol.for("react.forward_ref"),Ml=Symbol.for("react.suspense"),zl=Symbol.for("react.suspense_list"),Ds=Symbol.for("react.memo"),pt=Symbol.for("react.lazy"),ru=Symbol.for("react.offscreen"),ja=Symbol.iterator;function On(e){return e===null||typeof e!="object"?null:(e=ja&&e[ja]||e["@@iterator"],typeof e=="function"?e:null)}var J=Object.assign,qi;function Bn(e){if(qi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);qi=t&&t[1]||""}return`
`+qi+e}var Zi=!1;function Ji(e,t){if(!e||Zi)return"";Zi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var i=c.stack.split(`
`),l=r.stack.split(`
`),s=i.length-1,a=l.length-1;1<=s&&0<=a&&i[s]!==l[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==l[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==l[a]){var o=`
`+i[s].replace(" at new "," at ");return e.displayName&&o.includes("<anonymous>")&&(o=o.replace("<anonymous>",e.displayName)),o}while(1<=s&&0<=a);break}}}finally{Zi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Bn(e):""}function Yd(e){switch(e.tag){case 5:return Bn(e.type);case 16:return Bn("Lazy");case 13:return Bn("Suspense");case 19:return Bn("SuspenseList");case 0:case 2:case 15:return e=Ji(e.type,!1),e;case 11:return e=Ji(e.type.render,!1),e;case 1:return e=Ji(e.type,!0),e;default:return""}}function Dl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Jt:return"Fragment";case Zt:return"Portal";case Ol:return"Profiler";case Ms:return"StrictMode";case Ml:return"Suspense";case zl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case nu:return(e.displayName||"Context")+".Consumer";case tu:return(e._context.displayName||"Context")+".Provider";case zs:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ds:return t=e.displayName||null,t!==null?t:Dl(e.type)||"Memo";case pt:t=e._payload,e=e._init;try{return Dl(e(t))}catch{}}return null}function Qd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Dl(t);case 8:return t===Ms?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Nt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function iu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Kd(e){var t=iu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,l.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function jr(e){e._valueTracker||(e._valueTracker=Kd(e))}function lu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=iu(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ri(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Il(e,t){var n=t.checked;return J({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Na(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Nt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function su(e,t){t=t.checked,t!=null&&Os(e,"checked",t,!1)}function Al(e,t){su(e,t);var n=Nt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Rl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Rl(e,t.type,Nt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function La(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Rl(e,t,n){(t!=="number"||ri(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var $n=Array.isArray;function fn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Nt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Fl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(L(91));return J({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Pa(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(L(92));if($n(n)){if(1<n.length)throw Error(L(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Nt(n)}}function au(e,t){var n=Nt(t.value),r=Nt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Oa(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ou(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function bl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ou(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Nr,uu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Nr=Nr||document.createElement("div"),Nr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Nr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function nr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Un={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Xd=["Webkit","ms","Moz","O"];Object.keys(Un).forEach(function(e){Xd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Un[t]=Un[e]})});function cu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Un.hasOwnProperty(e)&&Un[e]?(""+t).trim():t+"px"}function du(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=cu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var qd=J({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Vl(e,t){if(t){if(qd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(L(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(L(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(L(61))}if(t.style!=null&&typeof t.style!="object")throw Error(L(62))}}function Bl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var $l=null;function Is(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Hl=null,pn=null,mn=null;function Ma(e){if(e=Sr(e)){if(typeof Hl!="function")throw Error(L(280));var t=e.stateNode;t&&(t=Fi(t),Hl(e.stateNode,e.type,t))}}function fu(e){pn?mn?mn.push(e):mn=[e]:pn=e}function pu(){if(pn){var e=pn,t=mn;if(mn=pn=null,Ma(e),t)for(e=0;e<t.length;e++)Ma(t[e])}}function mu(e,t){return e(t)}function hu(){}var el=!1;function vu(e,t,n){if(el)return e(t,n);el=!0;try{return mu(e,t,n)}finally{el=!1,(pn!==null||mn!==null)&&(hu(),pu())}}function rr(e,t){var n=e.stateNode;if(n===null)return null;var r=Fi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(L(231,t,typeof n));return n}var Gl=!1;if(at)try{var Mn={};Object.defineProperty(Mn,"passive",{get:function(){Gl=!0}}),window.addEventListener("test",Mn,Mn),window.removeEventListener("test",Mn,Mn)}catch{Gl=!1}function Zd(e,t,n,r,i,l,s,a,o){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(f){this.onError(f)}}var Wn=!1,ii=null,li=!1,Ul=null,Jd={onError:function(e){Wn=!0,ii=e}};function ef(e,t,n,r,i,l,s,a,o){Wn=!1,ii=null,Zd.apply(Jd,arguments)}function tf(e,t,n,r,i,l,s,a,o){if(ef.apply(this,arguments),Wn){if(Wn){var c=ii;Wn=!1,ii=null}else throw Error(L(198));li||(li=!0,Ul=c)}}function Kt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function gu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function za(e){if(Kt(e)!==e)throw Error(L(188))}function nf(e){var t=e.alternate;if(!t){if(t=Kt(e),t===null)throw Error(L(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var l=i.alternate;if(l===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===l.child){for(l=i.child;l;){if(l===n)return za(i),e;if(l===r)return za(i),t;l=l.sibling}throw Error(L(188))}if(n.return!==r.return)n=i,r=l;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=l;break}if(a===r){s=!0,r=i,n=l;break}a=a.sibling}if(!s){for(a=l.child;a;){if(a===n){s=!0,n=l,r=i;break}if(a===r){s=!0,r=l,n=i;break}a=a.sibling}if(!s)throw Error(L(189))}}if(n.alternate!==r)throw Error(L(190))}if(n.tag!==3)throw Error(L(188));return n.stateNode.current===n?e:t}function yu(e){return e=nf(e),e!==null?xu(e):null}function xu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=xu(e);if(t!==null)return t;e=e.sibling}return null}var wu=Oe.unstable_scheduleCallback,Da=Oe.unstable_cancelCallback,rf=Oe.unstable_shouldYield,lf=Oe.unstable_requestPaint,te=Oe.unstable_now,sf=Oe.unstable_getCurrentPriorityLevel,As=Oe.unstable_ImmediatePriority,Su=Oe.unstable_UserBlockingPriority,si=Oe.unstable_NormalPriority,af=Oe.unstable_LowPriority,_u=Oe.unstable_IdlePriority,Di=null,qe=null;function of(e){if(qe&&typeof qe.onCommitFiberRoot=="function")try{qe.onCommitFiberRoot(Di,e,void 0,(e.current.flags&128)===128)}catch{}}var Ge=Math.clz32?Math.clz32:df,uf=Math.log,cf=Math.LN2;function df(e){return e>>>=0,e===0?32:31-(uf(e)/cf|0)|0}var Lr=64,Pr=4194304;function Hn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ai(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,l=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=Hn(a):(l&=s,l!==0&&(r=Hn(l)))}else s=n&~i,s!==0?r=Hn(s):l!==0&&(r=Hn(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,l=t&-t,i>=l||i===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ge(t),i=1<<n,r|=e[n],t&=~i;return r}function ff(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function pf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,l=e.pendingLanes;0<l;){var s=31-Ge(l),a=1<<s,o=i[s];o===-1?(!(a&n)||a&r)&&(i[s]=ff(a,t)):o<=t&&(e.expiredLanes|=a),l&=~a}}function Wl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Eu(){var e=Lr;return Lr<<=1,!(Lr&4194240)&&(Lr=64),e}function tl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function xr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ge(t),e[t]=n}function mf(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ge(n),l=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~l}}function Rs(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ge(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var H=0;function Tu(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Cu,Fs,ku,ju,Nu,Yl=!1,Or=[],wt=null,St=null,_t=null,ir=new Map,lr=new Map,ht=[],hf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ia(e,t){switch(e){case"focusin":case"focusout":wt=null;break;case"dragenter":case"dragleave":St=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":ir.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":lr.delete(t.pointerId)}}function zn(e,t,n,r,i,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[i]},t!==null&&(t=Sr(t),t!==null&&Fs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function vf(e,t,n,r,i){switch(t){case"focusin":return wt=zn(wt,e,t,n,r,i),!0;case"dragenter":return St=zn(St,e,t,n,r,i),!0;case"mouseover":return _t=zn(_t,e,t,n,r,i),!0;case"pointerover":var l=i.pointerId;return ir.set(l,zn(ir.get(l)||null,e,t,n,r,i)),!0;case"gotpointercapture":return l=i.pointerId,lr.set(l,zn(lr.get(l)||null,e,t,n,r,i)),!0}return!1}function Lu(e){var t=Rt(e.target);if(t!==null){var n=Kt(t);if(n!==null){if(t=n.tag,t===13){if(t=gu(n),t!==null){e.blockedOn=t,Nu(e.priority,function(){ku(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Wr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ql(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);$l=r,n.target.dispatchEvent(r),$l=null}else return t=Sr(n),t!==null&&Fs(t),e.blockedOn=n,!1;t.shift()}return!0}function Aa(e,t,n){Wr(e)&&n.delete(t)}function gf(){Yl=!1,wt!==null&&Wr(wt)&&(wt=null),St!==null&&Wr(St)&&(St=null),_t!==null&&Wr(_t)&&(_t=null),ir.forEach(Aa),lr.forEach(Aa)}function Dn(e,t){e.blockedOn===t&&(e.blockedOn=null,Yl||(Yl=!0,Oe.unstable_scheduleCallback(Oe.unstable_NormalPriority,gf)))}function sr(e){function t(i){return Dn(i,e)}if(0<Or.length){Dn(Or[0],e);for(var n=1;n<Or.length;n++){var r=Or[n];r.blockedOn===e&&(r.blockedOn=null)}}for(wt!==null&&Dn(wt,e),St!==null&&Dn(St,e),_t!==null&&Dn(_t,e),ir.forEach(t),lr.forEach(t),n=0;n<ht.length;n++)r=ht[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ht.length&&(n=ht[0],n.blockedOn===null);)Lu(n),n.blockedOn===null&&ht.shift()}var hn=dt.ReactCurrentBatchConfig,oi=!0;function yf(e,t,n,r){var i=H,l=hn.transition;hn.transition=null;try{H=1,bs(e,t,n,r)}finally{H=i,hn.transition=l}}function xf(e,t,n,r){var i=H,l=hn.transition;hn.transition=null;try{H=4,bs(e,t,n,r)}finally{H=i,hn.transition=l}}function bs(e,t,n,r){if(oi){var i=Ql(e,t,n,r);if(i===null)dl(e,t,r,ui,n),Ia(e,r);else if(vf(i,e,t,n,r))r.stopPropagation();else if(Ia(e,r),t&4&&-1<hf.indexOf(e)){for(;i!==null;){var l=Sr(i);if(l!==null&&Cu(l),l=Ql(e,t,n,r),l===null&&dl(e,t,r,ui,n),l===i)break;i=l}i!==null&&r.stopPropagation()}else dl(e,t,r,null,n)}}var ui=null;function Ql(e,t,n,r){if(ui=null,e=Is(r),e=Rt(e),e!==null)if(t=Kt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=gu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ui=e,null}function Pu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(sf()){case As:return 1;case Su:return 4;case si:case af:return 16;case _u:return 536870912;default:return 16}default:return 16}}var gt=null,Vs=null,Yr=null;function Ou(){if(Yr)return Yr;var e,t=Vs,n=t.length,r,i="value"in gt?gt.value:gt.textContent,l=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[l-r];r++);return Yr=i.slice(e,1<r?1-r:void 0)}function Qr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Mr(){return!0}function Ra(){return!1}function ze(e){function t(n,r,i,l,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=l,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(l):l[a]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?Mr:Ra,this.isPropagationStopped=Ra,this}return J(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Mr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Mr)},persist:function(){},isPersistent:Mr}),t}var Ln={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bs=ze(Ln),wr=J({},Ln,{view:0,detail:0}),wf=ze(wr),nl,rl,In,Ii=J({},wr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$s,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==In&&(In&&e.type==="mousemove"?(nl=e.screenX-In.screenX,rl=e.screenY-In.screenY):rl=nl=0,In=e),nl)},movementY:function(e){return"movementY"in e?e.movementY:rl}}),Fa=ze(Ii),Sf=J({},Ii,{dataTransfer:0}),_f=ze(Sf),Ef=J({},wr,{relatedTarget:0}),il=ze(Ef),Tf=J({},Ln,{animationName:0,elapsedTime:0,pseudoElement:0}),Cf=ze(Tf),kf=J({},Ln,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),jf=ze(kf),Nf=J({},Ln,{data:0}),ba=ze(Nf),Lf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Pf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Of={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Mf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Of[e])?!!t[e]:!1}function $s(){return Mf}var zf=J({},wr,{key:function(e){if(e.key){var t=Lf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Qr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Pf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$s,charCode:function(e){return e.type==="keypress"?Qr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Qr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Df=ze(zf),If=J({},Ii,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Va=ze(If),Af=J({},wr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$s}),Rf=ze(Af),Ff=J({},Ln,{propertyName:0,elapsedTime:0,pseudoElement:0}),bf=ze(Ff),Vf=J({},Ii,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Bf=ze(Vf),$f=[9,13,27,32],Hs=at&&"CompositionEvent"in window,Yn=null;at&&"documentMode"in document&&(Yn=document.documentMode);var Hf=at&&"TextEvent"in window&&!Yn,Mu=at&&(!Hs||Yn&&8<Yn&&11>=Yn),Ba=" ",$a=!1;function zu(e,t){switch(e){case"keyup":return $f.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Du(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var en=!1;function Gf(e,t){switch(e){case"compositionend":return Du(t);case"keypress":return t.which!==32?null:($a=!0,Ba);case"textInput":return e=t.data,e===Ba&&$a?null:e;default:return null}}function Uf(e,t){if(en)return e==="compositionend"||!Hs&&zu(e,t)?(e=Ou(),Yr=Vs=gt=null,en=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Mu&&t.locale!=="ko"?null:t.data;default:return null}}var Wf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ha(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Wf[e.type]:t==="textarea"}function Iu(e,t,n,r){fu(r),t=ci(t,"onChange"),0<t.length&&(n=new Bs("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qn=null,ar=null;function Yf(e){Wu(e,0)}function Ai(e){var t=rn(e);if(lu(t))return e}function Qf(e,t){if(e==="change")return t}var Au=!1;if(at){var ll;if(at){var sl="oninput"in document;if(!sl){var Ga=document.createElement("div");Ga.setAttribute("oninput","return;"),sl=typeof Ga.oninput=="function"}ll=sl}else ll=!1;Au=ll&&(!document.documentMode||9<document.documentMode)}function Ua(){Qn&&(Qn.detachEvent("onpropertychange",Ru),ar=Qn=null)}function Ru(e){if(e.propertyName==="value"&&Ai(ar)){var t=[];Iu(t,ar,e,Is(e)),vu(Yf,t)}}function Kf(e,t,n){e==="focusin"?(Ua(),Qn=t,ar=n,Qn.attachEvent("onpropertychange",Ru)):e==="focusout"&&Ua()}function Xf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ai(ar)}function qf(e,t){if(e==="click")return Ai(t)}function Zf(e,t){if(e==="input"||e==="change")return Ai(t)}function Jf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var We=typeof Object.is=="function"?Object.is:Jf;function or(e,t){if(We(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Pl.call(t,i)||!We(e[i],t[i]))return!1}return!0}function Wa(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ya(e,t){var n=Wa(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Wa(n)}}function Fu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Fu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function bu(){for(var e=window,t=ri();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ri(e.document)}return t}function Gs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function ep(e){var t=bu(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Fu(n.ownerDocument.documentElement,n)){if(r!==null&&Gs(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,l=Math.min(r.start,i);r=r.end===void 0?l:Math.min(r.end,i),!e.extend&&l>r&&(i=r,r=l,l=i),i=Ya(n,l);var s=Ya(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var tp=at&&"documentMode"in document&&11>=document.documentMode,tn=null,Kl=null,Kn=null,Xl=!1;function Qa(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Xl||tn==null||tn!==ri(r)||(r=tn,"selectionStart"in r&&Gs(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Kn&&or(Kn,r)||(Kn=r,r=ci(Kl,"onSelect"),0<r.length&&(t=new Bs("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=tn)))}function zr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var nn={animationend:zr("Animation","AnimationEnd"),animationiteration:zr("Animation","AnimationIteration"),animationstart:zr("Animation","AnimationStart"),transitionend:zr("Transition","TransitionEnd")},al={},Vu={};at&&(Vu=document.createElement("div").style,"AnimationEvent"in window||(delete nn.animationend.animation,delete nn.animationiteration.animation,delete nn.animationstart.animation),"TransitionEvent"in window||delete nn.transitionend.transition);function Ri(e){if(al[e])return al[e];if(!nn[e])return e;var t=nn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Vu)return al[e]=t[n];return e}var Bu=Ri("animationend"),$u=Ri("animationiteration"),Hu=Ri("animationstart"),Gu=Ri("transitionend"),Uu=new Map,Ka="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Pt(e,t){Uu.set(e,t),Qt(t,[e])}for(var ol=0;ol<Ka.length;ol++){var ul=Ka[ol],np=ul.toLowerCase(),rp=ul[0].toUpperCase()+ul.slice(1);Pt(np,"on"+rp)}Pt(Bu,"onAnimationEnd");Pt($u,"onAnimationIteration");Pt(Hu,"onAnimationStart");Pt("dblclick","onDoubleClick");Pt("focusin","onFocus");Pt("focusout","onBlur");Pt(Gu,"onTransitionEnd");xn("onMouseEnter",["mouseout","mouseover"]);xn("onMouseLeave",["mouseout","mouseover"]);xn("onPointerEnter",["pointerout","pointerover"]);xn("onPointerLeave",["pointerout","pointerover"]);Qt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Qt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Qt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Qt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Qt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Qt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Gn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ip=new Set("cancel close invalid load scroll toggle".split(" ").concat(Gn));function Xa(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,tf(r,t,void 0,e),e.currentTarget=null}function Wu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],o=a.instance,c=a.currentTarget;if(a=a.listener,o!==l&&i.isPropagationStopped())break e;Xa(i,a,c),l=o}else for(s=0;s<r.length;s++){if(a=r[s],o=a.instance,c=a.currentTarget,a=a.listener,o!==l&&i.isPropagationStopped())break e;Xa(i,a,c),l=o}}}if(li)throw e=Ul,li=!1,Ul=null,e}function U(e,t){var n=t[ts];n===void 0&&(n=t[ts]=new Set);var r=e+"__bubble";n.has(r)||(Yu(t,e,2,!1),n.add(r))}function cl(e,t,n){var r=0;t&&(r|=4),Yu(n,e,r,t)}var Dr="_reactListening"+Math.random().toString(36).slice(2);function ur(e){if(!e[Dr]){e[Dr]=!0,eu.forEach(function(n){n!=="selectionchange"&&(ip.has(n)||cl(n,!1,e),cl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Dr]||(t[Dr]=!0,cl("selectionchange",!1,t))}}function Yu(e,t,n,r){switch(Pu(t)){case 1:var i=yf;break;case 4:i=xf;break;default:i=bs}n=i.bind(null,t,n,e),i=void 0,!Gl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function dl(e,t,n,r,i){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var o=s.tag;if((o===3||o===4)&&(o=s.stateNode.containerInfo,o===i||o.nodeType===8&&o.parentNode===i))return;s=s.return}for(;a!==null;){if(s=Rt(a),s===null)return;if(o=s.tag,o===5||o===6){r=l=s;continue e}a=a.parentNode}}r=r.return}vu(function(){var c=l,f=Is(n),p=[];e:{var v=Uu.get(e);if(v!==void 0){var y=Bs,g=e;switch(e){case"keypress":if(Qr(n)===0)break e;case"keydown":case"keyup":y=Df;break;case"focusin":g="focus",y=il;break;case"focusout":g="blur",y=il;break;case"beforeblur":case"afterblur":y=il;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Fa;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=_f;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Rf;break;case Bu:case $u:case Hu:y=Cf;break;case Gu:y=bf;break;case"scroll":y=wf;break;case"wheel":y=Bf;break;case"copy":case"cut":case"paste":y=jf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=Va}var w=(t&4)!==0,N=!w&&e==="scroll",h=w?v!==null?v+"Capture":null:v;w=[];for(var d=c,m;d!==null;){m=d;var x=m.stateNode;if(m.tag===5&&x!==null&&(m=x,h!==null&&(x=rr(d,h),x!=null&&w.push(cr(d,x,m)))),N)break;d=d.return}0<w.length&&(v=new y(v,g,null,n,f),p.push({event:v,listeners:w}))}}if(!(t&7)){e:{if(v=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",v&&n!==$l&&(g=n.relatedTarget||n.fromElement)&&(Rt(g)||g[ot]))break e;if((y||v)&&(v=f.window===f?f:(v=f.ownerDocument)?v.defaultView||v.parentWindow:window,y?(g=n.relatedTarget||n.toElement,y=c,g=g?Rt(g):null,g!==null&&(N=Kt(g),g!==N||g.tag!==5&&g.tag!==6)&&(g=null)):(y=null,g=c),y!==g)){if(w=Fa,x="onMouseLeave",h="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(w=Va,x="onPointerLeave",h="onPointerEnter",d="pointer"),N=y==null?v:rn(y),m=g==null?v:rn(g),v=new w(x,d+"leave",y,n,f),v.target=N,v.relatedTarget=m,x=null,Rt(f)===c&&(w=new w(h,d+"enter",g,n,f),w.target=m,w.relatedTarget=N,x=w),N=x,y&&g)t:{for(w=y,h=g,d=0,m=w;m;m=Xt(m))d++;for(m=0,x=h;x;x=Xt(x))m++;for(;0<d-m;)w=Xt(w),d--;for(;0<m-d;)h=Xt(h),m--;for(;d--;){if(w===h||h!==null&&w===h.alternate)break t;w=Xt(w),h=Xt(h)}w=null}else w=null;y!==null&&qa(p,v,y,w,!1),g!==null&&N!==null&&qa(p,N,g,w,!0)}}e:{if(v=c?rn(c):window,y=v.nodeName&&v.nodeName.toLowerCase(),y==="select"||y==="input"&&v.type==="file")var S=Qf;else if(Ha(v))if(Au)S=Zf;else{S=Xf;var _=Kf}else(y=v.nodeName)&&y.toLowerCase()==="input"&&(v.type==="checkbox"||v.type==="radio")&&(S=qf);if(S&&(S=S(e,c))){Iu(p,S,n,f);break e}_&&_(e,v,c),e==="focusout"&&(_=v._wrapperState)&&_.controlled&&v.type==="number"&&Rl(v,"number",v.value)}switch(_=c?rn(c):window,e){case"focusin":(Ha(_)||_.contentEditable==="true")&&(tn=_,Kl=c,Kn=null);break;case"focusout":Kn=Kl=tn=null;break;case"mousedown":Xl=!0;break;case"contextmenu":case"mouseup":case"dragend":Xl=!1,Qa(p,n,f);break;case"selectionchange":if(tp)break;case"keydown":case"keyup":Qa(p,n,f)}var C;if(Hs)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else en?zu(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Mu&&n.locale!=="ko"&&(en||T!=="onCompositionStart"?T==="onCompositionEnd"&&en&&(C=Ou()):(gt=f,Vs="value"in gt?gt.value:gt.textContent,en=!0)),_=ci(c,T),0<_.length&&(T=new ba(T,e,null,n,f),p.push({event:T,listeners:_}),C?T.data=C:(C=Du(n),C!==null&&(T.data=C)))),(C=Hf?Gf(e,n):Uf(e,n))&&(c=ci(c,"onBeforeInput"),0<c.length&&(f=new ba("onBeforeInput","beforeinput",null,n,f),p.push({event:f,listeners:c}),f.data=C))}Wu(p,t)})}function cr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ci(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,l=i.stateNode;i.tag===5&&l!==null&&(i=l,l=rr(e,n),l!=null&&r.unshift(cr(e,l,i)),l=rr(e,t),l!=null&&r.push(cr(e,l,i))),e=e.return}return r}function Xt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function qa(e,t,n,r,i){for(var l=t._reactName,s=[];n!==null&&n!==r;){var a=n,o=a.alternate,c=a.stateNode;if(o!==null&&o===r)break;a.tag===5&&c!==null&&(a=c,i?(o=rr(n,l),o!=null&&s.unshift(cr(n,o,a))):i||(o=rr(n,l),o!=null&&s.push(cr(n,o,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var lp=/\r\n?/g,sp=/\u0000|\uFFFD/g;function Za(e){return(typeof e=="string"?e:""+e).replace(lp,`
`).replace(sp,"")}function Ir(e,t,n){if(t=Za(t),Za(e)!==t&&n)throw Error(L(425))}function di(){}var ql=null,Zl=null;function Jl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var es=typeof setTimeout=="function"?setTimeout:void 0,ap=typeof clearTimeout=="function"?clearTimeout:void 0,Ja=typeof Promise=="function"?Promise:void 0,op=typeof queueMicrotask=="function"?queueMicrotask:typeof Ja<"u"?function(e){return Ja.resolve(null).then(e).catch(up)}:es;function up(e){setTimeout(function(){throw e})}function fl(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),sr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);sr(t)}function Et(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function eo(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Pn=Math.random().toString(36).slice(2),Ke="__reactFiber$"+Pn,dr="__reactProps$"+Pn,ot="__reactContainer$"+Pn,ts="__reactEvents$"+Pn,cp="__reactListeners$"+Pn,dp="__reactHandles$"+Pn;function Rt(e){var t=e[Ke];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ot]||n[Ke]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=eo(e);e!==null;){if(n=e[Ke])return n;e=eo(e)}return t}e=n,n=e.parentNode}return null}function Sr(e){return e=e[Ke]||e[ot],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function rn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(L(33))}function Fi(e){return e[dr]||null}var ns=[],ln=-1;function Ot(e){return{current:e}}function W(e){0>ln||(e.current=ns[ln],ns[ln]=null,ln--)}function G(e,t){ln++,ns[ln]=e.current,e.current=t}var Lt={},ve=Ot(Lt),Te=Ot(!1),$t=Lt;function wn(e,t){var n=e.type.contextTypes;if(!n)return Lt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},l;for(l in n)i[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ce(e){return e=e.childContextTypes,e!=null}function fi(){W(Te),W(ve)}function to(e,t,n){if(ve.current!==Lt)throw Error(L(168));G(ve,t),G(Te,n)}function Qu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(L(108,Qd(e)||"Unknown",i));return J({},n,r)}function pi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Lt,$t=ve.current,G(ve,e),G(Te,Te.current),!0}function no(e,t,n){var r=e.stateNode;if(!r)throw Error(L(169));n?(e=Qu(e,t,$t),r.__reactInternalMemoizedMergedChildContext=e,W(Te),W(ve),G(ve,e)):W(Te),G(Te,n)}var rt=null,bi=!1,pl=!1;function Ku(e){rt===null?rt=[e]:rt.push(e)}function fp(e){bi=!0,Ku(e)}function Mt(){if(!pl&&rt!==null){pl=!0;var e=0,t=H;try{var n=rt;for(H=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}rt=null,bi=!1}catch(i){throw rt!==null&&(rt=rt.slice(e+1)),wu(As,Mt),i}finally{H=t,pl=!1}}return null}var sn=[],an=0,mi=null,hi=0,De=[],Ie=0,Ht=null,it=1,lt="";function It(e,t){sn[an++]=hi,sn[an++]=mi,mi=e,hi=t}function Xu(e,t,n){De[Ie++]=it,De[Ie++]=lt,De[Ie++]=Ht,Ht=e;var r=it;e=lt;var i=32-Ge(r)-1;r&=~(1<<i),n+=1;var l=32-Ge(t)+i;if(30<l){var s=i-i%5;l=(r&(1<<s)-1).toString(32),r>>=s,i-=s,it=1<<32-Ge(t)+i|n<<i|r,lt=l+e}else it=1<<l|n<<i|r,lt=e}function Us(e){e.return!==null&&(It(e,1),Xu(e,1,0))}function Ws(e){for(;e===mi;)mi=sn[--an],sn[an]=null,hi=sn[--an],sn[an]=null;for(;e===Ht;)Ht=De[--Ie],De[Ie]=null,lt=De[--Ie],De[Ie]=null,it=De[--Ie],De[Ie]=null}var Pe=null,Le=null,K=!1,He=null;function qu(e,t){var n=Ae(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ro(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Pe=e,Le=Et(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Pe=e,Le=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Ht!==null?{id:it,overflow:lt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ae(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Pe=e,Le=null,!0):!1;default:return!1}}function rs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function is(e){if(K){var t=Le;if(t){var n=t;if(!ro(e,t)){if(rs(e))throw Error(L(418));t=Et(n.nextSibling);var r=Pe;t&&ro(e,t)?qu(r,n):(e.flags=e.flags&-4097|2,K=!1,Pe=e)}}else{if(rs(e))throw Error(L(418));e.flags=e.flags&-4097|2,K=!1,Pe=e}}}function io(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Pe=e}function Ar(e){if(e!==Pe)return!1;if(!K)return io(e),K=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Jl(e.type,e.memoizedProps)),t&&(t=Le)){if(rs(e))throw Zu(),Error(L(418));for(;t;)qu(e,t),t=Et(t.nextSibling)}if(io(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(L(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Le=Et(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Le=null}}else Le=Pe?Et(e.stateNode.nextSibling):null;return!0}function Zu(){for(var e=Le;e;)e=Et(e.nextSibling)}function Sn(){Le=Pe=null,K=!1}function Ys(e){He===null?He=[e]:He.push(e)}var pp=dt.ReactCurrentBatchConfig;function An(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(L(309));var r=n.stateNode}if(!r)throw Error(L(147,e));var i=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(s){var a=i.refs;s===null?delete a[l]:a[l]=s},t._stringRef=l,t)}if(typeof e!="string")throw Error(L(284));if(!n._owner)throw Error(L(290,e))}return e}function Rr(e,t){throw e=Object.prototype.toString.call(t),Error(L(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function lo(e){var t=e._init;return t(e._payload)}function Ju(e){function t(h,d){if(e){var m=h.deletions;m===null?(h.deletions=[d],h.flags|=16):m.push(d)}}function n(h,d){if(!e)return null;for(;d!==null;)t(h,d),d=d.sibling;return null}function r(h,d){for(h=new Map;d!==null;)d.key!==null?h.set(d.key,d):h.set(d.index,d),d=d.sibling;return h}function i(h,d){return h=jt(h,d),h.index=0,h.sibling=null,h}function l(h,d,m){return h.index=m,e?(m=h.alternate,m!==null?(m=m.index,m<d?(h.flags|=2,d):m):(h.flags|=2,d)):(h.flags|=1048576,d)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function a(h,d,m,x){return d===null||d.tag!==6?(d=wl(m,h.mode,x),d.return=h,d):(d=i(d,m),d.return=h,d)}function o(h,d,m,x){var S=m.type;return S===Jt?f(h,d,m.props.children,x,m.key):d!==null&&(d.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===pt&&lo(S)===d.type)?(x=i(d,m.props),x.ref=An(h,d,m),x.return=h,x):(x=ti(m.type,m.key,m.props,null,h.mode,x),x.ref=An(h,d,m),x.return=h,x)}function c(h,d,m,x){return d===null||d.tag!==4||d.stateNode.containerInfo!==m.containerInfo||d.stateNode.implementation!==m.implementation?(d=Sl(m,h.mode,x),d.return=h,d):(d=i(d,m.children||[]),d.return=h,d)}function f(h,d,m,x,S){return d===null||d.tag!==7?(d=Bt(m,h.mode,x,S),d.return=h,d):(d=i(d,m),d.return=h,d)}function p(h,d,m){if(typeof d=="string"&&d!==""||typeof d=="number")return d=wl(""+d,h.mode,m),d.return=h,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case kr:return m=ti(d.type,d.key,d.props,null,h.mode,m),m.ref=An(h,null,d),m.return=h,m;case Zt:return d=Sl(d,h.mode,m),d.return=h,d;case pt:var x=d._init;return p(h,x(d._payload),m)}if($n(d)||On(d))return d=Bt(d,h.mode,m,null),d.return=h,d;Rr(h,d)}return null}function v(h,d,m,x){var S=d!==null?d.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return S!==null?null:a(h,d,""+m,x);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case kr:return m.key===S?o(h,d,m,x):null;case Zt:return m.key===S?c(h,d,m,x):null;case pt:return S=m._init,v(h,d,S(m._payload),x)}if($n(m)||On(m))return S!==null?null:f(h,d,m,x,null);Rr(h,m)}return null}function y(h,d,m,x,S){if(typeof x=="string"&&x!==""||typeof x=="number")return h=h.get(m)||null,a(d,h,""+x,S);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case kr:return h=h.get(x.key===null?m:x.key)||null,o(d,h,x,S);case Zt:return h=h.get(x.key===null?m:x.key)||null,c(d,h,x,S);case pt:var _=x._init;return y(h,d,m,_(x._payload),S)}if($n(x)||On(x))return h=h.get(m)||null,f(d,h,x,S,null);Rr(d,x)}return null}function g(h,d,m,x){for(var S=null,_=null,C=d,T=d=0,j=null;C!==null&&T<m.length;T++){C.index>T?(j=C,C=null):j=C.sibling;var E=v(h,C,m[T],x);if(E===null){C===null&&(C=j);break}e&&C&&E.alternate===null&&t(h,C),d=l(E,d,T),_===null?S=E:_.sibling=E,_=E,C=j}if(T===m.length)return n(h,C),K&&It(h,T),S;if(C===null){for(;T<m.length;T++)C=p(h,m[T],x),C!==null&&(d=l(C,d,T),_===null?S=C:_.sibling=C,_=C);return K&&It(h,T),S}for(C=r(h,C);T<m.length;T++)j=y(C,h,T,m[T],x),j!==null&&(e&&j.alternate!==null&&C.delete(j.key===null?T:j.key),d=l(j,d,T),_===null?S=j:_.sibling=j,_=j);return e&&C.forEach(function(P){return t(h,P)}),K&&It(h,T),S}function w(h,d,m,x){var S=On(m);if(typeof S!="function")throw Error(L(150));if(m=S.call(m),m==null)throw Error(L(151));for(var _=S=null,C=d,T=d=0,j=null,E=m.next();C!==null&&!E.done;T++,E=m.next()){C.index>T?(j=C,C=null):j=C.sibling;var P=v(h,C,E.value,x);if(P===null){C===null&&(C=j);break}e&&C&&P.alternate===null&&t(h,C),d=l(P,d,T),_===null?S=P:_.sibling=P,_=P,C=j}if(E.done)return n(h,C),K&&It(h,T),S;if(C===null){for(;!E.done;T++,E=m.next())E=p(h,E.value,x),E!==null&&(d=l(E,d,T),_===null?S=E:_.sibling=E,_=E);return K&&It(h,T),S}for(C=r(h,C);!E.done;T++,E=m.next())E=y(C,h,T,E.value,x),E!==null&&(e&&E.alternate!==null&&C.delete(E.key===null?T:E.key),d=l(E,d,T),_===null?S=E:_.sibling=E,_=E);return e&&C.forEach(function(D){return t(h,D)}),K&&It(h,T),S}function N(h,d,m,x){if(typeof m=="object"&&m!==null&&m.type===Jt&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case kr:e:{for(var S=m.key,_=d;_!==null;){if(_.key===S){if(S=m.type,S===Jt){if(_.tag===7){n(h,_.sibling),d=i(_,m.props.children),d.return=h,h=d;break e}}else if(_.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===pt&&lo(S)===_.type){n(h,_.sibling),d=i(_,m.props),d.ref=An(h,_,m),d.return=h,h=d;break e}n(h,_);break}else t(h,_);_=_.sibling}m.type===Jt?(d=Bt(m.props.children,h.mode,x,m.key),d.return=h,h=d):(x=ti(m.type,m.key,m.props,null,h.mode,x),x.ref=An(h,d,m),x.return=h,h=x)}return s(h);case Zt:e:{for(_=m.key;d!==null;){if(d.key===_)if(d.tag===4&&d.stateNode.containerInfo===m.containerInfo&&d.stateNode.implementation===m.implementation){n(h,d.sibling),d=i(d,m.children||[]),d.return=h,h=d;break e}else{n(h,d);break}else t(h,d);d=d.sibling}d=Sl(m,h.mode,x),d.return=h,h=d}return s(h);case pt:return _=m._init,N(h,d,_(m._payload),x)}if($n(m))return g(h,d,m,x);if(On(m))return w(h,d,m,x);Rr(h,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,d!==null&&d.tag===6?(n(h,d.sibling),d=i(d,m),d.return=h,h=d):(n(h,d),d=wl(m,h.mode,x),d.return=h,h=d),s(h)):n(h,d)}return N}var _n=Ju(!0),ec=Ju(!1),vi=Ot(null),gi=null,on=null,Qs=null;function Ks(){Qs=on=gi=null}function Xs(e){var t=vi.current;W(vi),e._currentValue=t}function ls(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function vn(e,t){gi=e,Qs=on=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ee=!0),e.firstContext=null)}function Fe(e){var t=e._currentValue;if(Qs!==e)if(e={context:e,memoizedValue:t,next:null},on===null){if(gi===null)throw Error(L(308));on=e,gi.dependencies={lanes:0,firstContext:e}}else on=on.next=e;return t}var Ft=null;function qs(e){Ft===null?Ft=[e]:Ft.push(e)}function tc(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,qs(t)):(n.next=i.next,i.next=n),t.interleaved=n,ut(e,r)}function ut(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var mt=!1;function Zs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function nc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function st(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Tt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,V&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,ut(e,n)}return i=r.interleaved,i===null?(t.next=t,qs(r)):(t.next=i.next,i.next=t),r.interleaved=t,ut(e,n)}function Kr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Rs(e,n)}}function so(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?i=l=s:l=l.next=s,n=n.next}while(n!==null);l===null?i=l=t:l=l.next=t}else i=l=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function yi(e,t,n,r){var i=e.updateQueue;mt=!1;var l=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var o=a,c=o.next;o.next=null,s===null?l=c:s.next=c,s=o;var f=e.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==s&&(a===null?f.firstBaseUpdate=c:a.next=c,f.lastBaseUpdate=o))}if(l!==null){var p=i.baseState;s=0,f=c=o=null,a=l;do{var v=a.lane,y=a.eventTime;if((r&v)===v){f!==null&&(f=f.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var g=e,w=a;switch(v=t,y=n,w.tag){case 1:if(g=w.payload,typeof g=="function"){p=g.call(y,p,v);break e}p=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=w.payload,v=typeof g=="function"?g.call(y,p,v):g,v==null)break e;p=J({},p,v);break e;case 2:mt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,v=i.effects,v===null?i.effects=[a]:v.push(a))}else y={eventTime:y,lane:v,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(c=f=y,o=p):f=f.next=y,s|=v;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;v=a,a=v.next,v.next=null,i.lastBaseUpdate=v,i.shared.pending=null}}while(!0);if(f===null&&(o=p),i.baseState=o,i.firstBaseUpdate=c,i.lastBaseUpdate=f,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else l===null&&(i.shared.lanes=0);Ut|=s,e.lanes=s,e.memoizedState=p}}function ao(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(L(191,i));i.call(r)}}}var _r={},Ze=Ot(_r),fr=Ot(_r),pr=Ot(_r);function bt(e){if(e===_r)throw Error(L(174));return e}function Js(e,t){switch(G(pr,t),G(fr,e),G(Ze,_r),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:bl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=bl(t,e)}W(Ze),G(Ze,t)}function En(){W(Ze),W(fr),W(pr)}function rc(e){bt(pr.current);var t=bt(Ze.current),n=bl(t,e.type);t!==n&&(G(fr,e),G(Ze,n))}function ea(e){fr.current===e&&(W(Ze),W(fr))}var q=Ot(0);function xi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ml=[];function ta(){for(var e=0;e<ml.length;e++)ml[e]._workInProgressVersionPrimary=null;ml.length=0}var Xr=dt.ReactCurrentDispatcher,hl=dt.ReactCurrentBatchConfig,Gt=0,Z=null,le=null,ae=null,wi=!1,Xn=!1,mr=0,mp=0;function fe(){throw Error(L(321))}function na(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!We(e[n],t[n]))return!1;return!0}function ra(e,t,n,r,i,l){if(Gt=l,Z=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Xr.current=e===null||e.memoizedState===null?yp:xp,e=n(r,i),Xn){l=0;do{if(Xn=!1,mr=0,25<=l)throw Error(L(301));l+=1,ae=le=null,t.updateQueue=null,Xr.current=wp,e=n(r,i)}while(Xn)}if(Xr.current=Si,t=le!==null&&le.next!==null,Gt=0,ae=le=Z=null,wi=!1,t)throw Error(L(300));return e}function ia(){var e=mr!==0;return mr=0,e}function Qe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ae===null?Z.memoizedState=ae=e:ae=ae.next=e,ae}function be(){if(le===null){var e=Z.alternate;e=e!==null?e.memoizedState:null}else e=le.next;var t=ae===null?Z.memoizedState:ae.next;if(t!==null)ae=t,le=e;else{if(e===null)throw Error(L(310));le=e,e={memoizedState:le.memoizedState,baseState:le.baseState,baseQueue:le.baseQueue,queue:le.queue,next:null},ae===null?Z.memoizedState=ae=e:ae=ae.next=e}return ae}function hr(e,t){return typeof t=="function"?t(e):t}function vl(e){var t=be(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=le,i=r.baseQueue,l=n.pending;if(l!==null){if(i!==null){var s=i.next;i.next=l.next,l.next=s}r.baseQueue=i=l,n.pending=null}if(i!==null){l=i.next,r=r.baseState;var a=s=null,o=null,c=l;do{var f=c.lane;if((Gt&f)===f)o!==null&&(o=o.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var p={lane:f,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};o===null?(a=o=p,s=r):o=o.next=p,Z.lanes|=f,Ut|=f}c=c.next}while(c!==null&&c!==l);o===null?s=r:o.next=a,We(r,t.memoizedState)||(Ee=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=o,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do l=i.lane,Z.lanes|=l,Ut|=l,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function gl(e){var t=be(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,l=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do l=e(l,s.action),s=s.next;while(s!==i);We(l,t.memoizedState)||(Ee=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function ic(){}function lc(e,t){var n=Z,r=be(),i=t(),l=!We(r.memoizedState,i);if(l&&(r.memoizedState=i,Ee=!0),r=r.queue,la(oc.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||ae!==null&&ae.memoizedState.tag&1){if(n.flags|=2048,vr(9,ac.bind(null,n,r,i,t),void 0,null),oe===null)throw Error(L(349));Gt&30||sc(n,t,i)}return i}function sc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Z.updateQueue,t===null?(t={lastEffect:null,stores:null},Z.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ac(e,t,n,r){t.value=n,t.getSnapshot=r,uc(t)&&cc(e)}function oc(e,t,n){return n(function(){uc(t)&&cc(e)})}function uc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!We(e,n)}catch{return!0}}function cc(e){var t=ut(e,1);t!==null&&Ue(t,e,1,-1)}function oo(e){var t=Qe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:hr,lastRenderedState:e},t.queue=e,e=e.dispatch=gp.bind(null,Z,e),[t.memoizedState,e]}function vr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Z.updateQueue,t===null?(t={lastEffect:null,stores:null},Z.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function dc(){return be().memoizedState}function qr(e,t,n,r){var i=Qe();Z.flags|=e,i.memoizedState=vr(1|t,n,void 0,r===void 0?null:r)}function Vi(e,t,n,r){var i=be();r=r===void 0?null:r;var l=void 0;if(le!==null){var s=le.memoizedState;if(l=s.destroy,r!==null&&na(r,s.deps)){i.memoizedState=vr(t,n,l,r);return}}Z.flags|=e,i.memoizedState=vr(1|t,n,l,r)}function uo(e,t){return qr(8390656,8,e,t)}function la(e,t){return Vi(2048,8,e,t)}function fc(e,t){return Vi(4,2,e,t)}function pc(e,t){return Vi(4,4,e,t)}function mc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function hc(e,t,n){return n=n!=null?n.concat([e]):null,Vi(4,4,mc.bind(null,t,e),n)}function sa(){}function vc(e,t){var n=be();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&na(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function gc(e,t){var n=be();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&na(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function yc(e,t,n){return Gt&21?(We(n,t)||(n=Eu(),Z.lanes|=n,Ut|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ee=!0),e.memoizedState=n)}function hp(e,t){var n=H;H=n!==0&&4>n?n:4,e(!0);var r=hl.transition;hl.transition={};try{e(!1),t()}finally{H=n,hl.transition=r}}function xc(){return be().memoizedState}function vp(e,t,n){var r=kt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},wc(e))Sc(t,n);else if(n=tc(e,t,n,r),n!==null){var i=ye();Ue(n,e,r,i),_c(n,t,r)}}function gp(e,t,n){var r=kt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(wc(e))Sc(t,i);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var s=t.lastRenderedState,a=l(s,n);if(i.hasEagerState=!0,i.eagerState=a,We(a,s)){var o=t.interleaved;o===null?(i.next=i,qs(t)):(i.next=o.next,o.next=i),t.interleaved=i;return}}catch{}finally{}n=tc(e,t,i,r),n!==null&&(i=ye(),Ue(n,e,r,i),_c(n,t,r))}}function wc(e){var t=e.alternate;return e===Z||t!==null&&t===Z}function Sc(e,t){Xn=wi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function _c(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Rs(e,n)}}var Si={readContext:Fe,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},yp={readContext:Fe,useCallback:function(e,t){return Qe().memoizedState=[e,t===void 0?null:t],e},useContext:Fe,useEffect:uo,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,qr(4194308,4,mc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return qr(4194308,4,e,t)},useInsertionEffect:function(e,t){return qr(4,2,e,t)},useMemo:function(e,t){var n=Qe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Qe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=vp.bind(null,Z,e),[r.memoizedState,e]},useRef:function(e){var t=Qe();return e={current:e},t.memoizedState=e},useState:oo,useDebugValue:sa,useDeferredValue:function(e){return Qe().memoizedState=e},useTransition:function(){var e=oo(!1),t=e[0];return e=hp.bind(null,e[1]),Qe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Z,i=Qe();if(K){if(n===void 0)throw Error(L(407));n=n()}else{if(n=t(),oe===null)throw Error(L(349));Gt&30||sc(r,t,n)}i.memoizedState=n;var l={value:n,getSnapshot:t};return i.queue=l,uo(oc.bind(null,r,l,e),[e]),r.flags|=2048,vr(9,ac.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=Qe(),t=oe.identifierPrefix;if(K){var n=lt,r=it;n=(r&~(1<<32-Ge(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=mr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=mp++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},xp={readContext:Fe,useCallback:vc,useContext:Fe,useEffect:la,useImperativeHandle:hc,useInsertionEffect:fc,useLayoutEffect:pc,useMemo:gc,useReducer:vl,useRef:dc,useState:function(){return vl(hr)},useDebugValue:sa,useDeferredValue:function(e){var t=be();return yc(t,le.memoizedState,e)},useTransition:function(){var e=vl(hr)[0],t=be().memoizedState;return[e,t]},useMutableSource:ic,useSyncExternalStore:lc,useId:xc,unstable_isNewReconciler:!1},wp={readContext:Fe,useCallback:vc,useContext:Fe,useEffect:la,useImperativeHandle:hc,useInsertionEffect:fc,useLayoutEffect:pc,useMemo:gc,useReducer:gl,useRef:dc,useState:function(){return gl(hr)},useDebugValue:sa,useDeferredValue:function(e){var t=be();return le===null?t.memoizedState=e:yc(t,le.memoizedState,e)},useTransition:function(){var e=gl(hr)[0],t=be().memoizedState;return[e,t]},useMutableSource:ic,useSyncExternalStore:lc,useId:xc,unstable_isNewReconciler:!1};function Be(e,t){if(e&&e.defaultProps){t=J({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ss(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:J({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Bi={isMounted:function(e){return(e=e._reactInternals)?Kt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ye(),i=kt(e),l=st(r,i);l.payload=t,n!=null&&(l.callback=n),t=Tt(e,l,i),t!==null&&(Ue(t,e,i,r),Kr(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ye(),i=kt(e),l=st(r,i);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=Tt(e,l,i),t!==null&&(Ue(t,e,i,r),Kr(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ye(),r=kt(e),i=st(n,r);i.tag=2,t!=null&&(i.callback=t),t=Tt(e,i,r),t!==null&&(Ue(t,e,r,n),Kr(t,e,r))}};function co(e,t,n,r,i,l,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,s):t.prototype&&t.prototype.isPureReactComponent?!or(n,r)||!or(i,l):!0}function Ec(e,t,n){var r=!1,i=Lt,l=t.contextType;return typeof l=="object"&&l!==null?l=Fe(l):(i=Ce(t)?$t:ve.current,r=t.contextTypes,l=(r=r!=null)?wn(e,i):Lt),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Bi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=l),t}function fo(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Bi.enqueueReplaceState(t,t.state,null)}function as(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Zs(e);var l=t.contextType;typeof l=="object"&&l!==null?i.context=Fe(l):(l=Ce(t)?$t:ve.current,i.context=wn(e,l)),i.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(ss(e,t,l,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Bi.enqueueReplaceState(i,i.state,null),yi(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Tn(e,t){try{var n="",r=t;do n+=Yd(r),r=r.return;while(r);var i=n}catch(l){i=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:i,digest:null}}function yl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function os(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Sp=typeof WeakMap=="function"?WeakMap:Map;function Tc(e,t,n){n=st(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ei||(Ei=!0,ys=r),os(e,t)},n}function Cc(e,t,n){n=st(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){os(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){os(e,t),typeof r!="function"&&(Ct===null?Ct=new Set([this]):Ct.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function po(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Sp;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Ip.bind(null,e,t,n),t.then(e,e))}function mo(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ho(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=st(-1,1),t.tag=2,Tt(n,t,1))),n.lanes|=1),e)}var _p=dt.ReactCurrentOwner,Ee=!1;function ge(e,t,n,r){t.child=e===null?ec(t,null,n,r):_n(t,e.child,n,r)}function vo(e,t,n,r,i){n=n.render;var l=t.ref;return vn(t,i),r=ra(e,t,n,r,l,i),n=ia(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ct(e,t,i)):(K&&n&&Us(t),t.flags|=1,ge(e,t,r,i),t.child)}function go(e,t,n,r,i){if(e===null){var l=n.type;return typeof l=="function"&&!ma(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,kc(e,t,l,r,i)):(e=ti(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&i)){var s=l.memoizedProps;if(n=n.compare,n=n!==null?n:or,n(s,r)&&e.ref===t.ref)return ct(e,t,i)}return t.flags|=1,e=jt(l,r),e.ref=t.ref,e.return=t,t.child=e}function kc(e,t,n,r,i){if(e!==null){var l=e.memoizedProps;if(or(l,r)&&e.ref===t.ref)if(Ee=!1,t.pendingProps=r=l,(e.lanes&i)!==0)e.flags&131072&&(Ee=!0);else return t.lanes=e.lanes,ct(e,t,i)}return us(e,t,n,r,i)}function jc(e,t,n){var r=t.pendingProps,i=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},G(cn,je),je|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,G(cn,je),je|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,G(cn,je),je|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,G(cn,je),je|=r;return ge(e,t,i,n),t.child}function Nc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function us(e,t,n,r,i){var l=Ce(n)?$t:ve.current;return l=wn(t,l),vn(t,i),n=ra(e,t,n,r,l,i),r=ia(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ct(e,t,i)):(K&&r&&Us(t),t.flags|=1,ge(e,t,n,i),t.child)}function yo(e,t,n,r,i){if(Ce(n)){var l=!0;pi(t)}else l=!1;if(vn(t,i),t.stateNode===null)Zr(e,t),Ec(t,n,r),as(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var o=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=Fe(c):(c=Ce(n)?$t:ve.current,c=wn(t,c));var f=n.getDerivedStateFromProps,p=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";p||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||o!==c)&&fo(t,s,r,c),mt=!1;var v=t.memoizedState;s.state=v,yi(t,r,s,i),o=t.memoizedState,a!==r||v!==o||Te.current||mt?(typeof f=="function"&&(ss(t,n,f,r),o=t.memoizedState),(a=mt||co(t,n,a,r,v,o,c))?(p||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=o),s.props=r,s.state=o,s.context=c,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,nc(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:Be(t.type,a),s.props=c,p=t.pendingProps,v=s.context,o=n.contextType,typeof o=="object"&&o!==null?o=Fe(o):(o=Ce(n)?$t:ve.current,o=wn(t,o));var y=n.getDerivedStateFromProps;(f=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==p||v!==o)&&fo(t,s,r,o),mt=!1,v=t.memoizedState,s.state=v,yi(t,r,s,i);var g=t.memoizedState;a!==p||v!==g||Te.current||mt?(typeof y=="function"&&(ss(t,n,y,r),g=t.memoizedState),(c=mt||co(t,n,c,r,v,g,o)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,g,o),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,g,o)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),s.props=r,s.state=g,s.context=o,r=c):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),r=!1)}return cs(e,t,n,r,l,i)}function cs(e,t,n,r,i,l){Nc(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&no(t,n,!1),ct(e,t,l);r=t.stateNode,_p.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=_n(t,e.child,null,l),t.child=_n(t,null,a,l)):ge(e,t,a,l),t.memoizedState=r.state,i&&no(t,n,!0),t.child}function Lc(e){var t=e.stateNode;t.pendingContext?to(e,t.pendingContext,t.pendingContext!==t.context):t.context&&to(e,t.context,!1),Js(e,t.containerInfo)}function xo(e,t,n,r,i){return Sn(),Ys(i),t.flags|=256,ge(e,t,n,r),t.child}var ds={dehydrated:null,treeContext:null,retryLane:0};function fs(e){return{baseLanes:e,cachePool:null,transitions:null}}function Pc(e,t,n){var r=t.pendingProps,i=q.current,l=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),G(q,i&1),e===null)return is(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,l?(r=t.mode,l=t.child,s={mode:"hidden",children:s},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=s):l=Gi(s,r,0,null),e=Bt(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=fs(n),t.memoizedState=ds,e):aa(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return Ep(e,t,s,r,a,i,n);if(l){l=r.fallback,s=t.mode,i=e.child,a=i.sibling;var o={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=o,t.deletions=null):(r=jt(i,o),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?l=jt(a,l):(l=Bt(l,s,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,s=e.child.memoizedState,s=s===null?fs(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=ds,r}return l=e.child,e=l.sibling,r=jt(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function aa(e,t){return t=Gi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Fr(e,t,n,r){return r!==null&&Ys(r),_n(t,e.child,null,n),e=aa(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ep(e,t,n,r,i,l,s){if(n)return t.flags&256?(t.flags&=-257,r=yl(Error(L(422))),Fr(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,i=t.mode,r=Gi({mode:"visible",children:r.children},i,0,null),l=Bt(l,i,s,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&_n(t,e.child,null,s),t.child.memoizedState=fs(s),t.memoizedState=ds,l);if(!(t.mode&1))return Fr(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,l=Error(L(419)),r=yl(l,r,void 0),Fr(e,t,s,r)}if(a=(s&e.childLanes)!==0,Ee||a){if(r=oe,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==l.retryLane&&(l.retryLane=i,ut(e,i),Ue(r,e,i,-1))}return pa(),r=yl(Error(L(421))),Fr(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Ap.bind(null,e),i._reactRetry=t,null):(e=l.treeContext,Le=Et(i.nextSibling),Pe=t,K=!0,He=null,e!==null&&(De[Ie++]=it,De[Ie++]=lt,De[Ie++]=Ht,it=e.id,lt=e.overflow,Ht=t),t=aa(t,r.children),t.flags|=4096,t)}function wo(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ls(e.return,t,n)}function xl(e,t,n,r,i){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=i)}function Oc(e,t,n){var r=t.pendingProps,i=r.revealOrder,l=r.tail;if(ge(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&wo(e,n,t);else if(e.tag===19)wo(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(G(q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&xi(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),xl(t,!1,i,n,l);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&xi(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}xl(t,!0,n,null,l);break;case"together":xl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Zr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ct(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ut|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(L(153));if(t.child!==null){for(e=t.child,n=jt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=jt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Tp(e,t,n){switch(t.tag){case 3:Lc(t),Sn();break;case 5:rc(t);break;case 1:Ce(t.type)&&pi(t);break;case 4:Js(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;G(vi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(G(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?Pc(e,t,n):(G(q,q.current&1),e=ct(e,t,n),e!==null?e.sibling:null);G(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Oc(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),G(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,jc(e,t,n)}return ct(e,t,n)}var Mc,ps,zc,Dc;Mc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ps=function(){};zc=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,bt(Ze.current);var l=null;switch(n){case"input":i=Il(e,i),r=Il(e,r),l=[];break;case"select":i=J({},i,{value:void 0}),r=J({},r,{value:void 0}),l=[];break;case"textarea":i=Fl(e,i),r=Fl(e,r),l=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=di)}Vl(n,r);var s;n=null;for(c in i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&i[c]!=null)if(c==="style"){var a=i[c];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(tr.hasOwnProperty(c)?l||(l=[]):(l=l||[]).push(c,null));for(c in r){var o=r[c];if(a=i!=null?i[c]:void 0,r.hasOwnProperty(c)&&o!==a&&(o!=null||a!=null))if(c==="style")if(a){for(s in a)!a.hasOwnProperty(s)||o&&o.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in o)o.hasOwnProperty(s)&&a[s]!==o[s]&&(n||(n={}),n[s]=o[s])}else n||(l||(l=[]),l.push(c,n)),n=o;else c==="dangerouslySetInnerHTML"?(o=o?o.__html:void 0,a=a?a.__html:void 0,o!=null&&a!==o&&(l=l||[]).push(c,o)):c==="children"?typeof o!="string"&&typeof o!="number"||(l=l||[]).push(c,""+o):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(tr.hasOwnProperty(c)?(o!=null&&c==="onScroll"&&U("scroll",e),l||a===o||(l=[])):(l=l||[]).push(c,o))}n&&(l=l||[]).push("style",n);var c=l;(t.updateQueue=c)&&(t.flags|=4)}};Dc=function(e,t,n,r){n!==r&&(t.flags|=4)};function Rn(e,t){if(!K)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Cp(e,t,n){var r=t.pendingProps;switch(Ws(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return Ce(t.type)&&fi(),pe(t),null;case 3:return r=t.stateNode,En(),W(Te),W(ve),ta(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ar(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,He!==null&&(Ss(He),He=null))),ps(e,t),pe(t),null;case 5:ea(t);var i=bt(pr.current);if(n=t.type,e!==null&&t.stateNode!=null)zc(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(L(166));return pe(t),null}if(e=bt(Ze.current),Ar(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[Ke]=t,r[dr]=l,e=(t.mode&1)!==0,n){case"dialog":U("cancel",r),U("close",r);break;case"iframe":case"object":case"embed":U("load",r);break;case"video":case"audio":for(i=0;i<Gn.length;i++)U(Gn[i],r);break;case"source":U("error",r);break;case"img":case"image":case"link":U("error",r),U("load",r);break;case"details":U("toggle",r);break;case"input":Na(r,l),U("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},U("invalid",r);break;case"textarea":Pa(r,l),U("invalid",r)}Vl(n,l),i=null;for(var s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="children"?typeof a=="string"?r.textContent!==a&&(l.suppressHydrationWarning!==!0&&Ir(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(l.suppressHydrationWarning!==!0&&Ir(r.textContent,a,e),i=["children",""+a]):tr.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&U("scroll",r)}switch(n){case"input":jr(r),La(r,l,!0);break;case"textarea":jr(r),Oa(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=di)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ou(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Ke]=t,e[dr]=r,Mc(e,t,!1,!1),t.stateNode=e;e:{switch(s=Bl(n,r),n){case"dialog":U("cancel",e),U("close",e),i=r;break;case"iframe":case"object":case"embed":U("load",e),i=r;break;case"video":case"audio":for(i=0;i<Gn.length;i++)U(Gn[i],e);i=r;break;case"source":U("error",e),i=r;break;case"img":case"image":case"link":U("error",e),U("load",e),i=r;break;case"details":U("toggle",e),i=r;break;case"input":Na(e,r),i=Il(e,r),U("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=J({},r,{value:void 0}),U("invalid",e);break;case"textarea":Pa(e,r),i=Fl(e,r),U("invalid",e);break;default:i=r}Vl(n,i),a=i;for(l in a)if(a.hasOwnProperty(l)){var o=a[l];l==="style"?du(e,o):l==="dangerouslySetInnerHTML"?(o=o?o.__html:void 0,o!=null&&uu(e,o)):l==="children"?typeof o=="string"?(n!=="textarea"||o!=="")&&nr(e,o):typeof o=="number"&&nr(e,""+o):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(tr.hasOwnProperty(l)?o!=null&&l==="onScroll"&&U("scroll",e):o!=null&&Os(e,l,o,s))}switch(n){case"input":jr(e),La(e,r,!1);break;case"textarea":jr(e),Oa(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Nt(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?fn(e,!!r.multiple,l,!1):r.defaultValue!=null&&fn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=di)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)Dc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(L(166));if(n=bt(pr.current),bt(Ze.current),Ar(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ke]=t,(l=r.nodeValue!==n)&&(e=Pe,e!==null))switch(e.tag){case 3:Ir(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ir(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ke]=t,t.stateNode=r}return pe(t),null;case 13:if(W(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(K&&Le!==null&&t.mode&1&&!(t.flags&128))Zu(),Sn(),t.flags|=98560,l=!1;else if(l=Ar(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(L(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(L(317));l[Ke]=t}else Sn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),l=!1}else He!==null&&(Ss(He),He=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?se===0&&(se=3):pa())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return En(),ps(e,t),e===null&&ur(t.stateNode.containerInfo),pe(t),null;case 10:return Xs(t.type._context),pe(t),null;case 17:return Ce(t.type)&&fi(),pe(t),null;case 19:if(W(q),l=t.memoizedState,l===null)return pe(t),null;if(r=(t.flags&128)!==0,s=l.rendering,s===null)if(r)Rn(l,!1);else{if(se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=xi(e),s!==null){for(t.flags|=128,Rn(l,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,s=l.alternate,s===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=s.childLanes,l.lanes=s.lanes,l.child=s.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=s.memoizedProps,l.memoizedState=s.memoizedState,l.updateQueue=s.updateQueue,l.type=s.type,e=s.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return G(q,q.current&1|2),t.child}e=e.sibling}l.tail!==null&&te()>Cn&&(t.flags|=128,r=!0,Rn(l,!1),t.lanes=4194304)}else{if(!r)if(e=xi(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Rn(l,!0),l.tail===null&&l.tailMode==="hidden"&&!s.alternate&&!K)return pe(t),null}else 2*te()-l.renderingStartTime>Cn&&n!==1073741824&&(t.flags|=128,r=!0,Rn(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(n=l.last,n!==null?n.sibling=s:t.child=s,l.last=s)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=te(),t.sibling=null,n=q.current,G(q,r?n&1|2:n&1),t):(pe(t),null);case 22:case 23:return fa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?je&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(L(156,t.tag))}function kp(e,t){switch(Ws(t),t.tag){case 1:return Ce(t.type)&&fi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return En(),W(Te),W(ve),ta(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ea(t),null;case 13:if(W(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(L(340));Sn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return W(q),null;case 4:return En(),null;case 10:return Xs(t.type._context),null;case 22:case 23:return fa(),null;case 24:return null;default:return null}}var br=!1,me=!1,jp=typeof WeakSet=="function"?WeakSet:Set,M=null;function un(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ee(e,t,r)}else n.current=null}function ms(e,t,n){try{n()}catch(r){ee(e,t,r)}}var So=!1;function Np(e,t){if(ql=oi,e=bu(),Gs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var s=0,a=-1,o=-1,c=0,f=0,p=e,v=null;t:for(;;){for(var y;p!==n||i!==0&&p.nodeType!==3||(a=s+i),p!==l||r!==0&&p.nodeType!==3||(o=s+r),p.nodeType===3&&(s+=p.nodeValue.length),(y=p.firstChild)!==null;)v=p,p=y;for(;;){if(p===e)break t;if(v===n&&++c===i&&(a=s),v===l&&++f===r&&(o=s),(y=p.nextSibling)!==null)break;p=v,v=p.parentNode}p=y}n=a===-1||o===-1?null:{start:a,end:o}}else n=null}n=n||{start:0,end:0}}else n=null;for(Zl={focusedElem:e,selectionRange:n},oi=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var w=g.memoizedProps,N=g.memoizedState,h=t.stateNode,d=h.getSnapshotBeforeUpdate(t.elementType===t.type?w:Be(t.type,w),N);h.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(L(163))}}catch(x){ee(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return g=So,So=!1,g}function qn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var l=i.destroy;i.destroy=void 0,l!==void 0&&ms(t,n,l)}i=i.next}while(i!==r)}}function $i(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function hs(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ic(e){var t=e.alternate;t!==null&&(e.alternate=null,Ic(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ke],delete t[dr],delete t[ts],delete t[cp],delete t[dp])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ac(e){return e.tag===5||e.tag===3||e.tag===4}function _o(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ac(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function vs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=di));else if(r!==4&&(e=e.child,e!==null))for(vs(e,t,n),e=e.sibling;e!==null;)vs(e,t,n),e=e.sibling}function gs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(gs(e,t,n),e=e.sibling;e!==null;)gs(e,t,n),e=e.sibling}var ue=null,$e=!1;function ft(e,t,n){for(n=n.child;n!==null;)Rc(e,t,n),n=n.sibling}function Rc(e,t,n){if(qe&&typeof qe.onCommitFiberUnmount=="function")try{qe.onCommitFiberUnmount(Di,n)}catch{}switch(n.tag){case 5:me||un(n,t);case 6:var r=ue,i=$e;ue=null,ft(e,t,n),ue=r,$e=i,ue!==null&&($e?(e=ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ue.removeChild(n.stateNode));break;case 18:ue!==null&&($e?(e=ue,n=n.stateNode,e.nodeType===8?fl(e.parentNode,n):e.nodeType===1&&fl(e,n),sr(e)):fl(ue,n.stateNode));break;case 4:r=ue,i=$e,ue=n.stateNode.containerInfo,$e=!0,ft(e,t,n),ue=r,$e=i;break;case 0:case 11:case 14:case 15:if(!me&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var l=i,s=l.destroy;l=l.tag,s!==void 0&&(l&2||l&4)&&ms(n,t,s),i=i.next}while(i!==r)}ft(e,t,n);break;case 1:if(!me&&(un(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ee(n,t,a)}ft(e,t,n);break;case 21:ft(e,t,n);break;case 22:n.mode&1?(me=(r=me)||n.memoizedState!==null,ft(e,t,n),me=r):ft(e,t,n);break;default:ft(e,t,n)}}function Eo(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new jp),t.forEach(function(r){var i=Rp.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ve(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var l=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:ue=a.stateNode,$e=!1;break e;case 3:ue=a.stateNode.containerInfo,$e=!0;break e;case 4:ue=a.stateNode.containerInfo,$e=!0;break e}a=a.return}if(ue===null)throw Error(L(160));Rc(l,s,i),ue=null,$e=!1;var o=i.alternate;o!==null&&(o.return=null),i.return=null}catch(c){ee(i,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Fc(t,e),t=t.sibling}function Fc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ve(t,e),Ye(e),r&4){try{qn(3,e,e.return),$i(3,e)}catch(w){ee(e,e.return,w)}try{qn(5,e,e.return)}catch(w){ee(e,e.return,w)}}break;case 1:Ve(t,e),Ye(e),r&512&&n!==null&&un(n,n.return);break;case 5:if(Ve(t,e),Ye(e),r&512&&n!==null&&un(n,n.return),e.flags&32){var i=e.stateNode;try{nr(i,"")}catch(w){ee(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var l=e.memoizedProps,s=n!==null?n.memoizedProps:l,a=e.type,o=e.updateQueue;if(e.updateQueue=null,o!==null)try{a==="input"&&l.type==="radio"&&l.name!=null&&su(i,l),Bl(a,s);var c=Bl(a,l);for(s=0;s<o.length;s+=2){var f=o[s],p=o[s+1];f==="style"?du(i,p):f==="dangerouslySetInnerHTML"?uu(i,p):f==="children"?nr(i,p):Os(i,f,p,c)}switch(a){case"input":Al(i,l);break;case"textarea":au(i,l);break;case"select":var v=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!l.multiple;var y=l.value;y!=null?fn(i,!!l.multiple,y,!1):v!==!!l.multiple&&(l.defaultValue!=null?fn(i,!!l.multiple,l.defaultValue,!0):fn(i,!!l.multiple,l.multiple?[]:"",!1))}i[dr]=l}catch(w){ee(e,e.return,w)}}break;case 6:if(Ve(t,e),Ye(e),r&4){if(e.stateNode===null)throw Error(L(162));i=e.stateNode,l=e.memoizedProps;try{i.nodeValue=l}catch(w){ee(e,e.return,w)}}break;case 3:if(Ve(t,e),Ye(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{sr(t.containerInfo)}catch(w){ee(e,e.return,w)}break;case 4:Ve(t,e),Ye(e);break;case 13:Ve(t,e),Ye(e),i=e.child,i.flags&8192&&(l=i.memoizedState!==null,i.stateNode.isHidden=l,!l||i.alternate!==null&&i.alternate.memoizedState!==null||(ca=te())),r&4&&Eo(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(me=(c=me)||f,Ve(t,e),me=c):Ve(t,e),Ye(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!f&&e.mode&1)for(M=e,f=e.child;f!==null;){for(p=M=f;M!==null;){switch(v=M,y=v.child,v.tag){case 0:case 11:case 14:case 15:qn(4,v,v.return);break;case 1:un(v,v.return);var g=v.stateNode;if(typeof g.componentWillUnmount=="function"){r=v,n=v.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(w){ee(r,n,w)}}break;case 5:un(v,v.return);break;case 22:if(v.memoizedState!==null){Co(p);continue}}y!==null?(y.return=v,M=y):Co(p)}f=f.sibling}e:for(f=null,p=e;;){if(p.tag===5){if(f===null){f=p;try{i=p.stateNode,c?(l=i.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(a=p.stateNode,o=p.memoizedProps.style,s=o!=null&&o.hasOwnProperty("display")?o.display:null,a.style.display=cu("display",s))}catch(w){ee(e,e.return,w)}}}else if(p.tag===6){if(f===null)try{p.stateNode.nodeValue=c?"":p.memoizedProps}catch(w){ee(e,e.return,w)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;f===p&&(f=null),p=p.return}f===p&&(f=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:Ve(t,e),Ye(e),r&4&&Eo(e);break;case 21:break;default:Ve(t,e),Ye(e)}}function Ye(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Ac(n)){var r=n;break e}n=n.return}throw Error(L(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(nr(i,""),r.flags&=-33);var l=_o(e);gs(e,l,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=_o(e);vs(e,a,s);break;default:throw Error(L(161))}}catch(o){ee(e,e.return,o)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Lp(e,t,n){M=e,bc(e)}function bc(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var i=M,l=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||br;if(!s){var a=i.alternate,o=a!==null&&a.memoizedState!==null||me;a=br;var c=me;if(br=s,(me=o)&&!c)for(M=i;M!==null;)s=M,o=s.child,s.tag===22&&s.memoizedState!==null?ko(i):o!==null?(o.return=s,M=o):ko(i);for(;l!==null;)M=l,bc(l),l=l.sibling;M=i,br=a,me=c}To(e)}else i.subtreeFlags&8772&&l!==null?(l.return=i,M=l):To(e)}}function To(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:me||$i(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!me)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Be(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&ao(t,l,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ao(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var o=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":o.autoFocus&&n.focus();break;case"img":o.src&&(n.src=o.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var f=c.memoizedState;if(f!==null){var p=f.dehydrated;p!==null&&sr(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(L(163))}me||t.flags&512&&hs(t)}catch(v){ee(t,t.return,v)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Co(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function ko(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{$i(4,t)}catch(o){ee(t,n,o)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(o){ee(t,i,o)}}var l=t.return;try{hs(t)}catch(o){ee(t,l,o)}break;case 5:var s=t.return;try{hs(t)}catch(o){ee(t,s,o)}}}catch(o){ee(t,t.return,o)}if(t===e){M=null;break}var a=t.sibling;if(a!==null){a.return=t.return,M=a;break}M=t.return}}var Pp=Math.ceil,_i=dt.ReactCurrentDispatcher,oa=dt.ReactCurrentOwner,Re=dt.ReactCurrentBatchConfig,V=0,oe=null,re=null,ce=0,je=0,cn=Ot(0),se=0,gr=null,Ut=0,Hi=0,ua=0,Zn=null,_e=null,ca=0,Cn=1/0,nt=null,Ei=!1,ys=null,Ct=null,Vr=!1,yt=null,Ti=0,Jn=0,xs=null,Jr=-1,ei=0;function ye(){return V&6?te():Jr!==-1?Jr:Jr=te()}function kt(e){return e.mode&1?V&2&&ce!==0?ce&-ce:pp.transition!==null?(ei===0&&(ei=Eu()),ei):(e=H,e!==0||(e=window.event,e=e===void 0?16:Pu(e.type)),e):1}function Ue(e,t,n,r){if(50<Jn)throw Jn=0,xs=null,Error(L(185));xr(e,n,r),(!(V&2)||e!==oe)&&(e===oe&&(!(V&2)&&(Hi|=n),se===4&&vt(e,ce)),ke(e,r),n===1&&V===0&&!(t.mode&1)&&(Cn=te()+500,bi&&Mt()))}function ke(e,t){var n=e.callbackNode;pf(e,t);var r=ai(e,e===oe?ce:0);if(r===0)n!==null&&Da(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Da(n),t===1)e.tag===0?fp(jo.bind(null,e)):Ku(jo.bind(null,e)),op(function(){!(V&6)&&Mt()}),n=null;else{switch(Tu(r)){case 1:n=As;break;case 4:n=Su;break;case 16:n=si;break;case 536870912:n=_u;break;default:n=si}n=Yc(n,Vc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Vc(e,t){if(Jr=-1,ei=0,V&6)throw Error(L(327));var n=e.callbackNode;if(gn()&&e.callbackNode!==n)return null;var r=ai(e,e===oe?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ci(e,r);else{t=r;var i=V;V|=2;var l=$c();(oe!==e||ce!==t)&&(nt=null,Cn=te()+500,Vt(e,t));do try{zp();break}catch(a){Bc(e,a)}while(!0);Ks(),_i.current=l,V=i,re!==null?t=0:(oe=null,ce=0,t=se)}if(t!==0){if(t===2&&(i=Wl(e),i!==0&&(r=i,t=ws(e,i))),t===1)throw n=gr,Vt(e,0),vt(e,r),ke(e,te()),n;if(t===6)vt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Op(i)&&(t=Ci(e,r),t===2&&(l=Wl(e),l!==0&&(r=l,t=ws(e,l))),t===1))throw n=gr,Vt(e,0),vt(e,r),ke(e,te()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(L(345));case 2:At(e,_e,nt);break;case 3:if(vt(e,r),(r&130023424)===r&&(t=ca+500-te(),10<t)){if(ai(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){ye(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=es(At.bind(null,e,_e,nt),t);break}At(e,_e,nt);break;case 4:if(vt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ge(r);l=1<<s,s=t[s],s>i&&(i=s),r&=~l}if(r=i,r=te()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Pp(r/1960))-r,10<r){e.timeoutHandle=es(At.bind(null,e,_e,nt),r);break}At(e,_e,nt);break;case 5:At(e,_e,nt);break;default:throw Error(L(329))}}}return ke(e,te()),e.callbackNode===n?Vc.bind(null,e):null}function ws(e,t){var n=Zn;return e.current.memoizedState.isDehydrated&&(Vt(e,t).flags|=256),e=Ci(e,t),e!==2&&(t=_e,_e=n,t!==null&&Ss(t)),e}function Ss(e){_e===null?_e=e:_e.push.apply(_e,e)}function Op(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],l=i.getSnapshot;i=i.value;try{if(!We(l(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function vt(e,t){for(t&=~ua,t&=~Hi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ge(t),r=1<<n;e[n]=-1,t&=~r}}function jo(e){if(V&6)throw Error(L(327));gn();var t=ai(e,0);if(!(t&1))return ke(e,te()),null;var n=Ci(e,t);if(e.tag!==0&&n===2){var r=Wl(e);r!==0&&(t=r,n=ws(e,r))}if(n===1)throw n=gr,Vt(e,0),vt(e,t),ke(e,te()),n;if(n===6)throw Error(L(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,At(e,_e,nt),ke(e,te()),null}function da(e,t){var n=V;V|=1;try{return e(t)}finally{V=n,V===0&&(Cn=te()+500,bi&&Mt())}}function Wt(e){yt!==null&&yt.tag===0&&!(V&6)&&gn();var t=V;V|=1;var n=Re.transition,r=H;try{if(Re.transition=null,H=1,e)return e()}finally{H=r,Re.transition=n,V=t,!(V&6)&&Mt()}}function fa(){je=cn.current,W(cn)}function Vt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,ap(n)),re!==null)for(n=re.return;n!==null;){var r=n;switch(Ws(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&fi();break;case 3:En(),W(Te),W(ve),ta();break;case 5:ea(r);break;case 4:En();break;case 13:W(q);break;case 19:W(q);break;case 10:Xs(r.type._context);break;case 22:case 23:fa()}n=n.return}if(oe=e,re=e=jt(e.current,null),ce=je=t,se=0,gr=null,ua=Hi=Ut=0,_e=Zn=null,Ft!==null){for(t=0;t<Ft.length;t++)if(n=Ft[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,l=n.pending;if(l!==null){var s=l.next;l.next=i,r.next=s}n.pending=r}Ft=null}return e}function Bc(e,t){do{var n=re;try{if(Ks(),Xr.current=Si,wi){for(var r=Z.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}wi=!1}if(Gt=0,ae=le=Z=null,Xn=!1,mr=0,oa.current=null,n===null||n.return===null){se=1,gr=t,re=null;break}e:{var l=e,s=n.return,a=n,o=t;if(t=ce,a.flags|=32768,o!==null&&typeof o=="object"&&typeof o.then=="function"){var c=o,f=a,p=f.tag;if(!(f.mode&1)&&(p===0||p===11||p===15)){var v=f.alternate;v?(f.updateQueue=v.updateQueue,f.memoizedState=v.memoizedState,f.lanes=v.lanes):(f.updateQueue=null,f.memoizedState=null)}var y=mo(s);if(y!==null){y.flags&=-257,ho(y,s,a,l,t),y.mode&1&&po(l,c,t),t=y,o=c;var g=t.updateQueue;if(g===null){var w=new Set;w.add(o),t.updateQueue=w}else g.add(o);break e}else{if(!(t&1)){po(l,c,t),pa();break e}o=Error(L(426))}}else if(K&&a.mode&1){var N=mo(s);if(N!==null){!(N.flags&65536)&&(N.flags|=256),ho(N,s,a,l,t),Ys(Tn(o,a));break e}}l=o=Tn(o,a),se!==4&&(se=2),Zn===null?Zn=[l]:Zn.push(l),l=s;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var h=Tc(l,o,t);so(l,h);break e;case 1:a=o;var d=l.type,m=l.stateNode;if(!(l.flags&128)&&(typeof d.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Ct===null||!Ct.has(m)))){l.flags|=65536,t&=-t,l.lanes|=t;var x=Cc(l,a,t);so(l,x);break e}}l=l.return}while(l!==null)}Gc(n)}catch(S){t=S,re===n&&n!==null&&(re=n=n.return);continue}break}while(!0)}function $c(){var e=_i.current;return _i.current=Si,e===null?Si:e}function pa(){(se===0||se===3||se===2)&&(se=4),oe===null||!(Ut&268435455)&&!(Hi&268435455)||vt(oe,ce)}function Ci(e,t){var n=V;V|=2;var r=$c();(oe!==e||ce!==t)&&(nt=null,Vt(e,t));do try{Mp();break}catch(i){Bc(e,i)}while(!0);if(Ks(),V=n,_i.current=r,re!==null)throw Error(L(261));return oe=null,ce=0,se}function Mp(){for(;re!==null;)Hc(re)}function zp(){for(;re!==null&&!rf();)Hc(re)}function Hc(e){var t=Wc(e.alternate,e,je);e.memoizedProps=e.pendingProps,t===null?Gc(e):re=t,oa.current=null}function Gc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=kp(n,t),n!==null){n.flags&=32767,re=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{se=6,re=null;return}}else if(n=Cp(n,t,je),n!==null){re=n;return}if(t=t.sibling,t!==null){re=t;return}re=t=e}while(t!==null);se===0&&(se=5)}function At(e,t,n){var r=H,i=Re.transition;try{Re.transition=null,H=1,Dp(e,t,n,r)}finally{Re.transition=i,H=r}return null}function Dp(e,t,n,r){do gn();while(yt!==null);if(V&6)throw Error(L(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(L(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(mf(e,l),e===oe&&(re=oe=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Vr||(Vr=!0,Yc(si,function(){return gn(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=Re.transition,Re.transition=null;var s=H;H=1;var a=V;V|=4,oa.current=null,Np(e,n),Fc(n,e),ep(Zl),oi=!!ql,Zl=ql=null,e.current=n,Lp(n),lf(),V=a,H=s,Re.transition=l}else e.current=n;if(Vr&&(Vr=!1,yt=e,Ti=i),l=e.pendingLanes,l===0&&(Ct=null),of(n.stateNode),ke(e,te()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Ei)throw Ei=!1,e=ys,ys=null,e;return Ti&1&&e.tag!==0&&gn(),l=e.pendingLanes,l&1?e===xs?Jn++:(Jn=0,xs=e):Jn=0,Mt(),null}function gn(){if(yt!==null){var e=Tu(Ti),t=Re.transition,n=H;try{if(Re.transition=null,H=16>e?16:e,yt===null)var r=!1;else{if(e=yt,yt=null,Ti=0,V&6)throw Error(L(331));var i=V;for(V|=4,M=e.current;M!==null;){var l=M,s=l.child;if(M.flags&16){var a=l.deletions;if(a!==null){for(var o=0;o<a.length;o++){var c=a[o];for(M=c;M!==null;){var f=M;switch(f.tag){case 0:case 11:case 15:qn(8,f,l)}var p=f.child;if(p!==null)p.return=f,M=p;else for(;M!==null;){f=M;var v=f.sibling,y=f.return;if(Ic(f),f===c){M=null;break}if(v!==null){v.return=y,M=v;break}M=y}}}var g=l.alternate;if(g!==null){var w=g.child;if(w!==null){g.child=null;do{var N=w.sibling;w.sibling=null,w=N}while(w!==null)}}M=l}}if(l.subtreeFlags&2064&&s!==null)s.return=l,M=s;else e:for(;M!==null;){if(l=M,l.flags&2048)switch(l.tag){case 0:case 11:case 15:qn(9,l,l.return)}var h=l.sibling;if(h!==null){h.return=l.return,M=h;break e}M=l.return}}var d=e.current;for(M=d;M!==null;){s=M;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,M=m;else e:for(s=d;M!==null;){if(a=M,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:$i(9,a)}}catch(S){ee(a,a.return,S)}if(a===s){M=null;break e}var x=a.sibling;if(x!==null){x.return=a.return,M=x;break e}M=a.return}}if(V=i,Mt(),qe&&typeof qe.onPostCommitFiberRoot=="function")try{qe.onPostCommitFiberRoot(Di,e)}catch{}r=!0}return r}finally{H=n,Re.transition=t}}return!1}function No(e,t,n){t=Tn(n,t),t=Tc(e,t,1),e=Tt(e,t,1),t=ye(),e!==null&&(xr(e,1,t),ke(e,t))}function ee(e,t,n){if(e.tag===3)No(e,e,n);else for(;t!==null;){if(t.tag===3){No(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ct===null||!Ct.has(r))){e=Tn(n,e),e=Cc(t,e,1),t=Tt(t,e,1),e=ye(),t!==null&&(xr(t,1,e),ke(t,e));break}}t=t.return}}function Ip(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ye(),e.pingedLanes|=e.suspendedLanes&n,oe===e&&(ce&n)===n&&(se===4||se===3&&(ce&130023424)===ce&&500>te()-ca?Vt(e,0):ua|=n),ke(e,t)}function Uc(e,t){t===0&&(e.mode&1?(t=Pr,Pr<<=1,!(Pr&130023424)&&(Pr=4194304)):t=1);var n=ye();e=ut(e,t),e!==null&&(xr(e,t,n),ke(e,n))}function Ap(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Uc(e,n)}function Rp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(L(314))}r!==null&&r.delete(t),Uc(e,n)}var Wc;Wc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Te.current)Ee=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ee=!1,Tp(e,t,n);Ee=!!(e.flags&131072)}else Ee=!1,K&&t.flags&1048576&&Xu(t,hi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Zr(e,t),e=t.pendingProps;var i=wn(t,ve.current);vn(t,n),i=ra(null,t,r,e,i,n);var l=ia();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ce(r)?(l=!0,pi(t)):l=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Zs(t),i.updater=Bi,t.stateNode=i,i._reactInternals=t,as(t,r,e,n),t=cs(null,t,r,!0,l,n)):(t.tag=0,K&&l&&Us(t),ge(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Zr(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=bp(r),e=Be(r,e),i){case 0:t=us(null,t,r,e,n);break e;case 1:t=yo(null,t,r,e,n);break e;case 11:t=vo(null,t,r,e,n);break e;case 14:t=go(null,t,r,Be(r.type,e),n);break e}throw Error(L(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Be(r,i),us(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Be(r,i),yo(e,t,r,i,n);case 3:e:{if(Lc(t),e===null)throw Error(L(387));r=t.pendingProps,l=t.memoizedState,i=l.element,nc(e,t),yi(t,r,null,n);var s=t.memoizedState;if(r=s.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){i=Tn(Error(L(423)),t),t=xo(e,t,r,n,i);break e}else if(r!==i){i=Tn(Error(L(424)),t),t=xo(e,t,r,n,i);break e}else for(Le=Et(t.stateNode.containerInfo.firstChild),Pe=t,K=!0,He=null,n=ec(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Sn(),r===i){t=ct(e,t,n);break e}ge(e,t,r,n)}t=t.child}return t;case 5:return rc(t),e===null&&is(t),r=t.type,i=t.pendingProps,l=e!==null?e.memoizedProps:null,s=i.children,Jl(r,i)?s=null:l!==null&&Jl(r,l)&&(t.flags|=32),Nc(e,t),ge(e,t,s,n),t.child;case 6:return e===null&&is(t),null;case 13:return Pc(e,t,n);case 4:return Js(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=_n(t,null,r,n):ge(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Be(r,i),vo(e,t,r,i,n);case 7:return ge(e,t,t.pendingProps,n),t.child;case 8:return ge(e,t,t.pendingProps.children,n),t.child;case 12:return ge(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,l=t.memoizedProps,s=i.value,G(vi,r._currentValue),r._currentValue=s,l!==null)if(We(l.value,s)){if(l.children===i.children&&!Te.current){t=ct(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var a=l.dependencies;if(a!==null){s=l.child;for(var o=a.firstContext;o!==null;){if(o.context===r){if(l.tag===1){o=st(-1,n&-n),o.tag=2;var c=l.updateQueue;if(c!==null){c=c.shared;var f=c.pending;f===null?o.next=o:(o.next=f.next,f.next=o),c.pending=o}}l.lanes|=n,o=l.alternate,o!==null&&(o.lanes|=n),ls(l.return,n,t),a.lanes|=n;break}o=o.next}}else if(l.tag===10)s=l.type===t.type?null:l.child;else if(l.tag===18){if(s=l.return,s===null)throw Error(L(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),ls(s,n,t),s=l.sibling}else s=l.child;if(s!==null)s.return=l;else for(s=l;s!==null;){if(s===t){s=null;break}if(l=s.sibling,l!==null){l.return=s.return,s=l;break}s=s.return}l=s}ge(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,vn(t,n),i=Fe(i),r=r(i),t.flags|=1,ge(e,t,r,n),t.child;case 14:return r=t.type,i=Be(r,t.pendingProps),i=Be(r.type,i),go(e,t,r,i,n);case 15:return kc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Be(r,i),Zr(e,t),t.tag=1,Ce(r)?(e=!0,pi(t)):e=!1,vn(t,n),Ec(t,r,i),as(t,r,i,n),cs(null,t,r,!0,e,n);case 19:return Oc(e,t,n);case 22:return jc(e,t,n)}throw Error(L(156,t.tag))};function Yc(e,t){return wu(e,t)}function Fp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ae(e,t,n,r){return new Fp(e,t,n,r)}function ma(e){return e=e.prototype,!(!e||!e.isReactComponent)}function bp(e){if(typeof e=="function")return ma(e)?1:0;if(e!=null){if(e=e.$$typeof,e===zs)return 11;if(e===Ds)return 14}return 2}function jt(e,t){var n=e.alternate;return n===null?(n=Ae(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ti(e,t,n,r,i,l){var s=2;if(r=e,typeof e=="function")ma(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Jt:return Bt(n.children,i,l,t);case Ms:s=8,i|=8;break;case Ol:return e=Ae(12,n,t,i|2),e.elementType=Ol,e.lanes=l,e;case Ml:return e=Ae(13,n,t,i),e.elementType=Ml,e.lanes=l,e;case zl:return e=Ae(19,n,t,i),e.elementType=zl,e.lanes=l,e;case ru:return Gi(n,i,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case tu:s=10;break e;case nu:s=9;break e;case zs:s=11;break e;case Ds:s=14;break e;case pt:s=16,r=null;break e}throw Error(L(130,e==null?e:typeof e,""))}return t=Ae(s,n,t,i),t.elementType=e,t.type=r,t.lanes=l,t}function Bt(e,t,n,r){return e=Ae(7,e,r,t),e.lanes=n,e}function Gi(e,t,n,r){return e=Ae(22,e,r,t),e.elementType=ru,e.lanes=n,e.stateNode={isHidden:!1},e}function wl(e,t,n){return e=Ae(6,e,null,t),e.lanes=n,e}function Sl(e,t,n){return t=Ae(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Vp(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=tl(0),this.expirationTimes=tl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=tl(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ha(e,t,n,r,i,l,s,a,o){return e=new Vp(e,t,n,a,o),t===1?(t=1,l===!0&&(t|=8)):t=0,l=Ae(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zs(l),e}function Bp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Zt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Qc(e){if(!e)return Lt;e=e._reactInternals;e:{if(Kt(e)!==e||e.tag!==1)throw Error(L(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ce(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(L(171))}if(e.tag===1){var n=e.type;if(Ce(n))return Qu(e,n,t)}return t}function Kc(e,t,n,r,i,l,s,a,o){return e=ha(n,r,!0,e,i,l,s,a,o),e.context=Qc(null),n=e.current,r=ye(),i=kt(n),l=st(r,i),l.callback=t??null,Tt(n,l,i),e.current.lanes=i,xr(e,i,r),ke(e,r),e}function Ui(e,t,n,r){var i=t.current,l=ye(),s=kt(i);return n=Qc(n),t.context===null?t.context=n:t.pendingContext=n,t=st(l,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Tt(i,t,s),e!==null&&(Ue(e,i,s,l),Kr(e,i,s)),s}function ki(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Lo(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function va(e,t){Lo(e,t),(e=e.alternate)&&Lo(e,t)}function $p(){return null}var Xc=typeof reportError=="function"?reportError:function(e){console.error(e)};function ga(e){this._internalRoot=e}Wi.prototype.render=ga.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(L(409));Ui(e,t,null,null)};Wi.prototype.unmount=ga.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Wt(function(){Ui(null,e,null,null)}),t[ot]=null}};function Wi(e){this._internalRoot=e}Wi.prototype.unstable_scheduleHydration=function(e){if(e){var t=ju();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ht.length&&t!==0&&t<ht[n].priority;n++);ht.splice(n,0,e),n===0&&Lu(e)}};function ya(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Yi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Po(){}function Hp(e,t,n,r,i){if(i){if(typeof r=="function"){var l=r;r=function(){var c=ki(s);l.call(c)}}var s=Kc(t,r,e,0,null,!1,!1,"",Po);return e._reactRootContainer=s,e[ot]=s.current,ur(e.nodeType===8?e.parentNode:e),Wt(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var c=ki(o);a.call(c)}}var o=ha(e,0,!1,null,null,!1,!1,"",Po);return e._reactRootContainer=o,e[ot]=o.current,ur(e.nodeType===8?e.parentNode:e),Wt(function(){Ui(t,o,n,r)}),o}function Qi(e,t,n,r,i){var l=n._reactRootContainer;if(l){var s=l;if(typeof i=="function"){var a=i;i=function(){var o=ki(s);a.call(o)}}Ui(t,s,e,i)}else s=Hp(n,t,e,i,r);return ki(s)}Cu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Hn(t.pendingLanes);n!==0&&(Rs(t,n|1),ke(t,te()),!(V&6)&&(Cn=te()+500,Mt()))}break;case 13:Wt(function(){var r=ut(e,1);if(r!==null){var i=ye();Ue(r,e,1,i)}}),va(e,1)}};Fs=function(e){if(e.tag===13){var t=ut(e,134217728);if(t!==null){var n=ye();Ue(t,e,134217728,n)}va(e,134217728)}};ku=function(e){if(e.tag===13){var t=kt(e),n=ut(e,t);if(n!==null){var r=ye();Ue(n,e,t,r)}va(e,t)}};ju=function(){return H};Nu=function(e,t){var n=H;try{return H=e,t()}finally{H=n}};Hl=function(e,t,n){switch(t){case"input":if(Al(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Fi(r);if(!i)throw Error(L(90));lu(r),Al(r,i)}}}break;case"textarea":au(e,n);break;case"select":t=n.value,t!=null&&fn(e,!!n.multiple,t,!1)}};mu=da;hu=Wt;var Gp={usingClientEntryPoint:!1,Events:[Sr,rn,Fi,fu,pu,da]},Fn={findFiberByHostInstance:Rt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Up={bundleType:Fn.bundleType,version:Fn.version,rendererPackageName:Fn.rendererPackageName,rendererConfig:Fn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:dt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=yu(e),e===null?null:e.stateNode},findFiberByHostInstance:Fn.findFiberByHostInstance||$p,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Br=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Br.isDisabled&&Br.supportsFiber)try{Di=Br.inject(Up),qe=Br}catch{}}Me.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Gp;Me.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ya(t))throw Error(L(200));return Bp(e,t,null,n)};Me.createRoot=function(e,t){if(!ya(e))throw Error(L(299));var n=!1,r="",i=Xc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ha(e,1,!1,null,null,n,!1,r,i),e[ot]=t.current,ur(e.nodeType===8?e.parentNode:e),new ga(t)};Me.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(L(188)):(e=Object.keys(e).join(","),Error(L(268,e)));return e=yu(t),e=e===null?null:e.stateNode,e};Me.flushSync=function(e){return Wt(e)};Me.hydrate=function(e,t,n){if(!Yi(t))throw Error(L(200));return Qi(null,e,t,!0,n)};Me.hydrateRoot=function(e,t,n){if(!ya(e))throw Error(L(405));var r=n!=null&&n.hydratedSources||null,i=!1,l="",s=Xc;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Kc(t,null,e,1,n??null,i,!1,l,s),e[ot]=t.current,ur(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Wi(t)};Me.render=function(e,t,n){if(!Yi(t))throw Error(L(200));return Qi(null,e,t,!1,n)};Me.unmountComponentAtNode=function(e){if(!Yi(e))throw Error(L(40));return e._reactRootContainer?(Wt(function(){Qi(null,null,e,!1,function(){e._reactRootContainer=null,e[ot]=null})}),!0):!1};Me.unstable_batchedUpdates=da;Me.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yi(n))throw Error(L(200));if(e==null||e._reactInternals===void 0)throw Error(L(38));return Qi(e,t,n,!1,r)};Me.version="18.3.1-next-f1338f8080-20240426";function qc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(qc)}catch(e){console.error(e)}}qc(),qo.exports=Me;var Wp=qo.exports,Oo=Wp;Ll.createRoot=Oo.createRoot,Ll.hydrateRoot=Oo.hydrateRoot;const Yp=(e,t)=>{let n;return function(){clearTimeout(n),n=setTimeout(()=>{e()},t)}},Zc=e=>{let t="",n="";return typeof e=="object"&&Object.keys(e).forEach(function(r){t+=n+r+"="+encodeURIComponent(e[r]),n="&"}),t},Qp=(e,t=!1)=>{const n=new XMLHttpRequest;return n.open("POST",localize.ajaxurl,t),n.setRequestHeader("Content-Type","application/x-www-form-urlencoded; charset=UTF-8"),n.send(Zc(e)),t?n:JSON.parse(n.responseText)},Kp=e=>{const t={method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},body:Zc(e)};return fetch(localize.ajaxurl,t).then(n=>n.json()).then(n=>n).catch(n=>console.error("Fetch Error:",n))},Jc=(e=null,t=void 0)=>{const n=localStorage.getItem("eael_dashboard")?JSON.parse(localStorage.getItem("eael_dashboard")):{};return e?(n==null?void 0:n[e])??t:n},Xp=(e,t)=>{let n=Jc();n[e]=t,localStorage.setItem("eael_dashboard",JSON.stringify(n))};function kn({eaState:e,eaDispatch:t},n,r={}){const i=localize.eael_dashboard;let l,s;switch(n){case"SAVE_MODAL_DATA":s={action:"save_settings_with_ajax",security:localize.nonce,...r},l={toasts:!0,toastType:"error",toastMessage:i.i18n.toaster_error_msg,btnLoader:""},bn(s).then(a=>{a!=null&&a.success&&(l={...l,modal:"close",toastType:"success",toastMessage:i.i18n.toaster_success_msg}),t({type:n,payload:l})});return;case"SAVE_ELEMENTS_DATA":s={action:"save_settings_with_ajax",security:localize.nonce,elements:!0},l={toastType:"error",toastMessage:i.i18n.toaster_error_msg},Object.keys(e.elements).map(a=>{e.elements[a]===!0&&(s[a]=!0)}),bn(s).then(a=>{a!=null&&a.success&&(l={...l,toastType:"success",toastMessage:i.i18n.toaster_success_msg}),t({type:n,payload:l})});return;case"SAVE_TOOLS":s={action:"save_settings_with_ajax",security:localize.nonce,[r.key]:r.value},l={toastType:"error",toastMessage:i.i18n.toaster_error_msg},bn(s).then(a=>{a!=null&&a.success&&(l={...l,toastType:"success",toastMessage:i.i18n.toaster_success_msg}),t({type:n,payload:l})});return;case"REGENERATE_ASSETS":s={action:"clear_cache_files_with_ajax",security:localize.nonce},l={toastType:"error",toastMessage:i.i18n.toaster_error_msg},bn(s).then(a=>{a===!0&&(l={...l,toastType:"success",toastMessage:"Assets Regenerated!"}),t({type:n,payload:l})});return;case"INSTALL_TEMPLATELY":s={action:"wpdeveloper_install_plugin",security:localize.nonce,slug:"templately"},bn(s).then(()=>{t({type:n})})}}const Mo=Yp,jn=Qp,bn=Kp,qp=Jc,Zp=Xp,ed=R.createContext(),Y=()=>R.useContext(ed),ie=localize.eael_dashboard,qt=typeof wpdeveloperLicenseData>"u"?{}:wpdeveloperLicenseData,he={menu:"general",integrations:{},extensions:[],widgets:{},elements:{},proElements:[],extensionAll:!1,widgetAll:!1,licenseStatus:qt==null?void 0:qt.license_status,hiddenLicenseKey:qt==null?void 0:qt.hidden_license_key,modals:{},elementsActivateCatIndex:0,isDark:qp("isDark",!1),isTemplatelyInstalled:ie.is_templately_installed,toasts:!1,modalAccordion:Object.keys(ie.modal.loginRegisterSetting.accordion)[0],btnLoader:"",optinPromo:ie.admin_screen_promo.display,search404:!1,licenseFormOpen:!0};Object.keys(ie.integration_box.list).map(e=>{he.integrations[e]=ie.integration_box.list[e].status});Object.keys(ie.extensions.list).map(e=>{he.extensions.push(e),he.elements[e]=ie.extensions.list[e].is_activate,!ie.is_eapro_activate&&ie.extensions.list[e].is_pro&&(he.proElements.push(e),he.elements[e]=!1)});Object.keys(ie.widgets).map(e=>{he.widgets[e]=[],Object.keys(ie.widgets[e].elements).map(t=>{he.widgets[e].push(t),he.elements[t]=ie.widgets[e].elements[t].is_activate,!ie.is_eapro_activate&&ie.widgets[e].elements[t].is_pro&&(he.proElements.push(t),he.elements[t]=!1)})});ie.el_disabled_elements.map(e=>{const t=ie.replace_widget_old2new[e]??e;console.log(t,t.substring(5)),Object.keys(he.elements).includes(t.substring(5))&&(he.elements[t.substring(5)]=!1)});Object.keys(ie.modal).map(e=>{var n;const t=(n=ie.modal[e])==null?void 0:n.name;if(t!==void 0)he.modals[t]=ie.modal[e].value;else if(e==="loginRegisterSetting"){const r=ie.modal[e].accordion;Object.keys(r).map(i=>{var l,s,a,o;r[i].fields.map(c=>{const f=c==null?void 0:c.name;f!==void 0&&(he.modals[f]=c==null?void 0:c.value)}),((l=r[i])==null?void 0:l.status)!==void 0&&(he.modals[(s=r[i])==null?void 0:s.status.name]=(o=(a=r[i])==null?void 0:a.status)==null?void 0:o.value)})}});const Jp=ed.Provider;function em(){const{eaState:e,eaDispatch:t}=Y(),n=e.isDark?"images/EA-Logo-Dark.svg":"images/EA-Logo.svg",r=()=>{t({type:"LIGHT_DARK_TOGGLE"})};return u.jsx(u.Fragment,{children:u.jsx("section",{className:"ea__section-header",children:u.jsxs("div",{className:"ea__section-wrapper ea__header-content",children:[u.jsx("img",{src:localize.eael_dashboard.reactPath+n,alt:"logo"}),u.jsx("span",{className:"dark-icon pointer",onClick:r,children:u.jsx("i",{className:e.isDark?"ea-dash-icon ea-moon":"ea-dash-icon ea-sun"})})]})})})}function tm(e){const t=localize.eael_dashboard.menu[e.item],n=t.label,r=t.icon,{eaState:i,eaDispatch:l}=Y(),s=()=>{l({type:"SET_MENU",payload:e.item}),window.dispatchEvent(new Event("resize"))};return u.jsx(u.Fragment,{children:u.jsxs("div",{className:i.menu===e.item?"ea__sidebar-nav active":"ea__sidebar-nav",onClick:s,children:[u.jsx("span",{className:"ea__nav-icon",children:u.jsx("i",{className:r+" ea-dash-icon"})}),u.jsx("span",{className:"ea__nav-text",children:n})]})})}function nm(){const e=localize.eael_dashboard.menu,t=localize.eael_dashboard.is_eapro_activate,{eaState:n}=Y();return u.jsx(u.Fragment,{children:u.jsxs("div",{className:n.menu==="Elements"?"ea__sidebar-nav-list ea__elements-nav":"ea__sidebar-nav-list",children:[u.jsx("div",{className:"nav-sticky",children:Object.keys(e).map((r,i)=>{if(!(r==="go-premium"&&t))return u.jsx(tm,{item:r},i)})}),u.jsx("div",{})]})})}function rm(){const e=localize.eael_dashboard.whats_new;return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__general-content-item relative",children:[u.jsx("h3",{children:e.heading}),u.jsx("div",{className:"mb-6 flex flex-col gap-4",children:e.list.map((t,n)=>u.jsxs("div",{className:"ea__content-details flex gap-2",children:[u.jsx("span",{children:u.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[u.jsx("path",{d:"M3.00229 8.32568C2.5963 8.18129 2.5963 7.60714 3.00229 7.46275L6.08799 6.36538C6.21776 6.31923 6.31986 6.21713 6.36601 6.08736L7.46338 3.00166C7.60777 2.59567 8.18192 2.59567 8.32631 3.00166L9.42368 6.08736C9.46983 6.21713 9.57194 6.31923 9.7017 6.36538L12.7874 7.46275C13.1934 7.60714 13.1934 8.18129 12.7874 8.32568L9.7017 9.42305C9.57194 9.4692 9.46983 9.5713 9.42368 9.70107L8.32631 12.7868C8.18193 13.1928 7.60777 13.1928 7.46339 12.7868L6.36601 9.70107C6.31986 9.57131 6.21776 9.4692 6.08799 9.42305L3.00229 8.32568Z",fill:"#9189E1"}),u.jsx("path",{d:"M3.19451 5.74733C3.1436 5.87596 2.96155 5.87596 2.91064 5.74733L2.21125 3.98016C2.19573 3.94095 2.16469 3.90991 2.12549 3.8944L0.358309 3.195C0.22968 3.14409 0.22968 2.96204 0.358309 2.91113L2.12549 2.21174C2.16469 2.19622 2.19573 2.16518 2.21125 2.12598L2.91064 0.358798C2.96155 0.230169 3.1436 0.230169 3.19451 0.358798L3.89391 2.12598C3.90942 2.16518 3.94046 2.19622 3.97967 2.21174L5.74684 2.91113C5.87547 2.96204 5.87547 3.14409 5.74684 3.195L3.97967 3.8944C3.94046 3.90991 3.90942 3.94095 3.89391 3.98016L3.19451 5.74733Z",fill:"#EBE9FE"})]})}),u.jsxs("div",{children:[u.jsx("span",{className:"title--ex",children:t.label}),t.content]})]},n))}),u.jsx("a",{href:e.button.url,target:"_blank",children:u.jsxs("button",{className:"primary-btn changelog-btn",children:[e.button.label,u.jsx("i",{className:"ea-dash-icon ea-link"})]})})]})})}function im(){const e=localize.eael_dashboard.templately_promo,t=localize.eael_dashboard.i18n,{eaState:n,eaDispatch:r}=Y(),i=(n.isDark,"/images/templates-img.png"),l=()=>{r({type:"BUTTON_LOADER",payload:"tl"}),kn({eaState:n,eaDispatch:r},"INSTALL_TEMPLATELY")};return u.jsx(u.Fragment,{children:u.jsx("div",{className:"ea__general-content-item templates",children:u.jsxs("div",{className:"ea__templates-content-wrapper flex justify-between items-center",children:[u.jsxs("div",{className:"templates-content",children:[u.jsx("h2",{children:e.heading}),u.jsx("div",{className:"mb-6 flex flex-col gap-4",children:e.list.map((s,a)=>u.jsxs("div",{className:"ea__content-details flex gap-2",children:[u.jsx("span",{className:"check-icon ea-dash-icon ea-check"}),s]},a))}),u.jsxs("button",{className:"primary-btn install-btn",onClick:l,children:[u.jsx("i",{className:"ea-dash-icon ea-install"}),n.btnLoader==="tl"?t.enabling:e.button.label,n.btnLoader==="tl"&&u.jsx("span",{className:"eael_btn_loader"})]})]}),u.jsx("div",{className:"templates-img",children:u.jsx("img",{src:localize.eael_dashboard.reactPath+i,alt:"img"})})]})})})}function dn(e){const t=localize.eael_dashboard.community_box[e.index];return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__connect-others",children:[u.jsx("div",{className:"ea__others-icon "+t.icon_color,children:u.jsx("i",{className:t.icon+" ea-dash-icon"})}),u.jsx("h5",{children:t.heading}),u.jsx("p",{className:"mb-6",dangerouslySetInnerHTML:{__html:t.content}}),t.button===void 0||u.jsx("a",{href:t.button.url,target:"_blank",children:u.jsxs("button",{children:[u.jsx("span",{className:"underline",children:t.button.label}),u.jsx("i",{className:"ea-dash-icon ea-right-arrow"})]})})]})})}function lm(){const e=localize.eael_dashboard.sidebar_box;return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__sidebar-content",children:[u.jsx("h5",{children:e.heading}),u.jsx("p",{children:e.content}),u.jsxs("div",{className:"review-wrap",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx("i",{className:"ea-dash-icon ea-star"}),u.jsx("h6",{children:e.review.count})]}),u.jsx("span",{className:"reating-details",children:e.review.label})]}),u.jsx("a",{href:e.button.url,target:"_blank",children:u.jsxs("button",{className:"upgrade-button",children:[u.jsx("i",{className:"ea-dash-icon "+e.button.icon}),e.button.label]})})]})})}function sm(){return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__license-step",children:[u.jsxs("div",{className:"ea__license-step-items flex",children:[u.jsx("span",{className:"step-count",children:"1"}),u.jsxs("p",{children:["Log in to ",u.jsx("a",{href:"https://store.wpdeveloper.com/",target:"_blank",className:"step-details-ex",children:"your account"})," to get your license key."]})]}),u.jsxs("div",{className:"ea__license-step-items flex",children:[u.jsx("span",{className:"step-count",children:"2"}),u.jsxs("p",{children:["If you don't yet have a license key, get ",u.jsx("a",{href:"https://essential-addons.com/upgrade-ea-pro",className:"step-details-ex",target:"_blank",children:"Essential Addons Pro now"}),"."]})]}),u.jsxs("div",{className:"ea__license-step-items flex",children:[u.jsx("span",{className:"step-count",children:"3"}),u.jsx("p",{children:"Copy the license key from your account and paste it below."})]}),u.jsxs("div",{className:"ea__license-step-items flex",children:[u.jsx("span",{className:"step-count",children:"4"}),u.jsxs("p",{children:["Click on the ",u.jsx("span",{className:"step-details-ex",children:'"Activate License"'})," button."]})]})]})})}function zo(){const e=R.useRef(),{eaState:t,eaDispatch:n}=Y(),r=()=>{const o=typeof wpdeveloperLicenseManagerConfig>"u"?{}:wpdeveloperLicenseManagerConfig;if(n({type:"BUTTON_LOADER",payload:"license"}),t.licenseStatus!=="valid"){const c={action:"essential-addons-elementor/license/activate",license_key:e.current.value,_nonce:o==null?void 0:o.nonce},f=jn(c,!0);f.onreadystatechange=()=>{var d,m,x,S,_;const p=f.responseText?JSON.parse(f.responseText):{};let v=!1,y=!1,g=(d=p.data)==null?void 0:d.customer_email,w,N,h;if(p!=null&&p.success)switch(p.data.license){case"required_otp":y=!0,N=(m=p.data)==null?void 0:m.license_key;break;case"valid":w=(x=p.data)==null?void 0:x.license,N=(S=p.data)==null?void 0:S.license_key;break}else v=!0,h=(_=p.data)==null?void 0:_.message;n({type:"LICENSE_ACTIVATE",payload:{otp:y,licenseStatus:w,hiddenLicenseKey:N,licenseError:v,otpEmail:g,errorMessage:h,licenseKey:c.license_key}})}}else{const c={action:"essential-addons-elementor/license/deactivate",_nonce:o==null?void 0:o.nonce},f=jn(c,!0);f.onreadystatechange=()=>{var N;const p=f.responseText?JSON.parse(f.responseText):{};let v=!1,y,g,w;p!=null&&p.success?(y="",g=""):(v=!0,w=(N=p==null?void 0:p.data)==null?void 0:N.message),n({type:"LICENSE_DEACTIVATE",payload:{licenseStatus:y,hiddenLicenseKey:g,licenseError:v,errorMessage:w}})}}},i=t.otp===!0||t.licenseStatus==="valid",l=(t==null?void 0:t.licenseError)===!0,s=t.btnLoader==="license"?"Activating...":t.otp===!0?"Verification Required":"Activate License",a=t.btnLoader==="license"?"Deactivating":"Deactivate";return u.jsx(u.Fragment,{children:u.jsxs("div",{className:l?"ea__license-key ea__invalid-license":"ea__license-key",children:[u.jsxs("div",{className:"license-key-items flex items-center",children:[u.jsx("i",{className:"ea-dash-icon  ea-key"}),u.jsx("input",{ref:e,disabled:i,className:"input-api",type:"text",placeholder:t.hiddenLicenseKey||"Place Your License Key and Activate"}),u.jsxs("button",{className:t.licenseStatus==="valid"?"primary-btn install-btn deactivated":"primary-btn install-btn",onClick:r,disabled:t.otp===!0,children:[t.licenseStatus==="valid"?a:s,t.btnLoader==="license"&&u.jsx("span",{className:"eael_btn_loader"})]})]}),l&&u.jsxs("div",{className:"invalid-text flex items-center",children:[u.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:u.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M5.56222 2.33424C5.75217 1.98885 6.24847 1.98885 6.43842 2.33423L10.2472 9.25918C10.4304 9.59243 10.1894 10.0001 9.80907 10.0001H2.19159C1.81129 10.0001 1.57021 9.59243 1.75348 9.25918L5.56222 2.33424ZM7.31462 1.85232C6.74477 0.816152 5.25587 0.816157 4.686 1.85232L0.877266 8.77728C0.327442 9.77698 1.05069 11.0001 2.19159 11.0001H9.80907C10.95 11.0001 11.6732 9.77698 11.1234 8.77728L7.31462 1.85232ZM6.00032 4.00018C6.27647 4.00018 6.50032 4.22404 6.50032 4.50018V6.50018C6.50032 6.77633 6.27647 7.00018 6.00032 7.00018C5.72417 7.00018 5.50032 6.77633 5.50032 6.50018V4.50018C5.50032 4.22404 5.72417 4.00018 6.00032 4.00018ZM6.50032 8.50018C6.50032 8.77633 6.27647 9.00018 6.00032 9.00018C5.72417 9.00018 5.50032 8.77633 5.50032 8.50018C5.50032 8.22403 5.72417 8.00018 6.00032 8.00018C6.27647 8.00018 6.50032 8.22403 6.50032 8.50018Z",fill:"#D92D20"})}),u.jsx("span",{children:t.errorMessage})]})]})})}function am(){const e=R.useRef(),{eaState:t,eaDispatch:n}=Y(),r=typeof wpdeveloperLicenseManagerConfig>"u"?{}:wpdeveloperLicenseManagerConfig,i=()=>{n({type:"BUTTON_LOADER",payload:"otp"});const c={action:"essential-addons-elementor/license/submit-otp",license:t.licenseKey,otp:e.current.value,_nonce:r==null?void 0:r.nonce},f=jn(c,!0);f.onreadystatechange=()=>{var h;const p=f.responseText?JSON.parse(f.responseText):{};let v,y,g=t.hiddenLicenseKey,w,N;p!=null&&p.success?(v=!1,y=p.data.license,g=p.data.license_key):(v=!0,w=!0,N=(h=p==null?void 0:p.data)==null?void 0:h.message),n({type:"OTP_VERIFY",payload:{licenseStatus:y,hiddenLicenseKey:g,otpError:w,errorMessage:N,otp:v}})}},l=()=>{n({type:"BUTTON_LOADER",payload:"resend"});const c={action:"essential-addons-elementor/license/resend-otp",_nonce:r==null?void 0:r.nonce,license:t.licenseKey},f=jn(c,!0);f.onreadystatechange=()=>{var g;const p=f.responseText?JSON.parse(f.responseText):{};let v,y;p!=null&&p.success?(v="success",y="A verification code sent to your email"):(v="error",y=(g=p==null?void 0:p.data)==null?void 0:g.message),n({type:"RESEND_OTP",payload:{toastType:v,toastMessage:y}})}},s=(t==null?void 0:t.otpError)===!0,a=t.btnLoader==="otp"?"Verifying...":"Verify",o=t.btnLoader==="resend"?"Resending...":"Resend button";return u.jsx(u.Fragment,{children:u.jsxs("div",{className:s?"ea__license-verify warning":"ea__license-verify",children:[u.jsxs("p",{children:["License Verification code has been sent to this email ",u.jsx("span",{children:t.otpEmail}),". Please check your email for the code & insert it below"]}),u.jsxs("div",{children:[u.jsxs("div",{className:"license-key-items flex items-center",children:[u.jsx("input",{ref:e,className:"input-api verify",type:"text",placeholder:"Enter Your Verification Code"}),u.jsxs("button",{className:"primary-btn verify-btn",onClick:i,children:[a," ",t.btnLoader==="otp"&&u.jsx("span",{className:"eael_btn_loader"})]})]}),s&&u.jsxs("div",{className:"invalid-text flex items-center",children:[u.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:u.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M5.56222 2.33424C5.75217 1.98885 6.24847 1.98885 6.43842 2.33423L10.2472 9.25918C10.4304 9.59243 10.1894 10.0001 9.80907 10.0001H2.19159C1.81129 10.0001 1.57021 9.59243 1.75348 9.25918L5.56222 2.33424ZM7.31462 1.85232C6.74477 0.816152 5.25587 0.816157 4.686 1.85232L0.877266 8.77728C0.327442 9.77698 1.05069 11.0001 2.19159 11.0001H9.80907C10.95 11.0001 11.6732 9.77698 11.1234 8.77728L7.31462 1.85232ZM6.00032 4.00018C6.27647 4.00018 6.50032 4.22404 6.50032 4.50018V6.50018C6.50032 6.77633 6.27647 7.00018 6.00032 7.00018C5.72417 7.00018 5.50032 6.77633 5.50032 6.50018V4.50018C5.50032 4.22404 5.72417 4.00018 6.00032 4.00018ZM6.50032 8.50018C6.50032 8.77633 6.27647 9.00018 6.00032 9.00018C5.72417 9.00018 5.50032 8.77633 5.50032 8.50018C5.50032 8.22403 5.72417 8.00018 6.00032 8.00018C6.27647 8.00018 6.50032 8.22403 6.50032 8.50018Z",fill:"#D92D20"})}),u.jsx("span",{children:t.errorMessage})]})]}),u.jsxs("div",{className:"resend-content",children:["Haven’t received email? Retry clicking on ",u.jsx("span",{className:"resend-text",onClick:l,children:o}),". Please note that this verification code will expire after 15 minutes.",u.jsx("span",{className:"info-icon-wrap",children:u.jsx("i",{className:"ea-dash-icon  ea-info",children:u.jsxs("span",{className:"tooltip-api",children:["Check out this ",u.jsx("a",{target:"_blank",className:"color-ex",href:"https://essential-addons.com/docs/verify-essential-addons-pro-license-key/",children:"guide"})," to verify your license key. If you need any assistance with retrieving your License Verification Key, please ",u.jsx("a",{href:"https://wpdeveloper.com/support/",target:"_blank",className:"color-ex",children:"contact support."})]})})})]})]})})}function om(){return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__unlock-license flex gap-4",children:[u.jsx("div",{className:"ea__others-icon eaicon-unlock",children:u.jsx("i",{className:"ea-dash-icon ea-lock"})}),u.jsxs("div",{className:"max-w-454",children:[u.jsx("h4",{children:"Activate License"}),u.jsxs("p",{children:["Enter your license key here, to activate Essential Addons Pro and get automated updates and premium support. Follow the steps below or get help from the ",u.jsx("a",{href:"https://essential-addons.com/docs/verify-essential-addons-pro-license-key/",target:"_blank",children:"Validation Guide"})," to activate the key."]})]})]})})}function um(){return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__active-license flex gap-4",children:[u.jsx("div",{className:"ea__others-icon eaicon-active",children:u.jsx("i",{className:"ea-dash-icon ea-lock"})}),u.jsxs("div",{className:"max-w-454",children:[u.jsx("h4",{children:"You have Unlocked Essential Addons Pro"}),u.jsx("p",{children:"Congratulations! Now build your dream website effortlessly with more advanced features & priority support."}),u.jsxs("span",{className:"activated-btn",children:[u.jsx("i",{className:"ea-dash-icon  ea-check"}),"Activated"]})]})]})})}function cm(){const{eaState:e,eaDispatch:t}=Y(),n=(e==null?void 0:e.licenseFormOpen)===!0,r=()=>{t({type:"OPEN_LICENSE_FORM",payload:!n})};return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__general-content-item license-unlock relative",children:[e.licenseStatus!=="valid"?u.jsx(om,{}):u.jsx(um,{}),u.jsxs("div",{className:"ea__license-wrapper",children:[e.licenseStatus!=="valid"&&u.jsxs("div",{className:"ea__license-content",onClick:r,children:[u.jsx("h5",{children:"How to get license key?"}),u.jsx("i",{className:n?"ea-dash-icon ea-dropdown rotate-180":"ea-dash-icon ea-dropdown"})]}),e.licenseStatus==="valid"&&u.jsx("div",{className:"ea__license-options-wrapper",children:u.jsx(zo,{})}),e.licenseStatus!=="valid"&&n&&u.jsxs("div",{className:"ea__license-options-wrapper",children:[u.jsx(sm,{}),u.jsx(zo,{}),(e==null?void 0:e.otp)===!0&&u.jsx(am,{})]})]})]})})}function dm(){const e=localize.eael_dashboard,{eaState:t}=Y(),n={activated:0,deactivated:e.is_eapro_activate?0:-t.proElements.length};return Object.keys(t.elements).map(r=>{t.elements[r]?n.activated++:n.deactivated++}),u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__connect-others",children:[u.jsxs("div",{className:"ea__elements-wrapper elements-1",children:[u.jsx("i",{className:"ea-elements ea-dash-icon"}),u.jsx("h4",{children:n.activated+n.deactivated}),u.jsx("span",{children:e.i18n.total_elements})]}),u.jsxs("div",{className:"ea__elements-wrapper elements-2",children:[u.jsx("i",{className:"ea-active ea-dash-icon"}),u.jsx("h4",{children:n.activated}),u.jsx("span",{children:e.i18n.active})]}),u.jsxs("div",{className:"ea__elements-wrapper elements-3",children:[u.jsx("i",{className:"ea-incative ea-dash-icon"}),u.jsx("h4",{children:n.deactivated}),u.jsx("span",{children:e.i18n.inactive})]})]})})}function fm(){const e=localize.eael_dashboard.video_promo,t=localize.eael_dashboard.reactPath;return u.jsx(u.Fragment,{children:u.jsx("div",{className:"ea__general-content-item video-promo",children:u.jsxs("div",{className:"video-promo-wrapper flex justify-between items-center gap-4",children:[u.jsxs("div",{className:"templates-content",children:[u.jsx("h2",{children:e.heading}),u.jsx("p",{className:"mb-6",children:e.content}),u.jsx("a",{href:e.button.playlist,target:"_blank",children:u.jsx("button",{className:"primary-btn install-btn",children:e.button.label})})]}),u.jsx("div",{className:"templates-img",children:u.jsxs("a",{href:e.button.url,target:"_blank",children:[u.jsx("img",{src:t+e.image,alt:"video promo"}),u.jsx("span",{children:u.jsx("i",{className:"ea-dash-icon ea-play"})})]})})]})})})}function pm(){const e=localize.eael_dashboard.is_eapro_activate,{eaState:t}=Y();return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__main-content-wrapper flex gap-4",children:[u.jsxs("div",{className:"ea__general-content--wrapper",children:[e?u.jsx(cm,{}):u.jsx(rm,{}),t.isTemplatelyInstalled?u.jsx(fm,{}):u.jsx(im,{}),u.jsxs("div",{className:"ea__connect-others-wrapper flex gap-4",children:[u.jsx(dn,{index:0}),u.jsx(dn,{index:1}),u.jsx(dn,{index:2})]})]}),u.jsxs("div",{className:"ea__sidebar-info",children:[u.jsxs("div",{className:"ea__sidebar-sticky",children:[e||u.jsx(lm,{}),u.jsx(dm,{}),u.jsx("div",{children:u.jsx(dn,{index:3})})]}),u.jsx("div",{})]})]})})}function xa(e){var f,p,v;const t=e.source[e.index],n=localize.eael_dashboard.is_eapro_activate,r=t.is_pro&&!n,{eaState:i,eaDispatch:l}=Y(),s=!r&&i.elements[e.index],a=y=>{l({type:"ON_CHANGE_ELEMENT",payload:{key:e.index,value:y.target.checked}})},o=()=>{l({type:"OPEN_MODAL",payload:{key:t.setting.id,title:t.title}})},c=()=>{l({type:"GO_PRO_MODAL"})};return u.jsx(u.Fragment,{children:u.jsxs("div",{className:t.is_pro?"ea__content-items eael--pro-elements":"ea__content-items ",children:[u.jsxs("div",{className:"ea__content-head",children:[u.jsx("h5",{className:"eael-toggle-label",children:t.title}),u.jsxs("label",{className:"toggle-wrap",onClick:t.is_pro&&!n?c:void 0,children:[u.jsx("input",{type:"checkbox",checked:s,disabled:r,onChange:a}),u.jsx("span",{className:t.is_pro&&!n?"slider pro":"slider"})]})]}),u.jsxs("div",{className:t.promotion?"ea__content-footer":"ea__content-footer ea-no-label",children:[t.promotion?u.jsx("span",{className:"content-btn "+t.promotion,children:t.promotion}):"",u.jsxs("div",{className:"content-icons",children:[u.jsx("a",{href:t.doc_link,target:"_blank",children:u.jsx("i",{className:"ea-dash-icon ea-docs"})}),u.jsx("a",{href:t.demo_link,target:"_blank",children:u.jsx("i",{className:"ea-dash-icon ea-link-2"})}),((f=t.setting)==null?void 0:f.link)!==void 0&&u.jsx("a",{href:(p=t.setting)==null?void 0:p.link.replace("&#038;","&"),target:"_blank",children:u.jsx("i",{className:"ea-dash-icon ea-settings"})}),(v=t.setting)!=null&&v.id?u.jsx("i",{className:"ea-dash-icon ea-settings",onClick:o}):""]})]})]})})}function mm(e){const t=localize.eael_dashboard.widgets[e.index],{eaState:n,eaDispatch:r}=Y(),i=n[e.index]||!1,l=localize.eael_dashboard.i18n,s=a=>{r({type:"ON_CHANGE_ALL",payload:{key:e.index,value:a.target.checked}})};return u.jsx(u.Fragment,{children:u.jsxs("div",{id:"ID-"+e.index,className:"ea__contents",children:[u.jsxs("div",{className:"flex items-center gap-2 justify-between mb-4",children:[u.jsx("h3",{className:"ea__content-title",children:t.title}),u.jsx("div",{className:"ea__enable-elements",children:u.jsxs("div",{className:"toggle-wrapper flex items-center gap-2",children:[u.jsx("h5",{children:i?l.disable_all:l.enable_all}),u.jsxs("label",{className:"toggle-wrap",children:[u.jsx("input",{type:"checkbox",checked:i,onChange:s}),u.jsx("span",{className:"slider"})]})]})})]}),u.jsx("div",{className:"ea__content-wrapper",children:Object.keys(t.elements).map((a,o)=>u.jsx(xa,{source:t.elements,index:a},o))})]})})}function hm(e){const t=localize.eael_dashboard.widgets[e.index],{eaState:n}=Y(),r=i=>{var l;i.preventDefault(),window.scrollTo({top:((l=e.subCatRef.current.children["ID-"+e.index])==null?void 0:l.offsetTop)+35+n.scrollOffset,behavior:"smooth"})};return u.jsx(u.Fragment,{children:u.jsx("a",{className:e.activateIndex===n.elementsActivateCatIndex?"ea__icon-wrapper active":"ea__icon-wrapper",onClick:r,children:u.jsx("i",{className:"ea-dash-icon "+t.icon,children:u.jsx("span",{className:"ea__tooltip",children:t.title})})})})}function vm(){const e=localize.eael_dashboard.reactPath,t=localize.eael_dashboard.i18n;return u.jsx(u.Fragment,{children:u.jsx("div",{className:"ea__contents",children:u.jsxs("div",{className:"ea__not-found-wrapper",children:[u.jsx("img",{src:e+"images/not-found.png",alt:"img"}),u.jsx("h5",{children:t.search_not_found})]})})})}function gm(e){const{eaState:t,eaDispatch:n}=Y(),r=t.searchAll||!1,i=localize.eael_dashboard.i18n,l=s=>{n({type:"ON_CHANGE_ALL",payload:{key:"searchAll",value:s.target.checked}})};return u.jsx(u.Fragment,{children:t.search404?u.jsx(vm,{}):u.jsxs("div",{id:"ID-search-section",className:"ea__contents",children:[u.jsxs("div",{className:"flex items-center gap-2 justify-between mb-4",children:[u.jsxs("h3",{className:"ea__content-title",children:[i.search_result_for," ",e.searchTerm]}),u.jsx("div",{className:"ea__enable-elements",children:u.jsxs("div",{className:"toggle-wrapper flex items-center gap-2",children:[u.jsx("h5",{children:r?i.disable_all:i.enable_all}),u.jsxs("label",{className:"toggle-wrap",children:[u.jsx("input",{type:"checkbox",checked:r,onChange:l}),u.jsx("span",{className:"slider"})]})]})})]}),u.jsx("div",{className:"ea__content-wrapper",children:Object.keys(t.search).map((s,a)=>u.jsx(xa,{source:t.search,index:s},a))})]})})}function ym(){var v,y;const e=localize.eael_dashboard.widgets,{eaState:t,eaDispatch:n}=Y(),r=t.widgetAll,i=localize.eael_dashboard.i18n,l=g=>{n({type:"ON_CHANGE_ALL",payload:{key:"widgetAll",value:g.target.checked}})},s=R.useRef(),a=R.useRef(),o=R.useRef(),c=()=>{let g={},w=s.current.value,N=a.current.value;if(N!=="")for(const h in e[N].elements)e[N].elements[h].title.toLowerCase().includes(w.toLowerCase())&&(g[h]=e[N].elements[h]);else Object.keys(e).map(h=>{for(const d in e[h].elements)e[h].elements[d].title.toLowerCase().includes(w.toLowerCase())&&(g[d]=e[h].elements[d])});n({type:"ON_SEARCH",payload:{value:g}})},f=()=>{n({type:"BUTTON_LOADER",payload:"elements"}),kn({eaState:t,eaDispatch:n},"SAVE_ELEMENTS_DATA")},p=()=>{var h,d,m,x,S,_,C,T,j;const g=window.pageYOffset-32-t.scrollOffset,w=o.current.children;let N=0;g>((h=w[9])==null?void 0:h.offsetTop)?N=9:g>((d=w[8])==null?void 0:d.offsetTop)?N=8:g>((m=w[7])==null?void 0:m.offsetTop)?N=7:g>((x=w[6])==null?void 0:x.offsetTop)?N=6:g>((S=w[5])==null?void 0:S.offsetTop)?N=5:g>((_=w[4])==null?void 0:_.offsetTop)?N=4:g>((C=w[3])==null?void 0:C.offsetTop)?N=3:g>((T=w[2])==null?void 0:T.offsetTop)?N=2:g>((j=w[1])==null?void 0:j.offsetTop)&&(N=1),n({type:"ELEMENTS_CAT",payload:N})};return R.useEffect(()=>(window.addEventListener("scroll",p),()=>{window.removeEventListener("scroll",p)}),[]),u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__elements-nav-content elements-contents",children:[u.jsxs("div",{className:"ea__content-header sticky",children:[u.jsxs("div",{className:"ea__content-info flex justify-between items-center gap-2",children:[u.jsxs("div",{className:"ea__widget-elements flex items-center",children:[u.jsx("h4",{children:"Elements"}),u.jsxs("div",{className:"search--widget flex",children:[u.jsx("div",{className:"ea__input-search-wrapper",children:u.jsx("input",{ref:s,onChange:Mo(c,500),className:"input-name",type:"search",placeholder:"Search by name"})}),u.jsx("div",{className:"select-option-wrapper",children:u.jsxs("select",{ref:a,onChange:Mo(c,100),name:"select",id:"select-option",className:"form-select",children:[u.jsx("option",{value:"",children:i.all_widgets}),Object.keys(e).map((g,w)=>u.jsx("option",{value:g,children:e[g].title},w))]})})]})]}),u.jsx("div",{className:"ea__enable-elements",children:u.jsxs("div",{className:"toggle-wrapper flex items-center gap-2",children:[u.jsx("h5",{children:r?i.disable_all_elements:i.enable_all_elements}),u.jsxs("label",{className:"toggle-wrap",children:[u.jsx("input",{type:"checkbox",checked:r,onChange:l}),u.jsx("span",{className:"slider"})]})]})})]}),u.jsx("div",{className:"ea__content-icon flex",children:Object.keys(e).map((g,w)=>u.jsx(hm,{index:g,activateIndex:w,subCatRef:o},w))})]}),u.jsxs("div",{className:"ea__content-elements-wrapper relative",ref:o,children:[!!((v=s==null?void 0:s.current)!=null&&v.value)||Object.keys(e).map((g,w)=>u.jsx(mm,{index:g},w)),!!((y=s==null?void 0:s.current)!=null&&y.value)&&u.jsx(gm,{searchTerm:s.current.value})]}),t.search404||u.jsxs("div",{className:"ea__elements-button-wrap",children:[u.jsxs("button",{className:"primary-btn install-btn",onClick:f,children:[i.save_settings," ",t.btnLoader==="elements"&&u.jsx("span",{className:"eael_btn_loader"})]}),u.jsx("div",{className:"ea__section-overlay"})]})]})})}function xm(){const e=localize.eael_dashboard.extensions,{eaState:t,eaDispatch:n}=Y(),r=t.extensionAll,i=localize.eael_dashboard.i18n,l=a=>{n({type:"ON_CHANGE_ALL",payload:{key:"extensionAll",value:a.target.checked}})},s=()=>{n({type:"BUTTON_LOADER",payload:"extensions"}),kn({eaState:t,eaDispatch:n},"SAVE_ELEMENTS_DATA")};return u.jsx(u.Fragment,{children:u.jsx("div",{className:"ea__elements-nav-content",children:u.jsxs("div",{className:"ea__premimu-extensions-wrapper",children:[u.jsxs("div",{className:"ea__contents mb-4",children:[u.jsxs("div",{className:"flex items-center gap-2 justify-between mb-4",children:[u.jsx("h3",{className:"ea__content-title title",children:e.heading}),u.jsx("div",{className:"ea__enable-elements",children:u.jsxs("div",{className:"toggle-wrapper flex items-center gap-2",children:[u.jsx("h5",{children:r?i.disable_all:i.enable_all}),u.jsxs("label",{className:"toggle-wrap",children:[u.jsx("input",{type:"checkbox",checked:r,onChange:l}),u.jsx("span",{className:"slider"})]})]})})]}),u.jsx("div",{className:"ea__content-wrapper",children:Object.keys(e.list).map((a,o)=>u.jsx(xa,{source:e.list,index:a},o))})]}),u.jsx("div",{className:"ea__section-wrapper flex flex-end mb-5",children:u.jsxs("button",{className:"primary-btn install-btn flex flex-end",onClick:s,children:[i.save_settings," ",t.btnLoader==="extensions"&&u.jsx("span",{className:"eael_btn_loader"})]})})]})})})}function wm(){const e=localize.eael_dashboard.tools,t=localize.eael_dashboard.i18n,n=R.useRef(),{eaState:r,eaDispatch:i}=Y(),l=()=>{i({type:"BUTTON_LOADER",payload:"tools"}),kn({eaState:r,eaDispatch:i},"SAVE_TOOLS",{key:e.box_3.name,value:n.current.value})},s=()=>{kn({eaState:r,eaDispatch:i},"REGENERATE_ASSETS")};return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__elements-nav-content",children:[u.jsx("div",{className:"ea__tools-sticky",children:u.jsxs("div",{className:"ea__tools-content-wrapper",children:[u.jsxs("div",{className:"ea__connect-others flex gap-4 justify-between items-start",children:[u.jsxs("div",{className:"ea__connect--info flex gap-4 flex-1",children:[u.jsx("div",{className:"ea__others-icon eaicon-1",children:u.jsx("i",{className:"ea-dash-icon "+e.box_1.icon})}),u.jsxs("div",{children:[u.jsx("h5",{children:e.box_1.heading}),u.jsx("p",{children:e.box_1.content})]})]}),u.jsx("button",{className:"primary-btn changelog-btn",onClick:s,children:e.box_1.button.label})]}),u.jsxs("div",{className:"ea__connect-others flex gap-4 justify-between items-start",children:[u.jsxs("div",{className:"ea__connect--info flex gap-4 flex-1",children:[u.jsx("div",{className:"ea__others-icon eaicon-1",children:u.jsx("i",{className:"ea-dash-icon "+e.box_2.icon})}),u.jsxs("div",{children:[u.jsx("h5",{children:e.box_2.heading}),u.jsx("p",{children:e.box_2.content})]})]}),u.jsx("a",{className:"primary-btn changelog-btn",target:"_blank",href:e.box_2.button.url,children:e.box_2.button.label})]}),u.jsxs("div",{className:"ea__connect-others flex gap-6 justify-between items-start",children:[u.jsx("label",{children:e.box_3.heading}),u.jsxs("div",{className:"flex-1",children:[u.jsx("div",{className:"select-option-external",children:u.jsx("select",{name:e.box_3.name,defaultValue:e.box_3.value,id:"select-option",className:"form-select",ref:n,children:Object.keys(e.box_3.methods).map((a,o)=>u.jsx("option",{value:a,children:e.box_3.methods[a]},o))})}),u.jsx("span",{className:"select-details",children:e.box_3.content})]})]}),u.jsx("div",{className:"flex flex-end",children:u.jsxs("button",{className:"primary-btn install-btn flex flex-end",onClick:l,children:[t.save_settings," ",r.btnLoader==="tools"&&u.jsx("span",{className:"eael_btn_loader"})]})})]})}),u.jsx("div",{})]})})}function Sm(e){const t=localize.eael_dashboard.integration_box.list[e.index],n=localize.eael_dashboard.integration_box.enable,r=localize.eael_dashboard.integration_box.disable,{eaState:i,eaDispatch:l}=Y(),s=i.integrations[e.index],a=i[e.index]===!0,o=function(f,p){if(window.eaAjaxRunning===!0){setTimeout(o,500,f,p);return}window.eaAjaxRunning=!0;const v=jn(f,!0);v.onreadystatechange=()=>{(v.responseText?JSON.parse(v.responseText):{}).success&&l({type:"ON_CHANGE_INTEGRATION",payload:{key:p.index,value:p.isChecked}}),window.eaAjaxRunning=!1}},c=f=>{l({type:"INTEGRATION_LOADER",payload:e.index});const p={action:"wpdeveloper_deactivate_plugin",security:localize.nonce,slug:t.slug,basename:t.basename},v=f.target.checked;v&&(p.action="wpdeveloper_auto_active_even_not_installed"),setTimeout(o,100,p,{index:e.index,isChecked:v})};return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__integration-item",children:[u.jsxs("div",{className:"ea__integration-header flex gap-2 items-center",children:[u.jsx("img",{src:localize.eael_dashboard.reactPath+t.logo}),u.jsx("h5",{children:t.title})]}),u.jsxs("div",{className:"ea__integration-footer",children:[u.jsx("p",{children:t.desc}),u.jsxs("div",{className:"integration-settings flex justify-between items-center",children:[u.jsx("h5",{className:"eael-toggle-label",children:a?"Processing...":s?r:n}),u.jsxs("label",{className:" toggle-wrap",children:[u.jsx("input",{type:"checkbox",checked:s,onChange:c}),u.jsx("span",{className:a?"slider ea-loader":"slider"})]})]})]})]})})}function _m(){const e=localize.eael_dashboard.integration_box;return u.jsx(u.Fragment,{children:u.jsx("div",{className:"ea__elements-nav-content",children:u.jsx("div",{className:"ea__integration-content-wrapper",children:Object.keys(e.list).map((t,n)=>u.jsx(Sm,{index:t},n))})})})}function Em(){const e=localize.eael_dashboard.enhance_experience;return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__pro-elements-content",children:[u.jsxs("div",{children:[u.jsx("div",{className:"ea__active-user-wrapper",children:u.jsx("span",{className:"ea__active-users",children:e.top_heading})}),u.jsx("h3",{dangerouslySetInnerHTML:{__html:e.heading}})]}),u.jsxs("div",{className:"review-wrap flex items-center justify-between",children:[u.jsxs("div",{className:"flex gap-2",children:[u.jsx("i",{className:"ea-dash-icon ea-star"}),u.jsx("h6",{children:e.review.count}),u.jsx("span",{className:"reating-details",children:e.review.label})]}),u.jsx("a",{href:e.button.url,target:"_blank",children:u.jsxs("button",{className:"upgrade-button",children:[u.jsx("i",{className:"ea-dash-icon "+e.button.icon}),e.button.label]})})]})]})})}function Tm(){const e=localize.eael_dashboard.explore_pro_features,t=localize.eael_dashboard.reactPath;return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__pro-features flex justify-between items-center",children:[u.jsxs("div",{className:"ea__features-content",children:[u.jsx("h2",{children:e.heading}),u.jsx("p",{className:"mb-7",children:e.content}),u.jsx("div",{className:"ea__feature-list-wrap mb-6",children:e.list.map((n,r)=>u.jsxs("div",{className:"ea__feature-list-item flex gap-2 mb-4",children:[u.jsx("i",{className:"ea-dash-icon ea-active"}),u.jsx("p",{children:n})]},r))}),u.jsx("a",{href:e.button.url,target:"_blank",children:u.jsxs("span",{className:"primary-btn changelog-btn",children:[u.jsx("i",{className:"ea-dash-icon ea-link"}),e.button.label]})})]}),u.jsx("div",{className:"features-widget-wrapper",children:e.icons.map((n,r)=>u.jsx("div",{className:"features-widget-item",children:u.jsxs("a",{href:n.url,target:"_blank",children:[u.jsx("img",{src:t+n.icon,alt:"img"}),u.jsx("span",{className:"eael-tooltip",children:n.label})]})},r))})]})})}function Cm(e){const t=localize.eael_dashboard.premium_items.list[e.index];return u.jsx(u.Fragment,{children:u.jsxs("div",{className:"ea__premium-item",children:[u.jsx("div",{className:"ea__premimu-item-header flex gap-2 items-center",children:u.jsx("img",{src:localize.eael_dashboard.reactPath+t.image,alt:"img"})}),u.jsxs("div",{className:"ea__premium-item-footer",children:[u.jsx("h5",{children:t.heading}),u.jsx("p",{className:"mb-2",children:t.content}),u.jsx("a",{href:t.button.url,target:"_blank",children:u.jsx("button",{className:"underline",children:t.button.label})})]})]})})}function Do(e){return e!==null&&typeof e=="object"&&"constructor"in e&&e.constructor===Object}function wa(e,t){e===void 0&&(e={}),t===void 0&&(t={});const n=["__proto__","constructor","prototype"];Object.keys(t).filter(r=>n.indexOf(r)<0).forEach(r=>{typeof e[r]>"u"?e[r]=t[r]:Do(t[r])&&Do(e[r])&&Object.keys(t[r]).length>0&&wa(e[r],t[r])})}const td={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function Je(){const e=typeof document<"u"?document:{};return wa(e,td),e}const km={document:td,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(e){return typeof setTimeout>"u"?(e(),null):setTimeout(e,0)},cancelAnimationFrame(e){typeof setTimeout>"u"||clearTimeout(e)}};function Se(){const e=typeof window<"u"?window:{};return wa(e,km),e}function jm(e){return e===void 0&&(e=""),e.trim().split(" ").filter(t=>!!t.trim())}function Nm(e){const t=e;Object.keys(t).forEach(n=>{try{t[n]=null}catch{}try{delete t[n]}catch{}})}function nd(e,t){return t===void 0&&(t=0),setTimeout(e,t)}function ji(){return Date.now()}function Lm(e){const t=Se();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}function Pm(e,t){t===void 0&&(t="x");const n=Se();let r,i,l;const s=Lm(e);return n.WebKitCSSMatrix?(i=s.transform||s.webkitTransform,i.split(",").length>6&&(i=i.split(", ").map(a=>a.replace(",",".")).join(", ")),l=new n.WebKitCSSMatrix(i==="none"?"":i)):(l=s.MozTransform||s.OTransform||s.MsTransform||s.msTransform||s.transform||s.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),r=l.toString().split(",")),t==="x"&&(n.WebKitCSSMatrix?i=l.m41:r.length===16?i=parseFloat(r[12]):i=parseFloat(r[4])),t==="y"&&(n.WebKitCSSMatrix?i=l.m42:r.length===16?i=parseFloat(r[13]):i=parseFloat(r[5])),i||0}function $r(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"}function Om(e){return typeof window<"u"&&typeof window.HTMLElement<"u"?e instanceof HTMLElement:e&&(e.nodeType===1||e.nodeType===11)}function Ne(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const r=n<0||arguments.length<=n?void 0:arguments[n];if(r!=null&&!Om(r)){const i=Object.keys(Object(r)).filter(l=>t.indexOf(l)<0);for(let l=0,s=i.length;l<s;l+=1){const a=i[l],o=Object.getOwnPropertyDescriptor(r,a);o!==void 0&&o.enumerable&&($r(e[a])&&$r(r[a])?r[a].__swiper__?e[a]=r[a]:Ne(e[a],r[a]):!$r(e[a])&&$r(r[a])?(e[a]={},r[a].__swiper__?e[a]=r[a]:Ne(e[a],r[a])):e[a]=r[a])}}}return e}function Hr(e,t,n){e.style.setProperty(t,n)}function rd(e){let{swiper:t,targetPosition:n,side:r}=e;const i=Se(),l=-t.translate;let s=null,a;const o=t.params.speed;t.wrapperEl.style.scrollSnapType="none",i.cancelAnimationFrame(t.cssModeFrameID);const c=n>l?"next":"prev",f=(v,y)=>c==="next"&&v>=y||c==="prev"&&v<=y,p=()=>{a=new Date().getTime(),s===null&&(s=a);const v=Math.max(Math.min((a-s)/o,1),0),y=.5-Math.cos(v*Math.PI)/2;let g=l+y*(n-l);if(f(g,n)&&(g=n),t.wrapperEl.scrollTo({[r]:g}),f(g,n)){t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[r]:g})}),i.cancelAnimationFrame(t.cssModeFrameID);return}t.cssModeFrameID=i.requestAnimationFrame(p)};p()}function Xe(e,t){t===void 0&&(t="");const n=Se(),r=[...e.children];return n.HTMLSlotElement&&e instanceof HTMLSlotElement&&r.push(...e.assignedElements()),t?r.filter(i=>i.matches(t)):r}function Mm(e,t){const n=[t];for(;n.length>0;){const r=n.shift();if(e===r)return!0;n.push(...r.children,...r.shadowRoot?r.shadowRoot.children:[],...r.assignedElements?r.assignedElements():[])}}function zm(e,t){const n=Se();let r=t.contains(e);return!r&&n.HTMLSlotElement&&t instanceof HTMLSlotElement&&(r=[...t.assignedElements()].includes(e),r||(r=Mm(e,t))),r}function Ni(e){try{console.warn(e);return}catch{}}function Li(e,t){t===void 0&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:jm(t)),n}function Dm(e,t){const n=[];for(;e.previousElementSibling;){const r=e.previousElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function Im(e,t){const n=[];for(;e.nextElementSibling;){const r=e.nextElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function xt(e,t){return Se().getComputedStyle(e,null).getPropertyValue(t)}function Pi(e){let t=e,n;if(t){for(n=0;(t=t.previousSibling)!==null;)t.nodeType===1&&(n+=1);return n}}function id(e,t){const n=[];let r=e.parentElement;for(;r;)t?r.matches(t)&&n.push(r):n.push(r),r=r.parentElement;return n}function _s(e,t,n){const r=Se();return e[t==="width"?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-left":"margin-bottom"))}function et(e){return(Array.isArray(e)?e:[e]).filter(t=>!!t)}function Oi(e,t){t===void 0&&(t=""),typeof trustedTypes<"u"?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:n=>n}).createHTML(t):e.innerHTML=t}let _l;function Am(){const e=Se(),t=Je();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}function ld(){return _l||(_l=Am()),_l}let El;function Rm(e){let{userAgent:t}=e===void 0?{}:e;const n=ld(),r=Se(),i=r.navigator.platform,l=t||r.navigator.userAgent,s={ios:!1,android:!1},a=r.screen.width,o=r.screen.height,c=l.match(/(Android);?[\s\/]+([\d.]+)?/);let f=l.match(/(iPad).*OS\s([\d_]+)/);const p=l.match(/(iPod)(.*OS\s([\d_]+))?/),v=!f&&l.match(/(iPhone\sOS|iOS)\s([\d_]+)/),y=i==="Win32";let g=i==="MacIntel";const w=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!f&&g&&n.touch&&w.indexOf(`${a}x${o}`)>=0&&(f=l.match(/(Version)\/([\d.]+)/),f||(f=[0,1,"13_0_0"]),g=!1),c&&!y&&(s.os="android",s.android=!0),(f||v||p)&&(s.os="ios",s.ios=!0),s}function sd(e){return e===void 0&&(e={}),El||(El=Rm(e)),El}let Tl;function Fm(){const e=Se(),t=sd();let n=!1;function r(){const a=e.navigator.userAgent.toLowerCase();return a.indexOf("safari")>=0&&a.indexOf("chrome")<0&&a.indexOf("android")<0}if(r()){const a=String(e.navigator.userAgent);if(a.includes("Version/")){const[o,c]=a.split("Version/")[1].split(" ")[0].split(".").map(f=>Number(f));n=o<16||o===16&&c<2}}const i=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),l=r(),s=l||i&&t.ios;return{isSafari:n||l,needPerspectiveFix:n,need3dFix:s,isWebView:i}}function ad(){return Tl||(Tl=Fm()),Tl}function bm(e){let{swiper:t,on:n,emit:r}=e;const i=Se();let l=null,s=null;const a=()=>{!t||t.destroyed||!t.initialized||(r("beforeResize"),r("resize"))},o=()=>{!t||t.destroyed||!t.initialized||(l=new ResizeObserver(p=>{s=i.requestAnimationFrame(()=>{const{width:v,height:y}=t;let g=v,w=y;p.forEach(N=>{let{contentBoxSize:h,contentRect:d,target:m}=N;m&&m!==t.el||(g=d?d.width:(h[0]||h).inlineSize,w=d?d.height:(h[0]||h).blockSize)}),(g!==v||w!==y)&&a()})}),l.observe(t.el))},c=()=>{s&&i.cancelAnimationFrame(s),l&&l.unobserve&&t.el&&(l.unobserve(t.el),l=null)},f=()=>{!t||t.destroyed||!t.initialized||r("orientationchange")};n("init",()=>{if(t.params.resizeObserver&&typeof i.ResizeObserver<"u"){o();return}i.addEventListener("resize",a),i.addEventListener("orientationchange",f)}),n("destroy",()=>{c(),i.removeEventListener("resize",a),i.removeEventListener("orientationchange",f)})}function Vm(e){let{swiper:t,extendParams:n,on:r,emit:i}=e;const l=[],s=Se(),a=function(f,p){p===void 0&&(p={});const v=s.MutationObserver||s.WebkitMutationObserver,y=new v(g=>{if(t.__preventObserver__)return;if(g.length===1){i("observerUpdate",g[0]);return}const w=function(){i("observerUpdate",g[0])};s.requestAnimationFrame?s.requestAnimationFrame(w):s.setTimeout(w,0)});y.observe(f,{attributes:typeof p.attributes>"u"?!0:p.attributes,childList:t.isElement||(typeof p.childList>"u"?!0:p).childList,characterData:typeof p.characterData>"u"?!0:p.characterData}),l.push(y)},o=()=>{if(t.params.observer){if(t.params.observeParents){const f=id(t.hostEl);for(let p=0;p<f.length;p+=1)a(f[p])}a(t.hostEl,{childList:t.params.observeSlideChildren}),a(t.wrapperEl,{attributes:!1})}},c=()=>{l.forEach(f=>{f.disconnect()}),l.splice(0,l.length)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),r("init",o),r("destroy",c)}var Bm={on(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed||typeof t!="function")return r;const i=n?"unshift":"push";return e.split(" ").forEach(l=>{r.eventsListeners[l]||(r.eventsListeners[l]=[]),r.eventsListeners[l][i](t)}),r},once(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed||typeof t!="function")return r;function i(){r.off(e,i),i.__emitterProxy&&delete i.__emitterProxy;for(var l=arguments.length,s=new Array(l),a=0;a<l;a++)s[a]=arguments[a];t.apply(r,s)}return i.__emitterProxy=t,r.on(e,i,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;const r=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[r](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed||!n.eventsListeners||e.split(" ").forEach(r=>{typeof t>"u"?n.eventsListeners[r]=[]:n.eventsListeners[r]&&n.eventsListeners[r].forEach((i,l)=>{(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&n.eventsListeners[r].splice(l,1)})}),n},emit(){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsListeners)return e;let t,n,r;for(var i=arguments.length,l=new Array(i),s=0;s<i;s++)l[s]=arguments[s];return typeof l[0]=="string"||Array.isArray(l[0])?(t=l[0],n=l.slice(1,l.length),r=e):(t=l[0].events,n=l[0].data,r=l[0].context||e),n.unshift(r),(Array.isArray(t)?t:t.split(" ")).forEach(o=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(c=>{c.apply(r,[o,...n])}),e.eventsListeners&&e.eventsListeners[o]&&e.eventsListeners[o].forEach(c=>{c.apply(r,n)})}),e}};function $m(){const e=this;let t,n;const r=e.el;typeof e.params.width<"u"&&e.params.width!==null?t=e.params.width:t=r.clientWidth,typeof e.params.height<"u"&&e.params.height!==null?n=e.params.height:n=r.clientHeight,!(t===0&&e.isHorizontal()||n===0&&e.isVertical())&&(t=t-parseInt(xt(r,"padding-left")||0,10)-parseInt(xt(r,"padding-right")||0,10),n=n-parseInt(xt(r,"padding-top")||0,10)-parseInt(xt(r,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))}function Hm(){const e=this;function t(E,P){return parseFloat(E.getPropertyValue(e.getDirectionLabel(P))||0)}const n=e.params,{wrapperEl:r,slidesEl:i,size:l,rtlTranslate:s,wrongRTL:a}=e,o=e.virtual&&n.virtual.enabled,c=o?e.virtual.slides.length:e.slides.length,f=Xe(i,`.${e.params.slideClass}, swiper-slide`),p=o?e.virtual.slides.length:f.length;let v=[];const y=[],g=[];let w=n.slidesOffsetBefore;typeof w=="function"&&(w=n.slidesOffsetBefore.call(e));let N=n.slidesOffsetAfter;typeof N=="function"&&(N=n.slidesOffsetAfter.call(e));const h=e.snapGrid.length,d=e.slidesGrid.length;let m=n.spaceBetween,x=-w,S=0,_=0;if(typeof l>"u")return;typeof m=="string"&&m.indexOf("%")>=0?m=parseFloat(m.replace("%",""))/100*l:typeof m=="string"&&(m=parseFloat(m)),e.virtualSize=-m,f.forEach(E=>{s?E.style.marginLeft="":E.style.marginRight="",E.style.marginBottom="",E.style.marginTop=""}),n.centeredSlides&&n.cssMode&&(Hr(r,"--swiper-centered-offset-before",""),Hr(r,"--swiper-centered-offset-after",""));const C=n.grid&&n.grid.rows>1&&e.grid;C?e.grid.initSlides(f):e.grid&&e.grid.unsetSlides();let T;const j=n.slidesPerView==="auto"&&n.breakpoints&&Object.keys(n.breakpoints).filter(E=>typeof n.breakpoints[E].slidesPerView<"u").length>0;for(let E=0;E<p;E+=1){T=0;let P;if(f[E]&&(P=f[E]),C&&e.grid.updateSlide(E,P,f),!(f[E]&&xt(P,"display")==="none")){if(n.slidesPerView==="auto"){j&&(f[E].style[e.getDirectionLabel("width")]="");const D=getComputedStyle(P),I=P.style.transform,b=P.style.webkitTransform;if(I&&(P.style.transform="none"),b&&(P.style.webkitTransform="none"),n.roundLengths)T=e.isHorizontal()?_s(P,"width"):_s(P,"height");else{const B=t(D,"width"),A=t(D,"padding-left"),Q=t(D,"padding-right"),k=t(D,"margin-left"),O=t(D,"margin-right"),z=D.getPropertyValue("box-sizing");if(z&&z==="border-box")T=B+k+O;else{const{clientWidth:$,offsetWidth:X}=P;T=B+A+Q+k+O+(X-$)}}I&&(P.style.transform=I),b&&(P.style.webkitTransform=b),n.roundLengths&&(T=Math.floor(T))}else T=(l-(n.slidesPerView-1)*m)/n.slidesPerView,n.roundLengths&&(T=Math.floor(T)),f[E]&&(f[E].style[e.getDirectionLabel("width")]=`${T}px`);f[E]&&(f[E].swiperSlideSize=T),g.push(T),n.centeredSlides?(x=x+T/2+S/2+m,S===0&&E!==0&&(x=x-l/2-m),E===0&&(x=x-l/2-m),Math.abs(x)<1/1e3&&(x=0),n.roundLengths&&(x=Math.floor(x)),_%n.slidesPerGroup===0&&v.push(x),y.push(x)):(n.roundLengths&&(x=Math.floor(x)),(_-Math.min(e.params.slidesPerGroupSkip,_))%e.params.slidesPerGroup===0&&v.push(x),y.push(x),x=x+T+m),e.virtualSize+=T+m,S=T,_+=1}}if(e.virtualSize=Math.max(e.virtualSize,l)+N,s&&a&&(n.effect==="slide"||n.effect==="coverflow")&&(r.style.width=`${e.virtualSize+m}px`),n.setWrapperSize&&(r.style[e.getDirectionLabel("width")]=`${e.virtualSize+m}px`),C&&e.grid.updateWrapperSize(T,v),!n.centeredSlides){const E=[];for(let P=0;P<v.length;P+=1){let D=v[P];n.roundLengths&&(D=Math.floor(D)),v[P]<=e.virtualSize-l&&E.push(D)}v=E,Math.floor(e.virtualSize-l)-Math.floor(v[v.length-1])>1&&v.push(e.virtualSize-l)}if(o&&n.loop){const E=g[0]+m;if(n.slidesPerGroup>1){const P=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/n.slidesPerGroup),D=E*n.slidesPerGroup;for(let I=0;I<P;I+=1)v.push(v[v.length-1]+D)}for(let P=0;P<e.virtual.slidesBefore+e.virtual.slidesAfter;P+=1)n.slidesPerGroup===1&&v.push(v[v.length-1]+E),y.push(y[y.length-1]+E),e.virtualSize+=E}if(v.length===0&&(v=[0]),m!==0){const E=e.isHorizontal()&&s?"marginLeft":e.getDirectionLabel("marginRight");f.filter((P,D)=>!n.cssMode||n.loop?!0:D!==f.length-1).forEach(P=>{P.style[E]=`${m}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let E=0;g.forEach(D=>{E+=D+(m||0)}),E-=m;const P=E>l?E-l:0;v=v.map(D=>D<=0?-w:D>P?P+N:D)}if(n.centerInsufficientSlides){let E=0;g.forEach(D=>{E+=D+(m||0)}),E-=m;const P=(n.slidesOffsetBefore||0)+(n.slidesOffsetAfter||0);if(E+P<l){const D=(l-E-P)/2;v.forEach((I,b)=>{v[b]=I-D}),y.forEach((I,b)=>{y[b]=I+D})}}if(Object.assign(e,{slides:f,snapGrid:v,slidesGrid:y,slidesSizesGrid:g}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){Hr(r,"--swiper-centered-offset-before",`${-v[0]}px`),Hr(r,"--swiper-centered-offset-after",`${e.size/2-g[g.length-1]/2}px`);const E=-e.snapGrid[0],P=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(D=>D+E),e.slidesGrid=e.slidesGrid.map(D=>D+P)}if(p!==c&&e.emit("slidesLengthChange"),v.length!==h&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),y.length!==d&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!o&&!n.cssMode&&(n.effect==="slide"||n.effect==="fade")){const E=`${n.containerModifierClass}backface-hidden`,P=e.el.classList.contains(E);p<=n.maxBackfaceHiddenSlides?P||e.el.classList.add(E):P&&e.el.classList.remove(E)}}function Gm(e){const t=this,n=[],r=t.virtual&&t.params.virtual.enabled;let i=0,l;typeof e=="number"?t.setTransition(e):e===!0&&t.setTransition(t.params.speed);const s=a=>r?t.slides[t.getSlideIndexByData(a)]:t.slides[a];if(t.params.slidesPerView!=="auto"&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(a=>{n.push(a)});else for(l=0;l<Math.ceil(t.params.slidesPerView);l+=1){const a=t.activeIndex+l;if(a>t.slides.length&&!r)break;n.push(s(a))}else n.push(s(t.activeIndex));for(l=0;l<n.length;l+=1)if(typeof n[l]<"u"){const a=n[l].offsetHeight;i=a>i?a:i}(i||i===0)&&(t.wrapperEl.style.height=`${i}px`)}function Um(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let r=0;r<t.length;r+=1)t[r].swiperSlideOffset=(e.isHorizontal()?t[r].offsetLeft:t[r].offsetTop)-n-e.cssOverflowAdjustment()}const Io=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function Wm(e){e===void 0&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:r,rtlTranslate:i,snapGrid:l}=t;if(r.length===0)return;typeof r[0].swiperSlideOffset>"u"&&t.updateSlidesOffset();let s=-e;i&&(s=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let a=n.spaceBetween;typeof a=="string"&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*t.size:typeof a=="string"&&(a=parseFloat(a));for(let o=0;o<r.length;o+=1){const c=r[o];let f=c.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(f-=r[0].swiperSlideOffset);const p=(s+(n.centeredSlides?t.minTranslate():0)-f)/(c.swiperSlideSize+a),v=(s-l[0]+(n.centeredSlides?t.minTranslate():0)-f)/(c.swiperSlideSize+a),y=-(s-f),g=y+t.slidesSizesGrid[o],w=y>=0&&y<=t.size-t.slidesSizesGrid[o],N=y>=0&&y<t.size-1||g>1&&g<=t.size||y<=0&&g>=t.size;N&&(t.visibleSlides.push(c),t.visibleSlidesIndexes.push(o)),Io(c,N,n.slideVisibleClass),Io(c,w,n.slideFullyVisibleClass),c.progress=i?-p:p,c.originalProgress=i?-v:v}}function Ym(e){const t=this;if(typeof e>"u"){const f=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*f||0}const n=t.params,r=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:l,isEnd:s,progressLoop:a}=t;const o=l,c=s;if(r===0)i=0,l=!0,s=!0;else{i=(e-t.minTranslate())/r;const f=Math.abs(e-t.minTranslate())<1,p=Math.abs(e-t.maxTranslate())<1;l=f||i<=0,s=p||i>=1,f&&(i=0),p&&(i=1)}if(n.loop){const f=t.getSlideIndexByData(0),p=t.getSlideIndexByData(t.slides.length-1),v=t.slidesGrid[f],y=t.slidesGrid[p],g=t.slidesGrid[t.slidesGrid.length-1],w=Math.abs(e);w>=v?a=(w-v)/g:a=(w+g-y)/g,a>1&&(a-=1)}Object.assign(t,{progress:i,progressLoop:a,isBeginning:l,isEnd:s}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),l&&!o&&t.emit("reachBeginning toEdge"),s&&!c&&t.emit("reachEnd toEdge"),(o&&!l||c&&!s)&&t.emit("fromEdge"),t.emit("progress",i)}const Cl=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function Qm(){const e=this,{slides:t,params:n,slidesEl:r,activeIndex:i}=e,l=e.virtual&&n.virtual.enabled,s=e.grid&&n.grid&&n.grid.rows>1,a=p=>Xe(r,`.${n.slideClass}${p}, swiper-slide${p}`)[0];let o,c,f;if(l)if(n.loop){let p=i-e.virtual.slidesBefore;p<0&&(p=e.virtual.slides.length+p),p>=e.virtual.slides.length&&(p-=e.virtual.slides.length),o=a(`[data-swiper-slide-index="${p}"]`)}else o=a(`[data-swiper-slide-index="${i}"]`);else s?(o=t.find(p=>p.column===i),f=t.find(p=>p.column===i+1),c=t.find(p=>p.column===i-1)):o=t[i];o&&(s||(f=Im(o,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!f&&(f=t[0]),c=Dm(o,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!c===0&&(c=t[t.length-1]))),t.forEach(p=>{Cl(p,p===o,n.slideActiveClass),Cl(p,p===f,n.slideNextClass),Cl(p,p===c,n.slidePrevClass)}),e.emitSlidesClasses()}const ni=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=()=>e.isElement?"swiper-slide":`.${e.params.slideClass}`,r=t.closest(n());if(r){let i=r.querySelector(`.${e.params.lazyPreloaderClass}`);!i&&e.isElement&&(r.shadowRoot?i=r.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{r.shadowRoot&&(i=r.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),i&&i.remove())})),i&&i.remove()}},kl=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},Es=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const r=e.params.slidesPerView==="auto"?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),i=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const s=i,a=[s-t];a.push(...Array.from({length:t}).map((o,c)=>s+r+c)),e.slides.forEach((o,c)=>{a.includes(o.column)&&kl(e,c)});return}const l=i+r-1;if(e.params.rewind||e.params.loop)for(let s=i-t;s<=l+t;s+=1){const a=(s%n+n)%n;(a<i||a>l)&&kl(e,a)}else for(let s=Math.max(i-t,0);s<=Math.min(l+t,n-1);s+=1)s!==i&&(s>l||s<i)&&kl(e,s)};function Km(e){const{slidesGrid:t,params:n}=e,r=e.rtlTranslate?e.translate:-e.translate;let i;for(let l=0;l<t.length;l+=1)typeof t[l+1]<"u"?r>=t[l]&&r<t[l+1]-(t[l+1]-t[l])/2?i=l:r>=t[l]&&r<t[l+1]&&(i=l+1):r>=t[l]&&(i=l);return n.normalizeSlideIndex&&(i<0||typeof i>"u")&&(i=0),i}function Xm(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:r,params:i,activeIndex:l,realIndex:s,snapIndex:a}=t;let o=e,c;const f=y=>{let g=y-t.virtual.slidesBefore;return g<0&&(g=t.virtual.slides.length+g),g>=t.virtual.slides.length&&(g-=t.virtual.slides.length),g};if(typeof o>"u"&&(o=Km(t)),r.indexOf(n)>=0)c=r.indexOf(n);else{const y=Math.min(i.slidesPerGroupSkip,o);c=y+Math.floor((o-y)/i.slidesPerGroup)}if(c>=r.length&&(c=r.length-1),o===l&&!t.params.loop){c!==a&&(t.snapIndex=c,t.emit("snapIndexChange"));return}if(o===l&&t.params.loop&&t.virtual&&t.params.virtual.enabled){t.realIndex=f(o);return}const p=t.grid&&i.grid&&i.grid.rows>1;let v;if(t.virtual&&i.virtual.enabled&&i.loop)v=f(o);else if(p){const y=t.slides.find(w=>w.column===o);let g=parseInt(y.getAttribute("data-swiper-slide-index"),10);Number.isNaN(g)&&(g=Math.max(t.slides.indexOf(y),0)),v=Math.floor(g/i.grid.rows)}else if(t.slides[o]){const y=t.slides[o].getAttribute("data-swiper-slide-index");y?v=parseInt(y,10):v=o}else v=o;Object.assign(t,{previousSnapIndex:a,snapIndex:c,previousRealIndex:s,realIndex:v,previousIndex:l,activeIndex:o}),t.initialized&&Es(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(s!==v&&t.emit("realIndexChange"),t.emit("slideChange"))}function qm(e,t){const n=this,r=n.params;let i=e.closest(`.${r.slideClass}, swiper-slide`);!i&&n.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(a=>{!i&&a.matches&&a.matches(`.${r.slideClass}, swiper-slide`)&&(i=a)});let l=!1,s;if(i){for(let a=0;a<n.slides.length;a+=1)if(n.slides[a]===i){l=!0,s=a;break}}if(i&&l)n.clickedSlide=i,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(i.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=s;else{n.clickedSlide=void 0,n.clickedIndex=void 0;return}r.slideToClickedSlide&&n.clickedIndex!==void 0&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}var Zm={updateSize:$m,updateSlides:Hm,updateAutoHeight:Gm,updateSlidesOffset:Um,updateSlidesProgress:Wm,updateProgress:Ym,updateSlidesClasses:Qm,updateActiveIndex:Xm,updateClickedSlide:qm};function Jm(e){e===void 0&&(e=this.isHorizontal()?"x":"y");const t=this,{params:n,rtlTranslate:r,translate:i,wrapperEl:l}=t;if(n.virtualTranslate)return r?-i:i;if(n.cssMode)return i;let s=Pm(l,e);return s+=t.cssOverflowAdjustment(),r&&(s=-s),s||0}function eh(e,t){const n=this,{rtlTranslate:r,params:i,wrapperEl:l,progress:s}=n;let a=0,o=0;const c=0;n.isHorizontal()?a=r?-e:e:o=e,i.roundLengths&&(a=Math.floor(a),o=Math.floor(o)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?a:o,i.cssMode?l[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-a:-o:i.virtualTranslate||(n.isHorizontal()?a-=n.cssOverflowAdjustment():o-=n.cssOverflowAdjustment(),l.style.transform=`translate3d(${a}px, ${o}px, ${c}px)`);let f;const p=n.maxTranslate()-n.minTranslate();p===0?f=0:f=(e-n.minTranslate())/p,f!==s&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)}function th(){return-this.snapGrid[0]}function nh(){return-this.snapGrid[this.snapGrid.length-1]}function rh(e,t,n,r,i){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),r===void 0&&(r=!0);const l=this,{params:s,wrapperEl:a}=l;if(l.animating&&s.preventInteractionOnTransition)return!1;const o=l.minTranslate(),c=l.maxTranslate();let f;if(r&&e>o?f=o:r&&e<c?f=c:f=e,l.updateProgress(f),s.cssMode){const p=l.isHorizontal();if(t===0)a[p?"scrollLeft":"scrollTop"]=-f;else{if(!l.support.smoothScroll)return rd({swiper:l,targetPosition:-f,side:p?"left":"top"}),!0;a.scrollTo({[p?"left":"top"]:-f,behavior:"smooth"})}return!0}return t===0?(l.setTransition(0),l.setTranslate(f),n&&(l.emit("beforeTransitionStart",t,i),l.emit("transitionEnd"))):(l.setTransition(t),l.setTranslate(f),n&&(l.emit("beforeTransitionStart",t,i),l.emit("transitionStart")),l.animating||(l.animating=!0,l.onTranslateToWrapperTransitionEnd||(l.onTranslateToWrapperTransitionEnd=function(v){!l||l.destroyed||v.target===this&&(l.wrapperEl.removeEventListener("transitionend",l.onTranslateToWrapperTransitionEnd),l.onTranslateToWrapperTransitionEnd=null,delete l.onTranslateToWrapperTransitionEnd,l.animating=!1,n&&l.emit("transitionEnd"))}),l.wrapperEl.addEventListener("transitionend",l.onTranslateToWrapperTransitionEnd))),!0}var ih={getTranslate:Jm,setTranslate:eh,minTranslate:th,maxTranslate:nh,translateTo:rh};function lh(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration=`${e}ms`,n.wrapperEl.style.transitionDelay=e===0?"0ms":""),n.emit("setTransition",e,t)}function od(e){let{swiper:t,runCallbacks:n,direction:r,step:i}=e;const{activeIndex:l,previousIndex:s}=t;let a=r;a||(l>s?a="next":l<s?a="prev":a="reset"),t.emit(`transition${i}`),n&&a==="reset"?t.emit(`slideResetTransition${i}`):n&&l!==s&&(t.emit(`slideChangeTransition${i}`),a==="next"?t.emit(`slideNextTransition${i}`):t.emit(`slidePrevTransition${i}`))}function sh(e,t){e===void 0&&(e=!0);const n=this,{params:r}=n;r.cssMode||(r.autoHeight&&n.updateAutoHeight(),od({swiper:n,runCallbacks:e,direction:t,step:"Start"}))}function ah(e,t){e===void 0&&(e=!0);const n=this,{params:r}=n;n.animating=!1,!r.cssMode&&(n.setTransition(0),od({swiper:n,runCallbacks:e,direction:t,step:"End"}))}var oh={setTransition:lh,transitionStart:sh,transitionEnd:ah};function uh(e,t,n,r,i){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const l=this;let s=e;s<0&&(s=0);const{params:a,snapGrid:o,slidesGrid:c,previousIndex:f,activeIndex:p,rtlTranslate:v,wrapperEl:y,enabled:g}=l;if(!g&&!r&&!i||l.destroyed||l.animating&&a.preventInteractionOnTransition)return!1;typeof t>"u"&&(t=l.params.speed);const w=Math.min(l.params.slidesPerGroupSkip,s);let N=w+Math.floor((s-w)/l.params.slidesPerGroup);N>=o.length&&(N=o.length-1);const h=-o[N];if(a.normalizeSlideIndex)for(let C=0;C<c.length;C+=1){const T=-Math.floor(h*100),j=Math.floor(c[C]*100),E=Math.floor(c[C+1]*100);typeof c[C+1]<"u"?T>=j&&T<E-(E-j)/2?s=C:T>=j&&T<E&&(s=C+1):T>=j&&(s=C)}if(l.initialized&&s!==p&&(!l.allowSlideNext&&(v?h>l.translate&&h>l.minTranslate():h<l.translate&&h<l.minTranslate())||!l.allowSlidePrev&&h>l.translate&&h>l.maxTranslate()&&(p||0)!==s))return!1;s!==(f||0)&&n&&l.emit("beforeSlideChangeStart"),l.updateProgress(h);let d;s>p?d="next":s<p?d="prev":d="reset";const m=l.virtual&&l.params.virtual.enabled;if(!(m&&i)&&(v&&-h===l.translate||!v&&h===l.translate))return l.updateActiveIndex(s),a.autoHeight&&l.updateAutoHeight(),l.updateSlidesClasses(),a.effect!=="slide"&&l.setTranslate(h),d!=="reset"&&(l.transitionStart(n,d),l.transitionEnd(n,d)),!1;if(a.cssMode){const C=l.isHorizontal(),T=v?h:-h;if(t===0)m&&(l.wrapperEl.style.scrollSnapType="none",l._immediateVirtual=!0),m&&!l._cssModeVirtualInitialSet&&l.params.initialSlide>0?(l._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{y[C?"scrollLeft":"scrollTop"]=T})):y[C?"scrollLeft":"scrollTop"]=T,m&&requestAnimationFrame(()=>{l.wrapperEl.style.scrollSnapType="",l._immediateVirtual=!1});else{if(!l.support.smoothScroll)return rd({swiper:l,targetPosition:T,side:C?"left":"top"}),!0;y.scrollTo({[C?"left":"top"]:T,behavior:"smooth"})}return!0}const _=ad().isSafari;return m&&!i&&_&&l.isElement&&l.virtual.update(!1,!1,s),l.setTransition(t),l.setTranslate(h),l.updateActiveIndex(s),l.updateSlidesClasses(),l.emit("beforeTransitionStart",t,r),l.transitionStart(n,d),t===0?l.transitionEnd(n,d):l.animating||(l.animating=!0,l.onSlideToWrapperTransitionEnd||(l.onSlideToWrapperTransitionEnd=function(T){!l||l.destroyed||T.target===this&&(l.wrapperEl.removeEventListener("transitionend",l.onSlideToWrapperTransitionEnd),l.onSlideToWrapperTransitionEnd=null,delete l.onSlideToWrapperTransitionEnd,l.transitionEnd(n,d))}),l.wrapperEl.addEventListener("transitionend",l.onSlideToWrapperTransitionEnd)),!0}function ch(e,t,n,r){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const i=this;if(i.destroyed)return;typeof t>"u"&&(t=i.params.speed);const l=i.grid&&i.params.grid&&i.params.grid.rows>1;let s=e;if(i.params.loop)if(i.virtual&&i.params.virtual.enabled)s=s+i.virtual.slidesBefore;else{let a;if(l){const v=s*i.params.grid.rows;a=i.slides.find(y=>y.getAttribute("data-swiper-slide-index")*1===v).column}else a=i.getSlideIndexByData(s);const o=l?Math.ceil(i.slides.length/i.params.grid.rows):i.slides.length,{centeredSlides:c}=i.params;let f=i.params.slidesPerView;f==="auto"?f=i.slidesPerViewDynamic():(f=Math.ceil(parseFloat(i.params.slidesPerView,10)),c&&f%2===0&&(f=f+1));let p=o-a<f;if(c&&(p=p||a<Math.ceil(f/2)),r&&c&&i.params.slidesPerView!=="auto"&&!l&&(p=!1),p){const v=c?a<i.activeIndex?"prev":"next":a-i.activeIndex-1<i.params.slidesPerView?"next":"prev";i.loopFix({direction:v,slideTo:!0,activeSlideIndex:v==="next"?a+1:a-o+1,slideRealIndex:v==="next"?i.realIndex:void 0})}if(l){const v=s*i.params.grid.rows;s=i.slides.find(y=>y.getAttribute("data-swiper-slide-index")*1===v).column}else s=i.getSlideIndexByData(s)}return requestAnimationFrame(()=>{i.slideTo(s,t,n,r)}),i}function dh(e,t,n){t===void 0&&(t=!0);const r=this,{enabled:i,params:l,animating:s}=r;if(!i||r.destroyed)return r;typeof e>"u"&&(e=r.params.speed);let a=l.slidesPerGroup;l.slidesPerView==="auto"&&l.slidesPerGroup===1&&l.slidesPerGroupAuto&&(a=Math.max(r.slidesPerViewDynamic("current",!0),1));const o=r.activeIndex<l.slidesPerGroupSkip?1:a,c=r.virtual&&l.virtual.enabled;if(l.loop){if(s&&!c&&l.loopPreventsSliding)return!1;if(r.loopFix({direction:"next"}),r._clientLeft=r.wrapperEl.clientLeft,r.activeIndex===r.slides.length-1&&l.cssMode)return requestAnimationFrame(()=>{r.slideTo(r.activeIndex+o,e,t,n)}),!0}return l.rewind&&r.isEnd?r.slideTo(0,e,t,n):r.slideTo(r.activeIndex+o,e,t,n)}function fh(e,t,n){t===void 0&&(t=!0);const r=this,{params:i,snapGrid:l,slidesGrid:s,rtlTranslate:a,enabled:o,animating:c}=r;if(!o||r.destroyed)return r;typeof e>"u"&&(e=r.params.speed);const f=r.virtual&&i.virtual.enabled;if(i.loop){if(c&&!f&&i.loopPreventsSliding)return!1;r.loopFix({direction:"prev"}),r._clientLeft=r.wrapperEl.clientLeft}const p=a?r.translate:-r.translate;function v(d){return d<0?-Math.floor(Math.abs(d)):Math.floor(d)}const y=v(p),g=l.map(d=>v(d)),w=i.freeMode&&i.freeMode.enabled;let N=l[g.indexOf(y)-1];if(typeof N>"u"&&(i.cssMode||w)){let d;l.forEach((m,x)=>{y>=m&&(d=x)}),typeof d<"u"&&(N=w?l[d]:l[d>0?d-1:d])}let h=0;if(typeof N<"u"&&(h=s.indexOf(N),h<0&&(h=r.activeIndex-1),i.slidesPerView==="auto"&&i.slidesPerGroup===1&&i.slidesPerGroupAuto&&(h=h-r.slidesPerViewDynamic("previous",!0)+1,h=Math.max(h,0))),i.rewind&&r.isBeginning){const d=r.params.virtual&&r.params.virtual.enabled&&r.virtual?r.virtual.slides.length-1:r.slides.length-1;return r.slideTo(d,e,t,n)}else if(i.loop&&r.activeIndex===0&&i.cssMode)return requestAnimationFrame(()=>{r.slideTo(h,e,t,n)}),!0;return r.slideTo(h,e,t,n)}function ph(e,t,n){t===void 0&&(t=!0);const r=this;if(!r.destroyed)return typeof e>"u"&&(e=r.params.speed),r.slideTo(r.activeIndex,e,t,n)}function mh(e,t,n,r){t===void 0&&(t=!0),r===void 0&&(r=.5);const i=this;if(i.destroyed)return;typeof e>"u"&&(e=i.params.speed);let l=i.activeIndex;const s=Math.min(i.params.slidesPerGroupSkip,l),a=s+Math.floor((l-s)/i.params.slidesPerGroup),o=i.rtlTranslate?i.translate:-i.translate;if(o>=i.snapGrid[a]){const c=i.snapGrid[a],f=i.snapGrid[a+1];o-c>(f-c)*r&&(l+=i.params.slidesPerGroup)}else{const c=i.snapGrid[a-1],f=i.snapGrid[a];o-c<=(f-c)*r&&(l-=i.params.slidesPerGroup)}return l=Math.max(l,0),l=Math.min(l,i.slidesGrid.length-1),i.slideTo(l,e,t,n)}function hh(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:n}=e,r=t.slidesPerView==="auto"?e.slidesPerViewDynamic():t.slidesPerView;let i=e.getSlideIndexWhenGrid(e.clickedIndex),l;const s=e.isElement?"swiper-slide":`.${t.slideClass}`,a=e.grid&&e.params.grid&&e.params.grid.rows>1;if(t.loop){if(e.animating)return;l=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?e.slideToLoop(l):i>(a?(e.slides.length-r)/2-(e.params.grid.rows-1):e.slides.length-r)?(e.loopFix(),i=e.getSlideIndex(Xe(n,`${s}[data-swiper-slide-index="${l}"]`)[0]),nd(()=>{e.slideTo(i)})):e.slideTo(i)}else e.slideTo(i)}var vh={slideTo:uh,slideToLoop:ch,slideNext:dh,slidePrev:fh,slideReset:ph,slideToClosest:mh,slideToClickedSlide:hh};function gh(e,t){const n=this,{params:r,slidesEl:i}=n;if(!r.loop||n.virtual&&n.params.virtual.enabled)return;const l=()=>{Xe(i,`.${r.slideClass}, swiper-slide`).forEach((y,g)=>{y.setAttribute("data-swiper-slide-index",g)})},s=()=>{const v=Xe(i,`.${r.slideBlankClass}`);v.forEach(y=>{y.remove()}),v.length>0&&(n.recalcSlides(),n.updateSlides())},a=n.grid&&r.grid&&r.grid.rows>1;r.loopAddBlankSlides&&(r.slidesPerGroup>1||a)&&s();const o=r.slidesPerGroup*(a?r.grid.rows:1),c=n.slides.length%o!==0,f=a&&n.slides.length%r.grid.rows!==0,p=v=>{for(let y=0;y<v;y+=1){const g=n.isElement?Li("swiper-slide",[r.slideBlankClass]):Li("div",[r.slideClass,r.slideBlankClass]);n.slidesEl.append(g)}};if(c){if(r.loopAddBlankSlides){const v=o-n.slides.length%o;p(v),n.recalcSlides(),n.updateSlides()}else Ni("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");l()}else if(f){if(r.loopAddBlankSlides){const v=r.grid.rows-n.slides.length%r.grid.rows;p(v),n.recalcSlides(),n.updateSlides()}else Ni("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");l()}else l();n.loopFix({slideRealIndex:e,direction:r.centeredSlides?void 0:"next",initial:t})}function yh(e){let{slideRealIndex:t,slideTo:n=!0,direction:r,setTranslate:i,activeSlideIndex:l,initial:s,byController:a,byMousewheel:o}=e===void 0?{}:e;const c=this;if(!c.params.loop)return;c.emit("beforeLoopFix");const{slides:f,allowSlidePrev:p,allowSlideNext:v,slidesEl:y,params:g}=c,{centeredSlides:w,initialSlide:N}=g;if(c.allowSlidePrev=!0,c.allowSlideNext=!0,c.virtual&&g.virtual.enabled){n&&(!g.centeredSlides&&c.snapIndex===0?c.slideTo(c.virtual.slides.length,0,!1,!0):g.centeredSlides&&c.snapIndex<g.slidesPerView?c.slideTo(c.virtual.slides.length+c.snapIndex,0,!1,!0):c.snapIndex===c.snapGrid.length-1&&c.slideTo(c.virtual.slidesBefore,0,!1,!0)),c.allowSlidePrev=p,c.allowSlideNext=v,c.emit("loopFix");return}let h=g.slidesPerView;h==="auto"?h=c.slidesPerViewDynamic():(h=Math.ceil(parseFloat(g.slidesPerView,10)),w&&h%2===0&&(h=h+1));const d=g.slidesPerGroupAuto?h:g.slidesPerGroup;let m=w?Math.max(d,Math.ceil(h/2)):d;m%d!==0&&(m+=d-m%d),m+=g.loopAdditionalSlides,c.loopedSlides=m;const x=c.grid&&g.grid&&g.grid.rows>1;f.length<h+m||c.params.effect==="cards"&&f.length<h+m*2?Ni("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):x&&g.grid.fill==="row"&&Ni("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const S=[],_=[],C=x?Math.ceil(f.length/g.grid.rows):f.length,T=s&&C-N<h&&!w;let j=T?N:c.activeIndex;typeof l>"u"?l=c.getSlideIndex(f.find(A=>A.classList.contains(g.slideActiveClass))):j=l;const E=r==="next"||!r,P=r==="prev"||!r;let D=0,I=0;const B=(x?f[l].column:l)+(w&&typeof i>"u"?-h/2+.5:0);if(B<m){D=Math.max(m-B,d);for(let A=0;A<m-B;A+=1){const Q=A-Math.floor(A/C)*C;if(x){const k=C-Q-1;for(let O=f.length-1;O>=0;O-=1)f[O].column===k&&S.push(O)}else S.push(C-Q-1)}}else if(B+h>C-m){I=Math.max(B-(C-m*2),d),T&&(I=Math.max(I,h-C+N+1));for(let A=0;A<I;A+=1){const Q=A-Math.floor(A/C)*C;x?f.forEach((k,O)=>{k.column===Q&&_.push(O)}):_.push(Q)}}if(c.__preventObserver__=!0,requestAnimationFrame(()=>{c.__preventObserver__=!1}),c.params.effect==="cards"&&f.length<h+m*2&&(_.includes(l)&&_.splice(_.indexOf(l),1),S.includes(l)&&S.splice(S.indexOf(l),1)),P&&S.forEach(A=>{f[A].swiperLoopMoveDOM=!0,y.prepend(f[A]),f[A].swiperLoopMoveDOM=!1}),E&&_.forEach(A=>{f[A].swiperLoopMoveDOM=!0,y.append(f[A]),f[A].swiperLoopMoveDOM=!1}),c.recalcSlides(),g.slidesPerView==="auto"?c.updateSlides():x&&(S.length>0&&P||_.length>0&&E)&&c.slides.forEach((A,Q)=>{c.grid.updateSlide(Q,A,c.slides)}),g.watchSlidesProgress&&c.updateSlidesOffset(),n){if(S.length>0&&P){if(typeof t>"u"){const A=c.slidesGrid[j],k=c.slidesGrid[j+D]-A;o?c.setTranslate(c.translate-k):(c.slideTo(j+Math.ceil(D),0,!1,!0),i&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-k,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-k))}else if(i){const A=x?S.length/g.grid.rows:S.length;c.slideTo(c.activeIndex+A,0,!1,!0),c.touchEventsData.currentTranslate=c.translate}}else if(_.length>0&&E)if(typeof t>"u"){const A=c.slidesGrid[j],k=c.slidesGrid[j-I]-A;o?c.setTranslate(c.translate-k):(c.slideTo(j-I,0,!1,!0),i&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-k,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-k))}else{const A=x?_.length/g.grid.rows:_.length;c.slideTo(c.activeIndex-A,0,!1,!0)}}if(c.allowSlidePrev=p,c.allowSlideNext=v,c.controller&&c.controller.control&&!a){const A={slideRealIndex:t,direction:r,setTranslate:i,activeSlideIndex:l,byController:!0};Array.isArray(c.controller.control)?c.controller.control.forEach(Q=>{!Q.destroyed&&Q.params.loop&&Q.loopFix({...A,slideTo:Q.params.slidesPerView===g.slidesPerView?n:!1})}):c.controller.control instanceof c.constructor&&c.controller.control.params.loop&&c.controller.control.loopFix({...A,slideTo:c.controller.control.params.slidesPerView===g.slidesPerView?n:!1})}c.emit("loopFix")}function xh(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||!n||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const r=[];e.slides.forEach(i=>{const l=typeof i.swiperSlideIndex>"u"?i.getAttribute("data-swiper-slide-index")*1:i.swiperSlideIndex;r[l]=i}),e.slides.forEach(i=>{i.removeAttribute("data-swiper-slide-index")}),r.forEach(i=>{n.append(i)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}var wh={loopCreate:gh,loopFix:yh,loopDestroy:xh};function Sh(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n=t.params.touchEventsTarget==="container"?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})}function _h(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e[e.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}var Eh={setGrabCursor:Sh,unsetGrabCursor:_h};function Th(e,t){t===void 0&&(t=this);function n(r){if(!r||r===Je()||r===Se())return null;r.assignedSlot&&(r=r.assignedSlot);const i=r.closest(e);return!i&&!r.getRootNode?null:i||n(r.getRootNode().host)}return n(t)}function Ao(e,t,n){const r=Se(),{params:i}=e,l=i.edgeSwipeDetection,s=i.edgeSwipeThreshold;return l&&(n<=s||n>=r.innerWidth-s)?l==="prevent"?(t.preventDefault(),!0):!1:!0}function Ch(e){const t=this,n=Je();let r=e;r.originalEvent&&(r=r.originalEvent);const i=t.touchEventsData;if(r.type==="pointerdown"){if(i.pointerId!==null&&i.pointerId!==r.pointerId)return;i.pointerId=r.pointerId}else r.type==="touchstart"&&r.targetTouches.length===1&&(i.touchId=r.targetTouches[0].identifier);if(r.type==="touchstart"){Ao(t,r,r.targetTouches[0].pageX);return}const{params:l,touches:s,enabled:a}=t;if(!a||!l.simulateTouch&&r.pointerType==="mouse"||t.animating&&l.preventInteractionOnTransition)return;!t.animating&&l.cssMode&&l.loop&&t.loopFix();let o=r.target;if(l.touchEventsTarget==="wrapper"&&!zm(o,t.wrapperEl)||"which"in r&&r.which===3||"button"in r&&r.button>0||i.isTouched&&i.isMoved)return;const c=!!l.noSwipingClass&&l.noSwipingClass!=="",f=r.composedPath?r.composedPath():r.path;c&&r.target&&r.target.shadowRoot&&f&&(o=f[0]);const p=l.noSwipingSelector?l.noSwipingSelector:`.${l.noSwipingClass}`,v=!!(r.target&&r.target.shadowRoot);if(l.noSwiping&&(v?Th(p,o):o.closest(p))){t.allowClick=!0;return}if(l.swipeHandler&&!o.closest(l.swipeHandler))return;s.currentX=r.pageX,s.currentY=r.pageY;const y=s.currentX,g=s.currentY;if(!Ao(t,r,y))return;Object.assign(i,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),s.startX=y,s.startY=g,i.touchStartTime=ji(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,l.threshold>0&&(i.allowThresholdMove=!1);let w=!0;o.matches(i.focusableElements)&&(w=!1,o.nodeName==="SELECT"&&(i.isTouched=!1)),n.activeElement&&n.activeElement.matches(i.focusableElements)&&n.activeElement!==o&&(r.pointerType==="mouse"||r.pointerType!=="mouse"&&!o.matches(i.focusableElements))&&n.activeElement.blur();const N=w&&t.allowTouchMove&&l.touchStartPreventDefault;(l.touchStartForcePreventDefault||N)&&!o.isContentEditable&&r.preventDefault(),l.freeMode&&l.freeMode.enabled&&t.freeMode&&t.animating&&!l.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",r)}function kh(e){const t=Je(),n=this,r=n.touchEventsData,{params:i,touches:l,rtlTranslate:s,enabled:a}=n;if(!a||!i.simulateTouch&&e.pointerType==="mouse")return;let o=e;if(o.originalEvent&&(o=o.originalEvent),o.type==="pointermove"&&(r.touchId!==null||o.pointerId!==r.pointerId))return;let c;if(o.type==="touchmove"){if(c=[...o.changedTouches].find(S=>S.identifier===r.touchId),!c||c.identifier!==r.touchId)return}else c=o;if(!r.isTouched){r.startMoving&&r.isScrolling&&n.emit("touchMoveOpposite",o);return}const f=c.pageX,p=c.pageY;if(o.preventedByNestedSwiper){l.startX=f,l.startY=p;return}if(!n.allowTouchMove){o.target.matches(r.focusableElements)||(n.allowClick=!1),r.isTouched&&(Object.assign(l,{startX:f,startY:p,currentX:f,currentY:p}),r.touchStartTime=ji());return}if(i.touchReleaseOnEdges&&!i.loop)if(n.isVertical()){if(p<l.startY&&n.translate<=n.maxTranslate()||p>l.startY&&n.translate>=n.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else{if(s&&(f>l.startX&&-n.translate<=n.maxTranslate()||f<l.startX&&-n.translate>=n.minTranslate()))return;if(!s&&(f<l.startX&&n.translate<=n.maxTranslate()||f>l.startX&&n.translate>=n.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==o.target&&o.pointerType!=="mouse"&&t.activeElement.blur(),t.activeElement&&o.target===t.activeElement&&o.target.matches(r.focusableElements)){r.isMoved=!0,n.allowClick=!1;return}r.allowTouchCallbacks&&n.emit("touchMove",o),l.previousX=l.currentX,l.previousY=l.currentY,l.currentX=f,l.currentY=p;const v=l.currentX-l.startX,y=l.currentY-l.startY;if(n.params.threshold&&Math.sqrt(v**2+y**2)<n.params.threshold)return;if(typeof r.isScrolling>"u"){let S;n.isHorizontal()&&l.currentY===l.startY||n.isVertical()&&l.currentX===l.startX?r.isScrolling=!1:v*v+y*y>=25&&(S=Math.atan2(Math.abs(y),Math.abs(v))*180/Math.PI,r.isScrolling=n.isHorizontal()?S>i.touchAngle:90-S>i.touchAngle)}if(r.isScrolling&&n.emit("touchMoveOpposite",o),typeof r.startMoving>"u"&&(l.currentX!==l.startX||l.currentY!==l.startY)&&(r.startMoving=!0),r.isScrolling||o.type==="touchmove"&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;n.allowClick=!1,!i.cssMode&&o.cancelable&&o.preventDefault(),i.touchMoveStopPropagation&&!i.nested&&o.stopPropagation();let g=n.isHorizontal()?v:y,w=n.isHorizontal()?l.currentX-l.previousX:l.currentY-l.previousY;i.oneWayMovement&&(g=Math.abs(g)*(s?1:-1),w=Math.abs(w)*(s?1:-1)),l.diff=g,g*=i.touchRatio,s&&(g=-g,w=-w);const N=n.touchesDirection;n.swipeDirection=g>0?"prev":"next",n.touchesDirection=w>0?"prev":"next";const h=n.params.loop&&!i.cssMode,d=n.touchesDirection==="next"&&n.allowSlideNext||n.touchesDirection==="prev"&&n.allowSlidePrev;if(!r.isMoved){if(h&&d&&n.loopFix({direction:n.swipeDirection}),r.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const S=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});n.wrapperEl.dispatchEvent(S)}r.allowMomentumBounce=!1,i.grabCursor&&(n.allowSlideNext===!0||n.allowSlidePrev===!0)&&n.setGrabCursor(!0),n.emit("sliderFirstMove",o)}if(new Date().getTime(),i._loopSwapReset!==!1&&r.isMoved&&r.allowThresholdMove&&N!==n.touchesDirection&&h&&d&&Math.abs(g)>=1){Object.assign(l,{startX:f,startY:p,currentX:f,currentY:p,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}n.emit("sliderMove",o),r.isMoved=!0,r.currentTranslate=g+r.startTranslate;let m=!0,x=i.resistanceRatio;if(i.touchReleaseOnEdges&&(x=0),g>0?(h&&d&&r.allowThresholdMove&&r.currentTranslate>(i.centeredSlides?n.minTranslate()-n.slidesSizesGrid[n.activeIndex+1]-(i.slidesPerView!=="auto"&&n.slides.length-i.slidesPerView>=2?n.slidesSizesGrid[n.activeIndex+1]+n.params.spaceBetween:0)-n.params.spaceBetween:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>n.minTranslate()&&(m=!1,i.resistance&&(r.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+r.startTranslate+g)**x))):g<0&&(h&&d&&r.allowThresholdMove&&r.currentTranslate<(i.centeredSlides?n.maxTranslate()+n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween+(i.slidesPerView!=="auto"&&n.slides.length-i.slidesPerView>=2?n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween:0):n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-(i.slidesPerView==="auto"?n.slidesPerViewDynamic():Math.ceil(parseFloat(i.slidesPerView,10)))}),r.currentTranslate<n.maxTranslate()&&(m=!1,i.resistance&&(r.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-r.startTranslate-g)**x))),m&&(o.preventedByNestedSwiper=!0),!n.allowSlideNext&&n.swipeDirection==="next"&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!n.allowSlidePrev&&n.swipeDirection==="prev"&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),!n.allowSlidePrev&&!n.allowSlideNext&&(r.currentTranslate=r.startTranslate),i.threshold>0)if(Math.abs(g)>i.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,l.startX=l.currentX,l.startY=l.currentY,r.currentTranslate=r.startTranslate,l.diff=n.isHorizontal()?l.currentX-l.startX:l.currentY-l.startY;return}}else{r.currentTranslate=r.startTranslate;return}!i.followFinger||i.cssMode||((i.freeMode&&i.freeMode.enabled&&n.freeMode||i.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),i.freeMode&&i.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(r.currentTranslate),n.setTranslate(r.currentTranslate))}function jh(e){const t=this,n=t.touchEventsData;let r=e;r.originalEvent&&(r=r.originalEvent);let i;if(r.type==="touchend"||r.type==="touchcancel"){if(i=[...r.changedTouches].find(S=>S.identifier===n.touchId),!i||i.identifier!==n.touchId)return}else{if(n.touchId!==null||r.pointerId!==n.pointerId)return;i=r}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(r.type)&&!(["pointercancel","contextmenu"].includes(r.type)&&(t.browser.isSafari||t.browser.isWebView)))return;n.pointerId=null,n.touchId=null;const{params:s,touches:a,rtlTranslate:o,slidesGrid:c,enabled:f}=t;if(!f||!s.simulateTouch&&r.pointerType==="mouse")return;if(n.allowTouchCallbacks&&t.emit("touchEnd",r),n.allowTouchCallbacks=!1,!n.isTouched){n.isMoved&&s.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,n.startMoving=!1;return}s.grabCursor&&n.isMoved&&n.isTouched&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!1);const p=ji(),v=p-n.touchStartTime;if(t.allowClick){const S=r.path||r.composedPath&&r.composedPath();t.updateClickedSlide(S&&S[0]||r.target,S),t.emit("tap click",r),v<300&&p-n.lastClickTime<300&&t.emit("doubleTap doubleClick",r)}if(n.lastClickTime=ji(),nd(()=>{t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||a.diff===0&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset){n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;return}n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;let y;if(s.followFinger?y=o?t.translate:-t.translate:y=-n.currentTranslate,s.cssMode)return;if(s.freeMode&&s.freeMode.enabled){t.freeMode.onTouchEnd({currentPos:y});return}const g=y>=-t.maxTranslate()&&!t.params.loop;let w=0,N=t.slidesSizesGrid[0];for(let S=0;S<c.length;S+=S<s.slidesPerGroupSkip?1:s.slidesPerGroup){const _=S<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;typeof c[S+_]<"u"?(g||y>=c[S]&&y<c[S+_])&&(w=S,N=c[S+_]-c[S]):(g||y>=c[S])&&(w=S,N=c[c.length-1]-c[c.length-2])}let h=null,d=null;s.rewind&&(t.isBeginning?d=s.virtual&&s.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(h=0));const m=(y-c[w])/N,x=w<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;if(v>s.longSwipesMs){if(!s.longSwipes){t.slideTo(t.activeIndex);return}t.swipeDirection==="next"&&(m>=s.longSwipesRatio?t.slideTo(s.rewind&&t.isEnd?h:w+x):t.slideTo(w)),t.swipeDirection==="prev"&&(m>1-s.longSwipesRatio?t.slideTo(w+x):d!==null&&m<0&&Math.abs(m)>s.longSwipesRatio?t.slideTo(d):t.slideTo(w))}else{if(!s.shortSwipes){t.slideTo(t.activeIndex);return}t.navigation&&(r.target===t.navigation.nextEl||r.target===t.navigation.prevEl)?r.target===t.navigation.nextEl?t.slideTo(w+x):t.slideTo(w):(t.swipeDirection==="next"&&t.slideTo(h!==null?h:w+x),t.swipeDirection==="prev"&&t.slideTo(d!==null?d:w))}}function Ro(){const e=this,{params:t,el:n}=e;if(n&&n.offsetWidth===0)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:r,allowSlidePrev:i,snapGrid:l}=e,s=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const a=s&&t.loop;(t.slidesPerView==="auto"||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides&&!a?e.slideTo(e.slides.length-1,0,!1,!0):e.params.loop&&!s?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=i,e.allowSlideNext=r,e.params.watchOverflow&&l!==e.snapGrid&&e.checkOverflow()}function Nh(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function Lh(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:r}=e;if(!r)return;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,e.translate===0&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();let i;const l=e.maxTranslate()-e.minTranslate();l===0?i=0:i=(e.translate-e.minTranslate())/l,i!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function Ph(e){const t=this;ni(t,e.target),!(t.params.cssMode||t.params.slidesPerView!=="auto"&&!t.params.autoHeight)&&t.update()}function Oh(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const ud=(e,t)=>{const n=Je(),{params:r,el:i,wrapperEl:l,device:s}=e,a=!!r.nested,o=t==="on"?"addEventListener":"removeEventListener",c=t;!i||typeof i=="string"||(n[o]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:a}),i[o]("touchstart",e.onTouchStart,{passive:!1}),i[o]("pointerdown",e.onTouchStart,{passive:!1}),n[o]("touchmove",e.onTouchMove,{passive:!1,capture:a}),n[o]("pointermove",e.onTouchMove,{passive:!1,capture:a}),n[o]("touchend",e.onTouchEnd,{passive:!0}),n[o]("pointerup",e.onTouchEnd,{passive:!0}),n[o]("pointercancel",e.onTouchEnd,{passive:!0}),n[o]("touchcancel",e.onTouchEnd,{passive:!0}),n[o]("pointerout",e.onTouchEnd,{passive:!0}),n[o]("pointerleave",e.onTouchEnd,{passive:!0}),n[o]("contextmenu",e.onTouchEnd,{passive:!0}),(r.preventClicks||r.preventClicksPropagation)&&i[o]("click",e.onClick,!0),r.cssMode&&l[o]("scroll",e.onScroll),r.updateOnWindowResize?e[c](s.ios||s.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ro,!0):e[c]("observerUpdate",Ro,!0),i[o]("load",e.onLoad,{capture:!0}))};function Mh(){const e=this,{params:t}=e;e.onTouchStart=Ch.bind(e),e.onTouchMove=kh.bind(e),e.onTouchEnd=jh.bind(e),e.onDocumentTouchStart=Oh.bind(e),t.cssMode&&(e.onScroll=Lh.bind(e)),e.onClick=Nh.bind(e),e.onLoad=Ph.bind(e),ud(e,"on")}function zh(){ud(this,"off")}var Dh={attachEvents:Mh,detachEvents:zh};const Fo=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;function Ih(){const e=this,{realIndex:t,initialized:n,params:r,el:i}=e,l=r.breakpoints;if(!l||l&&Object.keys(l).length===0)return;const s=Je(),a=r.breakpointsBase==="window"||!r.breakpointsBase?r.breakpointsBase:"container",o=["window","container"].includes(r.breakpointsBase)||!r.breakpointsBase?e.el:s.querySelector(r.breakpointsBase),c=e.getBreakpoint(l,a,o);if(!c||e.currentBreakpoint===c)return;const p=(c in l?l[c]:void 0)||e.originalParams,v=Fo(e,r),y=Fo(e,p),g=e.params.grabCursor,w=p.grabCursor,N=r.enabled;v&&!y?(i.classList.remove(`${r.containerModifierClass}grid`,`${r.containerModifierClass}grid-column`),e.emitContainerClasses()):!v&&y&&(i.classList.add(`${r.containerModifierClass}grid`),(p.grid.fill&&p.grid.fill==="column"||!p.grid.fill&&r.grid.fill==="column")&&i.classList.add(`${r.containerModifierClass}grid-column`),e.emitContainerClasses()),g&&!w?e.unsetGrabCursor():!g&&w&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(_=>{if(typeof p[_]>"u")return;const C=r[_]&&r[_].enabled,T=p[_]&&p[_].enabled;C&&!T&&e[_].disable(),!C&&T&&e[_].enable()});const h=p.direction&&p.direction!==r.direction,d=r.loop&&(p.slidesPerView!==r.slidesPerView||h),m=r.loop;h&&n&&e.changeDirection(),Ne(e.params,p);const x=e.params.enabled,S=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),N&&!x?e.disable():!N&&x&&e.enable(),e.currentBreakpoint=c,e.emit("_beforeBreakpoint",p),n&&(d?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!m&&S?(e.loopCreate(t),e.updateSlides()):m&&!S&&e.loopDestroy()),e.emit("breakpoint",p)}function Ah(e,t,n){if(t===void 0&&(t="window"),!e||t==="container"&&!n)return;let r=!1;const i=Se(),l=t==="window"?i.innerHeight:n.clientHeight,s=Object.keys(e).map(a=>{if(typeof a=="string"&&a.indexOf("@")===0){const o=parseFloat(a.substr(1));return{value:l*o,point:a}}return{value:a,point:a}});s.sort((a,o)=>parseInt(a.value,10)-parseInt(o.value,10));for(let a=0;a<s.length;a+=1){const{point:o,value:c}=s[a];t==="window"?i.matchMedia(`(min-width: ${c}px)`).matches&&(r=o):c<=n.clientWidth&&(r=o)}return r||"max"}var Rh={setBreakpoint:Ih,getBreakpoint:Ah};function Fh(e,t){const n=[];return e.forEach(r=>{typeof r=="object"?Object.keys(r).forEach(i=>{r[i]&&n.push(t+i)}):typeof r=="string"&&n.push(t+r)}),n}function bh(){const e=this,{classNames:t,params:n,rtl:r,el:i,device:l}=e,s=Fh(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:r},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&n.grid.fill==="column"},{android:l.android},{ios:l.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...s),i.classList.add(...t),e.emitContainerClasses()}function Vh(){const e=this,{el:t,classNames:n}=e;!t||typeof t=="string"||(t.classList.remove(...n),e.emitContainerClasses())}var Bh={addClasses:bh,removeClasses:Vh};function $h(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:r}=n;if(r){const i=e.slides.length-1,l=e.slidesGrid[i]+e.slidesSizesGrid[i]+r*2;e.isLocked=e.size>l}else e.isLocked=e.snapGrid.length===1;n.allowSlideNext===!0&&(e.allowSlideNext=!e.isLocked),n.allowSlidePrev===!0&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}var Hh={checkOverflow:$h},Ts={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Gh(e,t){return function(r){r===void 0&&(r={});const i=Object.keys(r)[0],l=r[i];if(typeof l!="object"||l===null){Ne(t,r);return}if(e[i]===!0&&(e[i]={enabled:!0}),i==="navigation"&&e[i]&&e[i].enabled&&!e[i].prevEl&&!e[i].nextEl&&(e[i].auto=!0),["pagination","scrollbar"].indexOf(i)>=0&&e[i]&&e[i].enabled&&!e[i].el&&(e[i].auto=!0),!(i in e&&"enabled"in l)){Ne(t,r);return}typeof e[i]=="object"&&!("enabled"in e[i])&&(e[i].enabled=!0),e[i]||(e[i]={enabled:!1}),Ne(t,r)}}const jl={eventsEmitter:Bm,update:Zm,translate:ih,transition:oh,slide:vh,loop:wh,grabCursor:Eh,events:Dh,breakpoints:Rh,checkOverflow:Hh,classes:Bh},Nl={};let Sa=class tt{constructor(){let t,n;for(var r=arguments.length,i=new Array(r),l=0;l<r;l++)i[l]=arguments[l];i.length===1&&i[0].constructor&&Object.prototype.toString.call(i[0]).slice(8,-1)==="Object"?n=i[0]:[t,n]=i,n||(n={}),n=Ne({},n),t&&!n.el&&(n.el=t);const s=Je();if(n.el&&typeof n.el=="string"&&s.querySelectorAll(n.el).length>1){const f=[];return s.querySelectorAll(n.el).forEach(p=>{const v=Ne({},n,{el:p});f.push(new tt(v))}),f}const a=this;a.__swiper__=!0,a.support=ld(),a.device=sd({userAgent:n.userAgent}),a.browser=ad(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],n.modules&&Array.isArray(n.modules)&&a.modules.push(...n.modules);const o={};a.modules.forEach(f=>{f({params:n,swiper:a,extendParams:Gh(n,o),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const c=Ne({},Ts,o);return a.params=Ne({},c,Nl,n),a.originalParams=Ne({},a.params),a.passedParams=Ne({},n),a.params&&a.params.on&&Object.keys(a.params.on).forEach(f=>{a.on(f,a.params.on[f])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return a.params.direction==="horizontal"},isVertical(){return a.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(t){return this.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}getSlideIndex(t){const{slidesEl:n,params:r}=this,i=Xe(n,`.${r.slideClass}, swiper-slide`),l=Pi(i[0]);return Pi(t)-l}getSlideIndexByData(t){return this.getSlideIndex(this.slides.find(n=>n.getAttribute("data-swiper-slide-index")*1===t))}getSlideIndexWhenGrid(t){return this.grid&&this.params.grid&&this.params.grid.rows>1&&(this.params.grid.fill==="column"?t=Math.floor(t/this.params.grid.rows):this.params.grid.fill==="row"&&(t=t%Math.ceil(this.slides.length/this.params.grid.rows))),t}recalcSlides(){const t=this,{slidesEl:n,params:r}=t;t.slides=Xe(n,`.${r.slideClass}, swiper-slide`)}enable(){const t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))}disable(){const t=this;t.enabled&&(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))}setProgress(t,n){const r=this;t=Math.min(Math.max(t,0),1);const i=r.minTranslate(),s=(r.maxTranslate()-i)*t+i;r.translateTo(s,typeof n>"u"?0:n),r.updateActiveIndex(),r.updateSlidesClasses()}emitContainerClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=t.el.className.split(" ").filter(r=>r.indexOf("swiper")===0||r.indexOf(t.params.containerModifierClass)===0);t.emit("_containerClasses",n.join(" "))}getSlideClasses(t){const n=this;return n.destroyed?"":t.className.split(" ").filter(r=>r.indexOf("swiper-slide")===0||r.indexOf(n.params.slideClass)===0).join(" ")}emitSlidesClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=[];t.slides.forEach(r=>{const i=t.getSlideClasses(r);n.push({slideEl:r,classNames:i}),t.emit("_slideClass",r,i)}),t.emit("_slideClasses",n)}slidesPerViewDynamic(t,n){t===void 0&&(t="current"),n===void 0&&(n=!1);const r=this,{params:i,slides:l,slidesGrid:s,slidesSizesGrid:a,size:o,activeIndex:c}=r;let f=1;if(typeof i.slidesPerView=="number")return i.slidesPerView;if(i.centeredSlides){let p=l[c]?Math.ceil(l[c].swiperSlideSize):0,v;for(let y=c+1;y<l.length;y+=1)l[y]&&!v&&(p+=Math.ceil(l[y].swiperSlideSize),f+=1,p>o&&(v=!0));for(let y=c-1;y>=0;y-=1)l[y]&&!v&&(p+=l[y].swiperSlideSize,f+=1,p>o&&(v=!0))}else if(t==="current")for(let p=c+1;p<l.length;p+=1)(n?s[p]+a[p]-s[c]<o:s[p]-s[c]<o)&&(f+=1);else for(let p=c-1;p>=0;p-=1)s[c]-s[p]<o&&(f+=1);return f}update(){const t=this;if(!t||t.destroyed)return;const{snapGrid:n,params:r}=t;r.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(s=>{s.complete&&ni(t,s)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses();function i(){const s=t.rtlTranslate?t.translate*-1:t.translate,a=Math.min(Math.max(s,t.maxTranslate()),t.minTranslate());t.setTranslate(a),t.updateActiveIndex(),t.updateSlidesClasses()}let l;if(r.freeMode&&r.freeMode.enabled&&!r.cssMode)i(),r.autoHeight&&t.updateAutoHeight();else{if((r.slidesPerView==="auto"||r.slidesPerView>1)&&t.isEnd&&!r.centeredSlides){const s=t.virtual&&r.virtual.enabled?t.virtual.slides:t.slides;l=t.slideTo(s.length-1,0,!1,!0)}else l=t.slideTo(t.activeIndex,0,!1,!0);l||i()}r.watchOverflow&&n!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(t,n){n===void 0&&(n=!0);const r=this,i=r.params.direction;return t||(t=i==="horizontal"?"vertical":"horizontal"),t===i||t!=="horizontal"&&t!=="vertical"||(r.el.classList.remove(`${r.params.containerModifierClass}${i}`),r.el.classList.add(`${r.params.containerModifierClass}${t}`),r.emitContainerClasses(),r.params.direction=t,r.slides.forEach(l=>{t==="vertical"?l.style.width="":l.style.height=""}),r.emit("changeDirection"),n&&r.update()),r}changeLanguageDirection(t){const n=this;n.rtl&&t==="rtl"||!n.rtl&&t==="ltr"||(n.rtl=t==="rtl",n.rtlTranslate=n.params.direction==="horizontal"&&n.rtl,n.rtl?(n.el.classList.add(`${n.params.containerModifierClass}rtl`),n.el.dir="rtl"):(n.el.classList.remove(`${n.params.containerModifierClass}rtl`),n.el.dir="ltr"),n.update())}mount(t){const n=this;if(n.mounted)return!0;let r=t||n.params.el;if(typeof r=="string"&&(r=document.querySelector(r)),!r)return!1;r.swiper=n,r.parentNode&&r.parentNode.host&&r.parentNode.host.nodeName===n.params.swiperElementNodeName.toUpperCase()&&(n.isElement=!0);const i=()=>`.${(n.params.wrapperClass||"").trim().split(" ").join(".")}`;let s=r&&r.shadowRoot&&r.shadowRoot.querySelector?r.shadowRoot.querySelector(i()):Xe(r,i())[0];return!s&&n.params.createElements&&(s=Li("div",n.params.wrapperClass),r.append(s),Xe(r,`.${n.params.slideClass}`).forEach(a=>{s.append(a)})),Object.assign(n,{el:r,wrapperEl:s,slidesEl:n.isElement&&!r.parentNode.host.slideSlots?r.parentNode.host:s,hostEl:n.isElement?r.parentNode.host:r,mounted:!0,rtl:r.dir.toLowerCase()==="rtl"||xt(r,"direction")==="rtl",rtlTranslate:n.params.direction==="horizontal"&&(r.dir.toLowerCase()==="rtl"||xt(r,"direction")==="rtl"),wrongRTL:xt(s,"display")==="-webkit-box"}),!0}init(t){const n=this;if(n.initialized||n.mount(t)===!1)return n;n.emit("beforeInit"),n.params.breakpoints&&n.setBreakpoint(),n.addClasses(),n.updateSize(),n.updateSlides(),n.params.watchOverflow&&n.checkOverflow(),n.params.grabCursor&&n.enabled&&n.setGrabCursor(),n.params.loop&&n.virtual&&n.params.virtual.enabled?n.slideTo(n.params.initialSlide+n.virtual.slidesBefore,0,n.params.runCallbacksOnInit,!1,!0):n.slideTo(n.params.initialSlide,0,n.params.runCallbacksOnInit,!1,!0),n.params.loop&&n.loopCreate(void 0,!0),n.attachEvents();const i=[...n.el.querySelectorAll('[loading="lazy"]')];return n.isElement&&i.push(...n.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(l=>{l.complete?ni(n,l):l.addEventListener("load",s=>{ni(n,s.target)})}),Es(n),n.initialized=!0,Es(n),n.emit("init"),n.emit("afterInit"),n}destroy(t,n){t===void 0&&(t=!0),n===void 0&&(n=!0);const r=this,{params:i,el:l,wrapperEl:s,slides:a}=r;return typeof r.params>"u"||r.destroyed||(r.emit("beforeDestroy"),r.initialized=!1,r.detachEvents(),i.loop&&r.loopDestroy(),n&&(r.removeClasses(),l&&typeof l!="string"&&l.removeAttribute("style"),s&&s.removeAttribute("style"),a&&a.length&&a.forEach(o=>{o.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),o.removeAttribute("style"),o.removeAttribute("data-swiper-slide-index")})),r.emit("destroy"),Object.keys(r.eventsListeners).forEach(o=>{r.off(o)}),t!==!1&&(r.el&&typeof r.el!="string"&&(r.el.swiper=null),Nm(r)),r.destroyed=!0),null}static extendDefaults(t){Ne(Nl,t)}static get extendedDefaults(){return Nl}static get defaults(){return Ts}static installModule(t){tt.prototype.__modules__||(tt.prototype.__modules__=[]);const n=tt.prototype.__modules__;typeof t=="function"&&n.indexOf(t)<0&&n.push(t)}static use(t){return Array.isArray(t)?(t.forEach(n=>tt.installModule(n)),tt):(tt.installModule(t),tt)}};Object.keys(jl).forEach(e=>{Object.keys(jl[e]).forEach(t=>{Sa.prototype[t]=jl[e][t]})});Sa.use([bm,Vm]);const cd=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function Yt(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"&&!e.__swiper__}function yn(e,t){const n=["__proto__","constructor","prototype"];Object.keys(t).filter(r=>n.indexOf(r)<0).forEach(r=>{typeof e[r]>"u"?e[r]=t[r]:Yt(t[r])&&Yt(e[r])&&Object.keys(t[r]).length>0?t[r].__swiper__?e[r]=t[r]:yn(e[r],t[r]):e[r]=t[r]})}function dd(e){return e===void 0&&(e={}),e.navigation&&typeof e.navigation.nextEl>"u"&&typeof e.navigation.prevEl>"u"}function fd(e){return e===void 0&&(e={}),e.pagination&&typeof e.pagination.el>"u"}function pd(e){return e===void 0&&(e={}),e.scrollbar&&typeof e.scrollbar.el>"u"}function md(e){e===void 0&&(e="");const t=e.split(" ").map(r=>r.trim()).filter(r=>!!r),n=[];return t.forEach(r=>{n.indexOf(r)<0&&n.push(r)}),n.join(" ")}function Uh(e){return e===void 0&&(e=""),e?e.includes("swiper-wrapper")?e:`swiper-wrapper ${e}`:"swiper-wrapper"}function Wh(e){let{swiper:t,slides:n,passedParams:r,changedParams:i,nextEl:l,prevEl:s,scrollbarEl:a,paginationEl:o}=e;const c=i.filter(j=>j!=="children"&&j!=="direction"&&j!=="wrapperClass"),{params:f,pagination:p,navigation:v,scrollbar:y,virtual:g,thumbs:w}=t;let N,h,d,m,x,S,_,C;i.includes("thumbs")&&r.thumbs&&r.thumbs.swiper&&!r.thumbs.swiper.destroyed&&f.thumbs&&(!f.thumbs.swiper||f.thumbs.swiper.destroyed)&&(N=!0),i.includes("controller")&&r.controller&&r.controller.control&&f.controller&&!f.controller.control&&(h=!0),i.includes("pagination")&&r.pagination&&(r.pagination.el||o)&&(f.pagination||f.pagination===!1)&&p&&!p.el&&(d=!0),i.includes("scrollbar")&&r.scrollbar&&(r.scrollbar.el||a)&&(f.scrollbar||f.scrollbar===!1)&&y&&!y.el&&(m=!0),i.includes("navigation")&&r.navigation&&(r.navigation.prevEl||s)&&(r.navigation.nextEl||l)&&(f.navigation||f.navigation===!1)&&v&&!v.prevEl&&!v.nextEl&&(x=!0);const T=j=>{t[j]&&(t[j].destroy(),j==="navigation"?(t.isElement&&(t[j].prevEl.remove(),t[j].nextEl.remove()),f[j].prevEl=void 0,f[j].nextEl=void 0,t[j].prevEl=void 0,t[j].nextEl=void 0):(t.isElement&&t[j].el.remove(),f[j].el=void 0,t[j].el=void 0))};i.includes("loop")&&t.isElement&&(f.loop&&!r.loop?S=!0:!f.loop&&r.loop?_=!0:C=!0),c.forEach(j=>{if(Yt(f[j])&&Yt(r[j]))Object.assign(f[j],r[j]),(j==="navigation"||j==="pagination"||j==="scrollbar")&&"enabled"in r[j]&&!r[j].enabled&&T(j);else{const E=r[j];(E===!0||E===!1)&&(j==="navigation"||j==="pagination"||j==="scrollbar")?E===!1&&T(j):f[j]=r[j]}}),c.includes("controller")&&!h&&t.controller&&t.controller.control&&f.controller&&f.controller.control&&(t.controller.control=f.controller.control),i.includes("children")&&n&&g&&f.virtual.enabled?(g.slides=n,g.update(!0)):i.includes("virtual")&&g&&f.virtual.enabled&&(n&&(g.slides=n),g.update(!0)),i.includes("children")&&n&&f.loop&&(C=!0),N&&w.init()&&w.update(!0),h&&(t.controller.control=f.controller.control),d&&(t.isElement&&(!o||typeof o=="string")&&(o=document.createElement("div"),o.classList.add("swiper-pagination"),o.part.add("pagination"),t.el.appendChild(o)),o&&(f.pagination.el=o),p.init(),p.render(),p.update()),m&&(t.isElement&&(!a||typeof a=="string")&&(a=document.createElement("div"),a.classList.add("swiper-scrollbar"),a.part.add("scrollbar"),t.el.appendChild(a)),a&&(f.scrollbar.el=a),y.init(),y.updateSize(),y.setTranslate()),x&&(t.isElement&&((!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-button-next"),Oi(l,t.hostEl.constructor.nextButtonSvg),l.part.add("button-next"),t.el.appendChild(l)),(!s||typeof s=="string")&&(s=document.createElement("div"),s.classList.add("swiper-button-prev"),Oi(s,t.hostEl.constructor.prevButtonSvg),s.part.add("button-prev"),t.el.appendChild(s))),l&&(f.navigation.nextEl=l),s&&(f.navigation.prevEl=s),v.init(),v.update()),i.includes("allowSlideNext")&&(t.allowSlideNext=r.allowSlideNext),i.includes("allowSlidePrev")&&(t.allowSlidePrev=r.allowSlidePrev),i.includes("direction")&&t.changeDirection(r.direction,!1),(S||C)&&t.loopDestroy(),(_||C)&&t.loopCreate(),t.update()}function Yh(e,t){e===void 0&&(e={}),t===void 0&&(t=!0);const n={on:{}},r={},i={};yn(n,Ts),n._emitClasses=!0,n.init=!1;const l={},s=cd.map(o=>o.replace(/_/,"")),a=Object.assign({},e);return Object.keys(a).forEach(o=>{typeof e[o]>"u"||(s.indexOf(o)>=0?Yt(e[o])?(n[o]={},i[o]={},yn(n[o],e[o]),yn(i[o],e[o])):(n[o]=e[o],i[o]=e[o]):o.search(/on[A-Z]/)===0&&typeof e[o]=="function"?t?r[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:n.on[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:l[o]=e[o])}),["navigation","pagination","scrollbar"].forEach(o=>{n[o]===!0&&(n[o]={}),n[o]===!1&&delete n[o]}),{params:n,passedParams:i,rest:l,events:r}}function Qh(e,t){let{el:n,nextEl:r,prevEl:i,paginationEl:l,scrollbarEl:s,swiper:a}=e;dd(t)&&r&&i&&(a.params.navigation.nextEl=r,a.originalParams.navigation.nextEl=r,a.params.navigation.prevEl=i,a.originalParams.navigation.prevEl=i),fd(t)&&l&&(a.params.pagination.el=l,a.originalParams.pagination.el=l),pd(t)&&s&&(a.params.scrollbar.el=s,a.originalParams.scrollbar.el=s),a.init(n)}function Kh(e,t,n,r,i){const l=[];if(!t)return l;const s=o=>{l.indexOf(o)<0&&l.push(o)};if(n&&r){const o=r.map(i),c=n.map(i);o.join("")!==c.join("")&&s("children"),r.length!==n.length&&s("children")}return cd.filter(o=>o[0]==="_").map(o=>o.replace(/_/,"")).forEach(o=>{if(o in e&&o in t)if(Yt(e[o])&&Yt(t[o])){const c=Object.keys(e[o]),f=Object.keys(t[o]);c.length!==f.length?s(o):(c.forEach(p=>{e[o][p]!==t[o][p]&&s(o)}),f.forEach(p=>{e[o][p]!==t[o][p]&&s(o)}))}else e[o]!==t[o]&&s(o)}),l}const Xh=e=>{!e||e.destroyed||!e.params.virtual||e.params.virtual&&!e.params.virtual.enabled||(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function Mi(){return Mi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Mi.apply(this,arguments)}function hd(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function vd(e){const t=[];return ne.Children.toArray(e).forEach(n=>{hd(n)?t.push(n):n.props&&n.props.children&&vd(n.props.children).forEach(r=>t.push(r))}),t}function qh(e){const t=[],n={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return ne.Children.toArray(e).forEach(r=>{if(hd(r))t.push(r);else if(r.props&&r.props.slot&&n[r.props.slot])n[r.props.slot].push(r);else if(r.props&&r.props.children){const i=vd(r.props.children);i.length>0?i.forEach(l=>t.push(l)):n["container-end"].push(r)}else n["container-end"].push(r)}),{slides:t,slots:n}}function Zh(e,t,n){if(!n)return null;const r=f=>{let p=f;return f<0?p=t.length+f:p>=t.length&&(p=p-t.length),p},i=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${n.offset}px`}:{top:`${n.offset}px`},{from:l,to:s}=n,a=e.params.loop?-t.length:0,o=e.params.loop?t.length*2:t.length,c=[];for(let f=a;f<o;f+=1)f>=l&&f<=s&&c.push(t[r(f)]);return c.map((f,p)=>ne.cloneElement(f,{swiper:e,style:i,key:f.props.virtualIndex||f.key||`slide-${p}`}))}function er(e,t){return typeof window>"u"?R.useEffect(e,t):R.useLayoutEffect(e,t)}const bo=R.createContext(null),Jh=R.createContext(null),gd=R.forwardRef(function(e,t){let{className:n,tag:r="div",wrapperTag:i="div",children:l,onSwiper:s,...a}=e===void 0?{}:e,o=!1;const[c,f]=R.useState("swiper"),[p,v]=R.useState(null),[y,g]=R.useState(!1),w=R.useRef(!1),N=R.useRef(null),h=R.useRef(null),d=R.useRef(null),m=R.useRef(null),x=R.useRef(null),S=R.useRef(null),_=R.useRef(null),C=R.useRef(null),{params:T,passedParams:j,rest:E,events:P}=Yh(a),{slides:D,slots:I}=qh(l),b=()=>{g(!y)};Object.assign(T.on,{_containerClasses(O,z){f(z)}});const B=()=>{Object.assign(T.on,P),o=!0;const O={...T};if(delete O.wrapperClass,h.current=new Sa(O),h.current.virtual&&h.current.params.virtual.enabled){h.current.virtual.slides=D;const z={cache:!1,slides:D,renderExternal:v,renderExternalUpdate:!1};yn(h.current.params.virtual,z),yn(h.current.originalParams.virtual,z)}};N.current||B(),h.current&&h.current.on("_beforeBreakpoint",b);const A=()=>{o||!P||!h.current||Object.keys(P).forEach(O=>{h.current.on(O,P[O])})},Q=()=>{!P||!h.current||Object.keys(P).forEach(O=>{h.current.off(O,P[O])})};R.useEffect(()=>()=>{h.current&&h.current.off("_beforeBreakpoint",b)}),R.useEffect(()=>{!w.current&&h.current&&(h.current.emitSlidesClasses(),w.current=!0)}),er(()=>{if(t&&(t.current=N.current),!!N.current)return h.current.destroyed&&B(),Qh({el:N.current,nextEl:x.current,prevEl:S.current,paginationEl:_.current,scrollbarEl:C.current,swiper:h.current},T),s&&!h.current.destroyed&&s(h.current),()=>{h.current&&!h.current.destroyed&&h.current.destroy(!0,!1)}},[]),er(()=>{A();const O=Kh(j,d.current,D,m.current,z=>z.key);return d.current=j,m.current=D,O.length&&h.current&&!h.current.destroyed&&Wh({swiper:h.current,slides:D,passedParams:j,changedParams:O,nextEl:x.current,prevEl:S.current,scrollbarEl:C.current,paginationEl:_.current}),()=>{Q()}}),er(()=>{Xh(h.current)},[p]);function k(){return T.virtual?Zh(h.current,D,p):D.map((O,z)=>ne.cloneElement(O,{swiper:h.current,swiperSlideIndex:z}))}return ne.createElement(r,Mi({ref:N,className:md(`${c}${n?` ${n}`:""}`)},E),ne.createElement(Jh.Provider,{value:h.current},I["container-start"],ne.createElement(i,{className:Uh(T.wrapperClass)},I["wrapper-start"],k(),I["wrapper-end"]),dd(T)&&ne.createElement(ne.Fragment,null,ne.createElement("div",{ref:S,className:"swiper-button-prev"}),ne.createElement("div",{ref:x,className:"swiper-button-next"})),pd(T)&&ne.createElement("div",{ref:C,className:"swiper-scrollbar"}),fd(T)&&ne.createElement("div",{ref:_,className:"swiper-pagination"}),I["container-end"]))});gd.displayName="Swiper";const yd=R.forwardRef(function(e,t){let{tag:n="div",children:r,className:i="",swiper:l,zoom:s,lazy:a,virtualIndex:o,swiperSlideIndex:c,...f}=e===void 0?{}:e;const p=R.useRef(null),[v,y]=R.useState("swiper-slide"),[g,w]=R.useState(!1);function N(x,S,_){S===p.current&&y(_)}er(()=>{if(typeof c<"u"&&(p.current.swiperSlideIndex=c),t&&(t.current=p.current),!(!p.current||!l)){if(l.destroyed){v!=="swiper-slide"&&y("swiper-slide");return}return l.on("_slideClass",N),()=>{l&&l.off("_slideClass",N)}}}),er(()=>{l&&p.current&&!l.destroyed&&y(l.getSlideClasses(p.current))},[l]);const h={isActive:v.indexOf("swiper-slide-active")>=0,isVisible:v.indexOf("swiper-slide-visible")>=0,isPrev:v.indexOf("swiper-slide-prev")>=0,isNext:v.indexOf("swiper-slide-next")>=0},d=()=>typeof r=="function"?r(h):r,m=()=>{w(!0)};return ne.createElement(n,Mi({ref:p,className:md(`${v}${i?` ${i}`:""}`),"data-swiper-slide-index":o,onLoad:m},f),s&&ne.createElement(bo.Provider,{value:h},ne.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":typeof s=="number"?s:void 0},d(),a&&!g&&ne.createElement("div",{className:"swiper-lazy-preloader"}))),!s&&ne.createElement(bo.Provider,{value:h},d(),a&&!g&&ne.createElement("div",{className:"swiper-lazy-preloader"})))});yd.displayName="SwiperSlide";function ev(e,t,n,r){return e.params.createElements&&Object.keys(r).forEach(i=>{if(!n[i]&&n.auto===!0){let l=Xe(e.el,`.${r[i]}`)[0];l||(l=Li("div",r[i]),l.className=r[i],e.el.append(l)),n[i]=l,t[i]=l}}),n}function Vn(e){return e===void 0&&(e=""),`.${e.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function tv(e){let{swiper:t,extendParams:n,on:r,emit:i}=e;const l="swiper-pagination";n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:d=>d,formatFractionTotal:d=>d,bulletClass:`${l}-bullet`,bulletActiveClass:`${l}-bullet-active`,modifierClass:`${l}-`,currentClass:`${l}-current`,totalClass:`${l}-total`,hiddenClass:`${l}-hidden`,progressbarFillClass:`${l}-progressbar-fill`,progressbarOppositeClass:`${l}-progressbar-opposite`,clickableClass:`${l}-clickable`,lockClass:`${l}-lock`,horizontalClass:`${l}-horizontal`,verticalClass:`${l}-vertical`,paginationDisabledClass:`${l}-disabled`}}),t.pagination={el:null,bullets:[]};let s,a=0;function o(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&t.pagination.el.length===0}function c(d,m){const{bulletActiveClass:x}=t.params.pagination;d&&(d=d[`${m==="prev"?"previous":"next"}ElementSibling`],d&&(d.classList.add(`${x}-${m}`),d=d[`${m==="prev"?"previous":"next"}ElementSibling`],d&&d.classList.add(`${x}-${m}-${m}`)))}function f(d,m,x){if(d=d%x,m=m%x,m===d+1)return"next";if(m===d-1)return"previous"}function p(d){const m=d.target.closest(Vn(t.params.pagination.bulletClass));if(!m)return;d.preventDefault();const x=Pi(m)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===x)return;const S=f(t.realIndex,x,t.slides.length);S==="next"?t.slideNext():S==="previous"?t.slidePrev():t.slideToLoop(x)}else t.slideTo(x)}function v(){const d=t.rtl,m=t.params.pagination;if(o())return;let x=t.pagination.el;x=et(x);let S,_;const C=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,T=t.params.loop?Math.ceil(C/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(_=t.previousRealIndex||0,S=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):typeof t.snapIndex<"u"?(S=t.snapIndex,_=t.previousSnapIndex):(_=t.previousIndex||0,S=t.activeIndex||0),m.type==="bullets"&&t.pagination.bullets&&t.pagination.bullets.length>0){const j=t.pagination.bullets;let E,P,D;if(m.dynamicBullets&&(s=_s(j[0],t.isHorizontal()?"width":"height"),x.forEach(I=>{I.style[t.isHorizontal()?"width":"height"]=`${s*(m.dynamicMainBullets+4)}px`}),m.dynamicMainBullets>1&&_!==void 0&&(a+=S-(_||0),a>m.dynamicMainBullets-1?a=m.dynamicMainBullets-1:a<0&&(a=0)),E=Math.max(S-a,0),P=E+(Math.min(j.length,m.dynamicMainBullets)-1),D=(P+E)/2),j.forEach(I=>{const b=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(B=>`${m.bulletActiveClass}${B}`)].map(B=>typeof B=="string"&&B.includes(" ")?B.split(" "):B).flat();I.classList.remove(...b)}),x.length>1)j.forEach(I=>{const b=Pi(I);b===S?I.classList.add(...m.bulletActiveClass.split(" ")):t.isElement&&I.setAttribute("part","bullet"),m.dynamicBullets&&(b>=E&&b<=P&&I.classList.add(...`${m.bulletActiveClass}-main`.split(" ")),b===E&&c(I,"prev"),b===P&&c(I,"next"))});else{const I=j[S];if(I&&I.classList.add(...m.bulletActiveClass.split(" ")),t.isElement&&j.forEach((b,B)=>{b.setAttribute("part",B===S?"bullet-active":"bullet")}),m.dynamicBullets){const b=j[E],B=j[P];for(let A=E;A<=P;A+=1)j[A]&&j[A].classList.add(...`${m.bulletActiveClass}-main`.split(" "));c(b,"prev"),c(B,"next")}}if(m.dynamicBullets){const I=Math.min(j.length,m.dynamicMainBullets+4),b=(s*I-s)/2-D*s,B=d?"right":"left";j.forEach(A=>{A.style[t.isHorizontal()?B:"top"]=`${b}px`})}}x.forEach((j,E)=>{if(m.type==="fraction"&&(j.querySelectorAll(Vn(m.currentClass)).forEach(P=>{P.textContent=m.formatFractionCurrent(S+1)}),j.querySelectorAll(Vn(m.totalClass)).forEach(P=>{P.textContent=m.formatFractionTotal(T)})),m.type==="progressbar"){let P;m.progressbarOpposite?P=t.isHorizontal()?"vertical":"horizontal":P=t.isHorizontal()?"horizontal":"vertical";const D=(S+1)/T;let I=1,b=1;P==="horizontal"?I=D:b=D,j.querySelectorAll(Vn(m.progressbarFillClass)).forEach(B=>{B.style.transform=`translate3d(0,0,0) scaleX(${I}) scaleY(${b})`,B.style.transitionDuration=`${t.params.speed}ms`})}m.type==="custom"&&m.renderCustom?(Oi(j,m.renderCustom(t,S+1,T)),E===0&&i("paginationRender",j)):(E===0&&i("paginationRender",j),i("paginationUpdate",j)),t.params.watchOverflow&&t.enabled&&j.classList[t.isLocked?"add":"remove"](m.lockClass)})}function y(){const d=t.params.pagination;if(o())return;const m=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.grid&&t.params.grid.rows>1?t.slides.length/Math.ceil(t.params.grid.rows):t.slides.length;let x=t.pagination.el;x=et(x);let S="";if(d.type==="bullets"){let _=t.params.loop?Math.ceil(m/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&_>m&&(_=m);for(let C=0;C<_;C+=1)d.renderBullet?S+=d.renderBullet.call(t,C,d.bulletClass):S+=`<${d.bulletElement} ${t.isElement?'part="bullet"':""} class="${d.bulletClass}"></${d.bulletElement}>`}d.type==="fraction"&&(d.renderFraction?S=d.renderFraction.call(t,d.currentClass,d.totalClass):S=`<span class="${d.currentClass}"></span> / <span class="${d.totalClass}"></span>`),d.type==="progressbar"&&(d.renderProgressbar?S=d.renderProgressbar.call(t,d.progressbarFillClass):S=`<span class="${d.progressbarFillClass}"></span>`),t.pagination.bullets=[],x.forEach(_=>{d.type!=="custom"&&Oi(_,S||""),d.type==="bullets"&&t.pagination.bullets.push(..._.querySelectorAll(Vn(d.bulletClass)))}),d.type!=="custom"&&i("paginationRender",x[0])}function g(){t.params.pagination=ev(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const d=t.params.pagination;if(!d.el)return;let m;typeof d.el=="string"&&t.isElement&&(m=t.el.querySelector(d.el)),!m&&typeof d.el=="string"&&(m=[...document.querySelectorAll(d.el)]),m||(m=d.el),!(!m||m.length===0)&&(t.params.uniqueNavElements&&typeof d.el=="string"&&Array.isArray(m)&&m.length>1&&(m=[...t.el.querySelectorAll(d.el)],m.length>1&&(m=m.find(x=>id(x,".swiper")[0]===t.el))),Array.isArray(m)&&m.length===1&&(m=m[0]),Object.assign(t.pagination,{el:m}),m=et(m),m.forEach(x=>{d.type==="bullets"&&d.clickable&&x.classList.add(...(d.clickableClass||"").split(" ")),x.classList.add(d.modifierClass+d.type),x.classList.add(t.isHorizontal()?d.horizontalClass:d.verticalClass),d.type==="bullets"&&d.dynamicBullets&&(x.classList.add(`${d.modifierClass}${d.type}-dynamic`),a=0,d.dynamicMainBullets<1&&(d.dynamicMainBullets=1)),d.type==="progressbar"&&d.progressbarOpposite&&x.classList.add(d.progressbarOppositeClass),d.clickable&&x.addEventListener("click",p),t.enabled||x.classList.add(d.lockClass)}))}function w(){const d=t.params.pagination;if(o())return;let m=t.pagination.el;m&&(m=et(m),m.forEach(x=>{x.classList.remove(d.hiddenClass),x.classList.remove(d.modifierClass+d.type),x.classList.remove(t.isHorizontal()?d.horizontalClass:d.verticalClass),d.clickable&&(x.classList.remove(...(d.clickableClass||"").split(" ")),x.removeEventListener("click",p))})),t.pagination.bullets&&t.pagination.bullets.forEach(x=>x.classList.remove(...d.bulletActiveClass.split(" ")))}r("changeDirection",()=>{if(!t.pagination||!t.pagination.el)return;const d=t.params.pagination;let{el:m}=t.pagination;m=et(m),m.forEach(x=>{x.classList.remove(d.horizontalClass,d.verticalClass),x.classList.add(t.isHorizontal()?d.horizontalClass:d.verticalClass)})}),r("init",()=>{t.params.pagination.enabled===!1?h():(g(),y(),v())}),r("activeIndexChange",()=>{typeof t.snapIndex>"u"&&v()}),r("snapIndexChange",()=>{v()}),r("snapGridLengthChange",()=>{y(),v()}),r("destroy",()=>{w()}),r("enable disable",()=>{let{el:d}=t.pagination;d&&(d=et(d),d.forEach(m=>m.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass)))}),r("lock unlock",()=>{v()}),r("click",(d,m)=>{const x=m.target,S=et(t.pagination.el);if(t.params.pagination.el&&t.params.pagination.hideOnClick&&S&&S.length>0&&!x.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&x===t.navigation.nextEl||t.navigation.prevEl&&x===t.navigation.prevEl))return;const _=S[0].classList.contains(t.params.pagination.hiddenClass);i(_===!0?"paginationShow":"paginationHide"),S.forEach(C=>C.classList.toggle(t.params.pagination.hiddenClass))}});const N=()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:d}=t.pagination;d&&(d=et(d),d.forEach(m=>m.classList.remove(t.params.pagination.paginationDisabledClass))),g(),y(),v()},h=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:d}=t.pagination;d&&(d=et(d),d.forEach(m=>m.classList.add(t.params.pagination.paginationDisabledClass))),w()};Object.assign(t.pagination,{enable:N,disable:h,render:y,update:v,init:g,destroy:w})}function nv(e){let{swiper:t,extendParams:n,on:r,emit:i,params:l}=e;t.autoplay={running:!1,paused:!1,timeLeft:0},n({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let s,a,o=l&&l.autoplay?l.autoplay.delay:3e3,c=l&&l.autoplay?l.autoplay.delay:3e3,f,p=new Date().getTime(),v,y,g,w,N,h,d;function m(k){!t||t.destroyed||!t.wrapperEl||k.target===t.wrapperEl&&(t.wrapperEl.removeEventListener("transitionend",m),!(d||k.detail&&k.detail.bySwiperTouchMove)&&E())}const x=()=>{if(t.destroyed||!t.autoplay.running)return;t.autoplay.paused?v=!0:v&&(c=f,v=!1);const k=t.autoplay.paused?f:p+c-new Date().getTime();t.autoplay.timeLeft=k,i("autoplayTimeLeft",k,k/o),a=requestAnimationFrame(()=>{x()})},S=()=>{let k;return t.virtual&&t.params.virtual.enabled?k=t.slides.find(z=>z.classList.contains("swiper-slide-active")):k=t.slides[t.activeIndex],k?parseInt(k.getAttribute("data-swiper-autoplay"),10):void 0},_=k=>{if(t.destroyed||!t.autoplay.running)return;cancelAnimationFrame(a),x();let O=typeof k>"u"?t.params.autoplay.delay:k;o=t.params.autoplay.delay,c=t.params.autoplay.delay;const z=S();!Number.isNaN(z)&&z>0&&typeof k>"u"&&(O=z,o=z,c=z),f=O;const $=t.params.speed,X=()=>{!t||t.destroyed||(t.params.autoplay.reverseDirection?!t.isBeginning||t.params.loop||t.params.rewind?(t.slidePrev($,!0,!0),i("autoplay")):t.params.autoplay.stopOnLastSlide||(t.slideTo(t.slides.length-1,$,!0,!0),i("autoplay")):!t.isEnd||t.params.loop||t.params.rewind?(t.slideNext($,!0,!0),i("autoplay")):t.params.autoplay.stopOnLastSlide||(t.slideTo(0,$,!0,!0),i("autoplay")),t.params.cssMode&&(p=new Date().getTime(),requestAnimationFrame(()=>{_()})))};return O>0?(clearTimeout(s),s=setTimeout(()=>{X()},O)):requestAnimationFrame(()=>{X()}),O},C=()=>{p=new Date().getTime(),t.autoplay.running=!0,_(),i("autoplayStart")},T=()=>{t.autoplay.running=!1,clearTimeout(s),cancelAnimationFrame(a),i("autoplayStop")},j=(k,O)=>{if(t.destroyed||!t.autoplay.running)return;clearTimeout(s),k||(h=!0);const z=()=>{i("autoplayPause"),t.params.autoplay.waitForTransition?t.wrapperEl.addEventListener("transitionend",m):E()};if(t.autoplay.paused=!0,O){N&&(f=t.params.autoplay.delay),N=!1,z();return}f=(f||t.params.autoplay.delay)-(new Date().getTime()-p),!(t.isEnd&&f<0&&!t.params.loop)&&(f<0&&(f=0),z())},E=()=>{t.isEnd&&f<0&&!t.params.loop||t.destroyed||!t.autoplay.running||(p=new Date().getTime(),h?(h=!1,_(f)):_(),t.autoplay.paused=!1,i("autoplayResume"))},P=()=>{if(t.destroyed||!t.autoplay.running)return;const k=Je();k.visibilityState==="hidden"&&(h=!0,j(!0)),k.visibilityState==="visible"&&E()},D=k=>{k.pointerType==="mouse"&&(h=!0,d=!0,!(t.animating||t.autoplay.paused)&&j(!0))},I=k=>{k.pointerType==="mouse"&&(d=!1,t.autoplay.paused&&E())},b=()=>{t.params.autoplay.pauseOnMouseEnter&&(t.el.addEventListener("pointerenter",D),t.el.addEventListener("pointerleave",I))},B=()=>{t.el&&typeof t.el!="string"&&(t.el.removeEventListener("pointerenter",D),t.el.removeEventListener("pointerleave",I))},A=()=>{Je().addEventListener("visibilitychange",P)},Q=()=>{Je().removeEventListener("visibilitychange",P)};r("init",()=>{t.params.autoplay.enabled&&(b(),A(),C())}),r("destroy",()=>{B(),Q(),t.autoplay.running&&T()}),r("_freeModeStaticRelease",()=>{(g||h)&&E()}),r("_freeModeNoMomentumRelease",()=>{t.params.autoplay.disableOnInteraction?T():j(!0,!0)}),r("beforeTransitionStart",(k,O,z)=>{t.destroyed||!t.autoplay.running||(z||!t.params.autoplay.disableOnInteraction?j(!0,!0):T())}),r("sliderFirstMove",()=>{if(!(t.destroyed||!t.autoplay.running)){if(t.params.autoplay.disableOnInteraction){T();return}y=!0,g=!1,h=!1,w=setTimeout(()=>{h=!0,g=!0,j(!0)},200)}}),r("touchEnd",()=>{if(!(t.destroyed||!t.autoplay.running||!y)){if(clearTimeout(w),clearTimeout(s),t.params.autoplay.disableOnInteraction){g=!1,y=!1;return}g&&t.params.cssMode&&E(),g=!1,y=!1}}),r("slideChange",()=>{t.destroyed||!t.autoplay.running||(N=!0)}),Object.assign(t.autoplay,{start:C,stop:T,pause:j,resume:E})}function rv(){const e=localize.eael_dashboard.premium_items;return u.jsx(u.Fragment,{children:u.jsx("div",{className:"ea__elements-nav-content go-premium-wrapper",children:u.jsxs("div",{className:"ea__premium-content-wrapper",children:[u.jsx(Em,{}),u.jsx(Tm,{}),u.jsx("div",{className:"ea__slider-connect",children:u.jsx("div",{className:"ea__connect-wrapper flex",children:u.jsx(gd,{modules:[nv,tv],spaceBetween:16,slidesPerView:3,loop:!0,autoplay:{delay:2500,disableOnInteraction:!1},pagination:{clickable:!0},children:e.list.map((t,n)=>u.jsx(yd,{children:u.jsx(Cm,{index:n})},n))})})}),u.jsxs("div",{className:"ea__connect-others-wrapper flex gap-4",children:[u.jsx(dn,{index:4}),u.jsx(dn,{index:5})]})]})})})}function iv(){const{eaState:e,eaDispatch:t}=Y(),n=localize.eael_dashboard.modal[e.modalID],r=e.modals[n.name],i=l=>{t({type:"MODAL_ON_CHANGE",payload:{key:n.name,value:l.target.value}})};return u.jsxs(u.Fragment,{children:[u.jsxs("div",{className:"flex items-center gap-2",children:[n.title_icon?u.jsx("img",{src:localize.eael_dashboard.reactPath+n.title_icon,alt:"mapLogo"}):"",n.title?u.jsx("h4",{children:n.title}):""]}),u.jsxs("div",{children:[u.jsx("label",{className:"mb-2",children:n.label}),u.jsx("input",{className:"input-name",type:"text",placeholder:n.placeholder?n.placeholder:"",name:n.name,value:r,onChange:i}),n.link===void 0||u.jsx("a",{className:"ea__api-link",target:"_blank",href:n.link.url,children:n.link.text})]}),n.image?u.jsx("img",{className:"ea__modal-map-img",src:localize.eael_dashboard.reactPath+n.image,alt:n.title}):""]})}function lv(){const{eaState:e,eaDispatch:t}=Y(),n=localize.eael_dashboard.modal[e.modalID],r=e.modals[n.name],i=l=>{t({type:"MODAL_ON_CHANGE",payload:{key:n.name,value:l.target.value}})};return u.jsxs(u.Fragment,{children:[u.jsx("h4",{children:n.title}),u.jsx("div",{className:"select-option-wrapper",children:u.jsxs("select",{name:n.name,onChange:i,id:"select-option",className:"form-select",children:[u.jsx("option",{value:"all",children:"All"}),Object.keys(n.options).map((l,s)=>u.jsx("option",{value:l,selected:r===l,children:n.options[l]},s))]})})]})}function sv(){const{eaState:e,eaDispatch:t}=Y(),n=localize.eael_dashboard.modal[e.modalID],r=l=>{t({type:"MODAL_ACCORDION",payload:{key:l}})},i=(l,s)=>{const a=["lr_custom_profile_fields","lr_recaptcha_badge_hide"].includes(s)?l.target.checked?"on":"":l.target.value;t({type:"MODAL_ON_CHANGE",payload:{key:s,value:a}})};return u.jsx(u.Fragment,{children:Object.keys(n.accordion).map((l,s)=>{var a,o,c;if(!(!localize.eael_dashboard.is_eapro_activate&&((a=n.accordion[l])==null?void 0:a.isPro)===!0))return u.jsxs("div",{className:"ea__api-key-according",children:[u.jsx("div",{className:"ea__according-title",onClick:()=>r(l),children:u.jsxs("div",{className:"flex justify-between items-center gap-2 pointer",children:[u.jsxs("span",{className:"flex gap-2 items-center",children:[u.jsx("img",{src:localize.eael_dashboard.reactPath+n.accordion[l].icon,alt:"icon"}),u.jsxs("h4",{className:"flex items-center gap-2",children:[n.accordion[l].title,((o=n.accordion[l])==null?void 0:o.status)!=null&&u.jsxs("label",{className:"toggle-wrap",children:[u.jsx("input",{type:"checkbox",value:"on",name:n.accordion[l].status.name,checked:e.modals[n.accordion[l].status.name]==="on",onChange:f=>i(f,n.accordion[l].status.name)}),u.jsx("span",{className:"slider"})]})]})]}),u.jsx("i",{className:l===e.modalAccordion?"ea-dash-icon ea-dropdown rotate-180":"ea-dash-icon ea-dropdown"})]})}),u.jsxs("div",{className:l===e.modalAccordion?"ea__according-content flex flex-col gap-2 accordion-show":"ea__according-content flex flex-col gap-2",children:[((c=n.accordion[l])==null?void 0:c.info)!==void 0&&u.jsx("div",{className:"flex gap-4 items-center",children:u.jsx("p",{className:"info--text",children:n.accordion[l].info})}),n.accordion[l].fields.map((f,p)=>(f==null?void 0:f.type)==="checkbox"?u.jsxs("div",{className:"ea__hide-badge flex gap-2 items-center",children:[u.jsx("input",{type:"checkbox",name:f.name,checked:e.modals[f.name]==="on",onChange:v=>i(v,f.name)}),u.jsxs("label",{children:[f.label," ",(f==null?void 0:f.info)&&u.jsx("i",{className:"ea-dash-icon ea-info",children:u.jsx("span",{className:"ea__tooltip",children:f.info})})]})]},p):u.jsxs("div",{className:"flex gap-4 items-center",children:[u.jsx("label",{children:f.label}),u.jsx("input",{name:f.name,value:e.modals[f.name],onChange:v=>i(v,f.name),className:"input-name",type:"text",placeholder:f.placeholder})]},p))]})]},s)})})}function av(){const{eaState:e,eaDispatch:t}=Y(),n=R.useRef(),r=()=>{t({type:"CLOSE_MODAL"})},i=s=>{s.preventDefault();const a=new FormData(n.current),o={};a.forEach((c,f)=>{o[f]=c,f==="lr_custom_profile_fields_text"&&(o.lr_custom_profile_fields=o.lr_custom_profile_fields!==void 0?o.lr_custom_profile_fields:""),f==="lr_recaptcha_language_v3"&&(o.lr_recaptcha_badge_hide=o.lr_recaptcha_badge_hide!==void 0?o.lr_recaptcha_badge_hide:"")}),t({type:"BUTTON_LOADER",payload:"modal"}),kn({eaState:e,eaDispatch:t},"SAVE_MODAL_DATA",o)},l=localize.eael_dashboard.modal;return u.jsx(u.Fragment,{children:u.jsx("section",{className:"ea__modal-wrapper",children:u.jsxs("form",{action:"#",method:"post",ref:n,onSubmit:i,className:"ea__modal-content-wrapper",children:[u.jsx("div",{className:"ea__modal-header",children:u.jsx("h5",{children:e.modalTitle})}),u.jsxs("div",{className:"ea__modal-body",children:[e.modalID==="loginRegisterSetting"&&u.jsx(sv,{}),e.modalID==="postDuplicatorSetting"&&u.jsx(lv,{}),["loginRegisterSetting","postDuplicatorSetting"].includes(e.modalID)||u.jsx(iv,{})]}),u.jsxs("div",{className:"ea__modal-footer flex items-center",children:[e.modalID==="loginRegisterSetting"&&u.jsx("a",{className:"ea__api-link",target:"_blank",href:l[e.modalID].link.url,children:l[e.modalID].link.text}),u.jsx("div",{className:"flex flex-end flex-1",children:u.jsxs("button",{className:"ea__modal-btn",children:["Save ",e.btnLoader==="modal"&&u.jsx("span",{className:"eael_btn_loader"})]})})]}),u.jsx("div",{className:"ea__modal-close-btn",onClick:r,children:u.jsx("i",{className:"ea-dash-icon ea-close"})})]})})})}function ov(){const e=localize.eael_dashboard.pro_modal,t=localize.eael_dashboard.reactPath,{eaDispatch:n}=Y(),r=()=>{n({type:"CLOSE_MODAL"})};return u.jsx(u.Fragment,{children:u.jsx("section",{className:"ea__modal-wrapper",children:u.jsxs("div",{className:"ea__modal-content-wrapper go-premium-wrapper",children:[u.jsx("div",{className:"ea__modal-body",children:u.jsxs("div",{className:"go-premium-wrapper",children:[u.jsxs("div",{className:"flex flex-col items-center gap-2 mb-6",children:[u.jsx("img",{className:"mb-4",src:t+"images/go-pro-icon.svg",alt:"go-pro Logo"}),u.jsx("h3",{children:e.heading}),u.jsx("p",{className:"pro--content",children:e.content})]}),u.jsx("div",{className:"ea__feature-list-wrap",children:e.list.map((i,l)=>{let s=e.list.length===l+1?"":"mb-4";return u.jsxs("div",{className:"ea__feature-list-item flex gap-2 "+s,children:[u.jsx("span",{children:u.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:u.jsx("path",{d:"M6 8L7.33333 9.33333L10 6.66667M2 8C2 8.78793 2.15519 9.56815 2.45672 10.2961C2.75825 11.0241 3.20021 11.6855 3.75736 12.2426C4.31451 12.7998 4.97595 13.2417 5.7039 13.5433C6.43185 13.8448 7.21207 14 8 14C8.78793 14 9.56815 13.8448 10.2961 13.5433C11.0241 13.2417 11.6855 12.7998 12.2426 12.2426C12.7998 11.6855 13.2417 11.0241 13.5433 10.2961C13.8448 9.56815 14 8.78793 14 8C14 7.21207 13.8448 6.43185 13.5433 5.7039C13.2417 4.97595 12.7998 4.31451 12.2426 3.75736C11.6855 3.20021 11.0241 2.75825 10.2961 2.45672C9.56815 2.15519 8.78793 2 8 2C7.21207 2 6.43185 2.15519 5.7039 2.45672C4.97595 2.75825 4.31451 3.20021 3.75736 3.75736C3.20021 4.31451 2.75825 4.97595 2.45672 5.7039C2.15519 6.43185 2 7.21207 2 8Z",stroke:"#750EF4","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),u.jsx("p",{children:i})]},l)})})]})}),u.jsx("div",{className:"ea__modal-footer flex items-center",children:u.jsx("a",{href:e.button.url,className:"flex justify-center flex-1",target:"_blank",children:u.jsxs("button",{className:"upgrade-button",children:[u.jsx("i",{className:"ea-dash-icon ea-crown-1"}),e.button.label]})})}),u.jsx("div",{className:"ea__modal-close-btn",onClick:r,children:u.jsx("i",{className:"ea-dash-icon ea-close"})})]})})})}function uv(){const{eaState:e,eaDispatch:t}=Y(),n=e.toastType==="success"?"images/success.svg":e.toastType==="warning"?"images/warning.svg":"images/error.svg",r=e.toastType==="success"?"toaster-content":e.toastType==="warning"?"toaster-content ea__warning":"toaster-content ea__error";return R.useEffect(()=>{e.toasts===!0&&setTimeout(()=>{t({type:"CLOSE_TOAST"})},2e3)},[e.toasts]),u.jsx(u.Fragment,{children:u.jsx("div",{className:"ea__toaster-wrapper",children:u.jsx("div",{className:r,children:u.jsxs("div",{className:"flex items-center justify-between gap-2 flex-1",children:[u.jsxs("div",{className:"flex gap-2 items-center",children:[u.jsx("img",{src:localize.eael_dashboard.reactPath+n,alt:"logo icon"}),u.jsx("h5",{children:e.toastMessage})]}),u.jsx("i",{className:"ea-dash-icon ea-close",onClick:()=>{t({type:"CLOSE_TOAST"})}})]})})})})}function cv(){const e=localize.eael_dashboard,{eaDispatch:t}=Y(),n=()=>{t({type:"CLOSE_ADMIN_PROMOTION"})};return u.jsx(u.Fragment,{children:u.jsxs("div",{id:"eael-admin-promotion-message",className:"eael-admin-promotion-message",children:[u.jsx("i",{className:"e-notice__dismiss eael-admin-promotion-close",role:"button","aria-label":"Dismiss",tabIndex:"0",onClick:n}),u.jsx("p",{dangerouslySetInnerHTML:{__html:e.admin_screen_promo.content}})]})})}function dv(){const{eaState:e,eaDispatch:t}=Y(),n=R.useRef();return R.useEffect(()=>{e.isDark?document.body.classList.add("eael_dash_dark_mode"):document.body.classList.remove("eael_dash_dark_mode"),t({type:"SET_OFFSET_TOP",payload:n.current.offsetTop})},[e.isDark]),u.jsxs(u.Fragment,{children:[e.optinPromo&&u.jsx(cv,{}),u.jsxs("section",{id:"ea__dashboard--wrapper",className:"ea__dashboard--wrapper",ref:n,children:[u.jsx(em,{}),u.jsxs("section",{className:e.menu==="Elements"?"ea__section-wrapper ea__main-wrapper flex":"ea__section-wrapper ea__main-wrapper flex gap-4",children:[u.jsx(nm,{}),e.menu==="general"?u.jsx(pm,{}):"",e.menu==="elements"?u.jsx(ym,{}):"",e.menu==="extensions"?u.jsx(xm,{}):"",e.menu==="tools"?u.jsx(wm,{}):"",e.menu==="integrations"?u.jsx(_m,{}):"",e.menu==="go-premium"?u.jsx(rv,{}):""]}),e.modal==="open"&&u.jsx(av,{}),e.modalGoPremium==="open"&&u.jsx(ov,{}),e.toasts&&u.jsx(uv,{})]})]})}function fv(){localize.eael_dashboard;const e=(r,{type:i,payload:l})=>{let s,a,o,c,f,p,v,y,g,w,N,h,d,m;switch(i){case"SET_MENU":return{...r,menu:l};case"SET_OFFSET_TOP":return{...r,scrollOffset:l};case"INTEGRATION_LOADER":return{...r,[l]:!0};case"ON_CHANGE_INTEGRATION":return g={...r.integrations,[l.key]:l.value},{...r,integrations:g,[l.key]:!1};case"ON_CHANGE_ELEMENT":return w={...r.elements,[l.key]:l.value},{...r,elements:w};case"ON_CHANGE_ALL":return l.key==="extensionAll"?r.extensions.map(x=>{r.proElements.includes(x)||(r.elements[x]=l.value)}):l.key==="widgetAll"?Object.keys(r.widgets).map(x=>{r[x]=l.value,r.widgets[x].map(S=>{r.proElements.includes(S)||(r.elements[S]=l.value)})}):l.key==="searchAll"?Object.keys(r.search).map(x=>{r.proElements.includes(x)||(r.elements[x]=l.value)}):r.widgets[l.key].map(x=>{r.proElements.includes(x)||(r.elements[x]=l.value)}),{...r,[l.key]:l.value};case"ON_SEARCH":return m=Object.keys(l.value).length===0,{...r,search:l.value,search404:m};case"OPEN_LICENSE_FORM":return{...r,licenseFormOpen:l};case"LICENSE_ACTIVATE":return o=l.licenseError,f=l.otp,p=l.otpEmail,a=l.licenseStatus,y=l.hiddenLicenseKey,v=l.errorMessage,{...r,otp:f,licenseStatus:a,hiddenLicenseKey:y,licenseError:o,otpEmail:p,errorMessage:v,btnLoader:"",licenseKey:l.licenseKey};case"OTP_VERIFY":return a=l.licenseStatus,y=l.hiddenLicenseKey,c=l.otpError,v=l.errorMessage,f=l.otp,{...r,licenseStatus:a,hiddenLicenseKey:y,otpError:c,errorMessage:v,otp:f,btnLoader:""};case"LICENSE_DEACTIVATE":return a=l.licenseStatus,y=l.hiddenLicenseKey,o=l.licenseError,v=l.errorMessage,{...r,licenseStatus:a,hiddenLicenseKey:y,licenseError:o,errorMessage:v,btnLoader:""};case"RESEND_OTP":return d=l.toastType,h=l.toastMessage,{...r,otp:!0,toasts:!0,toastType:d,toastMessage:h,btnLoader:""};case"GO_PRO_MODAL":return{...r,modalGoPremium:"open"};case"BUTTON_LOADER":return{...r,btnLoader:l,licenseError:"",otpError:""};case"OPEN_MODAL":return{...r,modal:"open",modalID:l.key,modalTitle:l.title};case"CLOSE_MODAL":return{...r,modal:"close",modalGoPremium:"close"};case"CLOSE_TOAST":return{...r,toasts:!1};case"MODAL_ACCORDION":return{...r,modalAccordion:l.key};case"MODAL_ON_CHANGE":return N={...r.modals,[l.key]:l.value},{...r,modals:N};case"SAVE_MODAL_DATA":return{...r,...l};case"SAVE_ELEMENTS_DATA":return{...r,toasts:!0,btnLoader:"",...l};case"SAVE_TOOLS":return{...r,toasts:!0,btnLoader:"",...l};case"REGENERATE_ASSETS":return{...r,toasts:!0,...l};case"ELEMENTS_CAT":return{...r,elementsActivateCatIndex:l};case"LIGHT_DARK_TOGGLE":return Zp("isDark",!r.isDark),{...r,isDark:!r.isDark};case"INSTALL_TEMPLATELY":return{...r,isTemplatelyInstalled:!0,btnLoader:""};case"CLOSE_ADMIN_PROMOTION":return s={action:"eael_admin_promotion",security:localize.nonce},jn(s,!0),{...r,optinPromo:!1}}},[t,n]=R.useReducer(e,he);return u.jsx(Jp,{value:{eaState:t,eaDispatch:n},children:u.jsx(dv,{})})}Ll.createRoot(document.getElementById("eael-dashboard")).render(u.jsx(ne.StrictMode,{children:u.jsx(fv,{})}));
