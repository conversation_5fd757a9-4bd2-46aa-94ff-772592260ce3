!function(e){"use strict";var i="Morphext",s={animation:"bounceIn",separator:",",speed:2e3,complete:e.noop};function n(t,i){this.element=e(t),this.settings=e.extend({},s,i),this._defaults=s,this._init()}n.prototype={_init:function(){var s=this;this.phrases=[],this.element.addClass("morphext"),e.each(this.element.text().split(this.settings.separator),function(t,i){s.phrases.push(e.trim(i))}),this.index=-1,this.animate(),this.start()},animate:function(){this.index=++this.index%this.phrases.length,this.element[0].innerHTML='<span class="animated '+this.settings.animation+'">'+this.phrases[this.index]+"</span>",e.isFunction(this.settings.complete)&&this.settings.complete.call(this)},start:function(){var t=this;this._interval=setInterval(function(){t.animate()},this.settings.speed)},stop:function(){this._interval=clearInterval(this._interval)}},e.fn[i]=function(t){return this.each(function(){e.data(this,"plugin_"+i)||e.data(this,"plugin_"+i,new n(this,t))})}}(jQuery);