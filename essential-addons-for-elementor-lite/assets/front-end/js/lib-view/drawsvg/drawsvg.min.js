!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=function(e,n){return void 0===n&&(n="undefined"!=typeof window?require("jquery"):require("jquery")(e)),t(n),n}:t(jQuery)}(function(t){"use strict";var e,n="drawsvg",a={duration:1e3,stagger:200,easing:"swing",reverse:!1,callback:t.noop},o=((e=function e(o,i){var r=this,s=t.extend(a,i);r.$elm=t(o),r.$elm.is("svg")&&(r.options=s,r.$paths=r.$elm.find("path, circle, rect, polygon"),r.totalDuration=s.duration+s.stagger*r.$paths.length,r.duration=s.duration/r.totalDuration,r.$paths.each(function(t,e){var n=e.getTotalLength();e.pathLen=n,e.delay=s.stagger*t/r.totalDuration,e.style.strokeDasharray=[n,n].join(" "),e.style.strokeDashoffset=n}),r.$elm.attr("class",function(t,e){return[e,n+"-initialized"].join(" ")}))}).prototype.getVal=function(e,n){return 1-t.easing[n](e,e,0,1,1)},e.prototype.progress=function t(e){var n=this,a=n.options,o=n.duration;n.$paths.each(function(t,i){var r=i.style;if(1===e)r.strokeDashoffset=0;else if(0===e)r.strokeDashoffset=i.pathLen+"px";else if(e>=i.delay&&e<=o+i.delay){var s=(e-i.delay)/o;r.strokeDashoffset=n.getVal(s,a.easing)*i.pathLen*(a.reverse?-1:1)+"px"}})},e.prototype.animate=function e(){var a=this;a.$elm.attr("class",function(t,e){return[e,n+"-animating"].join(" ")}),t({len:0}).animate({len:1},{easing:"linear",duration:a.totalDuration,step:function(t,e){a.progress.call(a,t/e.end)},complete:function(){a.options.callback.call(this),a.$elm.attr("class",function(t,e){return e.replace(n+"-animating","")})}})},e);t.fn[n]=function(e,a){return this.each(function(){var i=t.data(this,n);i&&""+e===e&&i[e]?i[e](a):t.data(this,n,new o(this,e))})}});