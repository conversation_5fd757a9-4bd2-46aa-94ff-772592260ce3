!function(a){a.fn.fancyTable=function(e){var t=a.extend({inputStyle:"",inputPlaceholder:"Search...",pagination:!1,paginationClass:"eael-ec-pagination-btn",paginationClassActive:"active",pagClosest:3,perPage:10,sortable:!0,searchable:!0,matchCase:!1,exactMatch:!1,localeCompare:!1,searchInput:".ea-ec-search-wrap",paginationElement:".eael-event-calendar-pagination",onInit:function(){},beforeUpdate:function(){},onUpdate:function(){},sortFunction:function(e,n,r,s,i){return e==n&&s&&i?r.rowSortOrder[a(s).data("rowid")]>r.rowSortOrder[a(i).data("rowid")]:"numeric"==r.sortAs[r.sortColumn]?r.sortOrder>0?(parseFloat(e)||0)-(parseFloat(n)||0):(parseFloat(n)||0)-(parseFloat(e)||0):t.localeCompare?0>e.localeCompare(n)?-r.sortOrder:e.localeCompare(n)>0?r.sortOrder:0:e<n?-r.sortOrder:e>n?r.sortOrder:0},testing:!1},e),n=this;return this.settings=t,this.tableUpdate=function(e){if(t.beforeUpdate.call(this,e),e.fancyTable.matches=0,a(e).find("tbody tr").each(function(){var r=0,s=!0,i=!1;a(this).find("td").each(function(){t.globalSearch||!e.fancyTable.searchArr[r]||n.isSearchMatch(a(this).html(),e.fancyTable.searchArr[r])?t.globalSearch&&(!e.fancyTable.search||n.isSearchMatch(a(this).html(),e.fancyTable.search))&&(!Array.isArray(t.globalSearchExcludeColumns)||!t.globalSearchExcludeColumns.includes(r+1))&&(i=!0):s=!1,r++}),t.globalSearch&&i||!t.globalSearch&&s?(e.fancyTable.matches++,!t.pagination||e.fancyTable.matches>e.fancyTable.perPage*(e.fancyTable.page-1)&&e.fancyTable.matches<=e.fancyTable.perPage*e.fancyTable.page?a(this).show():a(this).hide()):a(this).hide()}),e.fancyTable.pages=Math.ceil(e.fancyTable.matches/e.fancyTable.perPage),t.pagination){var r=t.paginationElement;if(r.empty(),e.fancyTable.pages>1){for(var s=1;s<=e.fancyTable.pages;s++)if(1==s||s>e.fancyTable.page-(t.pagClosest+1)&&s<e.fancyTable.page+(t.pagClosest+1)||s==e.fancyTable.pages){var i=a("<a>",{html:s,"data-n":s,class:t.paginationClass+" "+(s==e.fancyTable.page?t.paginationClassActive:"")}).bind("click",function(){e.fancyTable.page=a(this).data("n"),n.tableUpdate(e)});s==e.fancyTable.pages&&e.fancyTable.page<e.fancyTable.pages-t.pagClosest-1&&r.append(a("<span>...</span>")),r.append(i),1==s&&e.fancyTable.page>t.pagClosest+2&&r.append(a("<span>...</span>"))}}}t.onUpdate.call(this,e)},this.isSearchMatch=function(a,e){if(t.matchCase||(a=a.toUpperCase(),e=e.toUpperCase()),"auto"==t.exactMatch&&e.match(/^".*?"$/))return a==(e=e.substring(1,e.length-1));if("auto"==t.exactMatch&&e.replace(/\s+/g,"").match(/^[<>]=?/)){var n=e.replace(/\s+/g,"").match(/^[<>]=?/)[0],r=e.replace(/\s+/g,"").substring(n.length);return">"==n&&1*a>1*r||"<"==n&&1*a<1*r||">="==n&&1*a>=1*r||"<="==n&&1*a<=1*r}if("auto"==t.exactMatch&&e.replace(/\s+/g,"").match(/^.+(\.\.|-).+$/)){var s=e.replace(/\s+/g,"").split(/\.\.|-/);return 1*a>=1*s[0]&&1*a<=1*s[1]}try{return!0===t.exactMatch?a==e:RegExp(e).test(a)}catch{return!1}},this.reinit=function(){a(this).each(function(){a(this).find("th a").contents().unwrap()}),a(this).fancyTable(this.settings)},this.tableSort=function(e){if(void 0!==e.fancyTable.sortColumn&&e.fancyTable.sortColumn<e.fancyTable.nColumns){var n=0;a(e).find("thead th").each(function(){a(this).attr("aria-sort",n==e.fancyTable.sortColumn?1==e.fancyTable.sortOrder?"ascending":-1==e.fancyTable.sortOrder?"descending":"other":null),n++}),a(e).find("thead th div.sortArrow").each(function(){a(this).remove()});var r=a("<div>",{class:"sortArrow"}).css({margin:"0.1em",display:"inline-block",width:0,height:0,"border-left":"0.4em solid transparent","border-right":"0.4em solid transparent"});r.css(e.fancyTable.sortOrder>0?{"border-top":"0.4em solid #000"}:{"border-bottom":"0.4em solid #000"}),a(e).find("thead th a").eq(e.fancyTable.sortColumn).append(r);var s=a(e).find("tbody tr").toArray().sort(function(n,r){var s=a(n).find("td").eq(e.fancyTable.sortColumn),i=a(r).find("td").eq(e.fancyTable.sortColumn),o=a(s).attr("data-sortvalue")?a(s).data("sortvalue"):s.html(),l=a(i).attr("data-sortvalue")?a(i).data("sortvalue"):i.html();return"case-insensitive"==e.fancyTable.sortAs[e.fancyTable.sortColumn]&&(o=o.toLowerCase(),l=l.toLowerCase()),t.sortFunction.call(this,o,l,e.fancyTable,n,r)});a(s).each(function(t){e.fancyTable.rowSortOrder[a(this).data("rowid")]=t}),a(e).find("tbody").empty().append(s)}},this.each(function(){if("TABLE"!==a(this).prop("tagName"))return console.warn("fancyTable: Element is not a table."),!0;var e=this;if(e.fancyTable={nColumns:a(e).find("td").first().parent().find("td").length,nRows:a(this).find("tbody tr").length,perPage:t.perPage,page:1,pages:0,matches:0,searchArr:[],search:"",sortColumn:t.sortColumn,sortOrder:void 0===t.sortOrder?1:/desc/i.test(t.sortOrder)||-1==t.sortOrder?-1:1,sortAs:[],paginationElement:t.paginationElement},e.fancyTable.rowSortOrder=Array(e.fancyTable.nRows),0==a(e).find("tbody").length){var r=a(e).html();a(e).empty(),a(e).append("<tbody>").append(a(r))}if(0==a(e).find("thead").length&&a(e).prepend(a("<thead>")),a(e).find("tbody tr").each(function(e){a(this).data("rowid",e)}),t.sortable){var s=0;a(e).find("thead th").each(function(){e.fancyTable.sortAs.push("numeric"==a(this).data("sortas")?"numeric":"case-insensitive"==a(this).data("sortas")?"case-insensitive":null);var t=a(this).html(),r=a("<a>",{href:"#","aria-label":"Sort by "+a(this).text(),html:t,"data-n":s,class:""}).bind("click",function(){return e.fancyTable.sortColumn==a(this).data("n")?e.fancyTable.sortOrder=-e.fancyTable.sortOrder:e.fancyTable.sortOrder=1,e.fancyTable.sortColumn=a(this).data("n"),n.tableSort(e),n.tableUpdate(e),!1});a(this).empty(),a(this).append(r),s++})}t.searchable&&(t.searchInput,t.globalSearch&&a("input").bind("change paste keyup",function(){e.fancyTable.search=a(this).val(),e.fancyTable.page=1,n.tableUpdate(e)})),n.tableSort(e),t.pagination&&!t.paginationElement&&t.paginationElement.html(""),n.tableUpdate(e),t.onInit.call(this,e)}),this}}(jQuery);