!function(e){e.fn.eaelZoomLense=function(t){let o=e.extend({lensWidth:100,lensHeight:100,borderRadius:"50%",lensBorder:"1px solid #aaa",resultBorder:"1px solid #aaa",gap:10,autoResize:!1,minLensSize:40,maxLensSize:300,step:10,zoomId:t.zoomId?"zoom_"+t.zoomId:"zoom"},t);return e(this).on("mouseenter touchstart mousemove touchmove",function(t){let i=e(this),n=o.zoomId;if(e(".eael-lens-"+n).length||e(".eael-result-"+n).length)return;let l=o.lensWidth,a=o.lensHeight,s=e("<div>",{class:"eael-lens-"+n,css:{position:"absolute",pointerEvents:"none",borderRadius:o.borderRadius,border:o.lensBorder,backgroundColor:"rgba(255,255,255,0.3)",zIndex:999}}).appendTo("body"),r=e("<div>",{class:"eael-result-"+n,css:{position:"absolute",border:o.resultBorder,backgroundRepeat:"no-repeat",zIndex:998}}).appendTo("body"),h=i.offset();function c(){let t=i.outerWidth(),n=i.outerHeight(),l=t,a=n,s=o.gap,r=e(window).width(),c=e(window).height(),p=e(window).scrollLeft(),f=e(window).scrollTop(),u=r-(h.left-p+t),d=h.left-p,g=c-(h.top-f+n),m=h.top-f,$={top:h.top,left:h.left},b=l,v=a;if(u>=l+s)$.left=h.left+t+s,$.top=h.top;else if(d>=l+s)$.left=h.left-l-s,$.top=h.top;else if(g>=a+s)$.left=h.left,$.top=h.top+n+s;else if(m>=a+s)$.left=h.left,$.top=h.top-a-s;else{let x=[{space:u,direction:"right",availableWidth:u,availableHeight:c},{space:d,direction:"left",availableWidth:d,availableHeight:c},{space:g,direction:"bottom",availableWidth:r,availableHeight:g},{space:m,direction:"top",availableWidth:r,availableHeight:m}];x.sort((e,t)=>t.space-e.space);let w=x[0],z=function e(t,o){let i=l/a,n=Math.min(l,t-s-20),r=Math.min(a,o-s-20);return n/i>r?n=r*i:r=n/i,(n<150||r<150)&&(i>=1?(n=150,r=150/i):(r=150,n=150*i)),{width:n,height:r}}(w.availableWidth,w.availableHeight);switch(b=z.width,v=z.height,w.direction){case"right":$.left=h.left+t+s,$.top=h.top;break;case"left":$.left=h.left-b-s,$.top=h.top;break;case"bottom":$.left=h.left,$.top=h.top+n+s;break;case"top":$.left=h.left,$.top=h.top-v-s}$.left=Math.max(p+10,Math.min($.left,p+r-b-10)),$.top=Math.max(f+10,Math.min($.top,f+c-v-10))}return{position:$,width:b,height:v,scaleX:b/l,scaleY:v/a}}let p=c();function f(e){let t=e.originalEvent.touches?e.originalEvent.touches[0]:e,o=t.pageX,n=t.pageY,h=i.offset(),c=o-l/2,p=n-a/2,f=h.left+i.outerWidth()-l,u=h.top+i.outerHeight()-a;c=Math.max(h.left,Math.min(c,f)),p=Math.max(h.top,Math.min(p,u)),s.css({width:l,height:a,top:p,left:c,display:"block"});let d=r.data("currentConfig")||{width:r.width(),height:r.height()},g=d.width/l,m=d.height/a;r.css({display:"block",backgroundPosition:`-${(c-h.left)*g}px -${(p-h.top)*m}px`})}r.css({width:p.width,height:p.height,top:p.position.top,left:p.position.left,backgroundImage:`url(${i.attr("src")})`,backgroundSize:`${i.outerWidth()*(p.width/l)}px ${i.outerHeight()*(p.height/a)}px`}),r.data("currentConfig",p),o.autoResize&&e(window).on("wheel."+n,function(e){if(!s.is(":visible"))return;e.preventDefault();let t=e.originalEvent.deltaY,n=t>0?1:-1,h=Math.max(o.minLensSize,Math.min(o.maxLensSize,l-n*o.step)),c=Math.max(o.minLensSize,Math.min(o.maxLensSize,a-n*o.step)),p=(l-h)/2,f=(a-c)/2;l=h,a=c;let u=s.offset();s.css({width:l,height:a,top:u.top+f,left:u.left+p});let d=r.data("currentConfig")||{width:r.width(),height:r.height()},g=d.width/l,m=d.height/a,$=i.offset();r.css({backgroundSize:`${i.outerWidth()*g}px ${i.outerHeight()*m}px`,backgroundPosition:`-${(s.offset().left-$.left)*g}px -${(s.offset().top-$.top)*m}px`})}),e(document).on("mousemove."+n+" touchmove."+n,f),e(window).on("scroll."+n+" resize."+n,function e(){let t=c();r.css({width:t.width,height:t.height,top:t.position.top,left:t.position.left,backgroundSize:`${i.outerWidth()*(t.width/l)}px ${i.outerHeight()*(t.height/a)}px`}),r.data("currentConfig",t)}),i.on("mouseleave."+n+" touchend."+n,function(){s.remove(),r.remove(),e(document).off("mousemove."+n+" touchmove."+n),e(window).off("wheel."+n+" scroll."+n+" resize."+n),i.off("mouseleave."+n+" touchend."+n)}),f(t)}),this},e.fn.eaelMagnify=function(t){let o=e.extend({lensSize:200,zoom:2,lensBorder:"2px solid #fff"},t);return this.each(function(){let t=e(this),i=null,n=o.zoom,l=!1;function a(){if(l)return;l=!0;let a=t[0].naturalWidth,s=t[0].naturalHeight;function r(e){if(!i)return;let o=e.originalEvent.touches?e.originalEvent.touches[0]:e,l=o.pageX,r=o.pageY,h=t.offset(),c=t.width(),p=t.height(),f=l-h.left,u=r-h.top;if(f<0||u<0||f>c||u>p){i.css("opacity",0);return}let d=a*n,g=s*n,m=f/c*a*n-i.width()/2,$=u/p*s*n-i.height()/2;i.css({left:`${l-i.width()/2}px`,top:`${r-i.height()/2}px`,backgroundPosition:`-${m}px -${$}px`,backgroundSize:`${d}px ${g}px`,opacity:1})}function h(){i&&(i.css("opacity",0),setTimeout(()=>{i?.remove(),i=null,e(document).off("touchend.eaelMagnify touchcancel.eaelMagnify")},200))}t.on("mouseenter touchstart",function(n){i||("touchstart"===n.type&&n.preventDefault(),i=e('<div class="eael-magnify-lens"></div>').css({width:o.lensSize,height:o.lensSize,border:o.lensBorder,backgroundImage:`url(${t.attr("src")})`,backgroundRepeat:"no-repeat",pointerEvents:"none",opacity:0,boxShadow:"0 5px 10px rgba(0, 0, 0, 0.2)",borderRadius:"50%",transition:"opacity 0.2s",position:"absolute",zIndex:9999}),e("body").append(i),"touchstart"===n.type&&r(n))}),t.on("mousemove touchmove",function(e){"touchmove"===e.type&&e.preventDefault(),r(e)}),t.on("mouseleave touchend touchcancel",h),e(document).on("touchend.eaelMagnify touchcancel.eaelMagnify",function(o){i&&!e(o.target).closest(t).length&&h()}),t.on("wheel",function(e){if(!i)return;e.preventDefault();let t=e.originalEvent.deltaY||e.originalEvent.wheelDelta;n=t>0?Math.max(1,n-.1):Math.min(5,n+.1),r(e)})}t[0].complete&&t[0].naturalWidth?a():t.one("load",a)})}}(jQuery);