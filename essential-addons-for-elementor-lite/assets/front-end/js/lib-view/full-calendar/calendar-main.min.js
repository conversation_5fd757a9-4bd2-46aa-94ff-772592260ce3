var FullCalendar=function(e){"use strict"; var t,n,r,o,i=function(e,t){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function a(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var s=function(){return(s=Object.assign||function e(t){for(var n,r=1,o=arguments.length;r<o;r++)for(var i in n=arguments[r])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};function l(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||t)}var u,c,d,p,f,h={},v=[],g=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function m(e,t){for(var n in t)e[n]=t[n];return e}function y(e){var t=e.parentNode;t&&t.removeChild(e)}function E(e,t,n){var r,o,i,a=arguments,s={};for(i in t)"key"==i?r=t[i]:"ref"==i?o=t[i]:s[i]=t[i];if(arguments.length>3)for(n=[n],i=3;i<arguments.length;i++)n.push(a[i]);if(null!=n&&(s.children=n),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===s[i]&&(s[i]=e.defaultProps[i]);return S(e,s,r,o,null)}function S(e,t,n,r,o){var i={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++u.__v:o};return null!=u.vnode&&u.vnode(i),i}function D(e){return e.children}function b(e,t){this.props=e,this.context=t}function C(e,t){if(null==t)return e.__?C(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?C(e):null}function $(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return $(e)}}function R(e){(!e.__d&&(e.__d=!0)&&c.push(e)&&!w.__r++||p!==u.debounceRendering)&&((p=u.debounceRendering)||d)(w)}function w(){for(var e;w.__r=c.length;)e=c.sort(function(e,t){return e.__v.__b-t.__v.__b}),c=[],e.some(function(e){var t,n,r,o,i,a;e.__d&&(i=(o=(t=e).__v).__e,(a=t.__P)&&(n=[],(r=m({},o)).__v=o.__v+1,H(a,o,r,t.__n,void 0!==a.ownerSVGElement,null!=o.__h?[i]:null,n,null==i?C(o):i,o.__h),O(n,o),o.__e!=i&&$(o)))})}function T(e,t,n,r,o,i,a,s,l,u){var c,d,p,f,g,m,y,E=r&&r.__k||v,b=E.length;for(n.__k=[],c=0;c<t.length;c++)if(null!=(f=n.__k[c]=null==(f=t[c])||"boolean"==typeof f?null:"string"==typeof f||"number"==typeof f||"bigint"==typeof f?S(null,f,null,null,f):Array.isArray(f)?S(D,{children:f},null,null,null):f.__b>0?S(f.type,f.props,f.key,null,f.__v):f)){if(f.__=n,f.__b=n.__b+1,null===(p=E[c])||p&&f.key==p.key&&f.type===p.type)E[c]=void 0;else for(d=0;d<b;d++){if((p=E[d])&&f.key==p.key&&f.type===p.type){E[d]=void 0;break}p=null}H(e,f,p=p||h,o,i,a,s,l,u),g=f.__e,(d=f.ref)&&p.ref!=d&&(y||(y=[]),p.ref&&y.push(p.ref,null,f),y.push(d,f.__c||g,f)),null!=g?(null==m&&(m=g),"function"==typeof f.type&&null!=f.__k&&f.__k===p.__k?f.__d=l=k(f,l,e):l=M(e,f,p,E,g,l),u||"option"!==n.type?"function"==typeof n.type&&(n.__d=l):e.value=""):l&&p.__e==l&&l.parentNode!=e&&(l=C(p))}for(n.__e=m,c=b;c--;)null!=E[c]&&("function"==typeof n.type&&null!=E[c].__e&&E[c].__e==n.__d&&(n.__d=C(r,c+1)),L(E[c],E[c]));if(y)for(c=0;c<y.length;c++)A(y[c],y[++c],y[++c])}function k(e,t,n){var r,o;for(r=0;r<e.__k.length;r++)(o=e.__k[r])&&(o.__=e,t="function"==typeof o.type?k(o,t,n):M(n,o,o,e.__k,o.__e,t));return t}function x(e,t){return t=t||[],null==e||"boolean"==typeof e||(Array.isArray(e)?e.some(function(e){x(e,t)}):t.push(e)),t}function M(e,t,n,r,o,i){var a,s,l;if(void 0!==t.__d)a=t.__d,t.__d=void 0;else if(null==n||o!=i||null==o.parentNode)n:if(null==i||i.parentNode!==e)e.appendChild(o),a=null;else{for(s=i,l=0;(s=s.nextSibling)&&l<r.length;l+=2)if(s==o)break n;e.insertBefore(o,i),a=i}return void 0!==a?a:o.nextSibling}function _(e,t,n){"-"===t[0]?e.setProperty(t,n):e[t]=null==n?"":"number"!=typeof n||g.test(t)?n:n+"px"}function I(e,t,n,r,o){var i;n:if("style"===t){if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||_(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||_(e.style,t,n[t])}}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase() in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r||e.addEventListener(t,i?N:P,i):e.removeEventListener(t,i?N:P,i);else if("dangerouslySetInnerHTML"!==t){if(o)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break n}catch(a){}"function"==typeof n||(null!=n&&(!1!==n||"a"===t[0]&&"r"===t[1])?e.setAttribute(t,n):e.removeAttribute(t))}}function P(e){this.l[e.type+!1](u.event?u.event(e):e)}function N(e){this.l[e.type+!0](u.event?u.event(e):e)}function H(e,t,n,r,o,i,a,s,l){var c,d,p,f,g,E,S,C,$,R,w,k=t.type;if(void 0!==t.constructor)return null;null!=n.__h&&(l=n.__h,s=t.__e=n.__e,t.__h=null,i=[s]),(c=u.__b)&&c(t);try{n:if("function"==typeof k){if(C=t.props,$=(c=k.contextType)&&r[c.__c],R=c?$?$.props.value:c.__:r,n.__c?S=(d=t.__c=n.__c).__=d.__E:("prototype"in k&&k.prototype.render?t.__c=d=new k(C,R):(t.__c=d=new b(C,R),d.constructor=k,d.render=U),$&&$.sub(d),d.props=C,d.state||(d.state={}),d.context=R,d.__n=r,p=d.__d=!0,d.__h=[]),null==d.__s&&(d.__s=d.state),null!=k.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=m({},d.__s)),m(d.__s,k.getDerivedStateFromProps(C,d.__s))),f=d.props,g=d.state,p)null==k.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(null==k.getDerivedStateFromProps&&C!==f&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(C,R),!d.__e&&null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(C,d.__s,R)||t.__v===n.__v){d.props=C,d.state=d.__s,t.__v!==n.__v&&(d.__d=!1),d.__v=t,t.__e=n.__e,t.__k=n.__k,t.__k.forEach(function(e){e&&(e.__=t)}),d.__h.length&&a.push(d);break n}null!=d.componentWillUpdate&&d.componentWillUpdate(C,d.__s,R),null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(f,g,E)})}d.context=R,d.props=C,d.state=d.__s,(c=u.__r)&&c(t),d.__d=!1,d.__v=t,d.__P=e,c=d.render(d.props,d.state,d.context),d.state=d.__s,null!=d.getChildContext&&(r=m(m({},r),d.getChildContext())),p||null==d.getSnapshotBeforeUpdate||(E=d.getSnapshotBeforeUpdate(f,g)),w=null!=c&&c.type===D&&null==c.key?c.props.children:c,T(e,Array.isArray(w)?w:[w],t,n,r,o,i,a,s,l),d.base=t.__e,t.__h=null,d.__h.length&&a.push(d),S&&(d.__E=d.__=null),d.__e=!1}else null==i&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=function e(t,n,r,o,i,a,s,l){var u,c,d,p,f=r.props,g=n.props,m=n.type,E=0;if("svg"===m&&(i=!0),null!=a){for(;E<a.length;E++)if((u=a[E])&&(u===t||(m?u.localName==m:3==u.nodeType))){t=u,a[E]=null;break}}if(null==t){if(null===m)return document.createTextNode(g);t=i?document.createElementNS("http://www.w3.org/2000/svg",m):document.createElement(m,g.is&&g),a=null,l=!1}if(null===m)f===g||l&&t.data===g||(t.data=g);else{if(a=a&&v.slice.call(t.childNodes),c=(f=r.props||h).dangerouslySetInnerHTML,d=g.dangerouslySetInnerHTML,!l){if(null!=a)for(f={},p=0;p<t.attributes.length;p++)f[t.attributes[p].name]=t.attributes[p].value;(d||c)&&(d&&(c&&d.__html==c.__html||d.__html===t.innerHTML)||(t.innerHTML=d&&d.__html||""))}if(function e(t,n,r,o,i){var a;for(a in r)"children"===a||"key"===a||a in n||I(t,a,null,r[a],o);for(a in n)i&&"function"!=typeof n[a]||"children"===a||"key"===a||"value"===a||"checked"===a||r[a]===n[a]||I(t,a,n[a],r[a],o)}(t,g,f,i,l),d)n.__k=[];else if(T(t,Array.isArray(E=n.props.children)?E:[E],n,r,o,i&&"foreignObject"!==m,a,s,t.firstChild,l),null!=a)for(E=a.length;E--;)null!=a[E]&&y(a[E]);l||("value"in g&&void 0!==(E=g.value)&&(E!==t.value||"progress"===m&&!E)&&I(t,"value",E,f.value,!1),"checked"in g&&void 0!==(E=g.checked)&&E!==t.checked&&I(t,"checked",E,f.checked,!1))}return t}(n.__e,t,n,r,o,i,a,l);(c=u.diffed)&&c(t)}catch(x){t.__v=null,(l||null!=i)&&(t.__e=s,t.__h=!!l,i[i.indexOf(s)]=null),u.__e(x,t,n)}}function O(e,t){u.__c&&u.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(n){u.__e(n,t.__v)}})}function A(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(r){u.__e(r,n)}}function L(e,t,n){var r,o,i;if(u.unmount&&u.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||A(r,null,t)),n||"function"==typeof e.type||(n=null!=(o=e.__e)),e.__e=e.__d=void 0,null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(a){u.__e(a,t)}r.base=r.__P=null}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&L(r[i],t,n);null!=o&&y(o)}function U(e,t,n){return this.constructor(e,n)}function W(e,t,n){var r,o,i;u.__&&u.__(e,t),o=(r="function"==typeof n)?null:n&&n.__k||t.__k,i=[],H(t,e=(!r&&n||t).__k=E(D,null,[e]),o||h,h,void 0!==t.ownerSVGElement,!r&&n?[n]:o?null:t.firstChild?v.slice.call(t.childNodes):null,i,!r&&n?n:o?o.__e:t.firstChild,r),O(i,e)}u={__e:function(e,t){for(var n,r,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((r=n.constructor)&&null!=r.getDerivedStateFromError&&(n.setState(r.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e),o=n.__d),o)return n.__E=n}catch(i){e=i}throw e},__v:0},b.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=m({},this.state),"function"==typeof e&&(e=e(m({},n),this.props)),e&&m(n,e),null!=e&&this.__v&&(t&&this.__h.push(t),R(this))},b.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),R(this))},b.prototype.render=D,c=[],d="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,w.__r=0,f=0;var V,z=[],F=u.__b,B=u.__r,G=u.diffed,q=u.__c,j=u.unmount;function Y(){z.forEach(function(e){if(e.__P)try{e.__H.__h.forEach(X),e.__H.__h.forEach(K),e.__H.__h=[]}catch(t){e.__H.__h=[],u.__e(t,e.__v)}}),z=[]}u.__b=function(e){F&&F(e)},u.__r=function(e){B&&B(e);var t=e.__c.__H;t&&(t.__h.forEach(X),t.__h.forEach(K),t.__h=[])},u.diffed=function(e){G&&G(e);var t=e.__c;t&&t.__H&&t.__H.__h.length&&(1!==z.push(t)&&V===u.requestAnimationFrame||((V=u.requestAnimationFrame)||function(e){var t,n=function(){clearTimeout(r),Z&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);Z&&(t=requestAnimationFrame(n))})(Y))},u.__c=function(e,t){t.some(function(e){try{e.__h.forEach(X),e.__h=e.__h.filter(function(e){return!e.__||K(e)})}catch(n){t.some(function(e){e.__h&&(e.__h=[])}),t=[],u.__e(n,e.__v)}}),q&&q(e,t)},u.unmount=function(e){j&&j(e);var t=e.__c;if(t&&t.__H)try{t.__H.__.forEach(X)}catch(n){u.__e(n,t.__v)}};var Z="function"==typeof requestAnimationFrame;function X(e){"function"==typeof e.__c&&e.__c()}function K(e){e.__c=e.__()}function Q(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function J(e){this.props=e}(J.prototype=new b).isPureReactComponent=!0,J.prototype.shouldComponentUpdate=function(e,t){return Q(this.props,e)||Q(this.state,t)};var ee=u.__b;u.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),ee&&ee(e)};var et=u.__e;u.__e=function(e,t,n){if(e.then){for(var r,o=t;o=o.__;)if((r=o.__c)&&r.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),r.__c(e,t)}et(e,t,n)};var en=u.unmount;function er(){this.__u=0,this.t=null,this.__b=null}function eo(e){var t=e.__.__c;return t&&t.__e&&t.__e(e)}function ei(){this.u=null,this.o=null}u.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&!0===e.__h&&(e.type=null),en&&en(e)},(er.prototype=new b).__c=function(e,t){var n=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(n);var o=eo(r.__v),i=!1,a=function(){i||(i=!0,n.__R=null,o?o(s):s())};n.__R=a;var s=function(){if(!--r.__u){if(r.state.__e){var e,t=r.state.__e;r.__v.__k[0]=function e(t,n,r){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(t){return e(t,n,r)}),t.__c&&t.__c.__P===n&&(t.__e&&r.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=r)),t}(t,t.__c.__P,t.__c.__O)}for(r.setState({__e:r.__b=null});e=r.t.pop();)e.forceUpdate()}},l=!0===t.__h;r.__u++||l||r.setState({__e:r.__b=r.__v.__k[0]}),e.then(a,a)},er.prototype.componentWillUnmount=function(){this.t=[]},er.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=function e(t,n,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(e){"function"==typeof e.__c&&e.__c()}),t.__c.__H=null),null!=(t=function e(t,n){for(var r in n)t[r]=n[r];return t}({},t)).__c&&(t.__c.__P===r&&(t.__c.__P=n),t.__c=null),t.__k=t.__k&&t.__k.map(function(t){return e(t,n,r)})),t}(this.__b,n,r.__O=r.__P)}this.__b=null}var o=t.__e&&E(D,null,e.fallback);return o&&(o.__h=null),[E(D,null,t.__e?null:e.children),o]};var ea=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};function es(e){return this.getChildContext=function(){return e.context},e.children}function el(e){var t=this,n=e.i;t.componentWillUnmount=function(){W(null,t.l),t.l=null,t.i=null},t.i&&t.i!==n&&t.componentWillUnmount(),e.__v?(t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),W(E(es,{context:t.context},e.__v),t.l)):t.l&&t.componentWillUnmount()}(ei.prototype=new b).__e=function(e){var t=this,n=eo(t.__v),r=t.o.get(e);return r[0]++,function(o){var i=function(){t.props.revealOrder?(r.push(o),ea(t,e,r)):o()};n?n(i):i()}},ei.prototype.render=function(e){this.u=null,this.o=new Map;var t=x(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},ei.prototype.componentDidUpdate=ei.prototype.componentDidMount=function(){var e=this;this.o.forEach(function(t,n){ea(e,n,t)})};var eu="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,ec=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;b.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(b.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})});var ed=u.event;function ep(){}function ef(){return this.cancelBubble}function eh(){return this.defaultPrevented}u.event=function(e){return ed&&(e=ed(e)),e.persist=ep,e.isPropagationStopped=ef,e.isDefaultPrevented=eh,e.nativeEvent=e};var ev={configurable:!0,get:function(){return this.class}},eg=u.vnode;u.vnode=function(e){var t=e.type,n=e.props,r=n;if("string"==typeof t){for(var o in r={},n){var i,a=n[o];"value"===o&&"defaultValue"in n&&null==a||("defaultValue"===o&&"value"in n&&null==n.value?o="value":"download"===o&&!0===a?a="":/ondoubleclick/i.test(o)?o="ondblclick":/^onchange(textarea|input)/i.test(o+t)&&(i=n.type,!("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(i))?o="oninput":/^on(Ani|Tra|Tou|BeforeInp)/.test(o)?o=o.toLowerCase():ec.test(o)?o=o.replace(/[A-Z0-9]/,"-$&").toLowerCase():null===a&&(a=void 0),r[o]=a)}"select"==t&&r.multiple&&Array.isArray(r.value)&&(r.value=x(n.children).forEach(function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)})),"select"==t&&null!=r.defaultValue&&(r.value=x(n.children).forEach(function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value})),e.props=r}t&&n.class!=n.className&&(ev.enumerable="className"in n,null!=n.className&&(r.class=n.className),Object.defineProperty(r,"className",ev)),e.$$typeof=eu,eg&&eg(e)};var em=u.__r;u.__r=function(e){em&&em(e)},"object"==typeof performance&&"function"==typeof performance.now&&performance.now.bind(performance);var ey="undefined"!=typeof globalThis?globalThis:window;function eE(e){e();var t=u.debounceRendering,n=[];for(u.debounceRendering=function e(t){n.push(t)},W(E(eS,{}),document.createElement("div"));n.length;)n.shift()();u.debounceRendering=t}ey.FullCalendarVDom?console.warn("FullCalendar VDOM already loaded"):ey.FullCalendarVDom={Component:b,createElement:E,render:W,createRef:function e(){return{current:null}},Fragment:D,createContext:function e(t){var n,r,o,i=(n=t,(o={__c:r="__cC"+f++,__:n,Consumer:function(e,t){return e.children(t)},Provider:function(e){var t,n;return this.getChildContext||(t=[],(n={})[r]=this,this.getChildContext=function(){return n},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&t.some(R)},this.sub=function(e){t.push(e);var n=e.componentWillUnmount;e.componentWillUnmount=function(){t.splice(t.indexOf(e),1),n&&n.call(e)}}),e.children}}).Provider.__=o.Consumer.contextType=o),a=i.Provider;return i.Provider=function(){var e=this,t=!this.getChildContext,n=a.apply(this,arguments);if(t){var r=[];this.shouldComponentUpdate=function(t){e.props.value!==t.value&&r.forEach(function(e){e.context=t.value,e.forceUpdate()})},this.sub=function(e){r.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){r.splice(r.indexOf(e),1),t&&t.call(e)}}}return n},i},createPortal:function e(t,n){return E(el,{__v:t,i:n})},flushSync:eE,unmountComponentAtNode:function e(t){W(null,t)}};var eS=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){return E("div",{})},t.prototype.componentDidMount=function(){this.setState({})},t}(b),eD=function(){function e(e,t){this.context=e,this.internalEventSource=t}return e.prototype.remove=function(){this.context.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})},e.prototype.refetch=function(){this.context.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId],isRefetch:!0})},Object.defineProperty(e.prototype,"id",{get:function(){return this.internalEventSource.publicId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"url",{get:function(){return this.internalEventSource.meta.url},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"format",{get:function(){return this.internalEventSource.meta.format},enumerable:!1,configurable:!0}),e}();function eb(e){e.parentNode&&e.parentNode.removeChild(e)}function eC(e,t){if(e.closest)return e.closest(t);if(!document.documentElement.contains(e))return null;do{if(e$(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}function e$(e,t){return(e.matches||e.matchesSelector||e.msMatchesSelector).call(e,t)}function eR(e,t){for(var n=e instanceof HTMLElement?[e]:e,r=[],o=0;o<n.length;o+=1)for(var i=n[o].querySelectorAll(t),a=0;a<i.length;a+=1)r.push(i[a]);return r}var e8=/(top|left|right|bottom|width|height)$/i;function ew(e,t){for(var n in t)eT(e,n,t[n])}function eT(e,t,n){null==n?e.style[t]="":"number"==typeof n&&e8.test(t)?e.style[t]=n+"px":e.style[t]=n}function ek(e){var t,n;return null!==(n=null===(t=e.composedPath)||void 0===t?void 0:t.call(e)[0])&&void 0!==n?n:e.target}function ex(e){return e.getRootNode?e.getRootNode():document}var eM=0;function e_(){return"fc-dom-"+(eM+=1)}function eI(e){e.preventDefault()}function eP(e,t,n,r){var o,i,a=(o=n,i=r,function(e){var t=eC(e.target,o);t&&i.call(t,e,t)});return e.addEventListener(t,a),function(){e.removeEventListener(t,a)}}var eN=["webkitTransitionEnd","otransitionend","oTransitionEnd","msTransitionEnd","transitionend",];function eH(e,t){var n=function(r){t(r),eN.forEach(function(t){e.removeEventListener(t,n)})};eN.forEach(function(t){e.addEventListener(t,n)})}function eO(e){return s({onClick:e},eA(e))}function eA(e){return{tabIndex:0,onKeyDown:function(t){("Enter"===t.key||" "===t.key)&&(e(t),t.preventDefault())}}}var eL=0;function eU(){return String(eL+=1)}function eW(){document.body.classList.add("fc-not-allowed")}function eV(){document.body.classList.remove("fc-not-allowed")}function ez(e){e.classList.add("fc-unselectable"),e.addEventListener("selectstart",eI)}function eF(e){e.classList.remove("fc-unselectable"),e.removeEventListener("selectstart",eI)}function eB(e){e.addEventListener("contextmenu",eI)}function eG(e){e.removeEventListener("contextmenu",eI)}function eq(e){var t,n,r=[],o=[];for("string"==typeof e?o=e.split(/\s*,\s*/):"function"==typeof e?o=[e]:Array.isArray(e)&&(o=e),t=0;t<o.length;t+=1)"string"==typeof(n=o[t])?r.push("-"===n.charAt(0)?{field:n.substring(1),order:-1}:{field:n,order:1}):"function"==typeof n&&r.push({func:n});return r}function ej(e,t,n){var r,o;for(r=0;r<n.length;r+=1)if(o=eY(e,t,n[r]))return o;return 0}function eY(e,t,n){return n.func?n.func(e,t):eZ(e[n.field],t[n.field])*(n.order||1)}function eZ(e,t){return e||t?null==t?-1:null==e?1:"string"==typeof e||"string"==typeof t?String(e).localeCompare(String(t)):e-t:0}function eX(e,t){var n=String(e);return"000".substr(0,t-n.length)+n}function e0(e,t,n){return"function"==typeof e?e.apply(void 0,t):"string"==typeof e?t.reduce(function(e,t,n){return e.replace("$"+n,t||"")},e):n}function e1(e,t){return e-t}function e4(e){return e%1==0}function eK(e){var t=e.querySelector(".fc-scrollgrid-shrink-frame"),n=e.querySelector(".fc-scrollgrid-shrink-cushion");if(!t)throw Error("needs fc-scrollgrid-shrink-frame className");if(!n)throw Error("needs fc-scrollgrid-shrink-cushion className");return e.getBoundingClientRect().width-t.getBoundingClientRect().width+n.getBoundingClientRect().width}var e3=["sun","mon","tue","wed","thu","fri","sat"];function e2(e,t){var n=ti(e);return n[2]+=7*t,ta(n)}function e5(e,t){var n=ti(e);return n[2]+=t,ta(n)}function e9(e,t){var n=ti(e);return n[6]+=t,ta(n)}function e7(e,t){return e6(e,t)/7}function e6(e,t){return(t.valueOf()-e.valueOf())/864e5}function eQ(e,t){var n=tt(e),r=tt(t);return{years:0,months:0,days:Math.round(e6(n,r)),milliseconds:t.valueOf()-r.valueOf()-(e.valueOf()-n.valueOf())}}function eJ(e,t){var n=te(e,t);return null!==n&&n%7==0?n/7:null}function te(e,t){return tl(e)===tl(t)?Math.round(e6(e,t)):null}function tt(e){return ta([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),])}function tn(e,t,n,r){var o,i,a,s,l=ta([t,0,1+(o=t,i=n,a=r,s=7+i-a,-((7+ta([o,0,s]).getUTCDay()-i)%7)+s-1)]),u=tt(e);return Math.floor(Math.round(e6(l,u))/7)+1}function tr(e){return[e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds(),]}function to(e){return new Date(e[0],e[1]||0,null==e[2]?1:e[2],e[3]||0,e[4]||0,e[5]||0)}function ti(e){return[e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds(),]}function ta(e){return 1===e.length&&(e=e.concat([0])),new Date(Date.UTC.apply(Date,e))}function ts(e){return!isNaN(e.valueOf())}function tl(e){return 36e5*e.getUTCHours()+6e4*e.getUTCMinutes()+1e3*e.getUTCSeconds()+e.getUTCMilliseconds()}function tu(e,t,n,r){return{instanceId:eU(),defId:e,range:t,forcedStartTzo:null==n?null:n,forcedEndTzo:null==r?null:r}}var tc=Object.prototype.hasOwnProperty;function td(e,t){var n={};if(t)for(var r in t){for(var o=[],i=e.length-1;i>=0;i-=1){var a=e[i][r];if("object"==typeof a&&a)o.unshift(a);else if(void 0!==a){n[r]=a;break}}o.length&&(n[r]=td(o))}for(var i=e.length-1;i>=0;i-=1){var s=e[i];for(var l in s)l in n||(n[l]=s[l])}return n}function tp(e,t){var n={};for(var r in e)t(e[r],r)&&(n[r]=e[r]);return n}function tf(e,t){var n={};for(var r in e)n[r]=t(e[r],r);return n}function th(e){for(var t={},n=0,r=e;n<r.length;n++)t[r[n]]=!0;return t}function tv(e){var t=[];for(var n in e)t.push(e[n]);return t}function tg(e,t){if(e===t)return!0;for(var n in e)if(tc.call(e,n)&&!(n in t))return!1;for(var n in t)if(tc.call(t,n)&&e[n]!==t[n])return!1;return!0}function tm(e,t){var n=[];for(var r in e)!tc.call(e,r)||r in t||n.push(r);for(var r in t)tc.call(t,r)&&e[r]!==t[r]&&n.push(r);return n}function ty(e,t,n){if(void 0===n&&(n={}),e===t)return!0;for(var r in t)if(!(r in e&&tE(e[r],t[r],n[r])))return!1;for(var r in e)if(!(r in t))return!1;return!0}function tE(e,t,n){return e===t||!0===n||!!n&&n(e,t)}function tS(e,t,n,r){void 0===t&&(t=0),void 0===r&&(r=1);var o=[];null==n&&(n=Object.keys(e).length);for(var i=t;i<n;i+=r){var a=e[i];void 0!==a&&o.push(a)}return o}function tD(e,t,n){var r=n.dateEnv,o=n.pluginHooks,i=n.options,a=e.defs,s=e.instances;for(var l in s=tp(s,function(e){return!a[e.defId].recurringDef}),a){var u=a[l];if(u.recurringDef){var c=u.recurringDef.duration;c||(c=u.allDay?i.defaultAllDayEventDuration:i.defaultTimedEventDuration);for(var d=tb(u,c,t,r,o.recurringTypes),p=0,f=d;p<f.length;p++){var h=f[p],v=tu(l,{start:h,end:r.add(h,c)});s[v.instanceId]=v}}}return{defs:a,instances:s}}function tb(e,t,n,r,o){var i=o[e.recurringDef.typeId].expand(e.recurringDef.typeData,{start:r.subtract(n.start,t),end:n.end},r);return e.allDay&&(i=i.map(tt)),i}var tC=["years","months","days","milliseconds"],t$=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function tR(e,t){var n;return"string"==typeof e?function e(t){var n=t$.exec(t);if(n){var r=n[1]?-1:1;return{years:0,months:0,days:r*(n[2]?parseInt(n[2],10):0),milliseconds:r*((n[3]?parseInt(n[3],10):0)*36e5+(n[4]?parseInt(n[4],10):0)*6e4+(n[5]?parseInt(n[5],10):0)*1e3+(n[6]?parseInt(n[6],10):0))}}return null}(e):"object"==typeof e&&e?t8(e):"number"==typeof e?t8(((n={})[t||"milliseconds"]=e,n)):null}function t8(e){var t={years:e.years||e.year||0,months:e.months||e.month||0,days:e.days||e.day||0,milliseconds:36e5*(e.hours||e.hour||0)+6e4*(e.minutes||e.minute||0)+1e3*(e.seconds||e.second||0)+(e.milliseconds||e.millisecond||e.ms||0)},n=e.weeks||e.week;return n&&(t.days+=7*n,t.specifiedWeeks=!0),t}function tw(e,t){return{years:e.years+t.years,months:e.months+t.months,days:e.days+t.days,milliseconds:e.milliseconds+t.milliseconds}}function tT(e,t){return{years:e.years*t,months:e.months*t,days:e.days*t,milliseconds:e.milliseconds*t}}function tk(e){return tx(e)/864e5}function tx(e){return e.years*(365*864e5)+e.months*(30*864e5)+864e5*e.days+e.milliseconds}function tM(e,t){for(var n=null,r=0;r<tC.length;r+=1){var o=tC[r];if(t[o]){var i=e[o]/t[o];if(!e4(i)||null!==n&&n!==i)return null;n=i}else if(e[o])return null}return n}function t_(e){var t=e.milliseconds;if(t){if(t%1e3!=0)return{unit:"millisecond",value:t};if(t%6e4!=0)return{unit:"second",value:t/1e3};if(t%36e5!=0)return{unit:"minute",value:t/6e4};if(t)return{unit:"hour",value:t/36e5}}return e.days?e.specifiedWeeks&&e.days%7==0?{unit:"week",value:e.days/7}:{unit:"day",value:e.days}:e.months?{unit:"month",value:e.months}:e.years?{unit:"year",value:e.years}:{unit:"millisecond",value:0}}function tI(e,t,n){void 0===n&&(n=!1);var r=e.toISOString();return r=r.replace(".000",""),n&&(r=r.replace("T00:00:00Z","")),r.length>10&&(null==t?r=r.replace("Z",""):0!==t&&(r=r.replace("Z",tH(t,!0)))),r}function tP(e){return e.toISOString().replace(/T.*$/,"")}function tN(e){return eX(e.getUTCHours(),2)+":"+eX(e.getUTCMinutes(),2)+":"+eX(e.getUTCSeconds(),2)}function tH(e,t){void 0===t&&(t=!1);var n=e<0?"-":"+",r=Math.abs(e),o=Math.floor(r/60),i=Math.round(r%60);return t?n+eX(o,2)+":"+eX(i,2):"GMT"+n+o+(i?":"+eX(i,2):"")}function tO(e,t,n){if(e===t)return!0;var r,o=e.length;if(o!==t.length)return!1;for(r=0;r<o;r+=1)if(!(n?n(e[r],t[r]):e[r]===t[r]))return!1;return!0}function tA(e,t,n){var r,o;return function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];if(r){if(!tO(r,i)){n&&n(o);var s=e.apply(this,i);t&&t(s,o)||(o=s)}}else o=e.apply(this,i);return r=i,o}}function tL(e,t,n){var r,o,i=this;return function(a){if(r){if(!tg(r,a)){n&&n(o);var s=e.call(i,a);t&&t(s,o)||(o=s)}}else o=e.call(i,a);return r=a,o}}var tU={week:3,separator:0,omitZeroMinute:0,meridiem:0,omitCommas:0},tW={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},tV=/\s*([ap])\.?m\.?/i,tz=/,/g,tF=/\s+/g,tB=/\u200e/g,tG=/UTC|GMT/,tq=function(){function e(e){var t={},n={},r=0;for(var o in e)o in tU?(n[o]=e[o],r=Math.max(tU[o],r)):(t[o]=e[o],o in tW&&(r=Math.max(tW[o],r)));this.standardDateProps=t,this.extendedSettings=n,this.severity=r,this.buildFormattingFunc=tA(tj)}return e.prototype.format=function(e,t){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,t)(e)},e.prototype.formatRange=function(e,t,n,r){var o,i,a,s=this.standardDateProps,l=this.extendedSettings,u=(o=e.marker,i=t.marker,a=n.calendarSystem,a.getMarkerYear(o)!==a.getMarkerYear(i)?5:a.getMarkerMonth(o)!==a.getMarkerMonth(i)?4:a.getMarkerDay(o)!==a.getMarkerDay(i)?2:tl(o)!==tl(i)?1:0);if(!u)return this.format(e,n);var c=u;c>1&&("numeric"===s.year||"2-digit"===s.year)&&("numeric"===s.month||"2-digit"===s.month)&&("numeric"===s.day||"2-digit"===s.day)&&(c=1);var d=this.format(e,n),p=this.format(t,n);if(d===p)return d;var f=function e(t,n){var r={};for(var o in t)o in tW&&!(tW[o]<=n)||(r[o]=t[o]);return r}(s,c),h=tj(f,l,n),v=h(e),g=h(t),m=function e(t,n,r,o){for(var i=0;i<t.length;){var a=t.indexOf(n,i);if(-1===a)break;var s=t.substr(0,a);i=a+n.length;for(var l=t.substr(i),u=0;u<r.length;){var c=r.indexOf(o,u);if(-1===c)break;var d=r.substr(0,c);u=c+o.length;var p=r.substr(u);if(s===d&&l===p)return{before:s,after:l}}}return null}(d,v,p,g),y=l.separator||r||n.defaultSeparator||"";return m?m.before+v+y+g+m.after:d+y+p},e.prototype.getLargestUnit=function(){switch(this.severity){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";case 2:return"day";default:return"time"}},e}();function tj(e,t,n){var r=Object.keys(e).length;return 1===r&&"short"===e.timeZoneName?function(e){return tH(e.timeZoneOffset)}:0===r&&t.week?function(e){var r,o,i,a,s,l;return r=n.computeWeekNumber(e.marker),o=n.weekText,i=n.weekTextLong,a=n.locale,s=t.week,l=[],"long"===s?l.push(i):("short"===s||"narrow"===s)&&l.push(o),("long"===s||"short"===s)&&l.push(" "),l.push(a.simpleNumberFormat.format(r)),"rtl"===a.options.direction&&l.reverse(),l.join("")}:function e(t,n,r){t=s({},t),n=s({},n),o=t,i=n,o.timeZoneName&&(o.hour||(o.hour="2-digit"),o.minute||(o.minute="2-digit")),"long"===o.timeZoneName&&(o.timeZoneName="short"),i.omitZeroMinute&&(o.second||o.millisecond)&&delete i.omitZeroMinute,t.timeZone="UTC";var o,i,a,l=new Intl.DateTimeFormat(r.locale.codes,t);if(n.omitZeroMinute){var u=s({},t);delete u.minute,a=new Intl.DateTimeFormat(r.locale.codes,u)}return function(e){var o,i,s,u,c,d,p,f,h,v,g=e.marker;return s=(o=a&&!g.getUTCMinutes()?a:l).format(g),u=e,c=t,d=n,p=r,s=s.replace(tB,""),"short"===c.timeZoneName&&(s=(f=s,h="UTC"===p.timeZone||null==u.timeZoneOffset?"UTC":tH(u.timeZoneOffset),v=!1,f=f.replace(tG,function(){return v=!0,h}),v||(f+=" "+h),f)),d.omitCommas&&(s=s.replace(tz,"").trim()),d.omitZeroMinute&&(s=s.replace(":00","")),!1===d.meridiem?s=s.replace(tV,"").trim():"narrow"===d.meridiem?s=s.replace(tV,function(e,t){return t.toLocaleLowerCase()}):"short"===d.meridiem?s=s.replace(tV,function(e,t){return t.toLocaleLowerCase()+"m"}):"lowercase"===d.meridiem&&(s=s.replace(tV,function(e){return e.toLocaleLowerCase()})),s=(s=s.replace(tF," ")).trim()}}(e,t,n)}function tY(e,t){var n=t.markerToArray(e.marker);return{marker:e.marker,timeZoneOffset:e.timeZoneOffset,array:n,year:n[0],month:n[1],day:n[2],hour:n[3],minute:n[4],second:n[5],millisecond:n[6]}}function tZ(e,t,n,r){var o=tY(e,n.calendarSystem),i=t?tY(t,n.calendarSystem):null;return{date:o,start:o,end:i,timeZone:n.timeZone,localeCodes:n.locale.codes,defaultSeparator:r||n.defaultSeparator}}var tX=function(){function e(e){this.cmdStr=e}return e.prototype.format=function(e,t,n){return t.cmdFormatter(this.cmdStr,tZ(e,null,t,n))},e.prototype.formatRange=function(e,t,n,r){return n.cmdFormatter(this.cmdStr,tZ(e,t,n,r))},e}(),t0=function(){function e(e){this.func=e}return e.prototype.format=function(e,t,n){return this.func(tZ(e,null,t,n))},e.prototype.formatRange=function(e,t,n,r){return this.func(tZ(e,t,n,r))},e}();function t1(e){return"object"==typeof e&&e?new tq(e):"string"==typeof e?new tX(e):"function"==typeof e?new t0(e):null}var t4={navLinkDayClick:tJ,navLinkWeekClick:tJ,duration:tR,bootstrapFontAwesome:tJ,buttonIcons:tJ,customButtons:tJ,defaultAllDayEventDuration:tR,defaultTimedEventDuration:tR,nextDayThreshold:tR,scrollTime:tR,scrollTimeReset:Boolean,slotMinTime:tR,slotMaxTime:tR,dayPopoverFormat:t1,slotDuration:tR,snapDuration:tR,headerToolbar:tJ,footerToolbar:tJ,defaultRangeSeparator:String,titleRangeSeparator:String,forceEventDuration:Boolean,dayHeaders:Boolean,dayHeaderFormat:t1,dayHeaderClassNames:tJ,dayHeaderContent:tJ,dayHeaderDidMount:tJ,dayHeaderWillUnmount:tJ,dayCellClassNames:tJ,dayCellContent:tJ,dayCellDidMount:tJ,dayCellWillUnmount:tJ,initialView:String,aspectRatio:Number,weekends:Boolean,weekNumberCalculation:tJ,weekNumbers:Boolean,weekNumberClassNames:tJ,weekNumberContent:tJ,weekNumberDidMount:tJ,weekNumberWillUnmount:tJ,editable:Boolean,viewClassNames:tJ,viewDidMount:tJ,viewWillUnmount:tJ,nowIndicator:Boolean,nowIndicatorClassNames:tJ,nowIndicatorContent:tJ,nowIndicatorDidMount:tJ,nowIndicatorWillUnmount:tJ,showNonCurrentDates:Boolean,lazyFetching:Boolean,startParam:String,endParam:String,timeZoneParam:String,timeZone:String,locales:tJ,locale:tJ,themeSystem:String,dragRevertDuration:Number,dragScroll:Boolean,allDayMaintainDuration:Boolean,unselectAuto:Boolean,dropAccept:tJ,eventOrder:eq,eventOrderStrict:Boolean,handleWindowResize:Boolean,windowResizeDelay:Number,longPressDelay:Number,eventDragMinDistance:Number,expandRows:Boolean,height:tJ,contentHeight:tJ,direction:String,weekNumberFormat:t1,eventResizableFromStart:Boolean,displayEventTime:Boolean,displayEventEnd:Boolean,weekText:String,weekTextLong:String,progressiveEventRendering:Boolean,businessHours:tJ,initialDate:tJ,now:tJ,eventDataTransform:tJ,stickyHeaderDates:tJ,stickyFooterScrollbar:tJ,viewHeight:tJ,defaultAllDay:Boolean,eventSourceFailure:tJ,eventSourceSuccess:tJ,eventDisplay:String,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventOverlap:tJ,eventConstraint:tJ,eventAllow:tJ,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String,eventClassNames:tJ,eventContent:tJ,eventDidMount:tJ,eventWillUnmount:tJ,selectConstraint:tJ,selectOverlap:tJ,selectAllow:tJ,droppable:Boolean,unselectCancel:String,slotLabelFormat:tJ,slotLaneClassNames:tJ,slotLaneContent:tJ,slotLaneDidMount:tJ,slotLaneWillUnmount:tJ,slotLabelClassNames:tJ,slotLabelContent:tJ,slotLabelDidMount:tJ,slotLabelWillUnmount:tJ,dayMaxEvents:tJ,dayMaxEventRows:tJ,dayMinWidth:Number,slotLabelInterval:tR,allDayText:String,allDayClassNames:tJ,allDayContent:tJ,allDayDidMount:tJ,allDayWillUnmount:tJ,slotMinWidth:Number,navLinks:Boolean,eventTimeFormat:t1,rerenderDelay:Number,moreLinkText:tJ,moreLinkHint:tJ,selectMinDistance:Number,selectable:Boolean,selectLongPressDelay:Number,eventLongPressDelay:Number,selectMirror:Boolean,eventMaxStack:Number,eventMinHeight:Number,eventMinWidth:Number,eventShortHeight:Number,slotEventOverlap:Boolean,plugins:tJ,firstDay:Number,dayCount:Number,dateAlignment:String,dateIncrement:tR,hiddenDays:tJ,monthMode:Boolean,fixedWeekCount:Boolean,validRange:tJ,visibleRange:tJ,titleFormat:tJ,eventInteractive:Boolean,noEventsText:String,viewHint:tJ,navLinkHint:tJ,closeHint:String,timeHint:String,eventHint:String,moreLinkClick:tJ,moreLinkClassNames:tJ,moreLinkContent:tJ,moreLinkDidMount:tJ,moreLinkWillUnmount:tJ},tK={eventDisplay:"auto",defaultRangeSeparator:" - ",titleRangeSeparator:" – ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",dayHeaders:!0,initialView:"",aspectRatio:1.35,headerToolbar:{start:"title",center:"",end:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,nowIndicator:!1,scrollTime:"06:00:00",scrollTimeReset:!0,slotMinTime:"00:00:00",slotMaxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5,expandRows:!1,navLinks:!1,selectable:!1,eventMinHeight:15,eventMinWidth:30,eventShortHeight:30},t3={datesSet:tJ,eventsSet:tJ,eventAdd:tJ,eventChange:tJ,eventRemove:tJ,windowResize:tJ,eventClick:tJ,eventMouseEnter:tJ,eventMouseLeave:tJ,select:tJ,unselect:tJ,loading:tJ,_unmount:tJ,_beforeprint:tJ,_afterprint:tJ,_noEventDrop:tJ,_noEventResize:tJ,_resize:tJ,_scrollRequest:tJ},t2={buttonText:tJ,buttonHints:tJ,views:tJ,plugins:tJ,initialEvents:tJ,events:tJ,eventSources:tJ},t5={headerToolbar:t9,footerToolbar:t9,buttonText:t9,buttonHints:t9,buttonIcons:t9,dateIncrement:t9};function t9(e,t){return"object"==typeof e&&"object"==typeof t&&e&&t?tg(e,t):e===t}var t7={type:String,component:tJ,buttonText:String,buttonTextKey:String,dateProfileGeneratorClass:tJ,usesMinMaxTime:Boolean,classNames:tJ,content:tJ,didMount:tJ,willUnmount:tJ};function t6(e){return td(e,t5)}function tQ(e,t){var n={},r={};for(var o in t)o in e&&(n[o]=t[o](e[o]));for(var o in e)o in t||(r[o]=e[o]);return{refined:n,extra:r}}function tJ(e){return e}function ne(e,t,n,r){for(var o=nr(),i=nm(n),a=0,s=e;a<s.length;a++){var l=nv(s[a],t,n,r,i);l&&nt(l,o)}return o}function nt(e,t){return void 0===t&&(t=nr()),t.defs[e.def.defId]=e.def,e.instance&&(t.instances[e.instance.instanceId]=e.instance),t}function nn(e,t){var n=e.instances[t];if(n){var r=e.defs[n.defId],o=ni(e,function(e){var t,n;return t=r,n=e,Boolean(t.groupId&&t.groupId===n.groupId)});return o.defs[r.defId]=r,o.instances[n.instanceId]=n,o}return nr()}function nr(){return{defs:{},instances:{}}}function no(e,t){return{defs:s(s({},e.defs),t.defs),instances:s(s({},e.instances),t.instances)}}function ni(e,t){var n=tp(e.defs,t),r=tp(e.instances,function(e){return n[e.defId]});return{defs:n,instances:r}}function na(e){return Array.isArray(e)?e:"string"==typeof e?e.split(/\s+/):[]}var ns={display:String,editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:tJ,overlap:tJ,allow:tJ,className:na,classNames:na,color:String,backgroundColor:String,borderColor:String,textColor:String},nl={display:null,startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function nu(e,t){var n,r,o=(n=e.constraint,r=t,Array.isArray(n)?ne(n,null,r,!0):"object"==typeof n&&n?ne([n],null,r,!0):null!=n?String(n):null);return{display:e.display||null,startEditable:null!=e.startEditable?e.startEditable:e.editable,durationEditable:null!=e.durationEditable?e.durationEditable:e.editable,constraints:null!=o?[o]:[],overlap:null!=e.overlap?e.overlap:null,allows:null!=e.allow?[e.allow]:[],backgroundColor:e.backgroundColor||e.color||"",borderColor:e.borderColor||e.color||"",textColor:e.textColor||"",classNames:(e.className||[]).concat(e.classNames||[])}}function nc(e){return e.reduce(nd,nl)}function nd(e,t){return{display:null!=t.display?t.display:e.display,startEditable:null!=t.startEditable?t.startEditable:e.startEditable,durationEditable:null!=t.durationEditable?t.durationEditable:e.durationEditable,constraints:e.constraints.concat(t.constraints),overlap:"boolean"==typeof t.overlap?t.overlap:e.overlap,allows:e.allows.concat(t.allows),backgroundColor:t.backgroundColor||e.backgroundColor,borderColor:t.borderColor||e.borderColor,textColor:t.textColor||e.textColor,classNames:e.classNames.concat(t.classNames)}}var np={id:String,groupId:String,title:String,url:String,interactive:Boolean},nf={start:tJ,end:tJ,date:tJ,allDay:Boolean},nh=s(s(s({},np),nf),{extendedProps:tJ});function nv(e,t,n,r,o){void 0===o&&(o=nm(n));var i,a,s,l=ng(e,n,o),u=l.refined,c=l.extra,d=(i=t,a=n,s=null,i&&(s=i.defaultAllDay),null==s&&(s=a.options.defaultAllDay),s),p=function e(t,n,r,o){for(var i=0;i<o.length;i+=1){var a=o[i].parse(t,r);if(a){var s=t.allDay;return null==s&&null==(s=n)&&null==(s=a.allDayGuess)&&(s=!1),{allDay:s,duration:a.duration,typeData:a.typeData,typeId:i}}}return null}(u,d,n.dateEnv,n.pluginHooks.recurringTypes);if(p){var f=ny(u,c,t?t.sourceId:"",p.allDay,Boolean(p.duration),n);return f.recurringDef={typeId:p.typeId,typeData:p.typeData,duration:p.duration},{def:f,instance:null}}var h=function e(t,n,r,o){var i,a,s=t.allDay,l=null,u=!1,c=null,d=null!=t.start?t.start:t.date;if(i=r.dateEnv.createMarkerMeta(d))l=i.marker;else if(!o)return null;return null!=t.end&&(a=r.dateEnv.createMarkerMeta(t.end)),null==s&&(s=null!=n?n:(!i||i.isTimeUnspecified)&&(!a||a.isTimeUnspecified)),s&&l&&(l=tt(l)),a&&(c=a.marker,s&&(c=tt(c)),l&&c<=l&&(c=null)),c?u=!0:o||(u=r.options.forceEventDuration||!1,c=r.dateEnv.add(l,s?r.options.defaultAllDayEventDuration:r.options.defaultTimedEventDuration)),{allDay:s,hasEnd:u,range:{start:l,end:c},forcedStartTzo:i?i.forcedTzo:null,forcedEndTzo:a?a.forcedTzo:null}}(u,d,n,r);if(h){var f=ny(u,c,t?t.sourceId:"",h.allDay,h.hasEnd,n),v=tu(f.defId,h.range,h.forcedStartTzo,h.forcedEndTzo);return{def:f,instance:v}}return null}function ng(e,t,n){return void 0===n&&(n=nm(t)),tQ(e,n)}function nm(e){return s(s(s({},ns),nh),e.pluginHooks.eventRefiners)}function ny(e,t,n,r,o,i){for(var a={title:e.title||"",groupId:e.groupId||"",publicId:e.id||"",url:e.url||"",recurringDef:null,defId:eU(),sourceId:n,allDay:r,hasEnd:o,interactive:e.interactive,ui:nu(e,i),extendedProps:s(s({},e.extendedProps||{}),t)},l=0,u=i.pluginHooks.eventDefMemberAdders;l<u.length;l++)s(a,(0,u[l])(e));return Object.freeze(a.ui.classNames),Object.freeze(a.extendedProps),a}function nE(e){var t=Math.floor(e6(e.start,e.end))||1,n=tt(e.start),r=e5(n,t);return{start:n,end:r}}function nS(e,t){void 0===t&&(t=tR(0));var n=null,r=null;if(e.end){r=tt(e.end);var o=e.end.valueOf()-r.valueOf();o&&o>=tx(t)&&(r=e5(r,1))}return e.start&&(n=tt(e.start),r&&r<=n&&(r=e5(n,1))),{start:n,end:r}}function nD(e){var t=nS(e);return e6(t.start,t.end)>1}function nb(e,t,n,r){return"year"===r?tR(n.diffWholeYears(e,t),"year"):"month"===r?tR(n.diffWholeMonths(e,t),"month"):eQ(e,t)}function nC(e,t){var n,r,o=[],i=t.start;for(e.sort(n$),n=0;n<e.length;n+=1)(r=e[n]).start>i&&o.push({start:i,end:r.start}),r.end>i&&(i=r.end);return i<t.end&&o.push({start:i,end:t.end}),o}function n$(e,t){return e.start.valueOf()-t.start.valueOf()}function nR(e,t){var n=e.start,r=e.end,o=null;return null!==t.start&&(n=null===n?t.start:new Date(Math.max(n.valueOf(),t.start.valueOf()))),null!=t.end&&(r=null===r?t.end:new Date(Math.min(r.valueOf(),t.end.valueOf()))),(null===n||null===r||n<r)&&(o={start:n,end:r}),o}function n8(e,t){return(null===e.start?null:e.start.valueOf())===(null===t.start?null:t.start.valueOf())&&(null===e.end?null:e.end.valueOf())===(null===t.end?null:t.end.valueOf())}function nw(e,t){return(null===e.end||null===t.start||e.end>t.start)&&(null===e.start||null===t.end||e.start<t.end)}function nT(e,t){return(null===e.start||null!==t.start&&t.start>=e.start)&&(null===e.end||null!==t.end&&t.end<=e.end)}function nk(e,t){return(null===e.start||t>=e.start)&&(null===e.end||t<e.end)}function nx(e,t,n,r){var o={},i={},a={},s=[],l=[],u=nP(e.defs,t);for(var c in e.defs){var d=e.defs[c],p=u[d.defId];"inverse-background"!==p.display||(d.groupId?(o[d.groupId]=[],a[d.groupId]||(a[d.groupId]=d)):i[c]=[])}for(var f in e.instances){var h=e.instances[f],d=e.defs[h.defId],p=u[d.defId],v=h.range,g=!d.allDay&&r?nS(v,r):v,m=nR(g,n);m&&("inverse-background"===p.display?d.groupId?o[d.groupId].push(m):i[h.defId].push(m):"none"!==p.display&&("background"===p.display?s:l).push({def:d,ui:p,instance:h,range:m,isStart:g.start&&g.start.valueOf()===m.start.valueOf(),isEnd:g.end&&g.end.valueOf()===m.end.valueOf()}))}for(var y in o)for(var E=o[y],S=nC(E,n),D=0,b=S;D<b.length;D++){var C=b[D],d=a[y],p=u[d.defId];s.push({def:d,ui:p,instance:null,range:C,isStart:!1,isEnd:!1})}for(var c in i)for(var E=i[c],S=nC(E,n),$=0,R=S;$<R.length;$++){var C=R[$];s.push({def:e.defs[c],ui:u[c],instance:null,range:C,isStart:!1,isEnd:!1})}return{bg:s,fg:l}}function nM(e){return"background"===e.ui.display||"inverse-background"===e.ui.display}function n_(e,t){e.fcSeg=t}function nI(e){return e.fcSeg||e.parentNode.fcSeg||null}function nP(e,t){return tf(e,function(e){return nN(e,t)})}function nN(e,t){var n=[];return t[""]&&n.push(t[""]),t[e.defId]&&n.push(t[e.defId]),n.push(e.ui),nc(n)}function nH(e,t){var n=e.map(nO);return n.sort(function(e,n){return ej(e,n,t)}),n.map(function(e){return e._seg})}function nO(e){var t=e.eventRange,n=t.def,r=t.instance?t.instance.range:t.range,o=r.start?r.start.valueOf():0,i=r.end?r.end.valueOf():0;return s(s(s({},n.extendedProps),n),{id:n.publicId,start:o,end:i,duration:i-o,allDay:Number(n.allDay),_seg:e})}function nA(e,t){for(var n=t.pluginHooks.isDraggableTransformers,r=e.eventRange,o=r.def,i=r.ui,a=i.startEditable,s=0,l=n;s<l.length;s++)a=(0,l[s])(a,o,i,t);return a}function nL(e,t){return e.isStart&&e.eventRange.ui.durationEditable&&t.options.eventResizableFromStart}function nU(e,t){return e.isEnd&&e.eventRange.ui.durationEditable}function nW(e,t,n,r,o,i,a){var s=n.dateEnv,l=n.options,u=l.displayEventTime,c=l.displayEventEnd,d=e.eventRange.def,p=e.eventRange.instance;null==u&&(u=!1!==r),null==c&&(c=!1!==o);var f=p.range.start,h=p.range.end,v=i||e.start||e.eventRange.range.start,g=a||e.end||e.eventRange.range.end,m=tt(f).valueOf()===tt(v).valueOf(),y=tt(e9(h,-1)).valueOf()===tt(e9(g,-1)).valueOf();return u&&!d.allDay&&(m||y)?(v=m?f:v,g=y?h:g,c&&d.hasEnd)?s.formatRange(v,g,t,{forcedStartTzo:i?null:p.forcedStartTzo,forcedEndTzo:a?null:p.forcedEndTzo}):s.format(v,t,{forcedTzo:i?null:p.forcedStartTzo}):""}function nV(e,t,n){var r=e.eventRange.range;return{isPast:r.end<(n||t.start),isFuture:r.start>=(n||t.end),isToday:t&&nk(t,r.start)}}function nz(e){var t=["fc-event"];return e.isMirror&&t.push("fc-event-mirror"),e.isDraggable&&t.push("fc-event-draggable"),(e.isStartResizable||e.isEndResizable)&&t.push("fc-event-resizable"),e.isDragging&&t.push("fc-event-dragging"),e.isResizing&&t.push("fc-event-resizing"),e.isSelected&&t.push("fc-event-selected"),e.isStart&&t.push("fc-event-start"),e.isEnd&&t.push("fc-event-end"),e.isPast&&t.push("fc-event-past"),e.isToday&&t.push("fc-event-today"),e.isFuture&&t.push("fc-event-future"),t}function nF(e){return e.instance?e.instance.instanceId:e.def.defId+":"+e.range.start.toISOString()}function nB(e,t){var n=e.eventRange,r=n.def,o=n.instance,i=r.url;if(i)return{href:i};var a=t.emitter,s=t.options.eventInteractive;return(null==s&&null==(s=r.interactive)&&(s=Boolean(a.hasHandlers("eventClick"))),s)?eA(function(e){a.trigger("eventClick",{el:e.target,event:new nQ(t,r,o),jsEvent:e,view:t.viewApi})}):{}}var nG={start:tJ,end:tJ,allDay:Boolean};function nq(e,t){return n8(e.range,t.range)&&e.allDay===t.allDay&&function e(t,n){for(var r in n)if("range"!==r&&"allDay"!==r&&t[r]!==n[r])return!1;for(var r in t)if(!(r in n))return!1;return!0}(e,t)}function nj(e,t,n){return s(s({},nY(e,t,n)),{timeZone:t.timeZone})}function nY(e,t,n){return{start:t.toDate(e.start),end:t.toDate(e.end),startStr:t.formatIso(e.start,{omitTime:n}),endStr:t.formatIso(e.end,{omitTime:n})}}function nZ(e,t,n){n.emitter.trigger("select",s(s({},nX(e,n)),{jsEvent:t?t.origEvent:null,view:n.viewApi||n.calendarApi.view}))}function nX(e,t){for(var n,r,o={},i=0,a=t.pluginHooks.dateSpanTransforms;i<a.length;i++)s(o,(0,a[i])(e,t));return s(o,(n=e,r=t.dateEnv,s(s({},nY(n.range,r,n.allDay)),{allDay:n.allDay}))),o}function n0(e,t,n){var r=n.dateEnv,o=n.options,i=t;return e?(i=tt(i),i=r.add(i,o.defaultAllDayEventDuration)):i=r.add(i,o.defaultTimedEventDuration),i}function n1(e,t,n,r){var o=nP(e.defs,t),i=nr();for(var a in e.defs){var s=e.defs[a];i.defs[a]=n4(s,o[a],n,r)}for(var l in e.instances){var u=e.instances[l],s=i.defs[u.defId];i.instances[l]=nK(u,s,o[u.defId],n,r)}return i}function n4(e,t,n,r){var o=n.standardProps||{};null==o.hasEnd&&t.durationEditable&&(n.startDelta||n.endDelta)&&(o.hasEnd=!0);var i=s(s(s({},e),o),{ui:s(s({},e.ui),o.ui)});n.extendedProps&&(i.extendedProps=s(s({},i.extendedProps),n.extendedProps));for(var a=0,l=r.pluginHooks.eventDefMutationAppliers;a<l.length;a++)(0,l[a])(i,n,r);return!i.hasEnd&&r.options.forceEventDuration&&(i.hasEnd=!0),i}function nK(e,t,n,r,o){var i=o.dateEnv,a=r.standardProps&&!0===r.standardProps.allDay,l=r.standardProps&&!1===r.standardProps.hasEnd,u=s({},e);return a&&(u.range=nE(u.range)),r.datesDelta&&n.startEditable&&(u.range={start:i.add(u.range.start,r.datesDelta),end:i.add(u.range.end,r.datesDelta)}),r.startDelta&&n.durationEditable&&(u.range={start:i.add(u.range.start,r.startDelta),end:u.range.end}),r.endDelta&&n.durationEditable&&(u.range={start:u.range.start,end:i.add(u.range.end,r.endDelta)}),l&&(u.range={start:u.range.start,end:n0(t.allDay,u.range.start,o)}),t.allDay&&(u.range={start:tt(u.range.start),end:tt(u.range.end)}),u.range.end<u.range.start&&(u.range.end=n0(t.allDay,u.range.start,o)),u}var n3=function(){function e(e,t,n){this.type=e,this.getCurrentData=t,this.dateEnv=n}return Object.defineProperty(e.prototype,"calendar",{get:function(){return this.getCurrentData().calendarApi},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this.getCurrentData().viewTitle},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"activeStart",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"activeEnd",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentStart",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentEnd",{get:function(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end)},enumerable:!1,configurable:!0}),e.prototype.getOption=function(e){return this.getCurrentData().options[e]},e}(),n2={id:String,defaultAllDay:Boolean,url:String,format:String,events:tJ,eventDataTransform:tJ,success:tJ,failure:tJ};function n5(e,t,n){if(void 0===n&&(n=n9(t)),"string"==typeof e?r={url:e}:"function"==typeof e||Array.isArray(e)?r={events:e}:"object"==typeof e&&e&&(r=e),r){var r,o=tQ(r,n),i=o.refined,a=o.extra,s=function e(t,n){for(var r=n.pluginHooks.eventSourceDefs,o=r.length-1;o>=0;o-=1){var i=r[o].parseMeta(t);if(i)return{sourceDefId:o,meta:i}}return null}(i,t);if(s)return{_raw:e,isFetching:!1,latestFetchId:"",fetchRange:null,defaultAllDay:i.defaultAllDay,eventDataTransform:i.eventDataTransform,success:i.success,failure:i.failure,publicId:i.id||"",sourceId:eU(),sourceDefId:s.sourceDefId,meta:s.meta,ui:nu(i,t),extendedProps:a}}return null}function n9(e){return s(s(s({},ns),n2),e.pluginHooks.eventSourceRefiners)}function n7(e,t){return("function"==typeof e&&(e=e()),null==e)?t.createNowMarker():t.createMarker(e)}var n6=function(){function e(){}return e.prototype.getCurrentData=function(){return this.currentDataManager.getCurrentData()},e.prototype.dispatch=function(e){return this.currentDataManager.dispatch(e)},Object.defineProperty(e.prototype,"view",{get:function(){return this.getCurrentData().viewApi},enumerable:!1,configurable:!0}),e.prototype.batchRendering=function(e){e()},e.prototype.updateSize=function(){this.trigger("_resize",!0)},e.prototype.setOption=function(e,t){this.dispatch({type:"SET_OPTION",optionName:e,rawOptionValue:t})},e.prototype.getOption=function(e){return this.currentDataManager.currentCalendarOptionsInput[e]},e.prototype.getAvailableLocaleCodes=function(){return Object.keys(this.getCurrentData().availableRawLocales)},e.prototype.on=function(e,t){var n=this.currentDataManager;n.currentCalendarOptionsRefiners[e]?n.emitter.on(e,t):console.warn("Unknown listener name '"+e+"'")},e.prototype.off=function(e,t){this.currentDataManager.emitter.off(e,t)},e.prototype.trigger=function(e){for(var t,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];(t=this.currentDataManager.emitter).trigger.apply(t,l([e],n))},e.prototype.changeView=function(e,t){var n=this;this.batchRendering(function(){if(n.unselect(),t){if(t.start&&t.end)n.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e}),n.dispatch({type:"SET_OPTION",optionName:"visibleRange",rawOptionValue:t});else{var r=n.getCurrentData().dateEnv;n.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e,dateMarker:r.createMarker(t)})}}else n.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e})})},e.prototype.zoomTo=function(e,t){var n,r;t=t||"day",n=this.getCurrentData().viewSpecs[t]||this.getUnitViewSpec(t),this.unselect(),n?this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:n.type,dateMarker:e}):this.dispatch({type:"CHANGE_DATE",dateMarker:e})},e.prototype.getUnitViewSpec=function(e){var t,n,r=this.getCurrentData(),o=r.viewSpecs,i=r.toolbarConfig,a=[].concat(i.header?i.header.viewsWithButtons:[],i.footer?i.footer.viewsWithButtons:[]);for(var s in o)a.push(s);for(t=0;t<a.length;t+=1)if((n=o[a[t]])&&n.singleUnit===e)return n;return null},e.prototype.prev=function(){this.unselect(),this.dispatch({type:"PREV"})},e.prototype.next=function(){this.unselect(),this.dispatch({type:"NEXT"})},e.prototype.prevYear=function(){var e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,-1)})},e.prototype.nextYear=function(){var e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,1)})},e.prototype.today=function(){var e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:n7(e.calendarOptions.now,e.dateEnv)})},e.prototype.gotoDate=function(e){var t=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.createMarker(e)})},e.prototype.incrementDate=function(e){var t=this.getCurrentData(),n=tR(e);n&&(this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.add(t.currentDate,n)}))},e.prototype.getDate=function(){var e=this.getCurrentData();return e.dateEnv.toDate(e.currentDate)},e.prototype.formatDate=function(e,t){var n=this.getCurrentData().dateEnv;return n.format(n.createMarker(e),t1(t))},e.prototype.formatRange=function(e,t,n){var r=this.getCurrentData().dateEnv;return r.formatRange(r.createMarker(e),r.createMarker(t),t1(n),n)},e.prototype.formatIso=function(e,t){var n=this.getCurrentData().dateEnv;return n.formatIso(n.createMarker(e),{omitTime:t})},e.prototype.select=function(e,t){n=null==t?null!=e.start?e:{start:e,end:null}:{start:e,end:t};var n,r=this.getCurrentData(),o=function e(t,n,r){var o,i,a,l,u,c,d,p,f=(o=t,i=n,a=tQ(o,nG),l=a.refined,u=a.extra,c=l.start?i.createMarkerMeta(l.start):null,d=l.end?i.createMarkerMeta(l.end):null,p=l.allDay,null==p&&(p=c&&c.isTimeUnspecified&&(!d||d.isTimeUnspecified)),s({range:{start:c?c.marker:null,end:d?d.marker:null},allDay:p},u)),h=f.range;if(!h.start)return null;if(!h.end){if(null==r)return null;h.end=n.add(h.start,r)}return f}(n,r.dateEnv,tR({days:1}));o&&(this.dispatch({type:"SELECT_DATES",selection:o}),nZ(o,null,r))},e.prototype.unselect=function(e){var t=this.getCurrentData();t.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),function e(t,n){n.emitter.trigger("unselect",{jsEvent:t?t.origEvent:null,view:n.viewApi||n.calendarApi.view})}(e,t))},e.prototype.addEvent=function(e,t){if(e instanceof nQ){var n,r=e._def,o=e._instance;return this.getCurrentData().eventStore.defs[r.defId]||(this.dispatch({type:"ADD_EVENTS",eventStore:nt({def:r,instance:o})}),this.triggerEventAdd(e)),e}var i=this.getCurrentData();if(t instanceof eD)n=t.internalEventSource;else if("boolean"==typeof t)t&&(n=tv(i.eventSources)[0]);else if(null!=t){var a=this.getEventSourceById(t);if(!a)return console.warn('Could not find an event source with ID "'+t+'"'),null;n=a.internalEventSource}var s=nv(e,n,i,!1);if(s){var l=new nQ(i,s.def,s.def.recurringDef?null:s.instance);return this.dispatch({type:"ADD_EVENTS",eventStore:nt(s)}),this.triggerEventAdd(l),l}return null},e.prototype.triggerEventAdd=function(e){var t=this;this.getCurrentData().emitter.trigger("eventAdd",{event:e,relatedEvents:[],revert:function(){t.dispatch({type:"REMOVE_EVENTS",eventStore:nJ(e)})}})},e.prototype.getEventById=function(e){var t=this.getCurrentData(),n=t.eventStore,r=n.defs,o=n.instances;for(var i in e=String(e),r){var a=r[i];if(a.publicId===e){if(a.recurringDef)return new nQ(t,a,null);for(var s in o){var l=o[s];if(l.defId===a.defId)return new nQ(t,a,l)}}}return null},e.prototype.getEvents=function(){var e=this.getCurrentData();return re(e.eventStore,e)},e.prototype.removeAllEvents=function(){this.dispatch({type:"REMOVE_ALL_EVENTS"})},e.prototype.getEventSources=function(){var e=this.getCurrentData(),t=e.eventSources,n=[];for(var r in t)n.push(new eD(e,t[r]));return n},e.prototype.getEventSourceById=function(e){var t=this.getCurrentData(),n=t.eventSources;for(var r in e=String(e),n)if(n[r].publicId===e)return new eD(t,n[r]);return null},e.prototype.addEventSource=function(e){var t=this.getCurrentData();if(e instanceof eD)return t.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;var n=n5(e,t);return n?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[n]}),new eD(t,n)):null},e.prototype.removeAllEventSources=function(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})},e.prototype.refetchEvents=function(){this.dispatch({type:"FETCH_EVENT_SOURCES",isRefetch:!0})},e.prototype.scrollToTime=function(e){var t=tR(e);t&&this.trigger("_scrollRequest",{time:t})},e}(),nQ=function(){function e(e,t,n){this._context=e,this._def=t,this._instance=n||null}return e.prototype.setProp=function(e,t){var n,r;if(e in nf)console.warn("Could not set date-related prop 'name'. Use one of the date-related methods instead.");else if("id"===e)t=np[e](t),this.mutate({standardProps:{publicId:t}});else if(e in np)t=np[e](t),this.mutate({standardProps:((n={})[e]=t,n)});else if(e in ns){var o=ns[e](t);o="color"===e?{backgroundColor:t,borderColor:t}:"editable"===e?{startEditable:t,durationEditable:t}:((r={})[e]=t,r),this.mutate({standardProps:{ui:o}})}else console.warn("Could not set prop '"+e+"'. Use setExtendedProp instead.")},e.prototype.setExtendedProp=function(e,t){var n;this.mutate({extendedProps:((n={})[e]=t,n)})},e.prototype.setStart=function(e,t){void 0===t&&(t={});var n=this._context.dateEnv,r=n.createMarker(e);if(r&&this._instance){var o=nb(this._instance.range.start,r,n,t.granularity);t.maintainDuration?this.mutate({datesDelta:o}):this.mutate({startDelta:o})}},e.prototype.setEnd=function(e,t){void 0===t&&(t={});var n,r=this._context.dateEnv;if((null==e||(n=r.createMarker(e)))&&this._instance){if(n){var o=nb(this._instance.range.end,n,r,t.granularity);this.mutate({endDelta:o})}else this.mutate({standardProps:{hasEnd:!1}})}},e.prototype.setDates=function(e,t,n){void 0===n&&(n={});var r,o=this._context.dateEnv,i={allDay:n.allDay},a=o.createMarker(e);if(a&&(null==t||(r=o.createMarker(t)))&&this._instance){var s=this._instance.range;!0===n.allDay&&(s=nE(s));var l=nb(s.start,a,o,n.granularity);if(r){var u,c,d=nb(s.end,r,o,n.granularity);(u=l,c=d,u.years===c.years&&u.months===c.months&&u.days===c.days&&u.milliseconds===c.milliseconds)?this.mutate({datesDelta:l,standardProps:i}):this.mutate({startDelta:l,endDelta:d,standardProps:i})}else i.hasEnd=!1,this.mutate({datesDelta:l,standardProps:i})}},e.prototype.moveStart=function(e){var t=tR(e);t&&this.mutate({startDelta:t})},e.prototype.moveEnd=function(e){var t=tR(e);t&&this.mutate({endDelta:t})},e.prototype.moveDates=function(e){var t=tR(e);t&&this.mutate({datesDelta:t})},e.prototype.setAllDay=function(e,t){void 0===t&&(t={});var n={allDay:e},r=t.maintainDuration;null==r&&(r=this._context.options.allDayMaintainDuration),this._def.allDay!==e&&(n.hasEnd=r),this.mutate({standardProps:n})},e.prototype.formatRange=function(e){var t=this._context.dateEnv,n=this._instance,r=t1(e);return this._def.hasEnd?t.formatRange(n.range.start,n.range.end,r,{forcedStartTzo:n.forcedStartTzo,forcedEndTzo:n.forcedEndTzo}):t.format(n.range.start,r,{forcedTzo:n.forcedStartTzo})},e.prototype.mutate=function(t){var n=this._instance;if(n){var r=this._def,o=this._context,i=o.getCurrentData().eventStore,a=nn(i,n.instanceId);a=n1(a,{"":{display:"",startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}},t,o);var s=new e(o,r,n);this._def=a.defs[r.defId],this._instance=a.instances[n.instanceId],o.dispatch({type:"MERGE_EVENTS",eventStore:a}),o.emitter.trigger("eventChange",{oldEvent:s,event:this,relatedEvents:re(a,o,n),revert:function(){o.dispatch({type:"RESET_EVENTS",eventStore:i})}})}},e.prototype.remove=function(){var e=this._context,t=nJ(this);e.dispatch({type:"REMOVE_EVENTS",eventStore:t}),e.emitter.trigger("eventRemove",{event:this,relatedEvents:[],revert:function(){e.dispatch({type:"MERGE_EVENTS",eventStore:t})}})},Object.defineProperty(e.prototype,"source",{get:function(){var e=this._def.sourceId;return e?new eD(this._context,this._context.getCurrentData().eventSources[e]):null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"start",{get:function(){return this._instance?this._context.dateEnv.toDate(this._instance.range.start):null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"end",{get:function(){return this._instance&&this._def.hasEnd?this._context.dateEnv.toDate(this._instance.range.end):null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"startStr",{get:function(){var e=this._instance;return e?this._context.dateEnv.formatIso(e.range.start,{omitTime:this._def.allDay,forcedTzo:e.forcedStartTzo}):""},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"endStr",{get:function(){var e=this._instance;return e&&this._def.hasEnd?this._context.dateEnv.formatIso(e.range.end,{omitTime:this._def.allDay,forcedTzo:e.forcedEndTzo}):""},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"id",{get:function(){return this._def.publicId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"groupId",{get:function(){return this._def.groupId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"allDay",{get:function(){return this._def.allDay},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this._def.title},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"url",{get:function(){return this._def.url},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"display",{get:function(){return this._def.ui.display||"auto"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"startEditable",{get:function(){return this._def.ui.startEditable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"durationEditable",{get:function(){return this._def.ui.durationEditable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"constraint",{get:function(){return this._def.ui.constraints[0]||null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overlap",{get:function(){return this._def.ui.overlap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"allow",{get:function(){return this._def.ui.allows[0]||null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"backgroundColor",{get:function(){return this._def.ui.backgroundColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"borderColor",{get:function(){return this._def.ui.borderColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textColor",{get:function(){return this._def.ui.textColor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"classNames",{get:function(){return this._def.ui.classNames},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"extendedProps",{get:function(){return this._def.extendedProps},enumerable:!1,configurable:!0}),e.prototype.toPlainObject=function(e){void 0===e&&(e={});var t=this._def,n=t.ui,r=this.startStr,o=this.endStr,i={};return t.title&&(i.title=t.title),r&&(i.start=r),o&&(i.end=o),t.publicId&&(i.id=t.publicId),t.groupId&&(i.groupId=t.groupId),t.url&&(i.url=t.url),n.display&&"auto"!==n.display&&(i.display=n.display),e.collapseColor&&n.backgroundColor&&n.backgroundColor===n.borderColor?i.color=n.backgroundColor:(n.backgroundColor&&(i.backgroundColor=n.backgroundColor),n.borderColor&&(i.borderColor=n.borderColor)),n.textColor&&(i.textColor=n.textColor),n.classNames.length&&(i.classNames=n.classNames),Object.keys(t.extendedProps).length&&(e.collapseExtendedProps?s(i,t.extendedProps):i.extendedProps=t.extendedProps),i},e.prototype.toJSON=function(){return this.toPlainObject()},e}();function nJ(e){var t,n,r=e._def,o=e._instance;return{defs:((t={})[r.defId]=r,t),instances:o?((n={})[o.instanceId]=o,n):{}}}function re(e,t,n){var r=e.defs,o=e.instances,i=[],a=n?n.instanceId:"";for(var s in o){var l=o[s],u=r[l.defId];l.instanceId!==a&&i.push(new nQ(t,u,l))}return i}var rt={};function rn(e,t){rt[e]=t}t=function(){function e(){}return e.prototype.getMarkerYear=function(e){return e.getUTCFullYear()},e.prototype.getMarkerMonth=function(e){return e.getUTCMonth()},e.prototype.getMarkerDay=function(e){return e.getUTCDate()},e.prototype.arrayToMarker=function(e){return ta(e)},e.prototype.markerToArray=function(e){return ti(e)},e}(),rt.gregory=t;var rr=/^\s*(\d{4})(-?(\d{2})(-?(\d{2})([T ](\d{2}):?(\d{2})(:?(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;function ro(e){var t=rr.exec(e);if(t){var n=new Date(Date.UTC(Number(t[1]),t[3]?Number(t[3])-1:0,Number(t[5]||1),Number(t[7]||0),Number(t[8]||0),Number(t[10]||0),t[12]?1e3*Number("0."+t[12]):0));if(ts(n)){var r=null;return t[13]&&(r=("-"===t[15]?-1:1)*(60*Number(t[16]||0)+Number(t[18]||0))),{marker:n,isTimeUnspecified:!t[6],timeZoneOffset:r}}}return null}var ri=function(){function e(e){var t,n=this.timeZone=e.timeZone,r="local"!==n&&"UTC"!==n;e.namedTimeZoneImpl&&r&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(n)),this.canComputeOffset=Boolean(!r||this.namedTimeZoneImpl),this.calendarSystem=new rt[t=e.calendarSystem],this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,"ISO"===e.weekNumberCalculation&&(this.weekDow=1,this.weekDoy=4),"number"==typeof e.firstDay&&(this.weekDow=e.firstDay),"function"==typeof e.weekNumberCalculation&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekText=null!=e.weekText?e.weekText:e.locale.options.weekText,this.weekTextLong=(null!=e.weekTextLong?e.weekTextLong:e.locale.options.weekTextLong)||this.weekText,this.cmdFormatter=e.cmdFormatter,this.defaultSeparator=e.defaultSeparator}return e.prototype.createMarker=function(e){var t=this.createMarkerMeta(e);return null===t?null:t.marker},e.prototype.createNowMarker=function(){return this.canComputeOffset?this.timestampToMarker(new Date().valueOf()):ta(tr(new Date))},e.prototype.createMarkerMeta=function(e){if("string"==typeof e)return this.parse(e);var t=null;return("number"==typeof e?t=this.timestampToMarker(e):e instanceof Date?isNaN(e=e.valueOf())||(t=this.timestampToMarker(e)):Array.isArray(e)&&(t=ta(e)),null!==t&&ts(t))?{marker:t,isTimeUnspecified:!1,forcedTzo:null}:null},e.prototype.parse=function(e){var t=ro(e);if(null===t)return null;var n=t.marker,r=null;return null!==t.timeZoneOffset&&(this.canComputeOffset?n=this.timestampToMarker(n.valueOf()-6e4*t.timeZoneOffset):r=t.timeZoneOffset),{marker:n,isTimeUnspecified:t.isTimeUnspecified,forcedTzo:r}},e.prototype.getYear=function(e){return this.calendarSystem.getMarkerYear(e)},e.prototype.getMonth=function(e){return this.calendarSystem.getMarkerMonth(e)},e.prototype.add=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]+=t.years,n[1]+=t.months,n[2]+=t.days,n[6]+=t.milliseconds,this.calendarSystem.arrayToMarker(n)},e.prototype.subtract=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]-=t.years,n[1]-=t.months,n[2]-=t.days,n[6]-=t.milliseconds,this.calendarSystem.arrayToMarker(n)},e.prototype.addYears=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[0]+=t,this.calendarSystem.arrayToMarker(n)},e.prototype.addMonths=function(e,t){var n=this.calendarSystem.markerToArray(e);return n[1]+=t,this.calendarSystem.arrayToMarker(n)},e.prototype.diffWholeYears=function(e,t){var n=this.calendarSystem;return tl(e)===tl(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)&&n.getMarkerMonth(e)===n.getMarkerMonth(t)?n.getMarkerYear(t)-n.getMarkerYear(e):null},e.prototype.diffWholeMonths=function(e,t){var n=this.calendarSystem;return tl(e)===tl(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)?n.getMarkerMonth(t)-n.getMarkerMonth(e)+(n.getMarkerYear(t)-n.getMarkerYear(e))*12:null},e.prototype.greatestWholeUnit=function(e,t){var n,r,o,i,a,s,l=this.diffWholeYears(e,t);return null!==l?{unit:"year",value:l}:null!==(l=this.diffWholeMonths(e,t))?{unit:"month",value:l}:null!==(l=eJ(e,t))?{unit:"week",value:l}:null!==(l=te(e,t))?{unit:"day",value:l}:e4(l=(n=e,((r=t).valueOf()-n.valueOf())/36e5))?{unit:"hour",value:l}:e4(l=(o=e,((i=t).valueOf()-o.valueOf())/6e4))?{unit:"minute",value:l}:e4(l=(a=e,((s=t).valueOf()-a.valueOf())/1e3))?{unit:"second",value:l}:{unit:"millisecond",value:t.valueOf()-e.valueOf()}},e.prototype.countDurationsBetween=function(e,t,n){var r,o,i;return n.years&&null!==(r=this.diffWholeYears(e,t))?r/(tk(o=n)/365):n.months&&null!==(r=this.diffWholeMonths(e,t))?r/(tk(i=n)/30):n.days&&null!==(r=te(e,t))?r/tk(n):(t.valueOf()-e.valueOf())/tx(n)},e.prototype.startOf=function(e,t){var n,r,o;return"year"===t?this.startOfYear(e):"month"===t?this.startOfMonth(e):"week"===t?this.startOfWeek(e):"day"===t?tt(e):"hour"===t?ta([(n=e).getUTCFullYear(),n.getUTCMonth(),n.getUTCDate(),n.getUTCHours(),]):"minute"===t?ta([(r=e).getUTCFullYear(),r.getUTCMonth(),r.getUTCDate(),r.getUTCHours(),r.getUTCMinutes(),]):"second"===t?ta([(o=e).getUTCFullYear(),o.getUTCMonth(),o.getUTCDate(),o.getUTCHours(),o.getUTCMinutes(),o.getUTCSeconds(),]):null},e.prototype.startOfYear=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),])},e.prototype.startOfMonth=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),])},e.prototype.startOfWeek=function(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7,])},e.prototype.computeWeekNumber=function(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):function e(t,n,r){var o=t.getUTCFullYear(),i=tn(t,o,n,r);if(i<1)return tn(t,o-1,n,r);var a=tn(t,o+1,n,r);return a>=1?Math.min(i,a):i}(e,this.weekDow,this.weekDoy)},e.prototype.format=function(e,t,n){return void 0===n&&(n={}),t.format({marker:e,timeZoneOffset:null!=n.forcedTzo?n.forcedTzo:this.offsetForMarker(e)},this)},e.prototype.formatRange=function(e,t,n,r){return void 0===r&&(r={}),r.isEndExclusive&&(t=e9(t,-1)),n.formatRange({marker:e,timeZoneOffset:null!=r.forcedStartTzo?r.forcedStartTzo:this.offsetForMarker(e)},{marker:t,timeZoneOffset:null!=r.forcedEndTzo?r.forcedEndTzo:this.offsetForMarker(t)},this,r.defaultSeparator)},e.prototype.formatIso=function(e,t){void 0===t&&(t={});var n=null;return t.omitTimeZoneOffset||(n=null!=t.forcedTzo?t.forcedTzo:this.offsetForMarker(e)),tI(e,n,t.omitTime)},e.prototype.timestampToMarker=function(e){return"local"===this.timeZone?ta(tr(new Date(e))):"UTC"!==this.timeZone&&this.namedTimeZoneImpl?ta(this.namedTimeZoneImpl.timestampToArray(e)):new Date(e)},e.prototype.offsetForMarker=function(e){return"local"===this.timeZone?-to(ti(e)).getTimezoneOffset():"UTC"===this.timeZone?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(ti(e)):null},e.prototype.toDate=function(e,t){return"local"===this.timeZone?to(ti(e)):new Date("UTC"===this.timeZone?e.valueOf():this.namedTimeZoneImpl?e.valueOf()-6e4*this.namedTimeZoneImpl.offsetForArray(ti(e)):e.valueOf()-(t||0))},e}(),ra=[],rs={code:"en",week:{dow:0,doy:4},direction:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekText:"W",weekTextLong:"Week",closeHint:"Close",timeHint:"Time",eventHint:"Event",allDayText:"all-day",moreLinkText:"more",noEventsText:"No events to display"},rl=s(s({},rs),{buttonHints:{prev:"Previous $0",next:"Next $0",today:function(e,t){return"day"===t?"Today":"This "+e}},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint:function(e){return"Show "+e+" more event"+(1===e?"":"s")}});function ru(e){for(var t=e.length>0?e[0].code:"en",n=ra.concat(e),r={en:rl},o=0,i=n;o<i.length;o++){var a=i[o];r[a.code]=a}return{map:r,defaultCode:t}}function rc(e,t){var n,r,o,i;return"object"!=typeof e||Array.isArray(e)?(n=e,r=t,o=[].concat(n||[]),i=function e(t,n){for(var r=0;r<t.length;r+=1)for(var o=t[r].toLocaleLowerCase().split("-"),i=o.length;i>0;i-=1){var a=o.slice(0,i).join("-");if(n[a])return n[a]}return null}(o,r)||rl,rd(n,o,i)):rd(e.code,[e.code],e)}function rd(e,t,n){var r=td([rs,n],["buttonText"]);delete r.code;var o=r.week;return delete r.week,{codeArg:e,codes:t,week:o,simpleNumberFormat:new Intl.NumberFormat(e),options:r}}function rp(e){var t=rc(e.locale||"en",ru([]).map);return new ri(s(s({timeZone:tK.timeZone,calendarSystem:"gregory"},e),{locale:t}))}var rf={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],display:"inverse-background",classNames:"fc-non-business",groupId:"_businessHours"};function rh(e,t){var n,r;return ne((n=e,(r=!0===n?[{}]:Array.isArray(n)?n.filter(function(e){return e.daysOfWeek}):"object"==typeof n&&n?[n]:[]).map(function(e){return s(s({},rf),e)})),null,t)}function rv(e,t){return e.left>=t.left&&e.left<t.right&&e.top>=t.top&&e.top<t.bottom}function rg(e,t){var n={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)};return n.left<n.right&&n.top<n.bottom&&n}function rm(e,t){return{left:Math.min(Math.max(e.left,t.left),t.right),top:Math.min(Math.max(e.top,t.top),t.bottom)}}function ry(e){return{left:(e.left+e.right)/2,top:(e.top+e.bottom)/2}}function rE(e,t){return{left:e.left-t.left,top:e.top-t.top}}function rS(){return null==r&&(r=function e(){if("undefined"==typeof document)return!0;var t=document.createElement("div");t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.innerHTML="<table><tr><td><div></div></td></tr></table>",t.querySelector("table").style.height="100px",t.querySelector("div").style.height="100%",document.body.appendChild(t);var n=t.querySelector("div").offsetHeight>0;return document.body.removeChild(t),n}()),r}var rD=nr(),rb=function(){function e(){this.getKeysForEventDefs=tA(this._getKeysForEventDefs),this.splitDateSelection=tA(this._splitDateSpan),this.splitEventStore=tA(this._splitEventStore),this.splitIndividualUi=tA(this._splitIndividualUi),this.splitEventDrag=tA(this._splitInteraction),this.splitEventResize=tA(this._splitInteraction),this.eventUiBuilders={}}return e.prototype.splitProps=function(e){var t=this,n=this.getKeyInfo(e),r=this.getKeysForEventDefs(e.eventStore),o=this.splitDateSelection(e.dateSelection),i=this.splitIndividualUi(e.eventUiBases,r),a=this.splitEventStore(e.eventStore,r),s=this.splitEventDrag(e.eventDrag),l=this.splitEventResize(e.eventResize),u={};for(var c in this.eventUiBuilders=tf(n,function(e,n){return t.eventUiBuilders[n]||tA(rC)}),n){var d=n[c],p=a[c]||rD,f=this.eventUiBuilders[c];u[c]={businessHours:d.businessHours||e.businessHours,dateSelection:o[c]||null,eventStore:p,eventUiBases:f(e.eventUiBases[""],d.ui,i[c]),eventSelection:p.instances[e.eventSelection]?e.eventSelection:"",eventDrag:s[c]||null,eventResize:l[c]||null}}return u},e.prototype._splitDateSpan=function(e){var t={};if(e)for(var n=this.getKeysForDateSpan(e),r=0,o=n;r<o.length;r++)t[o[r]]=e;return t},e.prototype._getKeysForEventDefs=function(e){var t=this;return tf(e.defs,function(e){return t.getKeysForEventDef(e)})},e.prototype._splitEventStore=function(e,t){var n=e.defs,r=e.instances,o={};for(var i in n)for(var a=0,s=t[i];a<s.length;a++){var l=s[a];o[l]||(o[l]=nr()),o[l].defs[i]=n[i]}for(var u in r)for(var c=r[u],d=0,p=t[c.defId];d<p.length;d++){var l=p[d];o[l]&&(o[l].instances[u]=c)}return o},e.prototype._splitIndividualUi=function(e,t){var n={};for(var r in e)if(r)for(var o=0,i=t[r];o<i.length;o++){var a=i[o];n[a]||(n[a]={}),n[a][r]=e[r]}return n},e.prototype._splitInteraction=function(e){var t={};if(e){var n=this._splitEventStore(e.affectedEvents,this._getKeysForEventDefs(e.affectedEvents)),r=this._getKeysForEventDefs(e.mutatedEvents),o=this._splitEventStore(e.mutatedEvents,r),i=function(r){t[r]||(t[r]={affectedEvents:n[r]||rD,mutatedEvents:o[r]||rD,isEvent:e.isEvent})};for(var a in n)i(a);for(var a in o)i(a)}return t},e}();function rC(e,t,n){var r=[];e&&r.push(e),t&&r.push(t);var o={"":nc(r)};return n&&s(o,n),o}function r$(e,t,n,r){return{dow:e.getUTCDay(),isDisabled:Boolean(r&&!nk(r.activeRange,e)),isOther:Boolean(r&&!nk(r.currentRange,e)),isToday:Boolean(t&&nk(t,e)),isPast:Boolean(n?e<n:!!t&&e<t.start),isFuture:Boolean(n?e>n:!!t&&e>=t.end)}}function rR(e,t){var n=["fc-day","fc-day-"+e3[e.dow],];return e.isDisabled?n.push("fc-day-disabled"):(e.isToday&&(n.push("fc-day-today"),n.push(t.getClass("today"))),e.isPast&&n.push("fc-day-past"),e.isFuture&&n.push("fc-day-future"),e.isOther&&n.push("fc-day-other")),n}var r8=t1({year:"numeric",month:"long",day:"numeric"}),rw=t1({week:"long"});function rT(e,t,n,r){void 0===n&&(n="day"),void 0===r&&(r=!0);var o=e.dateEnv,i=e.options,a=e.calendarApi,l=o.format(t,"week"===n?rw:r8);if(i.navLinks){var u=o.toDate(t),c=function(e){var r="day"===n?i.navLinkDayClick:"week"===n?i.navLinkWeekClick:null;"function"==typeof r?r.call(a,o.toDate(t),e):("string"==typeof r&&(n=r),a.zoomTo(t,n))};return s({title:e0(i.navLinkHint,[l,u],l),"data-navlink":""},r?eO(c):{onClick:c})}return{"aria-label":l}}var rk=null;function rx(){var e,t;return null===rk&&(rk=(e=document.createElement("div"),ew(e,{position:"absolute",top:-1e3,left:0,border:0,padding:0,overflow:"scroll",direction:"rtl"}),e.innerHTML="<div></div>",document.body.appendChild(e),t=e.firstChild.getBoundingClientRect().left>e.getBoundingClientRect().left,eb(e),t)),rk}function rM(){var e,t;return o||(o=(e=document.createElement("div"),e.style.overflow="scroll",e.style.position="absolute",e.style.top="-9999px",e.style.left="-9999px",document.body.appendChild(e),t=r_(e),document.body.removeChild(e),t)),o}function r_(e){return{x:e.offsetHeight-e.clientHeight,y:e.offsetWidth-e.clientWidth}}function rI(e,t){void 0===t&&(t=!1);var n=window.getComputedStyle(e),r=parseInt(n.borderLeftWidth,10)||0,o=parseInt(n.borderRightWidth,10)||0,i=parseInt(n.borderTopWidth,10)||0,a=parseInt(n.borderBottomWidth,10)||0,s=r_(e),l=s.y-r-o,u=s.x-i-a,c={borderLeft:r,borderRight:o,borderTop:i,borderBottom:a,scrollbarBottom:u,scrollbarLeft:0,scrollbarRight:0};return rx()&&"rtl"===n.direction?c.scrollbarLeft=l:c.scrollbarRight=l,t&&(c.paddingLeft=parseInt(n.paddingLeft,10)||0,c.paddingRight=parseInt(n.paddingRight,10)||0,c.paddingTop=parseInt(n.paddingTop,10)||0,c.paddingBottom=parseInt(n.paddingBottom,10)||0),c}function rP(e,t,n){void 0===t&&(t=!1);var r=n?e.getBoundingClientRect():rN(e),o=rI(e,t),i={left:r.left+o.borderLeft+o.scrollbarLeft,right:r.right-o.borderRight-o.scrollbarRight,top:r.top+o.borderTop,bottom:r.bottom-o.borderBottom-o.scrollbarBottom};return t&&(i.left+=o.paddingLeft,i.right-=o.paddingRight,i.top+=o.paddingTop,i.bottom-=o.paddingBottom),i}function rN(e){var t=e.getBoundingClientRect();return{left:t.left+window.pageXOffset,top:t.top+window.pageYOffset,right:t.right+window.pageXOffset,bottom:t.bottom+window.pageYOffset}}function rH(e){for(var t=[];e instanceof HTMLElement;){var n=window.getComputedStyle(e);if("fixed"===n.position)break;/(auto|scroll)/.test(n.overflow+n.overflowY+n.overflowX)&&t.push(e),e=e.parentNode}return t}function rO(e,t,n){var r=!1,o=function(){r||(r=!0,t.apply(this,arguments))},i=function(){!r&&(r=!0,n&&n.apply(this,arguments))},a=e(o,i);a&&"function"==typeof a.then&&a.then(o,i)}var rA=function(){function e(){this.handlers={},this.thisContext=null}return e.prototype.setThisContext=function(e){this.thisContext=e},e.prototype.setOptions=function(e){this.options=e},e.prototype.on=function(e,t){(function e(t,n,r){(t[n]||(t[n]=[])).push(r)})(this.handlers,e,t)},e.prototype.off=function(e,t){var n,r,o;n=this.handlers,r=e,o=t,o?n[r]&&(n[r]=n[r].filter(function(e){return e!==o})):delete n[r]},e.prototype.trigger=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=this.handlers[e]||[],o=[].concat(this.options&&this.options[e]||[],r),i=0,a=o;i<a.length;i++)a[i].apply(this.thisContext,t)},e.prototype.hasHandlers=function(e){return Boolean(this.handlers[e]&&this.handlers[e].length||this.options&&this.options[e])},e}(),rL=function(){function e(e,t,n,r){this.els=t;var o=this.originClientRect=e.getBoundingClientRect();n&&this.buildElHorizontals(o.left),r&&this.buildElVerticals(o.top)}return e.prototype.buildElHorizontals=function(e){for(var t=[],n=[],r=0,o=this.els;r<o.length;r++){var i=o[r].getBoundingClientRect();t.push(i.left-e),n.push(i.right-e)}this.lefts=t,this.rights=n},e.prototype.buildElVerticals=function(e){for(var t=[],n=[],r=0,o=this.els;r<o.length;r++){var i=o[r].getBoundingClientRect();t.push(i.top-e),n.push(i.bottom-e)}this.tops=t,this.bottoms=n},e.prototype.leftToIndex=function(e){var t,n=this.lefts,r=this.rights,o=n.length;for(t=0;t<o;t+=1)if(e>=n[t]&&e<r[t])return t},e.prototype.topToIndex=function(e){var t,n=this.tops,r=this.bottoms,o=n.length;for(t=0;t<o;t+=1)if(e>=n[t]&&e<r[t])return t},e.prototype.getWidth=function(e){return this.rights[e]-this.lefts[e]},e.prototype.getHeight=function(e){return this.bottoms[e]-this.tops[e]},e}(),rU=function(){function e(){}return e.prototype.getMaxScrollTop=function(){return this.getScrollHeight()-this.getClientHeight()},e.prototype.getMaxScrollLeft=function(){return this.getScrollWidth()-this.getClientWidth()},e.prototype.canScrollVertically=function(){return this.getMaxScrollTop()>0},e.prototype.canScrollHorizontally=function(){return this.getMaxScrollLeft()>0},e.prototype.canScrollUp=function(){return this.getScrollTop()>0},e.prototype.canScrollDown=function(){return this.getScrollTop()<this.getMaxScrollTop()},e.prototype.canScrollLeft=function(){return this.getScrollLeft()>0},e.prototype.canScrollRight=function(){return this.getScrollLeft()<this.getMaxScrollLeft()},e}(),rW=function(e){function t(t){var n=e.call(this)||this;return n.el=t,n}return a(t,e),t.prototype.getScrollTop=function(){return this.el.scrollTop},t.prototype.getScrollLeft=function(){return this.el.scrollLeft},t.prototype.setScrollTop=function(e){this.el.scrollTop=e},t.prototype.setScrollLeft=function(e){this.el.scrollLeft=e},t.prototype.getScrollWidth=function(){return this.el.scrollWidth},t.prototype.getScrollHeight=function(){return this.el.scrollHeight},t.prototype.getClientHeight=function(){return this.el.clientHeight},t.prototype.getClientWidth=function(){return this.el.clientWidth},t}(rU),rV=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.getScrollTop=function(){return window.pageYOffset},t.prototype.getScrollLeft=function(){return window.pageXOffset},t.prototype.setScrollTop=function(e){window.scroll(window.pageXOffset,e)},t.prototype.setScrollLeft=function(e){window.scroll(e,window.pageYOffset)},t.prototype.getScrollWidth=function(){return document.documentElement.scrollWidth},t.prototype.getScrollHeight=function(){return document.documentElement.scrollHeight},t.prototype.getClientHeight=function(){return document.documentElement.clientHeight},t.prototype.getClientWidth=function(){return document.documentElement.clientWidth},t}(rU),rz=function(){function e(e){this.iconOverrideOption&&this.setIconOverride(e[this.iconOverrideOption])}return e.prototype.setIconOverride=function(e){var t,n;if("object"==typeof e&&e){for(n in t=s({},this.iconClasses),e)t[n]=this.applyIconOverridePrefix(e[n]);this.iconClasses=t}else!1===e&&(this.iconClasses={})},e.prototype.applyIconOverridePrefix=function(e){var t=this.iconOverridePrefix;return t&&0!==e.indexOf(t)&&(e=t+e),e},e.prototype.getClass=function(e){return this.classes[e]||""},e.prototype.getIconClass=function(e,t){var n;return(n=t&&this.rtlIconClasses&&this.rtlIconClasses[e]||this.iconClasses[e])?this.baseIconClass+" "+n:""},e.prototype.getCustomButtonIconClass=function(e){var t;return this.iconOverrideCustomButtonOption&&(t=e[this.iconOverrideCustomButtonOption])?this.baseIconClass+" "+this.applyIconOverridePrefix(t):""},e}();if(rz.prototype.classes={},rz.prototype.iconClasses={},rz.prototype.baseIconClass="",rz.prototype.iconOverridePrefix="","undefined"==typeof FullCalendarVDom)throw Error("Please import the top-level fullcalendar lib before attempting to import a plugin.");var rF=FullCalendarVDom.Component,rB=FullCalendarVDom.createElement,rG=FullCalendarVDom.render,rq=FullCalendarVDom.createRef,rj=FullCalendarVDom.Fragment,rY=FullCalendarVDom.createContext,rZ=FullCalendarVDom.createPortal;FullCalendarVDom.flushSync;var rX=FullCalendarVDom.unmountComponentAtNode,r0=function(){function e(e,t,n,r){var o=this;this.execFunc=e,this.emitter=t,this.scrollTime=n,this.scrollTimeReset=r,this.handleScrollRequest=function(e){o.queuedRequest=s({},o.queuedRequest||{},e),o.drain()},t.on("_scrollRequest",this.handleScrollRequest),this.fireInitialScroll()}return e.prototype.detach=function(){this.emitter.off("_scrollRequest",this.handleScrollRequest)},e.prototype.update=function(e){e&&this.scrollTimeReset?this.fireInitialScroll():this.drain()},e.prototype.fireInitialScroll=function(){this.handleScrollRequest({time:this.scrollTime})},e.prototype.drain=function(){this.queuedRequest&&this.execFunc(this.queuedRequest)&&(this.queuedRequest=null)},e}(),r1=rY({});function r4(e,t,n,r,o,i,a,s,l,u,c,d,p){return{dateEnv:o,options:n,pluginHooks:a,emitter:u,dispatch:s,getCurrentData:l,calendarApi:c,viewSpec:e,viewApi:t,dateProfileGenerator:r,theme:i,isRtl:"rtl"===n.direction,addResizeHandler:function(e){u.on("_resize",e)},removeResizeHandler:function(e){u.off("_resize",e)},createScrollResponder:function(e){return new r0(e,u,tR(n.scrollTime),n.scrollTimeReset)},registerInteractiveComponent:d,unregisterInteractiveComponent:p}}var rK=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.shouldComponentUpdate=function(e,t){return this.debug&&console.log(tm(e,this.props),tm(t,this.state)),!ty(this.props,e,this.propEquality)||!ty(this.state,t,this.stateEquality)},t.prototype.safeSetState=function(e){ty(this.state,s(s({},this.state),e),this.stateEquality)||this.setState(e)},t.addPropsEquality=r2,t.addStateEquality=r5,t.contextType=r1,t}(rF);rK.prototype.propEquality={},rK.prototype.stateEquality={};var r3=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.contextType=r1,t}(rK);function r2(e){var t=Object.create(this.prototype.propEquality);s(t,e),this.prototype.propEquality=t}function r5(e){var t=Object.create(this.prototype.stateEquality);s(t,e),this.prototype.stateEquality=t}function r9(e,t){"function"==typeof e?e(t):e&&(e.current=t)}var r7=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.uid=eU(),t}return a(t,e),t.prototype.prepareHits=function(){},t.prototype.queryHit=function(e,t,n,r){return null},t.prototype.isValidSegDownEl=function(e){return!this.props.eventDrag&&!this.props.eventResize&&!eC(e,".fc-event-mirror")},t.prototype.isValidDateDownEl=function(e){return!eC(e,".fc-event:not(.fc-bg-event)")&&!eC(e,".fc-more-link")&&!eC(e,"a[data-navlink]")&&!eC(e,".fc-popover")},t}(r3);function r6(e){return{id:eU(),deps:e.deps||[],reducers:e.reducers||[],isLoadingFuncs:e.isLoadingFuncs||[],contextInit:[].concat(e.contextInit||[]),eventRefiners:e.eventRefiners||{},eventDefMemberAdders:e.eventDefMemberAdders||[],eventSourceRefiners:e.eventSourceRefiners||{},isDraggableTransformers:e.isDraggableTransformers||[],eventDragMutationMassagers:e.eventDragMutationMassagers||[],eventDefMutationAppliers:e.eventDefMutationAppliers||[],dateSelectionTransformers:e.dateSelectionTransformers||[],datePointTransforms:e.datePointTransforms||[],dateSpanTransforms:e.dateSpanTransforms||[],views:e.views||{},viewPropsTransformers:e.viewPropsTransformers||[],isPropsValid:e.isPropsValid||null,externalDefTransforms:e.externalDefTransforms||[],viewContainerAppends:e.viewContainerAppends||[],eventDropTransformers:e.eventDropTransformers||[],componentInteractions:e.componentInteractions||[],calendarInteractions:e.calendarInteractions||[],themeClasses:e.themeClasses||{},eventSourceDefs:e.eventSourceDefs||[],cmdFormatter:e.cmdFormatter,recurringTypes:e.recurringTypes||[],namedTimeZonedImpl:e.namedTimeZonedImpl,initialView:e.initialView||"",elementDraggingImpl:e.elementDraggingImpl,optionChangeHandlers:e.optionChangeHandlers||{},scrollGridImpl:e.scrollGridImpl||null,contentTypeHandlers:e.contentTypeHandlers||{},listenerRefiners:e.listenerRefiners||{},optionRefiners:e.optionRefiners||{},propSetHandlers:e.propSetHandlers||{}}}function rQ(e,t){return{reducers:e.reducers.concat(t.reducers),isLoadingFuncs:e.isLoadingFuncs.concat(t.isLoadingFuncs),contextInit:e.contextInit.concat(t.contextInit),eventRefiners:s(s({},e.eventRefiners),t.eventRefiners),eventDefMemberAdders:e.eventDefMemberAdders.concat(t.eventDefMemberAdders),eventSourceRefiners:s(s({},e.eventSourceRefiners),t.eventSourceRefiners),isDraggableTransformers:e.isDraggableTransformers.concat(t.isDraggableTransformers),eventDragMutationMassagers:e.eventDragMutationMassagers.concat(t.eventDragMutationMassagers),eventDefMutationAppliers:e.eventDefMutationAppliers.concat(t.eventDefMutationAppliers),dateSelectionTransformers:e.dateSelectionTransformers.concat(t.dateSelectionTransformers),datePointTransforms:e.datePointTransforms.concat(t.datePointTransforms),dateSpanTransforms:e.dateSpanTransforms.concat(t.dateSpanTransforms),views:s(s({},e.views),t.views),viewPropsTransformers:e.viewPropsTransformers.concat(t.viewPropsTransformers),isPropsValid:t.isPropsValid||e.isPropsValid,externalDefTransforms:e.externalDefTransforms.concat(t.externalDefTransforms),viewContainerAppends:e.viewContainerAppends.concat(t.viewContainerAppends),eventDropTransformers:e.eventDropTransformers.concat(t.eventDropTransformers),calendarInteractions:e.calendarInteractions.concat(t.calendarInteractions),componentInteractions:e.componentInteractions.concat(t.componentInteractions),themeClasses:s(s({},e.themeClasses),t.themeClasses),eventSourceDefs:e.eventSourceDefs.concat(t.eventSourceDefs),cmdFormatter:t.cmdFormatter||e.cmdFormatter,recurringTypes:e.recurringTypes.concat(t.recurringTypes),namedTimeZonedImpl:t.namedTimeZonedImpl||e.namedTimeZonedImpl,initialView:e.initialView||t.initialView,elementDraggingImpl:e.elementDraggingImpl||t.elementDraggingImpl,optionChangeHandlers:s(s({},e.optionChangeHandlers),t.optionChangeHandlers),scrollGridImpl:t.scrollGridImpl||e.scrollGridImpl,contentTypeHandlers:s(s({},e.contentTypeHandlers),t.contentTypeHandlers),listenerRefiners:s(s({},e.listenerRefiners),t.listenerRefiners),optionRefiners:s(s({},e.optionRefiners),t.optionRefiners),propSetHandlers:s(s({},e.propSetHandlers),t.propSetHandlers)}}var rJ=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t}(rz);function oe(e,t,n,r){if(t[e])return t[e];var o=function e(t,n,r,o){var i=r[t],a=o[t],l=function(e){return i&&null!==i[e]?i[e]:a&&null!==a[e]?a[e]:null},u=l("component"),c=l("superType"),d=null;if(c){if(c===t)throw Error("Can't have a custom view type that references itself");d=oe(c,n,r,o)}return(!u&&d&&(u=d.component),u)?{type:t,component:u,defaults:s(s({},d?d.defaults:{}),i?i.rawOptions:{}),overrides:s(s({},d?d.overrides:{}),a?a.rawOptions:{})}:null}(e,t,n,r);return o&&(t[e]=o),o}rJ.prototype.classes={root:"fc-theme-standard",tableCellShaded:"fc-cell-shaded",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active"},rJ.prototype.baseIconClass="fc-icon",rJ.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"},rJ.prototype.rtlIconClasses={prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"},rJ.prototype.iconOverrideOption="buttonIcons",rJ.prototype.iconOverrideCustomButtonOption="icon",rJ.prototype.iconOverridePrefix="fc-icon-";var ot=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.rootElRef=rq(),t.handleRootEl=function(e){r9(t.rootElRef,e),t.props.elRef&&r9(t.props.elRef,e)},t}return a(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.hookProps;return rB(oi,{hookProps:n,didMount:t.didMount,willUnmount:t.willUnmount,elRef:this.handleRootEl},function(r){return rB(or,{hookProps:n,content:t.content,defaultContent:t.defaultContent,backupElRef:e.rootElRef},function(e,o){return t.children(r,os(t.classNames,n),e,o)})})},t}(r3),on=rY(0);function or(e){return rB(on.Consumer,null,function(t){return rB(oo,s({renderId:t},e))})}var oo=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.innerElRef=rq(),t}return a(t,e),t.prototype.render=function(){return this.props.children(this.innerElRef,this.renderInnerContent())},t.prototype.componentDidMount=function(){this.updateCustomContent()},t.prototype.componentDidUpdate=function(){this.updateCustomContent()},t.prototype.componentWillUnmount=function(){this.customContentInfo&&this.customContentInfo.destroy&&this.customContentInfo.destroy()},t.prototype.renderInnerContent=function(){var e=this.customContentInfo,t=this.getInnerContent(),n=this.getContentMeta(t);return e&&e.contentKey===n.contentKey?e&&(e.contentVal=t[n.contentKey]):(e&&(e.destroy&&e.destroy(),e=this.customContentInfo=null),n.contentKey&&(e=this.customContentInfo=s({contentKey:n.contentKey,contentVal:t[n.contentKey]},n.buildLifecycleFuncs()))),e?[]:t},t.prototype.getInnerContent=function(){var e=this.props,t=ol(e.content,e.hookProps);return void 0===t&&(t=ol(e.defaultContent,e.hookProps)),null==t?null:t},t.prototype.getContentMeta=function(e){var t=this.context.pluginHooks.contentTypeHandlers,n="",r=null;if(e){for(var o in t)if(void 0!==e[o]){n=o,r=t[o];break}}return{contentKey:n,buildLifecycleFuncs:r}},t.prototype.updateCustomContent=function(){this.customContentInfo&&this.customContentInfo.render(this.innerElRef.current||this.props.backupElRef.current,this.customContentInfo.contentVal)},t}(r3),oi=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.handleRootEl=function(e){t.rootEl=e,t.props.elRef&&r9(t.props.elRef,e)},t}return a(t,e),t.prototype.render=function(){return this.props.children(this.handleRootEl)},t.prototype.componentDidMount=function(){var e=this.props.didMount;e&&e(s(s({},this.props.hookProps),{el:this.rootEl}))},t.prototype.componentWillUnmount=function(){var e=this.props.willUnmount;e&&e(s(s({},this.props.hookProps),{el:this.rootEl}))},t}(r3);function oa(){var e,t,n=[];return function(r,o){return t&&tg(t,o)&&r===e||(e=r,t=o,n=os(r,o)),n}}function os(e,t){return"function"==typeof e&&(e=e(t)),na(e)}function ol(e,t){return"function"==typeof e?e(t,rB):e}var ou=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.normalizeClassNames=oa(),t}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r={view:t.viewApi},o=this.normalizeClassNames(n.viewClassNames,r);return rB(oi,{hookProps:r,didMount:n.viewDidMount,willUnmount:n.viewWillUnmount,elRef:e.elRef},function(t){return e.children(t,["fc-"+e.viewSpec.type+"-view","fc-view"].concat(o))})},t}(r3);function oc(e){return tf(e,od)}function od(e){var t,n="function"==typeof e?{component:e}:e,r=n.component;return n.content&&(r=(t=n,function(e){return rB(r1.Consumer,null,function(n){return rB(ou,{viewSpec:n.viewSpec},function(r,o){var i=s(s({},e),{nextDayThreshold:n.options.nextDayThreshold});return rB(ot,{hookProps:i,classNames:t.classNames,content:t.content,didMount:t.didMount,willUnmount:t.willUnmount,elRef:r},function(e,t,n,r){return rB("div",{className:o.concat(t).join(" "),ref:e},r)})})})})),{superType:n.type,component:r,rawOptions:n}}function op(e,t,n,r){var o=oc(e),i=oc(t.views),a=function e(t,n){var r,o={};for(r in t)oe(r,o,t,n);for(r in n)oe(r,o,t,n);return o}(o,i);return tf(a,function(e){return function e(t,n,r,o,i){var a,l,u,c=t.overrides.duration||t.defaults.duration||o.duration||r.duration,d=null,p="",f="",h={};if(c&&(d=(a=c,l=JSON.stringify(a),u=of[l],void 0===u&&(u=tR(a),of[l]=u),u))){var v=t_(d);p=v.unit,1===v.value&&(f=p,h=n[p]?n[p].rawOptions:{})}var g=function(e){var n=e.buttonText||{},r=t.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[t.type]?n[t.type]:null!=n[f]?n[f]:null},m=function(e){var n=e.buttonHints||{},r=t.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[t.type]?n[t.type]:null!=n[f]?n[f]:null};return{type:t.type,component:t.component,duration:d,durationUnit:p,singleUnit:f,optionDefaults:t.defaults,optionOverrides:s(s({},h),t.overrides),buttonTextOverride:g(o)||g(r)||t.overrides.buttonText,buttonTextDefault:g(i)||t.defaults.buttonText||g(tK)||t.type,buttonTitleOverride:m(o)||m(r)||t.overrides.buttonHint,buttonTitleDefault:m(i)||t.defaults.buttonHint||m(tK)}}(e,i,t,n,r)})}var of={},oh=function(){function e(e){this.props=e,this.nowDate=n7(e.nowInput,e.dateEnv),this.initHiddenDays()}return e.prototype.buildPrev=function(e,t,n){var r=this.props.dateEnv,o=r.subtract(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(o,-1,n)},e.prototype.buildNext=function(e,t,n){var r=this.props.dateEnv,o=r.add(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(o,1,n)},e.prototype.build=function(e,t,n){void 0===n&&(n=!0);var r,o,i,a,s,l,u,c,d=this.props;return i=this.buildValidRange(),i=this.trimHiddenDays(i),n&&(e=(r=e,null!=(o=i).start&&r<o.start?o.start:null!=o.end&&r>=o.end?new Date(o.end.valueOf()-1):r)),a=this.buildCurrentRangeInfo(e,t),s=/^(year|month|week|day)$/.test(a.unit),l=this.buildRenderRange(this.trimHiddenDays(a.range),a.unit,s),u=l=this.trimHiddenDays(l),d.showNonCurrentDates||(u=nR(u,a.range)),u=nR(u=this.adjustActiveRange(u),i),c=nw(a.range,i),{validRange:i,currentRange:a.range,currentRangeUnit:a.unit,isRangeAllDay:s,activeRange:u,renderRange:l,slotMinTime:d.slotMinTime,slotMaxTime:d.slotMaxTime,isValid:c,dateIncrement:this.buildDateIncrement(a.duration)}},e.prototype.buildValidRange=function(){var e=this.props.validRangeInput,t="function"==typeof e?e.call(this.props.calendarApi,this.nowDate):e;return this.refineRange(t)||{start:null,end:null}},e.prototype.buildCurrentRangeInfo=function(e,t){var n,r=this.props,o=null,i=null,a=null;return r.duration?(o=r.duration,i=r.durationUnit,a=this.buildRangeFromDuration(e,t,o,i)):(n=this.props.dayCount)?(i="day",a=this.buildRangeFromDayCount(e,t,n)):(a=this.buildCustomVisibleRange(e))?i=r.dateEnv.greatestWholeUnit(a.start,a.end).unit:(i=t_(o=this.getFallbackDuration()).unit,a=this.buildRangeFromDuration(e,t,o,i)),{duration:o,unit:i,range:a}},e.prototype.getFallbackDuration=function(){return tR({day:1})},e.prototype.adjustActiveRange=function(e){var t=this.props,n=t.dateEnv,r=t.usesMinMaxTime,o=t.slotMinTime,i=t.slotMaxTime,a=e.start,s=e.end;return r&&(0>tk(o)&&(a=tt(a),a=n.add(a,o)),tk(i)>1&&(s=tt(s),s=e5(s,-1),s=n.add(s,i))),{start:a,end:s}},e.prototype.buildRangeFromDuration=function(e,t,n,r){var o,i,a,s=this.props,l=s.dateEnv,u=s.dateAlignment;if(!u){var c=this.props.dateIncrement;u=c&&tx(c)<tx(n)?t_(c).unit:r}function d(){o=l.startOf(e,u),i=l.add(o,n),a={start:o,end:i}}return 1>=tk(n)&&this.isHiddenDay(o)&&(o=tt(o=this.skipHiddenDays(o,t))),d(),this.trimHiddenDays(a)||(e=this.skipHiddenDays(e,t),d()),a},e.prototype.buildRangeFromDayCount=function(e,t,n){var r,o=this.props,i=o.dateEnv,a=o.dateAlignment,s=0,l=e;a&&(l=i.startOf(l,a)),l=tt(l),r=l=this.skipHiddenDays(l,t);do r=e5(r,1),this.isHiddenDay(r)||(s+=1);while(s<n);return{start:l,end:r}},e.prototype.buildCustomVisibleRange=function(e){var t=this.props,n=t.visibleRangeInput,r="function"==typeof n?n.call(t.calendarApi,t.dateEnv.toDate(e)):n,o=this.refineRange(r);return o&&(null==o.start||null==o.end)?null:o},e.prototype.buildRenderRange=function(e,t,n){return e},e.prototype.buildDateIncrement=function(e){var t,n=this.props.dateIncrement;return n||((t=this.props.dateAlignment)?tR(1,t):e||tR({days:1}))},e.prototype.refineRange=function(e){if(e){var t,n,r,o,i=(t=e,n=this.props.dateEnv,r=null,o=null,(t.start&&(r=n.createMarker(t.start)),t.end&&(o=n.createMarker(t.end)),!r&&!o||r&&o&&o<r)?null:{start:r,end:o});return i&&(i=nS(i)),i}return null},e.prototype.initHiddenDays=function(){var e,t=this.props.hiddenDays||[],n=[],r=0;for(!1===this.props.weekends&&t.push(0,6),e=0;e<7;e+=1)(n[e]=-1!==t.indexOf(e))||(r+=1);if(!r)throw Error("invalid hiddenDays");this.isHiddenDayHash=n},e.prototype.trimHiddenDays=function(e){var t=e.start,n=e.end;return(t&&(t=this.skipHiddenDays(t)),n&&(n=this.skipHiddenDays(n,-1,!0)),null==t||null==n||t<n)?{start:t,end:n}:null},e.prototype.isHiddenDay=function(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]},e.prototype.skipHiddenDays=function(e,t,n){for(void 0===t&&(t=1),void 0===n&&(n=!1);this.isHiddenDayHash[(e.getUTCDay()+(n?t:0)+7)%7];)e=e5(e,t);return e},e}();function ov(e){for(var t in e)if(e[t].isFetching)return!0;return!1}function og(e,t,n,r){for(var o={},i=0,a=t;i<a.length;i++){var l=a[i];o[l.sourceId]=l}return n&&(o=om(o,n,r)),s(s({},e),o)}function om(e,t,n){return oy(e,tp(e,function(e){var r,o,i;return r=e,o=t,i=n,oD(r,i)?!i.options.lazyFetching||!r.fetchRange||r.isFetching||o.start<r.fetchRange.start||o.end>r.fetchRange.end:!r.latestFetchId}),t,!1,n)}function oy(e,t,n,r,o){var i={};for(var a in e){var s=e[a];t[a]?i[a]=oE(s,n,r,o):i[a]=s}return i}function oE(e,t,n,r){var o=r.options,i=r.calendarApi,a=r.pluginHooks.eventSourceDefs[e.sourceDefId],l=eU();return a.fetch({eventSource:e,range:t,isRefetch:n,context:r},function(n){var a=n.rawEvents;o.eventSourceSuccess&&(a=o.eventSourceSuccess.call(i,a,n.xhr)||a),e.success&&(a=e.success.call(i,a,n.xhr)||a),r.dispatch({type:"RECEIVE_EVENTS",sourceId:e.sourceId,fetchId:l,fetchRange:t,rawEvents:a})},function(n){console.warn(n.message,n),o.eventSourceFailure&&o.eventSourceFailure.call(i,n),e.failure&&e.failure(n),r.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:e.sourceId,fetchId:l,fetchRange:t,error:n})}),s(s({},e),{isFetching:!0,latestFetchId:l})}function oS(e,t){return tp(e,function(e){return oD(e,t)})}function oD(e,t){return!t.pluginHooks.eventSourceDefs[e.sourceDefId].ignoreRange}function ob(e,t){var n;if(t){n=[];for(var r=0,o=e;r<o.length;r++){var i=o[r],a=t(i);a?n.push(a):null==a&&n.push(i)}}else n=e;return n}function oC(e,t){return ni(e,function(e){return e.sourceId!==t})}function o$(e,t,n,r,o){var i=e.headerToolbar?oR(e.headerToolbar,e,t,n,r,o):null,a=e.footerToolbar?oR(e.footerToolbar,e,t,n,r,o):null;return{header:i,footer:a}}function oR(e,t,n,r,o,i){var a={},s=[],l=!1;for(var u in e){var c=o8(e[u],t,n,r,o,i);a[u]=c.widgets,s.push.apply(s,c.viewsWithButtons),l=l||c.hasTitle}return{sectionWidgets:a,viewsWithButtons:s,hasTitle:l}}function o8(e,t,n,r,o,i){var a="rtl"===t.direction,s=t.customButtons||{},l=n.buttonText||{},u=t.buttonText||{},c=n.buttonHints||{},d=t.buttonHints||{},p=e?e.split(" "):[],f=[],h=!1;return{widgets:p.map(function(e){return e.split(",").map(function(e){if("title"===e)return h=!0,{buttonName:e};if(n=s[e])v=function(e){n.click&&n.click.call(e.target,e,e.target)},(g=r.getCustomButtonIconClass(n))||(g=r.getIconClass(e,a))||(m=n.text),y=n.hint||n.text;else if(p=o[e]){f.push(e),v=function(){i.changeView(e)},(m=p.buttonTextOverride)||(g=r.getIconClass(e,a))||(m=p.buttonTextDefault);var n,p,v,g,m,y,E=p.buttonTextOverride||p.buttonTextDefault;y=e0(p.buttonTitleOverride||p.buttonTitleDefault||t.viewHint,[E,e],E)}else if(i[e]){if(v=function(){i[e]()},(m=l[e])||(g=r.getIconClass(e,a))||(m=u[e]),"prevYear"===e||"nextYear"===e){var S="prevYear"===e?"prev":"next";y=e0(c[S]||d[S],[u.year||"year","year",],u[e])}else y=function(t){return e0(c[e]||d[e],[u[t]||t,t,],u[e])}}return{buttonName:e,buttonClick:v,buttonIcon:g,buttonText:m,buttonHint:y}})}),viewsWithButtons:f,hasTitle:h}}var ow=r6({eventSourceDefs:[{ignoreRange:!0,parseMeta:function(e){return Array.isArray(e.events)?e.events:null},fetch:function(e,t){t({rawEvents:e.eventSource.meta})}}]}),oT=r6({eventSourceDefs:[{parseMeta:function(e){return"function"==typeof e.events?e.events:null},fetch:function(e,t,n){var r=e.context.dateEnv;rO(e.eventSource.meta.bind(null,nj(e.range,r)),function(e){t({rawEvents:e})},n)}}]});function ok(e,t,n,r,o){e=e.toUpperCase();var i,a,s=null;"GET"===e?t=(i=t,a=n,i+(-1===i.indexOf("?")?"?":"&")+ox(a)):s=ox(n);var l=new XMLHttpRequest;l.open(e,t,!0),"GET"!==e&&l.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),l.onload=function(){if(l.status>=200&&l.status<400){var e=!1,t=void 0;try{t=JSON.parse(l.responseText),e=!0}catch(n){}e?r(t,l):o("Failure parsing JSON",l)}else o("Request failed",l)},l.onerror=function(){o("Request failed",l)},l.send(s)}function ox(e){var t=[];for(var n in e)t.push(encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t.join("&")}var oM=r6({eventSourceRefiners:{method:String,extraParams:tJ,startParam:String,endParam:String,timeZoneParam:String},eventSourceDefs:[{parseMeta:function(e){return e.url&&("json"===e.format||!e.format)?{url:e.url,format:"json",method:(e.method||"GET").toUpperCase(),extraParams:e.extraParams,startParam:e.startParam,endParam:e.endParam,timeZoneParam:e.timeZoneParam}:null},fetch:function(e,t,n){var r,o,i,a,l,u,c,d,p,f,h=e.eventSource.meta,v=(r=h,o=e.range,i=e.context,d=i.dateEnv,p=i.options,f={},null==(a=r.startParam)&&(a=p.startParam),null==(l=r.endParam)&&(l=p.endParam),null==(u=r.timeZoneParam)&&(u=p.timeZoneParam),s(f,c="function"==typeof r.extraParams?r.extraParams():r.extraParams||{}),f[a]=d.formatIso(o.start),f[l]=d.formatIso(o.end),"local"!==d.timeZone&&(f[u]=d.timeZone),f);ok(h.method,h.url,v,function(e,n){t({rawEvents:e,xhr:n})},function(e,t){n({message:e,xhr:t})})}}]}),o_=r6({recurringTypes:[{parse:function(e,t){if(e.daysOfWeek||e.startTime||e.endTime||e.startRecur||e.endRecur){var n,r,o={daysOfWeek:e.daysOfWeek||null,startTime:e.startTime||null,endTime:e.endTime||null,startRecur:e.startRecur?t.createMarker(e.startRecur):null,endRecur:e.endRecur?t.createMarker(e.endRecur):null},i=void 0;return e.duration&&(i=e.duration),!i&&e.startTime&&e.endTime&&(i=(n=e.endTime,r=e.startTime,{years:n.years-r.years,months:n.months-r.months,days:n.days-r.days,milliseconds:n.milliseconds-r.milliseconds})),{allDayGuess:Boolean(!e.startTime&&!e.endTime),duration:i,typeData:o}}return null},expand:function(e,t,n){var r=nR(t,{start:e.startRecur,end:e.endRecur});return r?function e(t,n,r,o){for(var i=t?th(t):null,a=tt(r.start),s=r.end,l=[];a<s;){var u=void 0;(!i||i[a.getUTCDay()])&&(u=n?o.add(a,n):a,l.push(u)),a=e5(a,1)}return l}(e.daysOfWeek,e.startTime,r,n):[]}}],eventRefiners:{daysOfWeek:tJ,startTime:tR,endTime:tR,duration:tR,startRecur:tJ,endRecur:tJ}}),oI=r6({optionChangeHandlers:{events:function(e,t){oP([e],t)},eventSources:oP}});function oP(e,t){for(var n=tv(t.getCurrentData().eventSources),r=[],o=0,i=e;o<i.length;o++){for(var a=i[o],s=!1,l=0;l<n.length;l+=1)if(n[l]._raw===a){n.splice(l,1),s=!0;break}s||r.push(a)}for(var u=0,c=n;u<c.length;u++){var d=c[u];t.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:d.sourceId})}for(var p=0,f=r;p<f.length;p++){var h=f[p];t.calendarApi.addEventSource(h)}}var oN=[ow,oT,oM,o_,oI,r6({isLoadingFuncs:[function(e){return ov(e.eventSources)},],contentTypeHandlers:{html:function e(){var t=null,n="";return{render:function e(r,o){(r!==t||o!==n)&&(r.innerHTML=o),t=r,n=o},destroy:function e(){t.innerHTML="",t=null,n=""}}},domNodes:function e(){var t=null,n=[];function r(){n.forEach(eb),n=[],t=null}return{render:function e(o,i){var a=Array.prototype.slice.call(i);if(o!==t||!tO(n,a)){for(var s=0,l=a;s<l.length;s++){var u=l[s];o.appendChild(u)}r()}t=o,n=a},destroy:r}}},propSetHandlers:{dateProfile:function e(t,n){n.emitter.trigger("datesSet",s(s({},nj(t.activeRange,n.dateEnv)),{view:n.viewApi}))},eventStore:function e(t,n){var r=n.emitter;r.hasHandlers("eventsSet")&&r.trigger("eventsSet",re(t,n))}}}),],oH=function(){function e(e){this.drainedOption=e,this.isRunning=!1,this.isDirty=!1,this.pauseDepths={},this.timeoutId=0}return e.prototype.request=function(e){this.isDirty=!0,this.isPaused()||(this.clearTimeout(),null==e?this.tryDrain():this.timeoutId=setTimeout(this.tryDrain.bind(this),e))},e.prototype.pause=function(e){void 0===e&&(e="");var t=this.pauseDepths;t[e]=(t[e]||0)+1,this.clearTimeout()},e.prototype.resume=function(e,t){void 0===e&&(e="");var n=this.pauseDepths;e in n&&(t?delete n[e]:(n[e]-=1,n[e]<=0&&delete n[e]),this.tryDrain())},e.prototype.isPaused=function(){return Object.keys(this.pauseDepths).length},e.prototype.tryDrain=function(){if(!this.isRunning&&!this.isPaused()){for(this.isRunning=!0;this.isDirty;)this.isDirty=!1,this.drained();this.isRunning=!1}},e.prototype.clear=function(){this.clearTimeout(),this.isDirty=!1,this.pauseDepths={}},e.prototype.clearTimeout=function(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0)},e.prototype.drained=function(){this.drainedOption&&this.drainedOption()},e}(),oO=function(){function e(e,t){this.runTaskOption=e,this.drainedOption=t,this.queue=[],this.delayedRunner=new oH(this.drain.bind(this))}return e.prototype.request=function(e,t){this.queue.push(e),this.delayedRunner.request(t)},e.prototype.pause=function(e){this.delayedRunner.pause(e)},e.prototype.resume=function(e,t){this.delayedRunner.resume(e,t)},e.prototype.drain=function(){for(var e=this.queue;e.length;){for(var t=[],n=void 0;n=e.shift();)this.runTask(n),t.push(n);this.drained(t)}},e.prototype.runTask=function(e){this.runTaskOption&&this.runTaskOption(e)},e.prototype.drained=function(e){this.drainedOption&&this.drainedOption(e)},e}();function oA(e,t,n){var r;return r=/^(year|month)$/.test(e.currentRangeUnit)?e.currentRange:e.activeRange,n.formatRange(r.start,r.end,t1(t.titleFormat||function e(t){var n=t.currentRangeUnit;if("year"===n)return{year:"numeric"};if("month"===n)return{year:"numeric",month:"long"};var r=te(t.currentRange.start,t.currentRange.end);return null!==r&&r>1?{year:"numeric",month:"short",day:"numeric"}:{year:"numeric",month:"long",day:"numeric"}}(e)),{isEndExclusive:e.isRangeAllDay,defaultSeparator:t.titleRangeSeparator})}var oL=function(){function e(e){var t,n,r,o=this;this.computeOptionsData=tA(this._computeOptionsData),this.computeCurrentViewData=tA(this._computeCurrentViewData),this.organizeRawLocales=tA(ru),this.buildLocale=tA(rc),this.buildPluginHooks=(n=[],r=[],function(e,o){return t&&tO(e,n)&&tO(o,r)||(t=function e(t,n){var r={},o={reducers:[],isLoadingFuncs:[],contextInit:[],eventRefiners:{},eventDefMemberAdders:[],eventSourceRefiners:{},isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],viewContainerAppends:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,initialView:"",elementDraggingImpl:null,optionChangeHandlers:{},scrollGridImpl:null,contentTypeHandlers:{},listenerRefiners:{},optionRefiners:{},propSetHandlers:{}};function i(e){for(var t=0,n=e;t<n.length;t++){var a=n[t];r[a.id]||(r[a.id]=!0,i(a.deps),o=rQ(o,a))}}return t&&i(t),i(n),o}(e,o)),n=e,r=o,t}),this.buildDateEnv=tA(oU),this.buildTheme=tA(oW),this.parseToolbars=tA(o$),this.buildViewSpecs=tA(op),this.buildDateProfileGenerator=tL(oV),this.buildViewApi=tA(oz),this.buildViewUiProps=tL(oG),this.buildEventUiBySource=tA(oF,tg),this.buildEventUiBases=tA(oB),this.parseContextBusinessHours=tL(oj),this.buildTitle=tA(oA),this.emitter=new rA,this.actionRunner=new oO(this._handleAction.bind(this),this.updateData.bind(this)),this.currentCalendarOptionsInput={},this.currentCalendarOptionsRefined={},this.currentViewOptionsInput={},this.currentViewOptionsRefined={},this.currentCalendarOptionsRefiners={},this.getCurrentData=function(){return o.data},this.dispatch=function(e){o.actionRunner.request(e)},this.props=e,this.actionRunner.pause();var i,a,l,u={},c=this.computeOptionsData(e.optionOverrides,u,e.calendarApi),d=c.calendarOptions.initialView||c.pluginHooks.initialView,p=this.computeCurrentViewData(d,c,e.optionOverrides,u);e.calendarApi.currentDataManager=this,this.emitter.setThisContext(e.calendarApi),this.emitter.setOptions(p.options);var f=(i=c.calendarOptions,a=c.dateEnv,null!=(l=i.initialDate)?a.createMarker(l):n7(i.now,a)),h=p.dateProfileGenerator.build(f);nk(h.activeRange,f)||(f=h.currentRange.start);for(var v={dateEnv:c.dateEnv,options:c.calendarOptions,pluginHooks:c.pluginHooks,calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},g=0,m=c.pluginHooks.contextInit;g<m.length;g++)(0,m[g])(v);for(var y,E,S,D,b=(y=c.calendarOptions,E=h,S=v,D=E?E.activeRange:null,og({},function e(t,n){var r=n9(n),o=[].concat(t.eventSources||[]),i=[];t.initialEvents&&o.unshift(t.initialEvents),t.events&&o.unshift(t.events);for(var a=0,s=o;a<s.length;a++){var l=n5(s[a],n,r);l&&i.push(l)}return i}(y,S),D,S)),C={dynamicOptionOverrides:u,currentViewType:d,currentDate:f,dateProfile:h,businessHours:this.parseContextBusinessHours(v),eventSources:b,eventUiBases:{},eventStore:nr(),renderableEventStore:nr(),dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null,selectionConfig:this.buildViewUiProps(v).selectionConfig},$=s(s({},v),C),R=0,w=c.pluginHooks.reducers;R<w.length;R++)s(C,(0,w[R])(null,null,$));oq(C,v)&&this.emitter.trigger("loading",!0),this.state=C,this.updateData(),this.actionRunner.resume()}return e.prototype.resetOptions=function(e,t){var n=this.props;n.optionOverrides=t?s(s({},n.optionOverrides),e):e,this.actionRunner.request({type:"NOTHING"})},e.prototype._handleAction=function(e){var t,n,r,o,i,a=this.props,l=this.state,u=this.emitter,c=(t=l.dynamicOptionOverrides,n=e,"SET_OPTION"===n.type?s(s({},t),((r={})[n.optionName]=n.rawOptionValue,r)):t),d=this.computeOptionsData(a.optionOverrides,c,a.calendarApi),p=(o=l.currentViewType,i=e,"CHANGE_VIEW_TYPE"===i.type&&(o=i.viewType),o),f=this.computeCurrentViewData(p,d,a.optionOverrides,c);a.calendarApi.currentDataManager=this,u.setThisContext(a.calendarApi),u.setOptions(f.options);var h,v,g={dateEnv:d.dateEnv,options:d.calendarOptions,pluginHooks:d.pluginHooks,calendarApi:a.calendarApi,dispatch:this.dispatch,emitter:u,getCurrentData:this.getCurrentData},m=l.currentDate,y=l.dateProfile;this.data&&this.data.dateProfileGenerator!==f.dateProfileGenerator&&(y=f.dateProfileGenerator.build(m)),m=(h=m,v=e,"CHANGE_DATE"===v.type?v.dateMarker:h),y=function e(t,n,r,o){var i;switch(n.type){case"CHANGE_VIEW_TYPE":return o.build(n.dateMarker||r);case"CHANGE_DATE":return o.build(n.dateMarker);case"PREV":if((i=o.buildPrev(t,r)).isValid)return i;break;case"NEXT":if((i=o.buildNext(t,r)).isValid)return i}return t}(y,e,m,f.dateProfileGenerator),"PREV"!==e.type&&"NEXT"!==e.type&&nk(y.currentRange,m)||(m=y.currentRange.start);for(var E=function e(t,n,r,o){var i,a,l,u,c,d,p,f,h=r?r.activeRange:null;switch(n.type){case"ADD_EVENT_SOURCES":return og(t,n.sources,h,o);case"REMOVE_EVENT_SOURCE":return i=t,a=n.sourceId,tp(i,function(e){return e.sourceId!==a});case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":if(r)return om(t,h,o);return t;case"FETCH_EVENT_SOURCES":return oy(t,n.sourceIds?th(n.sourceIds):oS(t,o),h,n.isRefetch||!1,o);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":return l=t,u=n.sourceId,c=n.fetchId,d=n.fetchRange,f=l[u],f&&c===f.latestFetchId?s(s({},l),((p={})[u]=s(s({},f),{isFetching:!1,fetchRange:d}),p)):l;case"REMOVE_ALL_EVENT_SOURCES":return{};default:return t}}(l.eventSources,e,y,g),S=function e(t,n,r,o,i){switch(n.type){case"RECEIVE_EVENTS":return function e(t,n,r,o,i,a){if(n&&r===n.latestFetchId){var s,l,u,c,d,p=ne((s=i,l=n,u=a,c=u.options.eventDataTransform,d=l?l.eventDataTransform:null,d&&(s=ob(s,d)),c&&(s=ob(s,c)),s),n,a);return o&&(p=tD(p,o,a)),no(oC(t,n.sourceId),p)}return t}(t,r[n.sourceId],n.fetchId,n.fetchRange,n.rawEvents,i);case"ADD_EVENTS":var a,s,l,u;return a=t,s=n.eventStore,l=o?o.activeRange:null,u=i,l&&(s=tD(s,l,u)),no(a,s);case"RESET_EVENTS":return n.eventStore;case"MERGE_EVENTS":return no(t,n.eventStore);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":if(o)return tD(t,o.activeRange,i);return t;case"REMOVE_EVENTS":return function e(t,n){var r=t.defs,o=t.instances,i={},a={};for(var s in r)n.defs[s]||(i[s]=r[s]);for(var l in o)!n.instances[l]&&i[o[l].defId]&&(a[l]=o[l]);return{defs:i,instances:a}}(t,n.eventStore);case"REMOVE_EVENT_SOURCE":return oC(t,n.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return ni(t,function(e){return!e.sourceId});case"REMOVE_ALL_EVENTS":return nr();default:return t}}(l.eventStore,e,E,y,g),D=ov(E)&&!f.options.progressiveEventRendering&&l.renderableEventStore||S,b=this.buildViewUiProps(g),C=b.eventUiSingleBase,$=b.selectionConfig,R=this.buildEventUiBySource(E),w=this.buildEventUiBases(D.defs,C,R),T={dynamicOptionOverrides:c,currentViewType:p,currentDate:m,dateProfile:y,eventSources:E,eventStore:S,renderableEventStore:D,selectionConfig:$,eventUiBases:w,businessHours:this.parseContextBusinessHours(g),dateSelection:function e(t,n){switch(n.type){case"UNSELECT_DATES":return null;case"SELECT_DATES":return n.selection;default:return t}}(l.dateSelection,e),eventSelection:function e(t,n){switch(n.type){case"UNSELECT_EVENT":return"";case"SELECT_EVENT":return n.eventInstanceId;default:return t}}(l.eventSelection,e),eventDrag:function e(t,n){var r;switch(n.type){case"UNSET_EVENT_DRAG":return null;case"SET_EVENT_DRAG":return{affectedEvents:(r=n.state).affectedEvents,mutatedEvents:r.mutatedEvents,isEvent:r.isEvent};default:return t}}(l.eventDrag,e),eventResize:function e(t,n){var r;switch(n.type){case"UNSET_EVENT_RESIZE":return null;case"SET_EVENT_RESIZE":return{affectedEvents:(r=n.state).affectedEvents,mutatedEvents:r.mutatedEvents,isEvent:r.isEvent};default:return t}}(l.eventResize,e)},k=s(s({},g),T),x=0,M=d.pluginHooks.reducers;x<M.length;x++)s(T,(0,M[x])(l,e,k));var _=oq(l,g),I=oq(T,g);!_&&I?u.trigger("loading",!0):_&&!I&&u.trigger("loading",!1),this.state=T,a.onAction&&a.onAction(e)},e.prototype.updateData=function(){var e,t,n,r,o,i,a,l,u,c=this.props,d=this.state,p=this.data,f=this.computeOptionsData(c.optionOverrides,d.dynamicOptionOverrides,c.calendarApi),h=this.computeCurrentViewData(d.currentViewType,f,c.optionOverrides,d.dynamicOptionOverrides),v=this.data=s(s(s({viewTitle:this.buildTitle(d.dateProfile,h.options,f.dateEnv),calendarApi:c.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},f),h),d),g=f.pluginHooks.optionChangeHandlers,m=p&&p.calendarOptions,y=f.calendarOptions;if(m&&m!==y)for(var E in m.timeZone!==y.timeZone&&(d.eventSources=v.eventSources=(e=v.eventSources,t=d.dateProfile,n=v,r=t?t.activeRange:null,oy(e,oS(e,n),r,!0,n)),d.eventStore=v.eventStore=(o=v.eventStore,i=p.dateEnv,a=v.dateEnv,l=o.defs,u=tf(o.instances,function(e){var t=l[e.defId];return t.allDay||t.recurringDef?e:s(s({},e),{range:{start:a.createMarker(i.toDate(e.range.start,e.forcedStartTzo)),end:a.createMarker(i.toDate(e.range.end,e.forcedEndTzo))},forcedStartTzo:a.canComputeOffset?null:e.forcedStartTzo,forcedEndTzo:a.canComputeOffset?null:e.forcedEndTzo})}),{defs:l,instances:u})),g)m[E]!==y[E]&&g[E](y[E],v);c.onData&&c.onData(v)},e.prototype._computeOptionsData=function(e,t,n){var r=this.processRawCalendarOptions(e,t),o=r.refinedOptions,i=r.pluginHooks,a=r.localeDefaults,s=r.availableLocaleData;oY(r.extra);var l=this.buildDateEnv(o.timeZone,o.locale,o.weekNumberCalculation,o.firstDay,o.weekText,i,s,o.defaultRangeSeparator),u=this.buildViewSpecs(i.views,e,t,a),c=this.buildTheme(o,i),d=this.parseToolbars(o,e,c,u,n);return{calendarOptions:o,pluginHooks:i,dateEnv:l,viewSpecs:u,theme:c,toolbarConfig:d,localeDefaults:a,availableRawLocales:s.map}},e.prototype.processRawCalendarOptions=function(e,t){var n=t6([tK,e,t,]),r=n.locales,o=n.locale,i=this.organizeRawLocales(r),a=i.map,l=this.buildLocale(o||i.defaultCode,a).options,u=this.buildPluginHooks(e.plugins||[],oN),c=this.currentCalendarOptionsRefiners=s(s(s(s(s({},t4),t3),t2),u.listenerRefiners),u.optionRefiners),d={},p=t6([tK,l,e,t,]),f={},h=this.currentCalendarOptionsInput,v=this.currentCalendarOptionsRefined,g=!1;for(var m in p)"plugins"!==m&&(p[m]===h[m]||t5[m]&&m in h&&t5[m](h[m],p[m])?f[m]=v[m]:c[m]?(f[m]=c[m](p[m]),g=!0):d[m]=h[m]);return g&&(this.currentCalendarOptionsInput=p,this.currentCalendarOptionsRefined=f),{rawOptions:this.currentCalendarOptionsInput,refinedOptions:this.currentCalendarOptionsRefined,pluginHooks:u,availableLocaleData:i,localeDefaults:l,extra:d}},e.prototype._computeCurrentViewData=function(e,t,n,r){var o=t.viewSpecs[e];if(!o)throw Error('viewType "'+e+"\" is not available. Please make sure you've loaded all neccessary plugins");var i=this.processRawViewOptions(o,t.pluginHooks,t.localeDefaults,n,r),a=i.refinedOptions;oY(i.extra);var s=this.buildDateProfileGenerator({dateProfileGeneratorClass:o.optionDefaults.dateProfileGeneratorClass,duration:o.duration,durationUnit:o.durationUnit,usesMinMaxTime:o.optionDefaults.usesMinMaxTime,dateEnv:t.dateEnv,calendarApi:this.props.calendarApi,slotMinTime:a.slotMinTime,slotMaxTime:a.slotMaxTime,showNonCurrentDates:a.showNonCurrentDates,dayCount:a.dayCount,dateAlignment:a.dateAlignment,dateIncrement:a.dateIncrement,hiddenDays:a.hiddenDays,weekends:a.weekends,nowInput:a.now,validRangeInput:a.validRange,visibleRangeInput:a.visibleRange,monthMode:a.monthMode,fixedWeekCount:a.fixedWeekCount});return{viewSpec:o,options:a,dateProfileGenerator:s,viewApi:this.buildViewApi(e,this.getCurrentData,t.dateEnv)}},e.prototype.processRawViewOptions=function(e,t,n,r,o){var i=t6([tK,e.optionDefaults,n,r,e.optionOverrides,o,]),a=s(s(s(s(s(s({},t4),t3),t2),t7),t.listenerRefiners),t.optionRefiners),l={},u=this.currentViewOptionsInput,c=this.currentViewOptionsRefined,d=!1,p={};for(var f in i)i[f]===u[f]||t5[f]&&t5[f](i[f],u[f])?l[f]=c[f]:(i[f]===this.currentCalendarOptionsInput[f]||t5[f]&&t5[f](i[f],this.currentCalendarOptionsInput[f])?f in this.currentCalendarOptionsRefined&&(l[f]=this.currentCalendarOptionsRefined[f]):a[f]?l[f]=a[f](i[f]):p[f]=i[f],d=!0);return d&&(this.currentViewOptionsInput=i,this.currentViewOptionsRefined=l),{rawOptions:this.currentViewOptionsInput,refinedOptions:this.currentViewOptionsRefined,extra:p}},e}();function oU(e,t,n,r,o,i,a,s){var l=rc(t||a.defaultCode,a.map);return new ri({calendarSystem:"gregory",timeZone:e,namedTimeZoneImpl:i.namedTimeZonedImpl,locale:l,weekNumberCalculation:n,firstDay:r,weekText:o,cmdFormatter:i.cmdFormatter,defaultSeparator:s})}function oW(e,t){return new(t.themeClasses[e.themeSystem]||rJ)(e)}function oV(e){return new(e.dateProfileGeneratorClass||oh)(e)}function oz(e,t,n){return new n3(e,t,n)}function oF(e){return tf(e,function(e){return e.ui})}function oB(e,t,n){var r={"":t};for(var o in e){var i=e[o];i.sourceId&&n[i.sourceId]&&(r[o]=n[i.sourceId])}return r}function oG(e){var t=e.options;return{eventUiSingleBase:nu({display:t.eventDisplay,editable:t.editable,startEditable:t.eventStartEditable,durationEditable:t.eventDurationEditable,constraint:t.eventConstraint,overlap:"boolean"==typeof t.eventOverlap?t.eventOverlap:void 0,allow:t.eventAllow,backgroundColor:t.eventBackgroundColor,borderColor:t.eventBorderColor,textColor:t.eventTextColor,color:t.eventColor},e),selectionConfig:nu({constraint:t.selectConstraint,overlap:"boolean"==typeof t.selectOverlap?t.selectOverlap:void 0,allow:t.selectAllow},e)}}function oq(e,t){for(var n=0,r=t.pluginHooks.isLoadingFuncs;n<r.length;n++)if((0,r[n])(e))return!0;return!1}function oj(e){return rh(e.options.businessHours,e)}function oY(e,t){for(var n in e)console.warn("Unknown option '"+n+"'"+(t?" for view '"+t+"'":""))}var oZ=function(e){function t(t){var n=e.call(this,t)||this;return n.handleData=function(e){n.dataManager?n.setState(e):n.state=e},n.dataManager=new oL({optionOverrides:t.optionOverrides,calendarApi:t.calendarApi,onData:n.handleData}),n}return a(t,e),t.prototype.render=function(){return this.props.children(this.state)},t.prototype.componentDidUpdate=function(e){var t=this.props.optionOverrides;t!==e.optionOverrides&&this.dataManager.resetOptions(t)},t}(rF),oX=function(){function e(){this.strictOrder=!1,this.allowReslicing=!1,this.maxCoord=-1,this.maxStackCnt=-1,this.levelCoords=[],this.entriesByLevel=[],this.stackCnts={}}return e.prototype.addSegs=function(e){for(var t=[],n=0,r=e;n<r.length;n++){var o=r[n];this.insertEntry(o,t)}return t},e.prototype.insertEntry=function(e,t){var n=this.findInsertion(e);return this.isInsertionValid(n,e)?(this.insertEntryAt(e,n),1):this.handleInvalidInsertion(n,e,t)},e.prototype.isInsertionValid=function(e,t){return(-1===this.maxCoord||e.levelCoord+t.thickness<=this.maxCoord)&&(-1===this.maxStackCnt||e.stackCnt<this.maxStackCnt)},e.prototype.handleInvalidInsertion=function(e,t,n){return this.allowReslicing&&e.touchingEntry?this.splitEntry(t,e.touchingEntry,n):(n.push(t),0)},e.prototype.splitEntry=function(e,t,n){var r=0,o=[],i=e.span,a=t.span;return(i.start<a.start&&(r+=this.insertEntry({index:e.index,thickness:e.thickness,span:{start:i.start,end:a.start}},o)),i.end>a.end&&(r+=this.insertEntry({index:e.index,thickness:e.thickness,span:{start:a.end,end:i.end}},o)),r)?(n.push.apply(n,l([{index:e.index,thickness:e.thickness,span:o3(a,i)}],o)),r):(n.push(e),0)},e.prototype.insertEntryAt=function(e,t){var n=this.entriesByLevel,r=this.levelCoords;-1===t.lateral?(o2(r,t.level,t.levelCoord),o2(n,t.level,[e])):o2(n[t.level],t.lateral,e),this.stackCnts[o1(e)]=t.stackCnt},e.prototype.findInsertion=function(e){for(var t=this.levelCoords,n=this.entriesByLevel,r=this.strictOrder,o=this.stackCnts,i=t.length,a=0,s=-1,l=-1,u=null,c=0,d=0;d<i;d+=1){var p=t[d];if(!r&&p>=a+e.thickness)break;for(var f=n[d],h=void 0,v=o5(f,e.span.start,o0),g=v[0]+v[1];(h=f[g])&&h.span.start<e.span.end;){var m=p+h.thickness;m>a&&(a=m,u=h,s=d,l=g),m===a&&(c=Math.max(c,o[o1(h)]+1)),g+=1}}var y=0;if(u)for(y=s+1;y<i&&t[y]<a;)y+=1;var E=-1;return y<i&&t[y]===a&&(E=o5(n[y],e.span.end,o0)[0]),{touchingLevel:s,touchingLateral:l,touchingEntry:u,stackCnt:c,levelCoord:a,level:y,lateral:E}},e.prototype.toRects=function(){for(var e=this.entriesByLevel,t=this.levelCoords,n=e.length,r=[],o=0;o<n;o+=1)for(var i=e[o],a=t[o],l=0,u=i;l<u.length;l++){var c=u[l];r.push(s(s({},c),{levelCoord:a}))}return r},e}();function o0(e){return e.span.end}function o1(e){return e.index+":"+e.span.start}function o4(e){for(var t=[],n=0,r=e;n<r.length;n++){for(var o=r[n],i=[],a={span:o.span,entries:[o]},s=0,l=t;s<l.length;s++){var u=l[s];o3(u.span,a.span)?a={entries:u.entries.concat(a.entries),span:oK(u.span,a.span)}:i.push(u)}i.push(a),t=i}return t}function oK(e,t){return{start:Math.min(e.start,t.start),end:Math.max(e.end,t.end)}}function o3(e,t){var n=Math.max(e.start,t.start),r=Math.min(e.end,t.end);return n<r?{start:n,end:r}:null}function o2(e,t,n){e.splice(t,0,n)}function o5(e,t,n){var r=0,o=e.length;if(!o||t<n(e[r]))return[0,0];if(t>n(e[o-1]))return[o,0];for(;r<o;){var i=Math.floor(r+(o-r)/2),a=n(e[i]);if(t<a)o=i;else{if(!(t>a))return[i,1];r=i+1}}return[r,0]}var o9=function(){function e(e){this.component=e.component,this.isHitComboAllowed=e.isHitComboAllowed||null}return e.prototype.destroy=function(){},e}();function o7(e){var t;return(t={})[e.component.uid]=e,t}var o6={},oQ=function(){function e(e,t){this.emitter=new rA}return e.prototype.destroy=function(){},e.prototype.setMirrorIsVisible=function(e){},e.prototype.setMirrorNeedsRevert=function(e){},e.prototype.setAutoScrollEnabled=function(e){},e}(),oJ={},ie={startTime:tR,duration:tR,create:Boolean,sourceId:String};function it(e){var t=tQ(e,ie),n=t.refined,r=t.extra;return{startTime:n.startTime||null,duration:n.duration||null,create:null==n.create||n.create,sourceId:n.sourceId,leftoverProps:r}}var ir=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this,t=this.props.widgetGroups.map(function(t){return e.renderWidgetGroup(t)});return rB.apply(void 0,l(["div",{className:"fc-toolbar-chunk"}],t))},t.prototype.renderWidgetGroup=function(e){for(var t=this.props,n=this.context.theme,r=[],o=!0,i=0,a=e;i<a.length;i++){var s=a[i],u=s.buttonName,c=s.buttonClick,d=s.buttonText,p=s.buttonIcon,f=s.buttonHint;if("title"===u)o=!1,r.push(rB("h2",{className:"fc-toolbar-title",id:t.titleId},t.title));else{var h=u===t.activeButton,v=!t.isTodayEnabled&&"today"===u||!t.isPrevEnabled&&"prev"===u||!t.isNextEnabled&&"next"===u,g=["fc-"+u+"-button",n.getClass("button")];h&&g.push(n.getClass("buttonActive")),r.push(rB("button",{type:"button",title:"function"==typeof f?f(t.navUnit):f,disabled:v,"aria-pressed":h,className:g.join(" "),onClick:c},d||(p?rB("span",{className:p}):"")))}}if(r.length>1){var m=o&&n.getClass("buttonGroup")||"";return rB.apply(void 0,l(["div",{className:m}],r))}return r[0]},t}(r3),io=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e,t,n=this.props,r=n.model,o=n.extraClassName,i=!1,a=r.sectionWidgets,s=a.center;return a.left?(i=!0,e=a.left):e=a.start,a.right?(i=!0,t=a.right):t=a.end,rB("div",{className:[o||"","fc-toolbar",i?"fc-toolbar-ltr":"",].join(" ")},this.renderSection("start",e||[]),this.renderSection("center",s||[]),this.renderSection("end",t||[]))},t.prototype.renderSection=function(e,t){var n=this.props;return rB(ir,{key:e,widgetGroups:t,title:n.title,navUnit:n.navUnit,activeButton:n.activeButton,isTodayEnabled:n.isTodayEnabled,isPrevEnabled:n.isPrevEnabled,isNextEnabled:n.isNextEnabled,titleId:n.titleId})},t}(r3),ii=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={availableWidth:null},t.handleEl=function(e){t.el=e,r9(t.props.elRef,e),t.updateAvailableWidth()},t.handleResize=function(){t.updateAvailableWidth()},t}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.state,n=e.aspectRatio,r=["fc-view-harness",n||e.liquid||e.height?"fc-view-harness-active":"fc-view-harness-passive"],o="",i="";return n?null!==t.availableWidth?o=t.availableWidth/n:i=1/n*100+"%":o=e.height||"",rB("div",{"aria-labelledby":e.labeledById,ref:this.handleEl,className:r.join(" "),style:{height:o,paddingBottom:i}},e.children)},t.prototype.componentDidMount=function(){this.context.addResizeHandler(this.handleResize)},t.prototype.componentWillUnmount=function(){this.context.removeResizeHandler(this.handleResize)},t.prototype.updateAvailableWidth=function(){this.el&&this.props.aspectRatio&&this.setState({availableWidth:this.el.offsetWidth})},t}(r3),ia=function(e){function t(t){var n=e.call(this,t)||this;return n.handleSegClick=function(e,t){var r=n.component,o=r.context,i=nI(t);if(i&&r.isValidSegDownEl(e.target)){var a=eC(e.target,".fc-event-forced-url"),s=a?a.querySelector("a[href]").href:"";o.emitter.trigger("eventClick",{el:t,event:new nQ(r.context,i.eventRange.def,i.eventRange.instance),jsEvent:e,view:o.viewApi}),s&&!e.defaultPrevented&&(window.location.href=s)}},n.destroy=eP(t.el,"click",".fc-event",n.handleSegClick),n}return a(t,e),t}(o9),is=function(e){function t(t){var n,r,o,i,a=e.call(this,t)||this;return a.handleEventElRemove=function(e){e===a.currentSegEl&&a.handleSegLeave(null,a.currentSegEl)},a.handleSegEnter=function(e,t){nI(t)&&(a.currentSegEl=t,a.triggerEvent("eventMouseEnter",e,t))},a.handleSegLeave=function(e,t){a.currentSegEl&&(a.currentSegEl=null,a.triggerEvent("eventMouseLeave",e,t))},a.removeHoverListeners=(n=t.el,r=a.handleSegEnter,o=a.handleSegLeave,eP(n,"mouseover",".fc-event",function(e,t){if(t!==i){i=t,r(e,t);var n=function(e){i=null,o(e,t),t.removeEventListener("mouseleave",n)};t.addEventListener("mouseleave",n)}})),a}return a(t,e),t.prototype.destroy=function(){this.removeHoverListeners()},t.prototype.triggerEvent=function(e,t,n){var r=this.component,o=r.context,i=nI(n);(!t||r.isValidSegDownEl(t.target))&&o.emitter.trigger(e,{el:n,event:new nQ(o,i.eventRange.def,i.eventRange.instance),jsEvent:t,view:o.viewApi})},t}(o9),il=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.buildViewContext=tA(r4),t.buildViewPropTransformers=tA(ic),t.buildToolbarProps=tA(iu),t.headerRef=rq(),t.footerRef=rq(),t.interactionsStore={},t.state={viewLabelId:e_()},t.registerInteractiveComponent=function(e,n){var r,o,i=(r=e,o=n,{component:r,el:o.el,useEventCenter:null==o.useEventCenter||o.useEventCenter,isHitComboAllowed:o.isHitComboAllowed||null}),a=[ia,is,].concat(t.props.pluginHooks.componentInteractions).map(function(e){return new e(i)});t.interactionsStore[e.uid]=a,o6[e.uid]=i},t.unregisterInteractiveComponent=function(e){var n=t.interactionsStore[e.uid];if(n){for(var r=0,o=n;r<o.length;r++)o[r].destroy();delete t.interactionsStore[e.uid]}delete o6[e.uid]},t.resizeRunner=new oH(function(){t.props.emitter.trigger("_resize",!0),t.props.emitter.trigger("windowResize",{view:t.props.viewApi})}),t.handleWindowResize=function(e){var n=t.props.options;n.handleWindowResize&&e.target===window&&t.resizeRunner.request(n.windowResizeDelay)},t}return a(t,e),t.prototype.render=function(){var e,t=this.props,n=t.toolbarConfig,r=t.options,o=this.buildToolbarProps(t.viewSpec,t.dateProfile,t.dateProfileGenerator,t.currentDate,n7(t.options.now,t.dateEnv),t.viewTitle),i=!1,a="";t.isHeightAuto||t.forPrint?a="":null!=r.height?i=!0:null!=r.contentHeight?a=r.contentHeight:e=Math.max(r.aspectRatio,.5);var l=this.buildViewContext(t.viewSpec,t.viewApi,t.options,t.dateProfileGenerator,t.dateEnv,t.theme,t.pluginHooks,t.dispatch,t.getCurrentData,t.emitter,t.calendarApi,this.registerInteractiveComponent,this.unregisterInteractiveComponent),u=n.header&&n.header.hasTitle?this.state.viewLabelId:"";return rB(r1.Provider,{value:l},n.header&&rB(io,s({ref:this.headerRef,extraClassName:"fc-header-toolbar",model:n.header,titleId:u},o)),rB(ii,{liquid:i,height:a,aspectRatio:e,labeledById:u},this.renderView(t),this.buildAppendContent()),n.footer&&rB(io,s({ref:this.footerRef,extraClassName:"fc-footer-toolbar",model:n.footer,titleId:""},o)))},t.prototype.componentDidMount=function(){var e=this.props;this.calendarInteractions=e.pluginHooks.calendarInteractions.map(function(t){return new t(e)}),window.addEventListener("resize",this.handleWindowResize);var t=e.pluginHooks.propSetHandlers;for(var n in t)t[n](e[n],e)},t.prototype.componentDidUpdate=function(e){var t=this.props,n=t.pluginHooks.propSetHandlers;for(var r in n)t[r]!==e[r]&&n[r](t[r],t)},t.prototype.componentWillUnmount=function(){window.removeEventListener("resize",this.handleWindowResize),this.resizeRunner.clear();for(var e=0,t=this.calendarInteractions;e<t.length;e++)t[e].destroy();this.props.emitter.trigger("_unmount")},t.prototype.buildAppendContent=function(){var e=this.props,t=e.pluginHooks.viewContainerAppends.map(function(t){return t(e)});return rB.apply(void 0,l([rj,{}],t))},t.prototype.renderView=function(e){for(var t=e.pluginHooks,n=e.viewSpec,r={dateProfile:e.dateProfile,businessHours:e.businessHours,eventStore:e.renderableEventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,isHeightAuto:e.isHeightAuto,forPrint:e.forPrint},o=this.buildViewPropTransformers(t.viewPropsTransformers),i=0,a=o;i<a.length;i++)s(r,a[i].transform(r,e));return rB(n.component,s({},r))},t}(rK);function iu(e,t,n,r,o,i){var a=n.build(o,void 0,!1),s=n.buildPrev(t,r,!1),l=n.buildNext(t,r,!1);return{title:i,activeButton:e.type,navUnit:e.singleUnit,isTodayEnabled:a.isValid&&!nk(t.currentRange,o),isPrevEnabled:s.isValid,isNextEnabled:l.isValid}}function ic(e){return e.map(function(e){return new e})}var id=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={forPrint:!1},t.handleBeforePrint=function(){t.setState({forPrint:!0})},t.handleAfterPrint=function(){t.setState({forPrint:!1})},t}return a(t,e),t.prototype.render=function(){var e=this.props,t=e.options,n=this.state.forPrint,r=n||"auto"===t.height||"auto"===t.contentHeight,o=r||null==t.height?"":t.height,i=["fc",n?"fc-media-print":"fc-media-screen","fc-direction-"+t.direction,e.theme.getClass("root"),];return rS()||i.push("fc-liquid-hack"),e.children(i,o,r,n)},t.prototype.componentDidMount=function(){var e=this.props.emitter;e.on("_beforeprint",this.handleBeforePrint),e.on("_afterprint",this.handleAfterPrint)},t.prototype.componentWillUnmount=function(){var e=this.props.emitter;e.off("_beforeprint",this.handleBeforePrint),e.off("_afterprint",this.handleAfterPrint)},t}(r3);function ip(e,t){return!e||t>10?t1({weekday:"short"}):t>1?t1({weekday:"short",month:"numeric",day:"numeric",omitCommas:!0}):t1({weekday:"long"})}var ih="fc-col-header-cell";function iv(e){return e.text}var ig=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.context,t=e.dateEnv,n=e.options,r=e.theme,o=e.viewApi,i=this.props,a=i.date,l=i.dateProfile,u=r$(a,i.todayRange,null,l),c=[ih].concat(rR(u,r)),d=t.format(a,i.dayHeaderFormat),p=!u.isDisabled&&i.colCnt>1?rT(this.context,a):{},f=s(s(s({date:t.toDate(a),view:o},i.extraHookProps),{text:d}),u);return rB(ot,{hookProps:f,classNames:n.dayHeaderClassNames,content:n.dayHeaderContent,defaultContent:iv,didMount:n.dayHeaderDidMount,willUnmount:n.dayHeaderWillUnmount},function(e,t,n,r){return rB("th",s({ref:e,role:"columnheader",className:c.concat(t).join(" "),"data-date":u.isDisabled?void 0:tP(a),colSpan:i.colSpan},i.extraDataAttrs),rB("div",{className:"fc-scrollgrid-sync-inner"},!u.isDisabled&&rB("a",s({ref:n,className:["fc-col-header-cell-cushion",i.isSticky?"fc-sticky":"",].join(" ")},p),r)))})},t}(r3),im=t1({weekday:"long"}),iy=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.dateEnv,r=t.theme,o=t.viewApi,i=t.options,a=e5(new Date(2592e5),e.dow),l={dow:e.dow,isDisabled:!1,isFuture:!1,isPast:!1,isToday:!1,isOther:!1},u=[ih].concat(rR(l,r),e.extraClassNames||[]),c=n.format(a,e.dayHeaderFormat),d=s(s(s(s({date:a},l),{view:o}),e.extraHookProps),{text:c});return rB(ot,{hookProps:d,classNames:i.dayHeaderClassNames,content:i.dayHeaderContent,defaultContent:iv,didMount:i.dayHeaderDidMount,willUnmount:i.dayHeaderWillUnmount},function(t,r,o,i){return rB("th",s({ref:t,role:"columnheader",className:u.concat(r).join(" "),colSpan:e.colSpan},e.extraDataAttrs),rB("div",{className:"fc-scrollgrid-sync-inner"},rB("a",{"aria-label":n.format(a,im),className:["fc-col-header-cell-cushion",e.isSticky?"fc-sticky":"",].join(" "),ref:o},i)))})},t}(r3),iE=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.initialNowDate=n7(n.options.now,n.dateEnv),r.initialNowQueriedMs=new Date().valueOf(),r.state=r.computeTiming().currentState,r}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.state;return e.children(t.nowDate,t.todayRange)},t.prototype.componentDidMount=function(){this.setTimeout()},t.prototype.componentDidUpdate=function(e){e.unit!==this.props.unit&&(this.clearTimeout(),this.setTimeout())},t.prototype.componentWillUnmount=function(){this.clearTimeout()},t.prototype.computeTiming=function(){var e=this.props,t=this.context,n=e9(this.initialNowDate,new Date().valueOf()-this.initialNowQueriedMs),r=t.dateEnv.startOf(n,e.unit),o=t.dateEnv.add(r,tR(1,e.unit)),i=o.valueOf()-n.valueOf();return i=Math.min(864e5,i),{currentState:{nowDate:r,todayRange:iS(r)},nextState:{nowDate:o,todayRange:iS(o)},waitMs:i}},t.prototype.setTimeout=function(){var e=this,t=this.computeTiming(),n=t.nextState,r=t.waitMs;this.timeoutId=setTimeout(function(){e.setState(n,function(){e.setTimeout()})},r)},t.prototype.clearTimeout=function(){this.timeoutId&&clearTimeout(this.timeoutId)},t.contextType=r1,t}(rF);function iS(e){var t=tt(e),n=e5(t,1);return{start:t,end:n}}var iD=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.createDayHeaderFormatter=tA(ib),t}return a(t,e),t.prototype.render=function(){var e=this.context,t=this.props,n=t.dates,r=t.dateProfile,o=t.datesRepDistinctDays,i=t.renderIntro,a=this.createDayHeaderFormatter(e.options.dayHeaderFormat,o,n.length);return rB(iE,{unit:"day"},function(e,t){return rB("tr",{role:"row"},i&&i("day"),n.map(function(e){return o?rB(ig,{key:e.toISOString(),date:e,dateProfile:r,todayRange:t,colCnt:n.length,dayHeaderFormat:a}):rB(iy,{key:e.getUTCDay(),dow:e.getUTCDay(),dayHeaderFormat:a})}))})},t}(r3);function ib(e,t,n){return e||ip(t,n)}var iC=function(){function e(e,t){for(var n=e.start,r=e.end,o=[],i=[],a=-1;n<r;)t.isHiddenDay(n)?o.push(a+.5):(o.push(a+=1),i.push(n)),n=e5(n,1);this.dates=i,this.indices=o,this.cnt=i.length}return e.prototype.sliceRange=function(e){var t=this.getDateDayIndex(e.start),n=this.getDateDayIndex(e5(e.end,-1)),r=Math.max(0,t),o=Math.min(this.cnt-1,n);return(r=Math.ceil(r),o=Math.floor(o),r<=o)?{firstIndex:r,lastIndex:o,isStart:t===r,isEnd:n===o}:null},e.prototype.getDateDayIndex=function(e){var t=this.indices,n=Math.floor(e6(this.dates[0],e));return n<0?t[0]-1:n>=t.length?t[t.length-1]+1:t[n]},e}(),i$=function(){function e(e,t){var n,r,o,i=e.dates;if(t){for(n=1,r=i[0].getUTCDay();n<i.length&&i[n].getUTCDay()!==r;n+=1);o=Math.ceil(i.length/n)}else o=1,n=i.length;this.rowCnt=o,this.colCnt=n,this.daySeries=e,this.cells=this.buildCells(),this.headerDates=this.buildHeaderDates()}return e.prototype.buildCells=function(){for(var e=[],t=0;t<this.rowCnt;t+=1){for(var n=[],r=0;r<this.colCnt;r+=1)n.push(this.buildCell(t,r));e.push(n)}return e},e.prototype.buildCell=function(e,t){var n=this.daySeries.dates[e*this.colCnt+t];return{key:n.toISOString(),date:n}},e.prototype.buildHeaderDates=function(){for(var e=[],t=0;t<this.colCnt;t+=1)e.push(this.cells[0][t].date);return e},e.prototype.sliceRange=function(e){var t=this.colCnt,n=this.daySeries.sliceRange(e),r=[];if(n)for(var o=n.firstIndex,i=n.lastIndex,a=o;a<=i;){var s=Math.floor(a/t),l=Math.min((s+1)*t,i+1);r.push({row:s,firstCol:a%t,lastCol:(l-1)%t,isStart:n.isStart&&a===o,isEnd:n.isEnd&&l-1===i}),a=l}return r},e}(),iR=function(){function e(){this.sliceBusinessHours=tA(this._sliceBusinessHours),this.sliceDateSelection=tA(this._sliceDateSpan),this.sliceEventStore=tA(this._sliceEventStore),this.sliceEventDrag=tA(this._sliceInteraction),this.sliceEventResize=tA(this._sliceInteraction),this.forceDayIfListItem=!1}return e.prototype.sliceProps=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];var a=e.eventUiBases,s=this.sliceEventStore.apply(this,l([e.eventStore,a,t,n],o));return{dateSelectionSegs:this.sliceDateSelection.apply(this,l([e.dateSelection,a,r],o)),businessHourSegs:this.sliceBusinessHours.apply(this,l([e.businessHours,t,n,r],o)),fgEventSegs:s.fg,bgEventSegs:s.bg,eventDrag:this.sliceEventDrag.apply(this,l([e.eventDrag,a,t,n],o)),eventResize:this.sliceEventResize.apply(this,l([e.eventResize,a,t,n],o)),eventSelection:e.eventSelection}},e.prototype.sliceNowDate=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return this._sliceDateSpan.apply(this,l([{range:{start:e,end:e9(e,1)},allDay:!1},{},t],n))},e.prototype._sliceBusinessHours=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];return e?this._sliceEventStore.apply(this,l([tD(e,i8(t,Boolean(n)),r),{},t,n],o)).bg:[]},e.prototype._sliceEventStore=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];if(e){var a=nx(e,t,i8(n,Boolean(r)),r);return{bg:this.sliceEventRanges(a.bg,o),fg:this.sliceEventRanges(a.fg,o)}}return{bg:[],fg:[]}},e.prototype._sliceInteraction=function(e,t,n,r){for(var o=[],i=4;i<arguments.length;i++)o[i-4]=arguments[i];if(!e)return null;var a=nx(e.mutatedEvents,t,i8(n,Boolean(r)),r);return{segs:this.sliceEventRanges(a.fg,o),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent}},e.prototype._sliceDateSpan=function(e,t,n){for(var r=[],o=3;o<arguments.length;o++)r[o-3]=arguments[o];if(!e)return[];for(var i,a,s,u,c,d=(i=e,a=t,s=n,u=ng({editable:!1},s),{def:c=ny(u.refined,u.extra,"",i.allDay,!0,s),ui:nN(c,a),instance:tu(c.defId,i.range),range:i.range,isStart:!0,isEnd:!0}),p=this.sliceRange.apply(this,l([e.range],r)),f=0,h=p;f<h.length;f++)h[f].eventRange=d;return p},e.prototype.sliceEventRanges=function(e,t){for(var n=[],r=0,o=e;r<o.length;r++){var i=o[r];n.push.apply(n,this.sliceEventRange(i,t))}return n},e.prototype.sliceEventRange=function(e,t){var n=e.range;this.forceDayIfListItem&&"list-item"===e.ui.display&&(n={start:n.start,end:e5(n.start,1)});for(var r=this.sliceRange.apply(this,l([n],t)),o=0,i=r;o<i.length;o++){var a=i[o];a.eventRange=e,a.isStart=e.isStart&&a.isStart,a.isEnd=e.isEnd&&a.isEnd}return r},e}();function i8(e,t){var n=e.activeRange;return t?n:{start:e9(n.start,e.slotMinTime.milliseconds),end:e9(n.end,e.slotMaxTime.milliseconds-864e5)}}function iw(e,t,n){var r=e.mutatedEvents.instances;for(var o in r)if(!nT(t.validRange,r[o].range))return!1;return ik({eventDrag:e},n)}function iT(e,t,n){return!!nT(t.validRange,e.range)&&ik({dateSelection:e},n)}function ik(e,t){var n=t.getCurrentData(),r=s({businessHours:n.businessHours,dateSelection:"",eventStore:n.eventStore,eventUiBases:n.eventUiBases,eventSelection:"",eventDrag:null,eventResize:null},e);return(t.pluginHooks.isPropsValid||ix)(r,t)}function ix(e,t,n,r){return void 0===n&&(n={}),(!e.eventDrag||!!function e(t,n,r,o){var i,a,l=n.getCurrentData(),u=t.eventDrag,c=u.mutatedEvents,d=c.defs,p=c.instances,f=nP(d,u.isEvent?t.eventUiBases:{"":l.selectionConfig});o&&(f=tf(f,o));var h=(i=t.eventStore,a=u.affectedEvents.instances,{defs:i.defs,instances:tp(i.instances,function(e){return!a[e.instanceId]})}),v=h.defs,g=h.instances,m=nP(v,t.eventUiBases);for(var y in p){var E=p[y],S=E.range,D=f[E.defId],b=d[E.defId];if(!iM(D.constraints,S,h,t.businessHours,n))return!1;var C=n.options.eventOverlap,$="function"==typeof C?C:null;for(var R in g){var w=g[R];if(nw(S,w.range)&&(!1===m[w.defId].overlap&&u.isEvent||!1===D.overlap||$&&!$(new nQ(n,v[w.defId],w),new nQ(n,b,E))))return!1}for(var T=l.eventStore,k=0,x=D.allows;k<x.length;k++){var M=x[k],_=s(s({},r),{range:E.range,allDay:b.allDay}),I=T.defs[b.defId],P=T.instances[y],N=void 0;if(N=I?new nQ(n,I,P):new nQ(n,b),!M(nX(_,n),N))return!1}}return!0}(e,t,n,r))&&(!e.dateSelection||!!function e(t,n,r,o){var i=t.eventStore,a=i.defs,l=i.instances,u=t.dateSelection,c=u.range,d=n.getCurrentData().selectionConfig;if(o&&(d=o(d)),!iM(d.constraints,c,i,t.businessHours,n))return!1;var p=n.options.selectOverlap,f="function"==typeof p?p:null;for(var h in l){var v=l[h];if(nw(c,v.range)&&(!1===d.overlap||f&&!f(new nQ(n,a[v.defId],v),null)))return!1}for(var g=0,m=d.allows;g<m.length;g++){var y=m[g],E=s(s({},r),u);if(!y(nX(E,n),null))return!1}return!0}(e,t,n,r))}function iM(e,t,n,r,o){for(var i=0,a=e;i<a.length;i++)if(!iP(i_(a[i],t,n,r,o),t))return!1;return!0}function i_(e,t,n,r,o){return"businessHours"===e?iI(tD(r,t,o)):"string"==typeof e?iI(ni(n,function(t){return t.groupId===e})):"object"==typeof e&&e?iI(tD(e,t,o)):[]}function iI(e){var t=e.instances,n=[];for(var r in t)n.push(t[r].range);return n}function iP(e,t){for(var n=0,r=e;n<r.length;n++)if(nT(r[n],t))return!0;return!1}var iN=/^(visible|hidden)$/,iH=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.handleEl=function(e){t.el=e,r9(t.props.elRef,e)},t}return a(t,e),t.prototype.render=function(){var e=this.props,t=e.liquid,n=e.liquidIsAbsolute,r=t&&n,o=["fc-scroller"];return t&&(n?o.push("fc-scroller-liquid-absolute"):o.push("fc-scroller-liquid")),rB("div",{ref:this.handleEl,className:o.join(" "),style:{overflowX:e.overflowX,overflowY:e.overflowY,left:r&&-(e.overcomeLeft||0)||"",right:r&&-(e.overcomeRight||0)||"",bottom:r&&-(e.overcomeBottom||0)||"",marginLeft:!r&&-(e.overcomeLeft||0)||"",marginRight:!r&&-(e.overcomeRight||0)||"",marginBottom:!r&&-(e.overcomeBottom||0)||"",maxHeight:e.maxHeight||""}},e.children)},t.prototype.needsXScrolling=function(){if(iN.test(this.props.overflowX))return!1;for(var e=this.el,t=this.el.getBoundingClientRect().width-this.getYScrollbarWidth(),n=e.children,r=0;r<n.length;r+=1)if(n[r].getBoundingClientRect().width>t)return!0;return!1},t.prototype.needsYScrolling=function(){if(iN.test(this.props.overflowY))return!1;for(var e=this.el,t=this.el.getBoundingClientRect().height-this.getXScrollbarWidth(),n=e.children,r=0;r<n.length;r+=1)if(n[r].getBoundingClientRect().height>t)return!0;return!1},t.prototype.getXScrollbarWidth=function(){return iN.test(this.props.overflowX)?0:this.el.offsetHeight-this.el.clientHeight},t.prototype.getYScrollbarWidth=function(){return iN.test(this.props.overflowY)?0:this.el.offsetWidth-this.el.clientWidth},t}(r3),iO=function(){function e(e){var t=this;this.masterCallback=e,this.currentMap={},this.depths={},this.callbackMap={},this.handleValue=function(e,n){var r=t,o=r.depths,i=r.currentMap,a=!1,s=!1;null!==e?(a=n in i,i[n]=e,o[n]=(o[n]||0)+1,s=!0):(o[n]-=1,o[n]||(delete i[n],delete t.callbackMap[n],a=!0)),t.masterCallback&&(a&&t.masterCallback(null,String(n)),s&&t.masterCallback(e,String(n)))}}return e.prototype.createRef=function(e){var t=this,n=this.callbackMap[e];return n||(n=this.callbackMap[e]=function(n){t.handleValue(n,String(e))}),n},e.prototype.collect=function(e,t,n){return tS(this.currentMap,e,t,n)},e.prototype.getAll=function(){return tv(this.currentMap)},e}();function iA(e){for(var t=eR(e,".fc-scrollgrid-shrink"),n=0,r=0,o=t;r<o.length;r++)n=Math.max(n,eK(o[r]));return Math.ceil(n)}function iL(e,t){return e.liquid&&t.liquid}function iU(e,t){return null!=t.maxHeight||iL(e,t)}function iW(e,t,n,r){var o=n.expandRows;return"function"==typeof t.content?t.content(n):rB("table",{role:"presentation",className:[t.tableClassName,e.syncRowHeights?"fc-scrollgrid-sync-table":"",].join(" "),style:{minWidth:n.tableMinWidth,width:n.clientWidth,height:o?n.clientHeight:""}},n.tableColGroupNode,rB(r?"thead":"tbody",{role:"presentation"},"function"==typeof t.rowContent?t.rowContent(n):t.rowContent))}function iV(e,t){return tO(e,t,tg)}function iz(e,t){for(var n=[],r=0,o=e;r<o.length;r++)for(var i=o[r],a=i.span||1,s=0;s<a;s+=1)n.push(rB("col",{style:{width:"shrink"===i.width?iF(t):i.width||"",minWidth:i.minWidth||""}}));return rB.apply(void 0,l(["colgroup",{}],n))}function iF(e){return null==e?4:e}function iB(e){for(var t=0,n=e;t<n.length;t++)if("shrink"===n[t].width)return!0;return!1}function iG(e,t){var n=["fc-scrollgrid",t.theme.getClass("table"),];return e&&n.push("fc-scrollgrid-liquid"),n}function iq(e,t){var n=["fc-scrollgrid-section","fc-scrollgrid-section-"+e.type,e.className];return t&&e.liquid&&null==e.maxHeight&&n.push("fc-scrollgrid-section-liquid"),e.isSticky&&n.push("fc-scrollgrid-section-sticky"),n}function ij(e){return rB("div",{className:"fc-scrollgrid-sticky-shim",style:{width:e.clientWidth,minWidth:e.tableMinWidth}})}function iY(e){var t=e.stickyHeaderDates;return(null==t||"auto"===t)&&(t="auto"===e.height||"auto"===e.viewHeight),t}function iZ(e){var t=e.stickyFooterScrollbar;return(null==t||"auto"===t)&&(t="auto"===e.height||"auto"===e.viewHeight),t}var iX=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.processCols=tA(function(e){return e},iV),t.renderMicroColGroup=tA(iz),t.scrollerRefs=new iO,t.scrollerElRefs=new iO(t._handleScrollerEl.bind(t)),t.state={shrinkWidth:null,forceYScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{}},t.handleSizing=function(){t.safeSetState(s({shrinkWidth:t.computeShrinkWidth()},t.computeScrollerDims()))},t}return a(t,e),t.prototype.render=function(){var e,t=this.props,n=this.state,r=this.context,o=t.sections||[],i=this.processCols(t.cols),a=this.renderMicroColGroup(i,n.shrinkWidth),s=iG(t.liquid,r);t.collapsibleWidth&&s.push("fc-scrollgrid-collapsible");for(var u=o.length,c=0,d=[],p=[],f=[];c<u&&"header"===(e=o[c]).type;)d.push(this.renderSection(e,a,!0)),c+=1;for(;c<u&&"body"===(e=o[c]).type;)p.push(this.renderSection(e,a,!1)),c+=1;for(;c<u&&"footer"===(e=o[c]).type;)f.push(this.renderSection(e,a,!0)),c+=1;var h=!rS(),v={role:"rowgroup"};return rB("table",{role:"grid",className:s.join(" "),style:{height:t.height}},Boolean(!h&&d.length)&&rB.apply(void 0,l(["thead",v],d)),Boolean(!h&&p.length)&&rB.apply(void 0,l(["tbody",v],p)),Boolean(!h&&f.length)&&rB.apply(void 0,l(["tfoot",v],f)),h&&rB.apply(void 0,l(l(l(["tbody",v],d),p),f)))},t.prototype.renderSection=function(e,t,n){return"outerContent"in e?rB(rj,{key:e.key},e.outerContent):rB("tr",{key:e.key,role:"presentation",className:iq(e,this.props.liquid).join(" ")},this.renderChunkTd(e,t,e.chunk,n))},t.prototype.renderChunkTd=function(e,t,n,r){if("outerContent"in n)return n.outerContent;var o=this.props,i=this.state,a=i.forceYScrollbars,s=i.scrollerClientWidths,l=i.scrollerClientHeights,u=iU(o,e),c=iL(o,e),d=o.liquid?a?"scroll":u?"auto":"hidden":"visible",p=e.key,f=iW(e,n,{tableColGroupNode:t,tableMinWidth:"",clientWidth:o.collapsibleWidth||void 0===s[p]?null:s[p],clientHeight:void 0!==l[p]?l[p]:null,expandRows:e.expandRows,syncRowHeights:!1,rowSyncHeights:[],reportRowHeightChange:function(){}},r);return rB(r?"th":"td",{ref:n.elRef,role:"presentation"},rB("div",{className:"fc-scroller-harness"+(c?" fc-scroller-harness-liquid":"")},rB(iH,{ref:this.scrollerRefs.createRef(p),elRef:this.scrollerElRefs.createRef(p),overflowY:d,overflowX:o.liquid?"hidden":"visible",maxHeight:e.maxHeight,liquid:c,liquidIsAbsolute:!0},f)))},t.prototype._handleScrollerEl=function(e,t){var n=function e(t,n){for(var r=0,o=t;r<o.length;r++){var i=o[r];if(i.key===n)return i}return null}(this.props.sections,t);n&&r9(n.chunk.scrollerElRef,e)},t.prototype.componentDidMount=function(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)},t.prototype.componentDidUpdate=function(){this.handleSizing()},t.prototype.componentWillUnmount=function(){this.context.removeResizeHandler(this.handleSizing)},t.prototype.computeShrinkWidth=function(){return iB(this.props.cols)?iA(this.scrollerElRefs.getAll()):0},t.prototype.computeScrollerDims=function(){var e=rM(),t=this.scrollerRefs,n=this.scrollerElRefs,r=!1,o={},i={};for(var a in t.currentMap){var s=t.currentMap[a];if(s&&s.needsYScrolling()){r=!0;break}}for(var l=0,u=this.props.sections;l<u.length;l++){var a=u[l].key,c=n.currentMap[a];if(c){var d=c.parentNode;o[a]=Math.floor(d.getBoundingClientRect().width-(r?e.y:0)),i[a]=Math.floor(d.getBoundingClientRect().height)}}return{forceYScrollbars:r,scrollerClientWidths:o,scrollerClientHeights:i}},t}(r3);iX.addStateEquality({scrollerClientWidths:tg,scrollerClientHeights:tg});var i0=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.elRef=rq(),t}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=e.seg,o=r.eventRange,i=o.ui,a={event:new nQ(t,o.def,o.instance),view:t.viewApi,timeText:e.timeText,textColor:i.textColor,backgroundColor:i.backgroundColor,borderColor:i.borderColor,isDraggable:!e.disableDragging&&nA(r,t),isStartResizable:!e.disableResizing&&nL(r,t),isEndResizable:!e.disableResizing&&nU(r),isMirror:Boolean(e.isDragging||e.isResizing||e.isDateSelecting),isStart:Boolean(r.isStart),isEnd:Boolean(r.isEnd),isPast:Boolean(e.isPast),isFuture:Boolean(e.isFuture),isToday:Boolean(e.isToday),isSelected:Boolean(e.isSelected),isDragging:Boolean(e.isDragging),isResizing:Boolean(e.isResizing)},s=nz(a).concat(i.classNames);return rB(ot,{hookProps:a,classNames:n.eventClassNames,content:n.eventContent,defaultContent:e.defaultContent,didMount:n.eventDidMount,willUnmount:n.eventWillUnmount,elRef:this.elRef},function(t,n,r,o){return e.children(t,s.concat(n),r,o,a)})},t.prototype.componentDidMount=function(){n_(this.elRef.current,this.props.seg)},t.prototype.componentDidUpdate=function(e){var t=this.props.seg;t!==e.seg&&n_(this.elRef.current,t)},t}(r3),i1=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=e.seg,r=nW(n,t.options.eventTimeFormat||e.defaultTimeFormat,t,e.defaultDisplayEventTime,e.defaultDisplayEventEnd);return rB(i0,{seg:n,timeText:r,disableDragging:e.disableDragging,disableResizing:e.disableResizing,defaultContent:e.defaultContent||i4,isDragging:e.isDragging,isResizing:e.isResizing,isDateSelecting:e.isDateSelecting,isSelected:e.isSelected,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday},function(r,o,i,a,l){return rB("a",s({className:e.extraClassNames.concat(o).join(" "),style:{borderColor:l.borderColor,backgroundColor:l.backgroundColor},ref:r},nB(n,t)),rB("div",{className:"fc-event-main",ref:i,style:{color:l.textColor}},a),l.isStartResizable&&rB("div",{className:"fc-event-resizer fc-event-resizer-start"}),l.isEndResizable&&rB("div",{className:"fc-event-resizer fc-event-resizer-end"}))})},t}(r3);function i4(e){return rB("div",{className:"fc-event-main-frame"},e.timeText&&rB("div",{className:"fc-event-time"},e.timeText),rB("div",{className:"fc-event-title-container"},rB("div",{className:"fc-event-title fc-sticky"},e.event.title||rB(rj,null,"\xa0"))))}var iK=function(e){return rB(r1.Consumer,null,function(t){var n=t.options;return rB(ot,{hookProps:{isAxis:e.isAxis,date:t.dateEnv.toDate(e.date),view:t.viewApi},classNames:n.nowIndicatorClassNames,content:n.nowIndicatorContent,didMount:n.nowIndicatorDidMount,willUnmount:n.nowIndicatorWillUnmount},e.children)})},i3=t1({day:"numeric"}),i2=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=i5({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,showDayNumber:e.showDayNumber,extraProps:e.extraHookProps,viewApi:t.viewApi,dateEnv:t.dateEnv});return rB(or,{hookProps:r,content:n.dayCellContent,defaultContent:e.defaultContent},e.children)},t}(r3);function i5(e){var t=e.date,n=e.dateEnv,r=r$(t,e.todayRange,null,e.dateProfile);return s(s(s({date:n.toDate(t),view:e.viewApi},r),{dayNumberText:e.showDayNumber?n.format(t,i3):""}),e.extraProps)}var i9=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.refineHookProps=tL(i5),t.normalizeClassNames=oa(),t}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=this.refineHookProps({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,showDayNumber:e.showDayNumber,extraProps:e.extraHookProps,viewApi:t.viewApi,dateEnv:t.dateEnv}),o=rR(r,t.theme).concat(r.isDisabled?[]:this.normalizeClassNames(n.dayCellClassNames,r)),i=r.isDisabled?{}:{"data-date":tP(e.date)};return rB(oi,{hookProps:r,didMount:n.dayCellDidMount,willUnmount:n.dayCellWillUnmount,elRef:e.elRef},function(t){return e.children(t,o,i,r.isDisabled)})},t}(r3);function i7(e){return rB("div",{className:"fc-"+e})}var i6=function(e){return rB(i0,{defaultContent:iQ,seg:e.seg,timeText:"",disableDragging:!0,disableResizing:!0,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday},function(e,t,n,r,o){return rB("div",{ref:e,className:["fc-bg-event"].concat(t).join(" "),style:{backgroundColor:o.backgroundColor}},r)})};function iQ(e){return e.event.title&&rB("div",{className:"fc-event-title"},e.event.title)}var iJ=function(e){return rB(r1.Consumer,null,function(t){var n=t.dateEnv,r=t.options,o=e.date,i=r.weekNumberFormat||e.defaultFormat,a=n.computeWeekNumber(o),s=n.format(o,i);return rB(ot,{hookProps:{num:a,text:s,date:o},classNames:r.weekNumberClassNames,content:r.weekNumberContent,defaultContent:ae,didMount:r.weekNumberDidMount,willUnmount:r.weekNumberWillUnmount},e.children)})};function ae(e){return e.text}var at=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={titleId:e_()},t.handleRootEl=function(e){t.rootEl=e,t.props.elRef&&r9(t.props.elRef,e)},t.handleDocumentMouseDown=function(e){var n=ek(e);t.rootEl.contains(n)||t.handleCloseClick()},t.handleDocumentKeyDown=function(e){"Escape"===e.key&&t.handleCloseClick()},t.handleCloseClick=function(){var e=t.props.onClose;e&&e()},t}return a(t,e),t.prototype.render=function(){var e=this.context,t=e.theme,n=e.options,r=this.props,o=this.state,i=["fc-popover",t.getClass("popover"),].concat(r.extraClassNames||[]);return rZ(rB("div",s({id:r.id,className:i.join(" "),"aria-labelledby":o.titleId},r.extraAttrs,{ref:this.handleRootEl}),rB("div",{className:"fc-popover-header "+t.getClass("popoverHeader")},rB("span",{className:"fc-popover-title",id:o.titleId},r.title),rB("span",{className:"fc-popover-close "+t.getIconClass("close"),title:n.closeHint,onClick:this.handleCloseClick})),rB("div",{className:"fc-popover-body "+t.getClass("popoverContent")},r.children)),r.parentEl)},t.prototype.componentDidMount=function(){document.addEventListener("mousedown",this.handleDocumentMouseDown),document.addEventListener("keydown",this.handleDocumentKeyDown),this.updateSize()},t.prototype.componentWillUnmount=function(){document.removeEventListener("mousedown",this.handleDocumentMouseDown),document.removeEventListener("keydown",this.handleDocumentKeyDown)},t.prototype.updateSize=function(){var e=this.context.isRtl,t=this.props,n=t.alignmentEl,r=t.alignGridTop,o=this.rootEl,i=function e(t){for(var n=rH(t),r=t.getBoundingClientRect(),o=0,i=n;o<i.length;o++){var a=rg(r,i[o].getBoundingClientRect());if(!a)return null;r=a}return r}(n);if(i){var a=o.getBoundingClientRect(),s=r?eC(n,".fc-scrollgrid").getBoundingClientRect().top:i.top,l=e?i.right-a.width:i.left;s=Math.max(s,10),l=Math.max(l=Math.min(l,document.documentElement.clientWidth-10-a.width),10);var u=o.offsetParent.getBoundingClientRect();ew(o,{top:s-u.top,left:l-u.left})}},t}(r3),an=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.handleRootEl=function(e){t.rootEl=e,e?t.context.registerInteractiveComponent(t,{el:e,useEventCenter:!1}):t.context.unregisterInteractiveComponent(t)},t}return a(t,e),t.prototype.render=function(){var e=this.context,t=e.options,n=e.dateEnv,r=this.props,o=r.startDate,i=r.todayRange,a=r.dateProfile,s=n.format(o,t.dayPopoverFormat);return rB(i9,{date:o,dateProfile:a,todayRange:i,elRef:this.handleRootEl},function(e,t,n){return rB(at,{elRef:e,id:r.id,title:s,extraClassNames:["fc-more-popover"].concat(t),extraAttrs:n,parentEl:r.parentEl,alignmentEl:r.alignmentEl,alignGridTop:r.alignGridTop,onClose:r.onClose},rB(i2,{date:o,dateProfile:a,todayRange:i},function(e,t){return t&&rB("div",{className:"fc-more-popover-misc",ref:e},t)}),r.children)})},t.prototype.queryHit=function(e,t,n,r){var o=this.rootEl,i=this.props;return e>=0&&e<n&&t>=0&&t<r?{dateProfile:i.dateProfile,dateSpan:s({allDay:!0,range:{start:i.startDate,end:i.endDate}},i.extraDateSpan),dayEl:o,rect:{left:0,top:0,right:n,bottom:r},layer:1}:null},t}(r7),ar=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.linkElRef=rq(),t.state={isPopoverOpen:!1,popoverId:e_()},t.handleClick=function(e){var n=t,r=n.props,o=n.context,i=o.options.moreLinkClick,a=ai(r).start;function s(e){var t=e.eventRange,n=t.def,r=t.instance,i=t.range;return{event:new nQ(o,n,r),start:o.dateEnv.toDate(i.start),end:o.dateEnv.toDate(i.end),isStart:e.isStart,isEnd:e.isEnd}}"function"==typeof i&&(i=i({date:a,allDay:Boolean(r.allDayDate),allSegs:r.allSegs.map(s),hiddenSegs:r.hiddenSegs.map(s),jsEvent:e,view:o.viewApi})),i&&"popover"!==i?"string"==typeof i&&o.calendarApi.zoomTo(a,i):t.setState({isPopoverOpen:!0})},t.handlePopoverClose=function(){t.setState({isPopoverOpen:!1})},t}return a(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.state;return rB(r1.Consumer,null,function(r){var o=r.viewApi,i=r.options,a=r.calendarApi,s=i.moreLinkText,l=t.moreCnt,u=ai(t),c="function"==typeof s?s.call(a,l):"+"+l+" "+s,d=e0(i.moreLinkHint,[l],c);return rB(rj,null,Boolean(t.moreCnt)&&rB(ot,{elRef:e.linkElRef,hookProps:{num:l,shortText:"+"+l,text:c,view:o},classNames:i.moreLinkClassNames,content:i.moreLinkContent,defaultContent:t.defaultContent||ao,didMount:i.moreLinkDidMount,willUnmount:i.moreLinkWillUnmount},function(r,o,i,a){return t.children(r,["fc-more-link"].concat(o),i,a,e.handleClick,d,n.isPopoverOpen,n.isPopoverOpen?n.popoverId:"")}),n.isPopoverOpen&&rB(an,{id:n.popoverId,startDate:u.start,endDate:u.end,dateProfile:t.dateProfile,todayRange:t.todayRange,extraDateSpan:t.extraDateSpan,parentEl:e.parentEl,alignmentEl:t.alignmentElRef.current,alignGridTop:t.alignGridTop,onClose:e.handlePopoverClose},t.popoverContent()))})},t.prototype.componentDidMount=function(){this.updateParentEl()},t.prototype.componentDidUpdate=function(){this.updateParentEl()},t.prototype.updateParentEl=function(){this.linkElRef.current&&(this.parentEl=eC(this.linkElRef.current,".fc-view-harness"))},t}(r3);function ao(e){return e.text}function ai(e){if(e.allDayDate)return{start:e.allDayDate,end:e5(e.allDayDate,1)};var t,n=e.hiddenSegs;return{start:aa(n),end:(t=n,t.reduce(al).eventRange.range.end)}}function aa(e){return e.reduce(as).eventRange.range.start}function as(e,t){return e.eventRange.range.start<t.eventRange.range.start?e:t}function al(e,t){return e.eventRange.range.end>t.eventRange.range.end?e:t}var au=function(e){function t(t,n){void 0===n&&(n={});var r=e.call(this)||this;return r.isRendering=!1,r.isRendered=!1,r.currentClassNames=[],r.customContentRenderId=0,r.handleAction=function(e){switch(e.type){case"SET_EVENT_DRAG":case"SET_EVENT_RESIZE":r.renderRunner.tryDrain()}},r.handleData=function(e){r.currentData=e,r.renderRunner.request(e.calendarOptions.rerenderDelay)},r.handleRenderRequest=function(){if(r.isRendering){r.isRendered=!0;var e=r.currentData;eE(function(){rG(rB(id,{options:e.calendarOptions,theme:e.theme,emitter:e.emitter},function(t,n,o,i){return r.setClassNames(t),r.setHeight(n),rB(on.Provider,{value:r.customContentRenderId},rB(il,s({isHeightAuto:o,forPrint:i},e)))}),r.el)})}else r.isRendered&&(r.isRendered=!1,rX(r.el),r.setClassNames([]),r.setHeight(""))},r.el=t,r.renderRunner=new oH(r.handleRenderRequest),new oL({optionOverrides:n,calendarApi:r,onAction:r.handleAction,onData:r.handleData}),r}return a(t,e),Object.defineProperty(t.prototype,"view",{get:function(){return this.currentData.viewApi},enumerable:!1,configurable:!0}),t.prototype.render=function(){var e=this.isRendering;e?this.customContentRenderId+=1:this.isRendering=!0,this.renderRunner.request(),e&&this.updateSize()},t.prototype.destroy=function(){this.isRendering&&(this.isRendering=!1,this.renderRunner.request())},t.prototype.updateSize=function(){var t=this;eE(function(){e.prototype.updateSize.call(t)})},t.prototype.batchRendering=function(e){this.renderRunner.pause("batchRendering"),e(),this.renderRunner.resume("batchRendering")},t.prototype.pauseRendering=function(){this.renderRunner.pause("pauseRendering")},t.prototype.resumeRendering=function(){this.renderRunner.resume("pauseRendering",!0)},t.prototype.resetOptions=function(e,t){this.currentDataManager.resetOptions(e,t)},t.prototype.setClassNames=function(e){if(!tO(e,this.currentClassNames)){for(var t=this.el.classList,n=0,r=this.currentClassNames;n<r.length;n++){var o=r[n];t.remove(o)}for(var i=0,a=e;i<a.length;i++){var o=a[i];t.add(o)}this.currentClassNames=e}},t.prototype.setHeight=function(e){eT(this.el,"height",e)},t}(n6);oJ.touchMouseIgnoreWait=500;var ac=0,ad=0,ap=!1,af=function(){function e(e){var t=this;this.subjectEl=null,this.selector="",this.handleSelector="",this.shouldIgnoreMove=!1,this.shouldWatchScroll=!0,this.isDragging=!1,this.isTouchDragging=!1,this.wasTouchScroll=!1,this.handleMouseDown=function(e){var n;if(!t.shouldIgnoreMouse()&&(n=e,0===n.button&&!n.ctrlKey)&&t.tryStart(e)){var r=t.createEventFromMouse(e,!0);t.emitter.trigger("pointerdown",r),t.initScrollWatch(r),t.shouldIgnoreMove||document.addEventListener("mousemove",t.handleMouseMove),document.addEventListener("mouseup",t.handleMouseUp)}},this.handleMouseMove=function(e){var n=t.createEventFromMouse(e);t.recordCoords(n),t.emitter.trigger("pointermove",n)},this.handleMouseUp=function(e){document.removeEventListener("mousemove",t.handleMouseMove),document.removeEventListener("mouseup",t.handleMouseUp),t.emitter.trigger("pointerup",t.createEventFromMouse(e)),t.cleanup()},this.handleTouchStart=function(e){if(t.tryStart(e)){t.isTouchDragging=!0;var n=t.createEventFromTouch(e,!0);t.emitter.trigger("pointerdown",n),t.initScrollWatch(n);var r=e.target;t.shouldIgnoreMove||r.addEventListener("touchmove",t.handleTouchMove),r.addEventListener("touchend",t.handleTouchEnd),r.addEventListener("touchcancel",t.handleTouchEnd),window.addEventListener("scroll",t.handleTouchScroll,!0)}},this.handleTouchMove=function(e){var n=t.createEventFromTouch(e);t.recordCoords(n),t.emitter.trigger("pointermove",n)},this.handleTouchEnd=function(e){if(t.isDragging){var n=e.target;n.removeEventListener("touchmove",t.handleTouchMove),n.removeEventListener("touchend",t.handleTouchEnd),n.removeEventListener("touchcancel",t.handleTouchEnd),window.removeEventListener("scroll",t.handleTouchScroll,!0),t.emitter.trigger("pointerup",t.createEventFromTouch(e)),t.cleanup(),t.isTouchDragging=!1,ac+=1,setTimeout(function(){ac-=1},oJ.touchMouseIgnoreWait)}},this.handleTouchScroll=function(){t.wasTouchScroll=!0},this.handleScroll=function(e){if(!t.shouldIgnoreMove){var n=window.pageXOffset-t.prevScrollX+t.prevPageX,r=window.pageYOffset-t.prevScrollY+t.prevPageY;t.emitter.trigger("pointermove",{origEvent:e,isTouch:t.isTouchDragging,subjectEl:t.subjectEl,pageX:n,pageY:r,deltaX:n-t.origPageX,deltaY:r-t.origPageY})}},this.containerEl=e,this.emitter=new rA,e.addEventListener("mousedown",this.handleMouseDown),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),1===(ad+=1)&&window.addEventListener("touchmove",ah,{passive:!1})}return e.prototype.destroy=function(){this.containerEl.removeEventListener("mousedown",this.handleMouseDown),this.containerEl.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),(ad-=1)||window.removeEventListener("touchmove",ah,{passive:!1})},e.prototype.tryStart=function(e){var t=this.querySubjectEl(e),n=e.target;return!!(t&&(!this.handleSelector||eC(n,this.handleSelector)))&&(this.subjectEl=t,this.isDragging=!0,this.wasTouchScroll=!1,!0)},e.prototype.cleanup=function(){ap=!1,this.isDragging=!1,this.subjectEl=null,this.destroyScrollWatch()},e.prototype.querySubjectEl=function(e){return this.selector?eC(e.target,this.selector):this.containerEl},e.prototype.shouldIgnoreMouse=function(){return ac||this.isTouchDragging},e.prototype.cancelTouchScroll=function(){this.isDragging&&(ap=!0)},e.prototype.initScrollWatch=function(e){this.shouldWatchScroll&&(this.recordCoords(e),window.addEventListener("scroll",this.handleScroll,!0))},e.prototype.recordCoords=function(e){this.shouldWatchScroll&&(this.prevPageX=e.pageX,this.prevPageY=e.pageY,this.prevScrollX=window.pageXOffset,this.prevScrollY=window.pageYOffset)},e.prototype.destroyScrollWatch=function(){this.shouldWatchScroll&&window.removeEventListener("scroll",this.handleScroll,!0)},e.prototype.createEventFromMouse=function(e,t){var n=0,r=0;return t?(this.origPageX=e.pageX,this.origPageY=e.pageY):(n=e.pageX-this.origPageX,r=e.pageY-this.origPageY),{origEvent:e,isTouch:!1,subjectEl:this.subjectEl,pageX:e.pageX,pageY:e.pageY,deltaX:n,deltaY:r}},e.prototype.createEventFromTouch=function(e,t){var n,r,o=e.touches,i=0,a=0;return o&&o.length?(n=o[0].pageX,r=o[0].pageY):(n=e.pageX,r=e.pageY),t?(this.origPageX=n,this.origPageY=r):(i=n-this.origPageX,a=r-this.origPageY),{origEvent:e,isTouch:!0,subjectEl:this.subjectEl,pageX:n,pageY:r,deltaX:i,deltaY:a}},e}();function ah(e){ap&&e.preventDefault()}var av=function(){function e(){this.isVisible=!1,this.sourceEl=null,this.mirrorEl=null,this.sourceElRect=null,this.parentNode=document.body,this.zIndex=9999,this.revertDuration=0}return e.prototype.start=function(e,t,n){this.sourceEl=e,this.sourceElRect=this.sourceEl.getBoundingClientRect(),this.origScreenX=t-window.pageXOffset,this.origScreenY=n-window.pageYOffset,this.deltaX=0,this.deltaY=0,this.updateElPosition()},e.prototype.handleMove=function(e,t){this.deltaX=e-window.pageXOffset-this.origScreenX,this.deltaY=t-window.pageYOffset-this.origScreenY,this.updateElPosition()},e.prototype.setIsVisible=function(e){e?this.isVisible||(this.mirrorEl&&(this.mirrorEl.style.display=""),this.isVisible=e,this.updateElPosition()):this.isVisible&&(this.mirrorEl&&(this.mirrorEl.style.display="none"),this.isVisible=e)},e.prototype.stop=function(e,t){var n=this,r=function(){n.cleanup(),t()};e&&this.mirrorEl&&this.isVisible&&this.revertDuration&&(this.deltaX||this.deltaY)?this.doRevertAnimation(r,this.revertDuration):setTimeout(r,0)},e.prototype.doRevertAnimation=function(e,t){var n=this.mirrorEl,r=this.sourceEl.getBoundingClientRect();n.style.transition="top "+t+"ms,left "+t+"ms",ew(n,{left:r.left,top:r.top}),eH(n,function(){n.style.transition="",e()})},e.prototype.cleanup=function(){this.mirrorEl&&(eb(this.mirrorEl),this.mirrorEl=null),this.sourceEl=null},e.prototype.updateElPosition=function(){this.sourceEl&&this.isVisible&&ew(this.getMirrorEl(),{left:this.sourceElRect.left+this.deltaX,top:this.sourceElRect.top+this.deltaY})},e.prototype.getMirrorEl=function(){var e=this.sourceElRect,t=this.mirrorEl;return t||((t=this.mirrorEl=this.sourceEl.cloneNode(!0)).classList.add("fc-unselectable"),t.classList.add("fc-event-dragging"),ew(t,{position:"fixed",zIndex:this.zIndex,visibility:"",boxSizing:"border-box",width:e.right-e.left,height:e.bottom-e.top,right:"auto",bottom:"auto",margin:0}),this.parentNode.appendChild(t)),t},e}(),ag=function(e){function t(t,n){var r=e.call(this)||this;return r.handleScroll=function(){r.scrollTop=r.scrollController.getScrollTop(),r.scrollLeft=r.scrollController.getScrollLeft(),r.handleScrollChange()},r.scrollController=t,r.doesListening=n,r.scrollTop=r.origScrollTop=t.getScrollTop(),r.scrollLeft=r.origScrollLeft=t.getScrollLeft(),r.scrollWidth=t.getScrollWidth(),r.scrollHeight=t.getScrollHeight(),r.clientWidth=t.getClientWidth(),r.clientHeight=t.getClientHeight(),r.clientRect=r.computeClientRect(),r.doesListening&&r.getEventTarget().addEventListener("scroll",r.handleScroll),r}return a(t,e),t.prototype.destroy=function(){this.doesListening&&this.getEventTarget().removeEventListener("scroll",this.handleScroll)},t.prototype.getScrollTop=function(){return this.scrollTop},t.prototype.getScrollLeft=function(){return this.scrollLeft},t.prototype.setScrollTop=function(e){this.scrollController.setScrollTop(e),this.doesListening||(this.scrollTop=Math.max(Math.min(e,this.getMaxScrollTop()),0),this.handleScrollChange())},t.prototype.setScrollLeft=function(e){this.scrollController.setScrollLeft(e),this.doesListening||(this.scrollLeft=Math.max(Math.min(e,this.getMaxScrollLeft()),0),this.handleScrollChange())},t.prototype.getClientWidth=function(){return this.clientWidth},t.prototype.getClientHeight=function(){return this.clientHeight},t.prototype.getScrollWidth=function(){return this.scrollWidth},t.prototype.getScrollHeight=function(){return this.scrollHeight},t.prototype.handleScrollChange=function(){},t}(rU),am=function(e){function t(t,n){return e.call(this,new rW(t),n)||this}return a(t,e),t.prototype.getEventTarget=function(){return this.scrollController.el},t.prototype.computeClientRect=function(){return rP(this.scrollController.el)},t}(ag),ay=function(e){function t(t){return e.call(this,new rV,t)||this}return a(t,e),t.prototype.getEventTarget=function(){return window},t.prototype.computeClientRect=function(){return{left:this.scrollLeft,right:this.scrollLeft+this.clientWidth,top:this.scrollTop,bottom:this.scrollTop+this.clientHeight}},t.prototype.handleScrollChange=function(){this.clientRect=this.computeClientRect()},t}(ag),aE="function"==typeof performance?performance.now:Date.now,aS=function(){function e(){var e=this;this.isEnabled=!0,this.scrollQuery=[window,".fc-scroller"],this.edgeThreshold=50,this.maxVelocity=300,this.pointerScreenX=null,this.pointerScreenY=null,this.isAnimating=!1,this.scrollCaches=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.animate=function(){if(e.isAnimating){var t=e.computeBestEdge(e.pointerScreenX+window.pageXOffset,e.pointerScreenY+window.pageYOffset);if(t){var n=aE();e.handleSide(t,(n-e.msSinceRequest)/1e3),e.requestAnimation(n)}else e.isAnimating=!1}}}return e.prototype.start=function(e,t,n){this.isEnabled&&(this.scrollCaches=this.buildCaches(n),this.pointerScreenX=null,this.pointerScreenY=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.handleMove(e,t))},e.prototype.handleMove=function(e,t){if(this.isEnabled){var n=e-window.pageXOffset,r=t-window.pageYOffset,o=null===this.pointerScreenY?0:r-this.pointerScreenY,i=null===this.pointerScreenX?0:n-this.pointerScreenX;o<0?this.everMovedUp=!0:o>0&&(this.everMovedDown=!0),i<0?this.everMovedLeft=!0:i>0&&(this.everMovedRight=!0),this.pointerScreenX=n,this.pointerScreenY=r,this.isAnimating||(this.isAnimating=!0,this.requestAnimation(aE()))}},e.prototype.stop=function(){if(this.isEnabled){this.isAnimating=!1;for(var e=0,t=this.scrollCaches;e<t.length;e++)t[e].destroy();this.scrollCaches=null}},e.prototype.requestAnimation=function(e){this.msSinceRequest=e,requestAnimationFrame(this.animate)},e.prototype.handleSide=function(e,t){var n=e.scrollCache,r=this.edgeThreshold,o=r-e.distance,i=o*o/(r*r)*this.maxVelocity*t,a=1;switch(e.name){case"left":a=-1;case"right":n.setScrollLeft(n.getScrollLeft()+i*a);break;case"top":a=-1;case"bottom":n.setScrollTop(n.getScrollTop()+i*a)}},e.prototype.computeBestEdge=function(e,t){for(var n=this.edgeThreshold,r=null,o=this.scrollCaches||[],i=0,a=o;i<a.length;i++){var s=a[i],l=s.clientRect,u=e-l.left,c=l.right-e,d=t-l.top,p=l.bottom-t;u>=0&&c>=0&&d>=0&&p>=0&&(d<=n&&this.everMovedUp&&s.canScrollUp()&&(!r||r.distance>d)&&(r={scrollCache:s,name:"top",distance:d}),p<=n&&this.everMovedDown&&s.canScrollDown()&&(!r||r.distance>p)&&(r={scrollCache:s,name:"bottom",distance:p}),u<=n&&this.everMovedLeft&&s.canScrollLeft()&&(!r||r.distance>u)&&(r={scrollCache:s,name:"left",distance:u}),c<=n&&this.everMovedRight&&s.canScrollRight()&&(!r||r.distance>c)&&(r={scrollCache:s,name:"right",distance:c}))}return r},e.prototype.buildCaches=function(e){return this.queryScrollEls(e).map(function(e){return e===window?new ay(!1):new am(e,!1)})},e.prototype.queryScrollEls=function(e){for(var t=[],n=0,r=this.scrollQuery;n<r.length;n++){var o=r[n];"object"==typeof o?t.push(o):t.push.apply(t,Array.prototype.slice.call(ex(e).querySelectorAll(o)))}return t},e}(),aD=function(e){function t(t,n){var r=e.call(this,t)||this;r.containerEl=t,r.delay=null,r.minDistance=0,r.touchScrollAllowed=!0,r.mirrorNeedsRevert=!1,r.isInteracting=!1,r.isDragging=!1,r.isDelayEnded=!1,r.isDistanceSurpassed=!1,r.delayTimeoutId=null,r.onPointerDown=function(e){r.isDragging||(r.isInteracting=!0,r.isDelayEnded=!1,r.isDistanceSurpassed=!1,ez(document.body),eB(document.body),e.isTouch||e.origEvent.preventDefault(),r.emitter.trigger("pointerdown",e),!r.isInteracting||r.pointer.shouldIgnoreMove||(r.mirror.setIsVisible(!1),r.mirror.start(e.subjectEl,e.pageX,e.pageY),r.startDelay(e),r.minDistance||r.handleDistanceSurpassed(e)))},r.onPointerMove=function(e){if(r.isInteracting){if(r.emitter.trigger("pointermove",e),!r.isDistanceSurpassed){var t=r.minDistance,n=void 0,o=e.deltaX,i=e.deltaY;(n=o*o+i*i)>=t*t&&r.handleDistanceSurpassed(e)}r.isDragging&&("scroll"!==e.origEvent.type&&(r.mirror.handleMove(e.pageX,e.pageY),r.autoScroller.handleMove(e.pageX,e.pageY)),r.emitter.trigger("dragmove",e))}},r.onPointerUp=function(e){r.isInteracting&&(r.isInteracting=!1,eF(document.body),eG(document.body),r.emitter.trigger("pointerup",e),r.isDragging&&(r.autoScroller.stop(),r.tryStopDrag(e)),r.delayTimeoutId&&(clearTimeout(r.delayTimeoutId),r.delayTimeoutId=null))};var o=r.pointer=new af(t);return o.emitter.on("pointerdown",r.onPointerDown),o.emitter.on("pointermove",r.onPointerMove),o.emitter.on("pointerup",r.onPointerUp),n&&(o.selector=n),r.mirror=new av,r.autoScroller=new aS,r}return a(t,e),t.prototype.destroy=function(){this.pointer.destroy(),this.onPointerUp({})},t.prototype.startDelay=function(e){var t=this;"number"==typeof this.delay?this.delayTimeoutId=setTimeout(function(){t.delayTimeoutId=null,t.handleDelayEnd(e)},this.delay):this.handleDelayEnd(e)},t.prototype.handleDelayEnd=function(e){this.isDelayEnded=!0,this.tryStartDrag(e)},t.prototype.handleDistanceSurpassed=function(e){this.isDistanceSurpassed=!0,this.tryStartDrag(e)},t.prototype.tryStartDrag=function(e){this.isDelayEnded&&this.isDistanceSurpassed&&(!this.pointer.wasTouchScroll||this.touchScrollAllowed)&&(this.isDragging=!0,this.mirrorNeedsRevert=!1,this.autoScroller.start(e.pageX,e.pageY,this.containerEl),this.emitter.trigger("dragstart",e),!1===this.touchScrollAllowed&&this.pointer.cancelTouchScroll())},t.prototype.tryStopDrag=function(e){this.mirror.stop(this.mirrorNeedsRevert,this.stopDrag.bind(this,e))},t.prototype.stopDrag=function(e){this.isDragging=!1,this.emitter.trigger("dragend",e)},t.prototype.setIgnoreMove=function(e){this.pointer.shouldIgnoreMove=e},t.prototype.setMirrorIsVisible=function(e){this.mirror.setIsVisible(e)},t.prototype.setMirrorNeedsRevert=function(e){this.mirrorNeedsRevert=e},t.prototype.setAutoScrollEnabled=function(e){this.autoScroller.isEnabled=e},t}(oQ),ab=function(){function e(e){this.origRect=rN(e),this.scrollCaches=rH(e).map(function(e){return new am(e,!0)})}return e.prototype.destroy=function(){for(var e=0,t=this.scrollCaches;e<t.length;e++)t[e].destroy()},e.prototype.computeLeft=function(){for(var e=this.origRect.left,t=0,n=this.scrollCaches;t<n.length;t++){var r=n[t];e+=r.origScrollLeft-r.getScrollLeft()}return e},e.prototype.computeTop=function(){for(var e=this.origRect.top,t=0,n=this.scrollCaches;t<n.length;t++){var r=n[t];e+=r.origScrollTop-r.getScrollTop()}return e},e.prototype.isWithinClipping=function(e,t){for(var n={left:e,top:t},r=0,o=this.scrollCaches;r<o.length;r++){var i=o[r];if(!aC(i.getEventTarget())&&!rv(n,i.clientRect))return!1}return!0},e}();function aC(e){var t=e.tagName;return"HTML"===t||"BODY"===t}var a$=function(){function e(e,t){var n=this;this.useSubjectCenter=!1,this.requireInitial=!0,this.initialHit=null,this.movingHit=null,this.finalHit=null,this.handlePointerDown=function(e){var t=n.dragging;n.initialHit=null,n.movingHit=null,n.finalHit=null,n.prepareHits(),n.processFirstCoord(e),n.initialHit||!n.requireInitial?(t.setIgnoreMove(!1),n.emitter.trigger("pointerdown",e)):t.setIgnoreMove(!0)},this.handleDragStart=function(e){n.emitter.trigger("dragstart",e),n.handleMove(e,!0)},this.handleDragMove=function(e){n.emitter.trigger("dragmove",e),n.handleMove(e)},this.handlePointerUp=function(e){n.releaseHits(),n.emitter.trigger("pointerup",e)},this.handleDragEnd=function(e){n.movingHit&&n.emitter.trigger("hitupdate",null,!0,e),n.finalHit=n.movingHit,n.movingHit=null,n.emitter.trigger("dragend",e)},this.droppableStore=t,e.emitter.on("pointerdown",this.handlePointerDown),e.emitter.on("dragstart",this.handleDragStart),e.emitter.on("dragmove",this.handleDragMove),e.emitter.on("pointerup",this.handlePointerUp),e.emitter.on("dragend",this.handleDragEnd),this.dragging=e,this.emitter=new rA}return e.prototype.processFirstCoord=function(e){var t,n={left:e.pageX,top:e.pageY},r=n,o=e.subjectEl;o instanceof HTMLElement&&(t=rN(o),r=rm(r,t));var i=this.initialHit=this.queryHitForOffset(r.left,r.top);if(i){if(this.useSubjectCenter&&t){var a=rg(t,i.rect);a&&(r=ry(a))}this.coordAdjust=rE(r,n)}else this.coordAdjust={left:0,top:0}},e.prototype.handleMove=function(e,t){var n=this.queryHitForOffset(e.pageX+this.coordAdjust.left,e.pageY+this.coordAdjust.top);(t||!aR(this.movingHit,n))&&(this.movingHit=n,this.emitter.trigger("hitupdate",n,!1,e))},e.prototype.prepareHits=function(){this.offsetTrackers=tf(this.droppableStore,function(e){return e.component.prepareHits(),new ab(e.el)})},e.prototype.releaseHits=function(){var e=this.offsetTrackers;for(var t in e)e[t].destroy();this.offsetTrackers={}},e.prototype.queryHitForOffset=function(e,t){var n=this.droppableStore,r=this.offsetTrackers,o=null;for(var i in n){var a=n[i].component,s=r[i];if(s&&s.isWithinClipping(e,t)){var l=s.computeLeft(),u=s.computeTop(),c=e-l,d=t-u,p=s.origRect,f=p.right-p.left,h=p.bottom-p.top;if(c>=0&&c<f&&d>=0&&d<h){var v=a.queryHit(c,d,f,h);v&&nT(v.dateProfile.activeRange,v.dateSpan.range)&&(!o||v.layer>o.layer)&&(v.componentId=i,v.context=a.context,v.rect.left+=l,v.rect.right+=l,v.rect.top+=u,v.rect.bottom+=u,o=v)}}}return o},e}();function aR(e,t){return!e&&!t||Boolean(e)===Boolean(t)&&nq(e.dateSpan,t.dateSpan)}function a8(e,t){for(var n,r,o={},i=0,a=t.pluginHooks.datePointTransforms;i<a.length;i++)s(o,(0,a[i])(e,t));return s(o,(n=e,r=t.dateEnv,{date:r.toDate(n.range.start),dateStr:r.formatIso(n.range.start,{omitTime:n.allDay}),allDay:n.allDay})),o}var aw=function(e){function t(t){var n=e.call(this,t)||this;n.handlePointerDown=function(e){var t=n.dragging,r=e.origEvent.target;t.setIgnoreMove(!n.component.isValidDateDownEl(r))},n.handleDragEnd=function(e){var t=n.component;if(!n.dragging.pointer.wasTouchScroll){var r=n.hitDragging,o=r.initialHit,i=r.finalHit;if(o&&i&&aR(o,i)){var a=t.context,l=s(s({},a8(o.dateSpan,a)),{dayEl:o.dayEl,jsEvent:e.origEvent,view:a.viewApi||a.calendarApi.view});a.emitter.trigger("dateClick",l)}}},n.dragging=new aD(t.el),n.dragging.autoScroller.isEnabled=!1;var r=n.hitDragging=new a$(n.dragging,o7(t));return r.emitter.on("pointerdown",n.handlePointerDown),r.emitter.on("dragend",n.handleDragEnd),n}return a(t,e),t.prototype.destroy=function(){this.dragging.destroy()},t}(o9),aT=function(e){function t(t){var n=e.call(this,t)||this;n.dragSelection=null,n.handlePointerDown=function(e){var t,r,o,i=n,a=i.component,s=i.dragging,l=a.context.options.selectable&&a.isValidDateDownEl(e.origEvent.target);s.setIgnoreMove(!l),s.delay=e.isTouch?(t=a,r=t.context.options,o=r.selectLongPressDelay,null==o&&(o=r.longPressDelay),o):null},n.handleDragStart=function(e){n.component.context.calendarApi.unselect(e)},n.handleHitUpdate=function(e,t){var r=n.component.context,o=null,i=!1;if(e){var a=n.hitDragging.initialHit;e.componentId===a.componentId&&n.isHitComboAllowed&&!n.isHitComboAllowed(a,e)||(o=function e(t,n,r){var o=t.dateSpan,i=n.dateSpan,a=[o.range.start,o.range.end,i.range.start,i.range.end,];a.sort(e1);for(var l={},u=0,c=r;u<c.length;u++){var d=(0,c[u])(t,n);if(!1===d)return null;d&&s(l,d)}return l.range={start:a[0],end:a[3]},l.allDay=o.allDay,l}(a,e,r.pluginHooks.dateSelectionTransformers)),o&&iT(o,e.dateProfile,r)||(i=!0,o=null)}o?r.dispatch({type:"SELECT_DATES",selection:o}):t||r.dispatch({type:"UNSELECT_DATES"}),i?eW():eV(),t||(n.dragSelection=o)},n.handlePointerUp=function(e){n.dragSelection&&(nZ(n.dragSelection,e,n.component.context),n.dragSelection=null)};var r=t.component.context.options,o=n.dragging=new aD(t.el);o.touchScrollAllowed=!1,o.minDistance=r.selectMinDistance||0,o.autoScroller.isEnabled=r.dragScroll;var i=n.hitDragging=new a$(n.dragging,o7(t));return i.emitter.on("pointerdown",n.handlePointerDown),i.emitter.on("dragstart",n.handleDragStart),i.emitter.on("hitupdate",n.handleHitUpdate),i.emitter.on("pointerup",n.handlePointerUp),n}return a(t,e),t.prototype.destroy=function(){this.dragging.destroy()},t}(o9),ak=function(e){function t(n){var r=e.call(this,n)||this;r.subjectEl=null,r.subjectSeg=null,r.isDragging=!1,r.eventRange=null,r.relevantEvents=null,r.receivingContext=null,r.validMutation=null,r.mutatedRelevantEvents=null,r.handlePointerDown=function(e){var t=e.origEvent.target,n=r,o=n.component,i=n.dragging,a=i.mirror,s=o.context.options,l=o.context;r.subjectEl=e.subjectEl;var u,c,d,p=r.subjectSeg=nI(e.subjectEl),f=(r.eventRange=p.eventRange).instance.instanceId;r.relevantEvents=nn(l.getCurrentData().eventStore,f),i.minDistance=e.isTouch?0:s.eventDragMinDistance,i.delay=e.isTouch&&f!==o.props.eventSelection?(u=o,c=u.context.options,d=c.eventLongPressDelay,null==d&&(d=c.longPressDelay),d):null,s.fixedMirrorParent?a.parentNode=s.fixedMirrorParent:a.parentNode=eC(t,".fc"),a.revertDuration=s.dragRevertDuration;var h=o.isValidSegDownEl(t)&&!eC(t,".fc-event-resizer");i.setIgnoreMove(!h),r.isDragging=h&&e.subjectEl.classList.contains("fc-event-draggable")},r.handleDragStart=function(e){var t=r.component.context,n=r.eventRange,o=n.instance.instanceId;e.isTouch?o!==r.component.props.eventSelection&&t.dispatch({type:"SELECT_EVENT",eventInstanceId:o}):t.dispatch({type:"UNSELECT_EVENT"}),r.isDragging&&(t.calendarApi.unselect(e),t.emitter.trigger("eventDragStart",{el:r.subjectEl,event:new nQ(t,n.def,n.instance),jsEvent:e.origEvent,view:t.viewApi}))},r.handleHitUpdate=function(e,t){if(r.isDragging){var n=r.relevantEvents,o=r.hitDragging.initialHit,i=r.component.context,a=null,s=null,l=null,u=!1,c={affectedEvents:n,mutatedEvents:nr(),isEvent:!0};if(e){var d=(a=e.context).options;i===a||d.editable&&d.droppable?(s=function e(t,n,r){var o=t.dateSpan,i=n.dateSpan,a=o.range.start,s=i.range.start,l={};o.allDay!==i.allDay&&(l.allDay=i.allDay,l.hasEnd=n.context.options.allDayMaintainDuration,i.allDay&&(a=tt(a)));var u=nb(a,s,t.context.dateEnv,t.componentId===n.componentId?t.largeUnit:null);u.milliseconds&&(l.allDay=!1);for(var c={datesDelta:u,standardProps:l},d=0,p=r;d<p.length;d++)(0,p[d])(c,t,n);return c}(o,e,a.getCurrentData().pluginHooks.eventDragMutationMassagers))&&(l=n1(n,a.getCurrentData().eventUiBases,s,a),c.mutatedEvents=l,iw(c,e.dateProfile,a)||(u=!0,s=null,l=null,c.mutatedEvents=nr())):a=null}r.displayDrag(a,c),u?eW():eV(),t||(i===a&&aR(o,e)&&(s=null),r.dragging.setMirrorNeedsRevert(!s),r.dragging.setMirrorIsVisible(!e||!ex(r.subjectEl).querySelector(".fc-event-mirror")),r.receivingContext=a,r.validMutation=s,r.mutatedRelevantEvents=l)}},r.handlePointerUp=function(){r.isDragging||r.cleanup()},r.handleDragEnd=function(e){if(r.isDragging){var t=r.component.context,n=t.viewApi,o=r,i=o.receivingContext,a=o.validMutation,l=r.eventRange.def,u=r.eventRange.instance,c=new nQ(t,l,u),d=r.relevantEvents,p=r.mutatedRelevantEvents,f=r.hitDragging.finalHit;if(r.clearDrag(),t.emitter.trigger("eventDragStop",{el:r.subjectEl,event:c,jsEvent:e.origEvent,view:n}),a){if(i===t){var h=new nQ(t,p.defs[l.defId],u?p.instances[u.instanceId]:null);t.dispatch({type:"MERGE_EVENTS",eventStore:p});for(var v={oldEvent:c,event:h,relatedEvents:re(p,t,u),revert:function(){t.dispatch({type:"MERGE_EVENTS",eventStore:d})}},g={},m=0,y=t.getCurrentData().pluginHooks.eventDropTransformers;m<y.length;m++)s(g,(0,y[m])(a,t));t.emitter.trigger("eventDrop",s(s(s({},v),g),{el:e.subjectEl,delta:a.datesDelta,jsEvent:e.origEvent,view:n})),t.emitter.trigger("eventChange",v)}else if(i){var E={event:c,relatedEvents:re(d,t,u),revert:function(){t.dispatch({type:"MERGE_EVENTS",eventStore:d})}};t.emitter.trigger("eventLeave",s(s({},E),{draggedEl:e.subjectEl,view:n})),t.dispatch({type:"REMOVE_EVENTS",eventStore:d}),t.emitter.trigger("eventRemove",E);var S=p.defs[l.defId],D=p.instances[u.instanceId],b=new nQ(i,S,D);i.dispatch({type:"MERGE_EVENTS",eventStore:p});var C={event:b,relatedEvents:re(p,i,D),revert:function(){i.dispatch({type:"REMOVE_EVENTS",eventStore:p})}};i.emitter.trigger("eventAdd",C),e.isTouch&&i.dispatch({type:"SELECT_EVENT",eventInstanceId:u.instanceId}),i.emitter.trigger("drop",s(s({},a8(f.dateSpan,i)),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:f.context.viewApi})),i.emitter.trigger("eventReceive",s(s({},C),{draggedEl:e.subjectEl,view:f.context.viewApi}))}}else t.emitter.trigger("_noEventDrop")}r.cleanup()};var o=r.component.context.options,i=r.dragging=new aD(n.el);i.pointer.selector=t.SELECTOR,i.touchScrollAllowed=!1,i.autoScroller.isEnabled=o.dragScroll;var a=r.hitDragging=new a$(r.dragging,o6);return a.useSubjectCenter=n.useEventCenter,a.emitter.on("pointerdown",r.handlePointerDown),a.emitter.on("dragstart",r.handleDragStart),a.emitter.on("hitupdate",r.handleHitUpdate),a.emitter.on("pointerup",r.handlePointerUp),a.emitter.on("dragend",r.handleDragEnd),r}return a(t,e),t.prototype.destroy=function(){this.dragging.destroy()},t.prototype.displayDrag=function(e,t){var n=this.component.context,r=this.receivingContext;r&&r!==e&&(r===n?r.dispatch({type:"SET_EVENT_DRAG",state:{affectedEvents:t.affectedEvents,mutatedEvents:nr(),isEvent:!0}}):r.dispatch({type:"UNSET_EVENT_DRAG"})),e&&e.dispatch({type:"SET_EVENT_DRAG",state:t})},t.prototype.clearDrag=function(){var e=this.component.context,t=this.receivingContext;t&&t.dispatch({type:"UNSET_EVENT_DRAG"}),e!==t&&e.dispatch({type:"UNSET_EVENT_DRAG"})},t.prototype.cleanup=function(){this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingContext=null,this.validMutation=null,this.mutatedRelevantEvents=null},t.SELECTOR=".fc-event-draggable, .fc-event-resizable",t}(o9),ax=function(e){function t(t){var n=e.call(this,t)||this;n.draggingSegEl=null,n.draggingSeg=null,n.eventRange=null,n.relevantEvents=null,n.validMutation=null,n.mutatedRelevantEvents=null,n.handlePointerDown=function(e){var t=n.component,r=n.querySegEl(e),o=nI(r),i=n.eventRange=o.eventRange;n.dragging.minDistance=t.context.options.eventDragMinDistance,n.dragging.setIgnoreMove(!n.component.isValidSegDownEl(e.origEvent.target)||e.isTouch&&n.component.props.eventSelection!==i.instance.instanceId)},n.handleDragStart=function(e){var t=n.component.context,r=n.eventRange;n.relevantEvents=nn(t.getCurrentData().eventStore,n.eventRange.instance.instanceId);var o=n.querySegEl(e);n.draggingSegEl=o,n.draggingSeg=nI(o),t.calendarApi.unselect(),t.emitter.trigger("eventResizeStart",{el:o,event:new nQ(t,r.def,r.instance),jsEvent:e.origEvent,view:t.viewApi})},n.handleHitUpdate=function(e,t,r){var o=n.component.context,i=n.relevantEvents,a=n.hitDragging.initialHit,s=n.eventRange.instance,l=null,u=null,c=!1,d={affectedEvents:i,mutatedEvents:nr(),isEvent:!0};e&&(e.componentId===a.componentId&&n.isHitComboAllowed&&!n.isHitComboAllowed(a,e)||(l=function e(t,n,r,o){var i,a=t.context.dateEnv,s=nb(t.dateSpan.range.start,n.dateSpan.range.start,a,t.largeUnit);if(r){if(a.add(o.start,s)<o.end)return{startDelta:s}}else if(a.add(o.end,s)>o.start)return{endDelta:s};return null}(a,e,r.subjectEl.classList.contains("fc-event-resizer-start"),s.range))),l&&(u=n1(i,o.getCurrentData().eventUiBases,l,o),d.mutatedEvents=u,iw(d,e.dateProfile,o)||(c=!0,l=null,u=null,d.mutatedEvents=null)),u?o.dispatch({type:"SET_EVENT_RESIZE",state:d}):o.dispatch({type:"UNSET_EVENT_RESIZE"}),c?eW():eV(),t||(l&&aR(a,e)&&(l=null),n.validMutation=l,n.mutatedRelevantEvents=u)},n.handleDragEnd=function(e){var t=n.component.context,r=n.eventRange.def,o=n.eventRange.instance,i=new nQ(t,r,o),a=n.relevantEvents,l=n.mutatedRelevantEvents;if(t.emitter.trigger("eventResizeStop",{el:n.draggingSegEl,event:i,jsEvent:e.origEvent,view:t.viewApi}),n.validMutation){var u=new nQ(t,l.defs[r.defId],o?l.instances[o.instanceId]:null);t.dispatch({type:"MERGE_EVENTS",eventStore:l});var c={oldEvent:i,event:u,relatedEvents:re(l,t,o),revert:function(){t.dispatch({type:"MERGE_EVENTS",eventStore:a})}};t.emitter.trigger("eventResize",s(s({},c),{el:n.draggingSegEl,startDelta:n.validMutation.startDelta||tR(0),endDelta:n.validMutation.endDelta||tR(0),jsEvent:e.origEvent,view:t.viewApi})),t.emitter.trigger("eventChange",c)}else t.emitter.trigger("_noEventResize");n.draggingSeg=null,n.relevantEvents=null,n.validMutation=null};var r=t.component,o=n.dragging=new aD(t.el);o.pointer.selector=".fc-event-resizer",o.touchScrollAllowed=!1,o.autoScroller.isEnabled=r.context.options.dragScroll;var i=n.hitDragging=new a$(n.dragging,o7(t));return i.emitter.on("pointerdown",n.handlePointerDown),i.emitter.on("dragstart",n.handleDragStart),i.emitter.on("hitupdate",n.handleHitUpdate),i.emitter.on("dragend",n.handleDragEnd),n}return a(t,e),t.prototype.destroy=function(){this.dragging.destroy()},t.prototype.querySegEl=function(e){return eC(e.subjectEl,".fc-event")},t}(o9),aM=function(){function e(e){var t=this;this.context=e,this.isRecentPointerDateSelect=!1,this.matchesCancel=!1,this.matchesEvent=!1,this.onSelect=function(e){e.jsEvent&&(t.isRecentPointerDateSelect=!0)},this.onDocumentPointerDown=function(e){var n=t.context.options.unselectCancel,r=ek(e.origEvent);t.matchesCancel=!!eC(r,n),t.matchesEvent=!!eC(r,ak.SELECTOR)},this.onDocumentPointerUp=function(e){var n=t.context,r=t.documentPointer,o=n.getCurrentData();if(!r.wasTouchScroll){if(o.dateSelection&&!t.isRecentPointerDateSelect){var i=n.options.unselectAuto;!i||i&&t.matchesCancel||n.calendarApi.unselect(e)}o.eventSelection&&!t.matchesEvent&&n.dispatch({type:"UNSELECT_EVENT"})}t.isRecentPointerDateSelect=!1};var n=this.documentPointer=new af(document);n.shouldIgnoreMove=!0,n.shouldWatchScroll=!1,n.emitter.on("pointerdown",this.onDocumentPointerDown),n.emitter.on("pointerup",this.onDocumentPointerUp),e.emitter.on("select",this.onSelect)}return e.prototype.destroy=function(){this.context.emitter.off("select",this.onSelect),this.documentPointer.destroy()},e}(),a_=function(){function e(e,t){var n=this;this.receivingContext=null,this.droppableEvent=null,this.suppliedDragMeta=null,this.dragMeta=null,this.handleDragStart=function(e){n.dragMeta=n.buildDragMeta(e.subjectEl)},this.handleHitUpdate=function(e,t,r){var o=n.hitDragging.dragging,i=null,a=null,l=!1,u={affectedEvents:nr(),mutatedEvents:nr(),isEvent:n.dragMeta.create};e&&(i=e.context,n.canDropElOnCalendar(r.subjectEl,i)&&(a=function e(t,n,r){for(var o=s({},n.leftoverProps),i=0,a=r.pluginHooks.externalDefTransforms;i<a.length;i++)s(o,(0,a[i])(t,n));var l,u=ng(o,r),c=ny(u.refined,u.extra,n.sourceId,t.allDay,r.options.forceEventDuration||Boolean(n.duration),r),d=t.range.start;t.allDay&&n.startTime&&(d=r.dateEnv.add(d,n.startTime));var p=n.duration?r.dateEnv.add(d,n.duration):n0(t.allDay,d,r),f=tu(c.defId,{start:d,end:p});return{def:c,instance:f}}(e.dateSpan,n.dragMeta,i),u.mutatedEvents=nt(a),(l=!iw(u,e.dateProfile,i))&&(u.mutatedEvents=nr(),a=null))),n.displayDrag(i,u),o.setMirrorIsVisible(t||!a||!document.querySelector(".fc-event-mirror")),l?eW():eV(),t||(o.setMirrorNeedsRevert(!a),n.receivingContext=i,n.droppableEvent=a)},this.handleDragEnd=function(e){var t=n,r=t.receivingContext,o=t.droppableEvent;if(n.clearDrag(),r&&o){var i=n.hitDragging.finalHit,a=i.context.viewApi,l=n.dragMeta;if(r.emitter.trigger("drop",s(s({},a8(i.dateSpan,r)),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:a})),l.create){var u=nt(o);r.dispatch({type:"MERGE_EVENTS",eventStore:u}),e.isTouch&&r.dispatch({type:"SELECT_EVENT",eventInstanceId:o.instance.instanceId}),r.emitter.trigger("eventReceive",{event:new nQ(r,o.def,o.instance),relatedEvents:[],revert:function(){r.dispatch({type:"REMOVE_EVENTS",eventStore:u})},draggedEl:e.subjectEl,view:a})}}n.receivingContext=null,n.droppableEvent=null};var r=this.hitDragging=new a$(e,o6);r.requireInitial=!1,r.emitter.on("dragstart",this.handleDragStart),r.emitter.on("hitupdate",this.handleHitUpdate),r.emitter.on("dragend",this.handleDragEnd),this.suppliedDragMeta=t}return e.prototype.buildDragMeta=function(e){var t,n,r,o,i;return"object"==typeof this.suppliedDragMeta?it(this.suppliedDragMeta):"function"==typeof this.suppliedDragMeta?it(this.suppliedDragMeta(e)):(t=e,r="event",i=(n=t,o=oJ.dataAttrPrefix,n.getAttribute("data-"+(o?o+"-":"")+r)||""),it(i?JSON.parse(i):{create:!1}))},e.prototype.displayDrag=function(e,t){var n=this.receivingContext;n&&n!==e&&n.dispatch({type:"UNSET_EVENT_DRAG"}),e&&e.dispatch({type:"SET_EVENT_DRAG",state:t})},e.prototype.clearDrag=function(){this.receivingContext&&this.receivingContext.dispatch({type:"UNSET_EVENT_DRAG"})},e.prototype.canDropElOnCalendar=function(e,t){var n=t.options.dropAccept;return"function"==typeof n?n.call(t.calendarApi,e):"string"!=typeof n||!n||Boolean(e$(e,n))},e}();oJ.dataAttrPrefix="";var aI=function(){function e(e,t){var n=this;void 0===t&&(t={}),this.handlePointerDown=function(e){var t=n.dragging,r=n.settings,o=r.minDistance,i=r.longPressDelay;t.minDistance=null!=o?o:e.isTouch?0:tK.eventDragMinDistance,t.delay=e.isTouch?null!=i?i:tK.longPressDelay:0},this.handleDragStart=function(e){e.isTouch&&n.dragging.delay&&e.subjectEl.classList.contains("fc-event")&&n.dragging.mirror.getMirrorEl().classList.add("fc-event-selected")},this.settings=t;var r=this.dragging=new aD(e);r.touchScrollAllowed=!1,null!=t.itemSelector&&(r.pointer.selector=t.itemSelector),null!=t.appendTo&&(r.mirror.parentNode=t.appendTo),r.emitter.on("pointerdown",this.handlePointerDown),r.emitter.on("dragstart",this.handleDragStart),new a_(r,t.eventData)}return e.prototype.destroy=function(){this.dragging.destroy()},e}(),aP=function(e){function t(t){var n=e.call(this,t)||this;n.shouldIgnoreMove=!1,n.mirrorSelector="",n.currentMirrorEl=null,n.handlePointerDown=function(e){n.emitter.trigger("pointerdown",e),n.shouldIgnoreMove||n.emitter.trigger("dragstart",e)},n.handlePointerMove=function(e){n.shouldIgnoreMove||n.emitter.trigger("dragmove",e)},n.handlePointerUp=function(e){n.emitter.trigger("pointerup",e),n.shouldIgnoreMove||n.emitter.trigger("dragend",e)};var r=n.pointer=new af(t);return r.emitter.on("pointerdown",n.handlePointerDown),r.emitter.on("pointermove",n.handlePointerMove),r.emitter.on("pointerup",n.handlePointerUp),n}return a(t,e),t.prototype.destroy=function(){this.pointer.destroy()},t.prototype.setIgnoreMove=function(e){this.shouldIgnoreMove=e},t.prototype.setMirrorIsVisible=function(e){if(e)this.currentMirrorEl&&(this.currentMirrorEl.style.visibility="",this.currentMirrorEl=null);else{var t=this.mirrorSelector?document.querySelector(this.mirrorSelector):null;t&&(this.currentMirrorEl=t,t.style.visibility="hidden")}},t}(oQ),aN=function(){function e(e,t){var n=document;e===document||e instanceof Element?(n=e,t=t||{}):t=e||{};var r=this.dragging=new aP(n);"string"==typeof t.itemSelector?r.pointer.selector=t.itemSelector:n===document&&(r.pointer.selector="[data-event]"),"string"==typeof t.mirrorSelector&&(r.mirrorSelector=t.mirrorSelector),new a_(r,t.eventData)}return e.prototype.destroy=function(){this.dragging.destroy()},e}(),aH=r6({componentInteractions:[aw,aT,ak,ax],calendarInteractions:[aM],elementDraggingImpl:aD,optionRefiners:{fixedMirrorParent:tJ},listenerRefiners:{dateClick:tJ,eventDragStart:tJ,eventDragStop:tJ,eventDrop:tJ,eventResizeStart:tJ,eventResizeStop:tJ,eventResize:tJ,drop:tJ,eventReceive:tJ,eventLeave:tJ}}),aO=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.headerElRef=rq(),t}return a(t,e),t.prototype.renderSimpleLayout=function(e,t){var n=this.props,r=this.context,o=[],i=iY(r.options);return e&&o.push({type:"header",key:"header",isSticky:i,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),o.push({type:"body",key:"body",liquid:!0,chunk:{content:t}}),rB(ou,{viewSpec:r.viewSpec},function(e,t){return rB("div",{ref:e,className:["fc-daygrid"].concat(t).join(" ")},rB(iX,{liquid:!n.isHeightAuto&&!n.forPrint,collapsibleWidth:n.forPrint,cols:[],sections:o}))})},t.prototype.renderHScrollLayout=function(e,t,n,r){var o=this.context.pluginHooks.scrollGridImpl;if(!o)throw Error("No ScrollGrid implementation");var i=this.props,a=this.context,s=!i.forPrint&&iY(a.options),l=!i.forPrint&&iZ(a.options),u=[];return e&&u.push({type:"header",key:"header",isSticky:s,chunks:[{key:"main",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}]}),u.push({type:"body",key:"body",liquid:!0,chunks:[{key:"main",content:t}]}),l&&u.push({type:"footer",key:"footer",isSticky:!0,chunks:[{key:"main",content:ij}]}),rB(ou,{viewSpec:a.viewSpec},function(e,t){return rB("div",{ref:e,className:["fc-daygrid"].concat(t).join(" ")},rB(o,{liquid:!i.isHeightAuto&&!i.forPrint,collapsibleWidth:i.forPrint,colGroups:[{cols:[{span:n,minWidth:r}]}],sections:u}))})},t}(r7);function aA(e,t){for(var n=[],r=0;r<t;r+=1)n[r]=[];for(var o=0,i=e;o<i.length;o++){var a=i[o];n[a.row].push(a)}return n}function aL(e,t){for(var n=[],r=0;r<t;r+=1)n[r]=[];for(var o=0,i=e;o<i.length;o++){var a=i[o];n[a.firstCol].push(a)}return n}function aU(e,t){var n=[];if(e){for(var r=0;r<t;r+=1)n[r]={affectedInstances:e.affectedInstances,isEvent:e.isEvent,segs:[]};for(var o=0,i=e.segs;o<i.length;o++){var a=i[o];n[a.row].segs.push(a)}}else for(var r=0;r<t;r+=1)n[r]=null;return n}var aW=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.props,t=rT(this.context,e.date);return rB(i2,{date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,showDayNumber:e.showDayNumber,extraHookProps:e.extraHookProps,defaultContent:aV},function(n,r){return(r||e.forceDayTop)&&rB("div",{className:"fc-daygrid-day-top",ref:n},rB("a",s({id:e.dayNumberId,className:"fc-daygrid-day-number"},t),r||rB(rj,null,"\xa0")))})},t}(r3);function aV(e){return e.dayNumberText}var az=t1({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"});function aF(e){var t=e.eventRange.ui.display;return"list-item"===t||"auto"===t&&!e.eventRange.def.allDay&&e.firstCol===e.lastCol&&e.isStart&&e.isEnd}var aB=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.props;return rB(i1,s({},e,{extraClassNames:["fc-daygrid-event","fc-daygrid-block-event","fc-h-event"],defaultTimeFormat:az,defaultDisplayEventEnd:e.defaultDisplayEventEnd,disableResizing:!e.seg.eventRange.def.allDay}))},t}(r3),aG=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options.eventTimeFormat||az,r=nW(e.seg,n,t,!0,e.defaultDisplayEventEnd);return rB(i0,{seg:e.seg,timeText:r,defaultContent:aq,isDragging:e.isDragging,isResizing:!1,isDateSelecting:!1,isSelected:e.isSelected,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday},function(n,r,o,i){return rB("a",s({className:["fc-daygrid-event","fc-daygrid-dot-event"].concat(r).join(" "),ref:n},nB(e.seg,t)),i)})},t}(r3);function aq(e){return rB(rj,null,rB("div",{className:"fc-daygrid-event-dot",style:{borderColor:e.borderColor||e.backgroundColor}}),e.timeText&&rB("div",{className:"fc-event-time"},e.timeText),rB("div",{className:"fc-event-title"},e.event.title||rB(rj,null,"\xa0")))}var aj=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.compileSegs=tA(aY),t}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.compileSegs(e.singlePlacements),n=t.allSegs,r=t.invisibleSegs;return rB(ar,{dateProfile:e.dateProfile,todayRange:e.todayRange,allDayDate:e.allDayDate,moreCnt:e.moreCnt,allSegs:n,hiddenSegs:r,alignmentElRef:e.alignmentElRef,alignGridTop:e.alignGridTop,extraDateSpan:e.extraDateSpan,popoverContent:function(){var t=(e.eventDrag?e.eventDrag.affectedInstances:null)||(e.eventResize?e.eventResize.affectedInstances:null)||{};return rB(rj,null,n.map(function(n){var r=n.eventRange.instance.instanceId;return rB("div",{className:"fc-daygrid-event-harness",key:r,style:{visibility:t[r]?"hidden":""}},aF(n)?rB(aG,s({seg:n,isDragging:!1,isSelected:r===e.eventSelection,defaultDisplayEventEnd:!1},nV(n,e.todayRange))):rB(aB,s({seg:n,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:r===e.eventSelection,defaultDisplayEventEnd:!1},nV(n,e.todayRange))))}))}},function(e,t,n,r,o,i,a,l){return rB("a",s({ref:e,className:["fc-daygrid-more-link"].concat(t).join(" "),title:i,"aria-expanded":a,"aria-controls":l},eO(o)),r)})},t}(r3);function aY(e){for(var t=[],n=[],r=0,o=e;r<o.length;r++){var i=o[r];t.push(i.seg),i.isVisible||n.push(i.seg)}return{allSegs:t,invisibleSegs:n}}var aZ=t1({week:"narrow"}),aX=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.rootElRef=rq(),t.state={dayNumberId:e_()},t.handleRootEl=function(e){r9(t.rootElRef,e),r9(t.props.elRef,e)},t}return a(t,e),t.prototype.render=function(){var e=this.context,t=this.props,n=this.state,r=this.rootElRef,o=t.date,i=t.dateProfile,a=rT(e,o,"week");return rB(i9,{date:o,dateProfile:i,todayRange:t.todayRange,showDayNumber:t.showDayNumber,extraHookProps:t.extraHookProps,elRef:this.handleRootEl},function(e,l,u,c){return rB("td",s({ref:e,role:"gridcell",className:["fc-daygrid-day"].concat(l,t.extraClassNames||[]).join(" ")},u,t.extraDataAttrs,t.showDayNumber?{"aria-labelledby":n.dayNumberId}:{}),rB("div",{className:"fc-daygrid-day-frame fc-scrollgrid-sync-inner",ref:t.innerElRef},t.showWeekNumber&&rB(iJ,{date:o,defaultFormat:aZ},function(e,t,n,r){return rB("a",s({ref:e,className:["fc-daygrid-week-number"].concat(t).join(" ")},a),r)}),!c&&rB(aW,{date:o,dateProfile:i,showDayNumber:t.showDayNumber,dayNumberId:n.dayNumberId,forceDayTop:t.forceDayTop,todayRange:t.todayRange,extraHookProps:t.extraHookProps}),rB("div",{className:"fc-daygrid-day-events",ref:t.fgContentElRef},t.fgContent,rB("div",{className:"fc-daygrid-day-bottom",style:{marginTop:t.moreMarginTop}},rB(aj,{allDayDate:o,singlePlacements:t.singlePlacements,moreCnt:t.moreCnt,alignmentElRef:r,alignGridTop:!t.showDayNumber,extraDateSpan:t.extraDateSpan,dateProfile:t.dateProfile,eventSelection:t.eventSelection,eventDrag:t.eventDrag,eventResize:t.eventResize,todayRange:t.todayRange}))),rB("div",{className:"fc-daygrid-day-bg"},t.bgContent)))})},t}(r7);function a0(e,t,n,r){if(e.firstCol===t&&e.lastCol===n-1)return e;var o=e.eventRange,i=o.range,a=nR(i,{start:r[t].date,end:e5(r[n-1].date,1)});return s(s({},e),{firstCol:t,lastCol:n-1,eventRange:{def:o.def,ui:s(s({},o.ui),{durationEditable:!1}),instance:o.instance,range:a},isStart:e.isStart&&a.start.valueOf()===i.start.valueOf(),isEnd:e.isEnd&&a.end.valueOf()===i.end.valueOf()})}var a1=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.hiddenConsumes=!1,t.forceHidden={},t}return a(t,e),t.prototype.addSegs=function(t){for(var n=this,r=e.prototype.addSegs.call(this,t),o=this.entriesByLevel,i=function(e){return!n.forceHidden[o1(e)]},a=0;a<o.length;a+=1)o[a]=o[a].filter(i);return r},t.prototype.handleInvalidInsertion=function(t,n,r){var o=this.entriesByLevel,i=this.forceHidden,a=t.touchingEntry,l=t.touchingLevel,u=t.touchingLateral;if(this.hiddenConsumes&&a){var c=o1(a);if(!i[c]){if(this.allowReslicing){var d=s(s({},a),{span:o3(a.span,n.span)});i[o1(d)]=!0,o[l][u]=d,this.splitEntry(a,n,r)}else i[c]=!0,r.push(a)}}return e.prototype.handleInvalidInsertion.call(this,t,n,r)},t}(oX),a4=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.cellElRefs=new iO,t.frameElRefs=new iO,t.fgElRefs=new iO,t.segHarnessRefs=new iO,t.rootElRef=rq(),t.state={framePositions:null,maxContentHeight:null,eventInstanceHeights:{}},t}return a(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.state,r=this.context.options,o=t.cells.length,i=aL(t.businessHourSegs,o),a=aL(t.bgEventSegs,o),s=aL(this.getHighlightSegs(),o),l=aL(this.getMirrorSegs(),o),u=function e(t,n,r,o,i,a,s){var l=new a1;l.allowReslicing=!0,l.strictOrder=o,!0===n||!0===r?(l.maxCoord=a,l.hiddenConsumes=!0):"number"==typeof n?l.maxStackCnt=n:"number"==typeof r&&(l.maxStackCnt=r,l.hiddenConsumes=!0);for(var u=[],c=[],d=0;d<t.length;d+=1){var p=t[d],f=i[p.eventRange.instance.instanceId];null!=f?u.push({index:d,thickness:f,span:{start:p.firstCol,end:p.lastCol+1}}):c.push(p)}for(var h=l.addSegs(u),v=function e(t,n,r){for(var o=function e(t,n){for(var r=[],o=0;o<n;o+=1)r.push([]);for(var i=0,a=t;i<a.length;i++)for(var s=a[i],o=s.span.start;o<s.span.end;o+=1)r[o].push(s);return r}(t,r.length),i=[],a=[],s=[],l=0;l<r.length;l+=1){for(var u=o[l],c=[],d=0,p=0,f=0,h=u;f<h.length;f++){var v=h[f],g=n[v.index];c.push({seg:a0(g,l,l+1,r),isVisible:!0,isAbsolute:!1,absoluteTop:v.levelCoord,marginTop:v.levelCoord-d}),d=v.levelCoord+v.thickness}var m=[];d=0,p=0;for(var y=0,E=u;y<E.length;y++){var v=E[y],g=n[v.index],S=v.span.end-v.span.start>1,D=v.span.start===l;p+=v.levelCoord-d,d=v.levelCoord+v.thickness,S?(p+=v.thickness,D&&m.push({seg:a0(g,v.span.start,v.span.end,r),isVisible:!0,isAbsolute:!0,absoluteTop:v.levelCoord,marginTop:0})):D&&(m.push({seg:a0(g,v.span.start,v.span.end,r),isVisible:!0,isAbsolute:!1,absoluteTop:v.levelCoord,marginTop:p}),p=0)}i.push(c),a.push(m),s.push(p)}return{singleColPlacements:i,multiColPlacements:a,leftoverMargins:s}}(l.toRects(),t,s),g=v.singleColPlacements,m=v.multiColPlacements,y=v.leftoverMargins,E=[],S=[],D=0,b=c;D<b.length;D++){var p=b[D];m[p.firstCol].push({seg:p,isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(var C=p.firstCol;C<=p.lastCol;C+=1)g[C].push({seg:a0(p,C,C+1,s),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(var C=0;C<s.length;C+=1)E.push(0);for(var $=0,R=h;$<R.length;$++){var w=R[$],p=t[w.index],T=w.span;m[T.start].push({seg:a0(p,T.start,T.end,s),isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(var C=T.start;C<T.end;C+=1)E[C]+=1,g[C].push({seg:a0(p,C,C+1,s),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(var C=0;C<s.length;C+=1)S.push(y[C]);return{singleColPlacements:g,multiColPlacements:m,moreCnts:E,moreMarginTops:S}}(nH(t.fgEventSegs,r.eventOrder),t.dayMaxEvents,t.dayMaxEventRows,r.eventOrderStrict,n.eventInstanceHeights,n.maxContentHeight,t.cells),c=u.singleColPlacements,d=u.multiColPlacements,p=u.moreCnts,f=u.moreMarginTops,h=t.eventDrag&&t.eventDrag.affectedInstances||t.eventResize&&t.eventResize.affectedInstances||{};return rB("tr",{ref:this.rootElRef,role:"row"},t.renderIntro&&t.renderIntro(),t.cells.map(function(n,r){var o=e.renderFgSegs(r,t.forPrint?c[r]:d[r],t.todayRange,h),u=e.renderFgSegs(r,function e(t,n){if(!t.length)return[];var r=function e(t){for(var n={},r=0,o=t;r<o.length;r++)for(var i=o[r],a=0,s=i;a<s.length;a++){var l=s[a];n[l.seg.eventRange.instance.instanceId]=l.absoluteTop}return n}(n);return t.map(function(e){return{seg:e,isVisible:!0,isAbsolute:!0,absoluteTop:r[e.eventRange.instance.instanceId],marginTop:0}})}(l[r],d),t.todayRange,{},Boolean(t.eventDrag),Boolean(t.eventResize),!1);return rB(aX,{key:n.key,elRef:e.cellElRefs.createRef(n.key),innerElRef:e.frameElRefs.createRef(n.key),dateProfile:t.dateProfile,date:n.date,showDayNumber:t.showDayNumbers,showWeekNumber:t.showWeekNumbers&&0===r,forceDayTop:t.showWeekNumbers,todayRange:t.todayRange,eventSelection:t.eventSelection,eventDrag:t.eventDrag,eventResize:t.eventResize,extraHookProps:n.extraHookProps,extraDataAttrs:n.extraDataAttrs,extraClassNames:n.extraClassNames,extraDateSpan:n.extraDateSpan,moreCnt:p[r],moreMarginTop:f[r],singlePlacements:c[r],fgContentElRef:e.fgElRefs.createRef(n.key),fgContent:rB(rj,null,rB(rj,null,o),rB(rj,null,u)),bgContent:rB(rj,null,e.renderFillSegs(s[r],"highlight"),e.renderFillSegs(i[r],"non-business"),e.renderFillSegs(a[r],"bg-event"))})}))},t.prototype.componentDidMount=function(){this.updateSizing(!0)},t.prototype.componentDidUpdate=function(e,t){var n=this.props;this.updateSizing(!tg(e,n))},t.prototype.getHighlightSegs=function(){var e=this.props;return e.eventDrag&&e.eventDrag.segs.length?e.eventDrag.segs:e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:e.dateSelectionSegs},t.prototype.getMirrorSegs=function(){var e=this.props;return e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:[]},t.prototype.renderFgSegs=function(e,t,n,r,o,i,a){var l=this.context,u=this.props.eventSelection,c=this.state.framePositions,d=1===this.props.cells.length,p=o||i||a,f=[];if(c)for(var h=0,v=t;h<v.length;h++){var g=v[h],m=g.seg,y=m.eventRange.instance.instanceId,E=y+":"+e,S=g.isVisible&&!r[y],D=g.isAbsolute,b="",C="";D&&(l.isRtl?(C=0,b=c.lefts[m.lastCol]-c.lefts[m.firstCol]):(b=0,C=c.rights[m.firstCol]-c.rights[m.lastCol])),f.push(rB("div",{className:"fc-daygrid-event-harness"+(D?" fc-daygrid-event-harness-abs":""),key:E,ref:p?null:this.segHarnessRefs.createRef(E),style:{visibility:S?"":"hidden",marginTop:D?"":g.marginTop,top:D?g.absoluteTop:"",left:b,right:C}},aF(m)?rB(aG,s({seg:m,isDragging:o,isSelected:y===u,defaultDisplayEventEnd:d},nV(m,n))):rB(aB,s({seg:m,isDragging:o,isResizing:i,isDateSelecting:a,isSelected:y===u,defaultDisplayEventEnd:d},nV(m,n)))))}return f},t.prototype.renderFillSegs=function(e,t){var n=this.context.isRtl,r=this.props.todayRange,o=this.state.framePositions,i=[];if(o)for(var a=0,u=e;a<u.length;a++){var c=u[a],d=n?{right:0,left:o.lefts[c.lastCol]-o.lefts[c.firstCol]}:{left:0,right:o.rights[c.firstCol]-o.rights[c.lastCol]};i.push(rB("div",{key:nF(c.eventRange),className:"fc-daygrid-bg-harness",style:d},"bg-event"===t?rB(i6,s({seg:c},nV(c,r))):i7(t)))}return rB.apply(void 0,l([rj,{}],i))},t.prototype.updateSizing=function(e){var t=this.props,n=this.frameElRefs;if(!t.forPrint&&null!==t.clientWidth){if(e){var r=t.cells.map(function(e){return n.currentMap[e.key]});if(r.length){var o=this.rootElRef.current;this.setState({framePositions:new rL(o,r,!0,!1)})}}var i=this.state.eventInstanceHeights,a=this.queryEventInstanceHeights(),l=!0===t.dayMaxEvents||!0===t.dayMaxEventRows;this.safeSetState({eventInstanceHeights:s(s({},i),a),maxContentHeight:l?this.computeMaxContentHeight():null})}},t.prototype.queryEventInstanceHeights=function(){var e=this.segHarnessRefs.currentMap,t={};for(var n in e){var r=Math.round(e[n].getBoundingClientRect().height),o=n.split(":")[0];t[o]=Math.max(t[o]||0,r)}return t},t.prototype.computeMaxContentHeight=function(){var e=this.props.cells[0].key,t=this.cellElRefs.currentMap[e],n=this.fgElRefs.currentMap[e];return t.getBoundingClientRect().bottom-n.getBoundingClientRect().top},t.prototype.getCellEls=function(){var e=this.cellElRefs.currentMap;return this.props.cells.map(function(t){return e[t.key]})},t}(r7);a4.addStateEquality({eventInstanceHeights:tg});var aK=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.splitBusinessHourSegs=tA(aA),t.splitBgEventSegs=tA(aA),t.splitFgEventSegs=tA(aA),t.splitDateSelectionSegs=tA(aA),t.splitEventDrag=tA(aU),t.splitEventResize=tA(aU),t.rowRefs=new iO,t.handleRootEl=function(e){t.rootEl=e,e?t.context.registerInteractiveComponent(t,{el:e,isHitComboAllowed:t.props.isHitComboAllowed}):t.context.unregisterInteractiveComponent(t)},t}return a(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.dateProfile,r=t.dayMaxEventRows,o=t.dayMaxEvents,i=t.expandRows,a=t.cells.length,s=this.splitBusinessHourSegs(t.businessHourSegs,a),l=this.splitBgEventSegs(t.bgEventSegs,a),u=this.splitFgEventSegs(t.fgEventSegs,a),c=this.splitDateSelectionSegs(t.dateSelectionSegs,a),d=this.splitEventDrag(t.eventDrag,a),p=this.splitEventResize(t.eventResize,a),f=!0===o||!0===r;return f&&!i&&(f=!1,r=null,o=null),rB("div",{className:["fc-daygrid-body",f?"fc-daygrid-body-balanced":"fc-daygrid-body-unbalanced",i?"":"fc-daygrid-body-natural"].join(" "),ref:this.handleRootEl,style:{width:t.clientWidth,minWidth:t.tableMinWidth}},rB(iE,{unit:"day"},function(f,h){return rB(rj,null,rB("table",{role:"presentation",className:"fc-scrollgrid-sync-table",style:{width:t.clientWidth,minWidth:t.tableMinWidth,height:i?t.clientHeight:""}},t.colGroupNode,rB("tbody",{role:"presentation"},t.cells.map(function(i,f){return rB(a4,{ref:e.rowRefs.createRef(f),key:i.length?i[0].date.toISOString():f,showDayNumbers:a>1,showWeekNumbers:t.showWeekNumbers,todayRange:h,dateProfile:n,cells:i,renderIntro:t.renderRowIntro,businessHourSegs:s[f],eventSelection:t.eventSelection,bgEventSegs:l[f].filter(a3),fgEventSegs:u[f],dateSelectionSegs:c[f],eventDrag:d[f],eventResize:p[f],dayMaxEvents:o,dayMaxEventRows:r,clientWidth:t.clientWidth,clientHeight:t.clientHeight,forPrint:t.forPrint})}))))}))},t.prototype.prepareHits=function(){this.rowPositions=new rL(this.rootEl,this.rowRefs.collect().map(function(e){return e.getCellEls()[0]}),!1,!0),this.colPositions=new rL(this.rootEl,this.rowRefs.currentMap[0].getCellEls(),!0,!1)},t.prototype.queryHit=function(e,t){var n=this.colPositions,r=this.rowPositions,o=n.leftToIndex(e),i=r.topToIndex(t);if(null!=i&&null!=o){var a=this.props.cells[i][o];return{dateProfile:this.props.dateProfile,dateSpan:s({range:this.getCellRange(i,o),allDay:!0},a.extraDateSpan),dayEl:this.getCellEl(i,o),rect:{left:n.lefts[o],right:n.rights[o],top:r.tops[i],bottom:r.bottoms[i]},layer:0}}return null},t.prototype.getCellEl=function(e,t){return this.rowRefs.currentMap[e].getCellEls()[t]},t.prototype.getCellRange=function(e,t){var n=this.props.cells[e][t].date,r=e5(n,1);return{start:n,end:r}},t}(r7);function a3(e){return e.eventRange.def.allDay}var a2=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.forceDayIfListItem=!0,t}return a(t,e),t.prototype.sliceRange=function(e,t){return t.sliceRange(e)},t}(iR),a5=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.slicer=new a2,t.tableRef=rq(),t}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context;return rB(aK,s({ref:this.tableRef},this.slicer.sliceProps(e,e.dateProfile,e.nextDayThreshold,t,e.dayTableModel),{dateProfile:e.dateProfile,cells:e.dayTableModel.cells,colGroupNode:e.colGroupNode,tableMinWidth:e.tableMinWidth,renderRowIntro:e.renderRowIntro,dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,showWeekNumbers:e.showWeekNumbers,expandRows:e.expandRows,headerAlignElRef:e.headerAlignElRef,clientWidth:e.clientWidth,clientHeight:e.clientHeight,forPrint:e.forPrint}))},t}(r7),a9=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.buildDayTableModel=tA(a7),t.headerRef=rq(),t.tableRef=rq(),t}return a(t,e),t.prototype.render=function(){var e=this,t=this.context,n=t.options,r=t.dateProfileGenerator,o=this.props,i=this.buildDayTableModel(o.dateProfile,r),a=n.dayHeaders&&rB(iD,{ref:this.headerRef,dateProfile:o.dateProfile,dates:i.headerDates,datesRepDistinctDays:1===i.rowCnt}),s=function(t){return rB(a5,{ref:e.tableRef,dateProfile:o.dateProfile,dayTableModel:i,businessHours:o.businessHours,dateSelection:o.dateSelection,eventStore:o.eventStore,eventUiBases:o.eventUiBases,eventSelection:o.eventSelection,eventDrag:o.eventDrag,eventResize:o.eventResize,nextDayThreshold:n.nextDayThreshold,colGroupNode:t.tableColGroupNode,tableMinWidth:t.tableMinWidth,dayMaxEvents:n.dayMaxEvents,dayMaxEventRows:n.dayMaxEventRows,showWeekNumbers:n.weekNumbers,expandRows:!o.isHeightAuto,headerAlignElRef:e.headerElRef,clientWidth:t.clientWidth,clientHeight:t.clientHeight,forPrint:o.forPrint})};return n.dayMinWidth?this.renderHScrollLayout(a,s,i.colCnt,n.dayMinWidth):this.renderSimpleLayout(a,s)},t}(aO);function a7(e,t){var n=new iC(e.renderRange,t);return new i$(n,/year|month|week/.test(e.currentRangeUnit))}var a6=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.buildRenderRange=function(t,n,r){var o,i=this.props.dateEnv,a=e.prototype.buildRenderRange.call(this,t,n,r),s=a.start,l=a.end;if(/^(year|month)$/.test(n)&&(s=i.startOfWeek(s),(o=i.startOfWeek(l)).valueOf()!==l.valueOf()&&(l=e2(o,1))),this.props.monthMode&&this.props.fixedWeekCount){var u=Math.ceil(e7(s,l));l=e2(l,6-u)}return{start:s,end:l}},t}(oh),aQ=r6({initialView:"dayGridMonth",views:{dayGrid:{component:a9,dateProfileGeneratorClass:a6},dayGridDay:{type:"dayGrid",duration:{days:1}},dayGridWeek:{type:"dayGrid",duration:{weeks:1}},dayGridMonth:{type:"dayGrid",duration:{months:1},monthMode:!0,fixedWeekCount:!0}}}),aJ=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.getKeyInfo=function(){return{allDay:{},timed:{}}},t.prototype.getKeysForDateSpan=function(e){return e.allDay?["allDay"]:["timed"]},t.prototype.getKeysForEventDef=function(e){return e.allDay?nM(e)?["timed","allDay"]:["allDay"]:["timed"]},t}(rb),se=t1({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"short"});function st(e){var t=["fc-timegrid-slot","fc-timegrid-slot-label",e.isLabeled?"fc-scrollgrid-shrink":"fc-timegrid-slot-minor",];return rB(r1.Consumer,null,function(n){if(!e.isLabeled)return rB("td",{className:t.join(" "),"data-time":e.isoTimeStr});var r=n.dateEnv,o=n.options,i=n.viewApi,a=null==o.slotLabelFormat?se:Array.isArray(o.slotLabelFormat)?t1(o.slotLabelFormat[0]):t1(o.slotLabelFormat),s={level:0,time:e.time,date:r.toDate(e.date),view:i,text:r.format(e.date,a)};return rB(ot,{hookProps:s,classNames:o.slotLabelClassNames,content:o.slotLabelContent,defaultContent:sn,didMount:o.slotLabelDidMount,willUnmount:o.slotLabelWillUnmount},function(n,r,o,i){return rB("td",{ref:n,className:t.concat(r).join(" "),"data-time":e.isoTimeStr},rB("div",{className:"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame"},rB("div",{className:"fc-timegrid-slot-label-cushion fc-scrollgrid-shrink-cushion",ref:o},i)))})})}function sn(e){return e.text}var sr=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){return this.props.slatMetas.map(function(e){return rB("tr",{key:e.key},rB(st,s({},e)))})},t}(r3),so=t1({week:"short"}),si=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.allDaySplitter=new aJ,t.headerElRef=rq(),t.rootElRef=rq(),t.scrollerElRef=rq(),t.state={slatCoords:null},t.handleScrollTopRequest=function(e){var n=t.scrollerElRef.current;n&&(n.scrollTop=e)},t.renderHeadAxis=function(e,n){void 0===n&&(n="");var r=t.context.options,o=t.props.dateProfile.renderRange,i=1===e6(o.start,o.end)?rT(t.context,o.start,"week"):{};return r.weekNumbers&&"day"===e?rB(iJ,{date:o.start,defaultFormat:so},function(e,t,r,o){return rB("th",{ref:e,"aria-hidden":!0,className:["fc-timegrid-axis","fc-scrollgrid-shrink",].concat(t).join(" ")},rB("div",{className:"fc-timegrid-axis-frame fc-scrollgrid-shrink-frame fc-timegrid-axis-frame-liquid",style:{height:n}},rB("a",s({ref:r,className:"fc-timegrid-axis-cushion fc-scrollgrid-shrink-cushion fc-scrollgrid-sync-inner"},i),o)))}):rB("th",{"aria-hidden":!0,className:"fc-timegrid-axis"},rB("div",{className:"fc-timegrid-axis-frame",style:{height:n}}))},t.renderTableRowAxis=function(e){var n=t.context,r=n.options,o=n.viewApi;return rB(ot,{hookProps:{text:r.allDayText,view:o},classNames:r.allDayClassNames,content:r.allDayContent,defaultContent:sa,didMount:r.allDayDidMount,willUnmount:r.allDayWillUnmount},function(t,n,r,o){return rB("td",{ref:t,"aria-hidden":!0,className:["fc-timegrid-axis","fc-scrollgrid-shrink",].concat(n).join(" ")},rB("div",{className:"fc-timegrid-axis-frame fc-scrollgrid-shrink-frame"+(null==e?" fc-timegrid-axis-frame-liquid":""),style:{height:e}},rB("span",{className:"fc-timegrid-axis-cushion fc-scrollgrid-shrink-cushion fc-scrollgrid-sync-inner",ref:r},o)))})},t.handleSlatCoords=function(e){t.setState({slatCoords:e})},t}return a(t,e),t.prototype.renderSimpleLayout=function(e,t,n){var r=this.context,o=this.props,i=[],a=iY(r.options);return e&&i.push({type:"header",key:"header",isSticky:a,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),t&&(i.push({type:"body",key:"all-day",chunk:{content:t}}),i.push({type:"body",key:"all-day-divider",outerContent:rB("tr",{role:"presentation",className:"fc-scrollgrid-section"},rB("td",{className:"fc-timegrid-divider "+r.theme.getClass("tableCellShaded")}))})),i.push({type:"body",key:"body",liquid:!0,expandRows:Boolean(r.options.expandRows),chunk:{scrollerElRef:this.scrollerElRef,content:n}}),rB(ou,{viewSpec:r.viewSpec,elRef:this.rootElRef},function(e,t){return rB("div",{className:["fc-timegrid"].concat(t).join(" "),ref:e},rB(iX,{liquid:!o.isHeightAuto&&!o.forPrint,collapsibleWidth:o.forPrint,cols:[{width:"shrink"}],sections:i}))})},t.prototype.renderHScrollLayout=function(e,t,n,r,o,i,a){var s=this,l=this.context.pluginHooks.scrollGridImpl;if(!l)throw Error("No ScrollGrid implementation");var u=this.context,c=this.props,d=!c.forPrint&&iY(u.options),p=!c.forPrint&&iZ(u.options),f=[];e&&f.push({type:"header",key:"header",isSticky:d,syncRowHeights:!0,chunks:[{key:"axis",rowContent:function(e){return rB("tr",{role:"presentation"},s.renderHeadAxis("day",e.rowSyncHeights[0]))}},{key:"cols",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e},]}),t&&(f.push({type:"body",key:"all-day",syncRowHeights:!0,chunks:[{key:"axis",rowContent:function(e){return rB("tr",{role:"presentation"},s.renderTableRowAxis(e.rowSyncHeights[0]))}},{key:"cols",content:t},]}),f.push({key:"all-day-divider",type:"body",outerContent:rB("tr",{role:"presentation",className:"fc-scrollgrid-section"},rB("td",{colSpan:2,className:"fc-timegrid-divider "+u.theme.getClass("tableCellShaded")}))}));var h=u.options.nowIndicator;return f.push({type:"body",key:"body",liquid:!0,expandRows:Boolean(u.options.expandRows),chunks:[{key:"axis",content:function(e){return rB("div",{className:"fc-timegrid-axis-chunk"},rB("table",{"aria-hidden":!0,style:{height:e.expandRows?e.clientHeight:""}},e.tableColGroupNode,rB("tbody",null,rB(sr,{slatMetas:i}))),rB("div",{className:"fc-timegrid-now-indicator-container"},rB(iE,{unit:h?"minute":"day"},function(e){var t=h&&a&&a.safeComputeTop(e);return"number"==typeof t?rB(iK,{isAxis:!0,date:e},function(e,n,r,o){return rB("div",{ref:e,className:["fc-timegrid-now-indicator-arrow"].concat(n).join(" "),style:{top:t}},o)}):null})))}},{key:"cols",scrollerElRef:this.scrollerElRef,content:n},]}),p&&f.push({key:"footer",type:"footer",isSticky:!0,chunks:[{key:"axis",content:ij},{key:"cols",content:ij},]}),rB(ou,{viewSpec:u.viewSpec,elRef:this.rootElRef},function(e,t){return rB("div",{className:["fc-timegrid"].concat(t).join(" "),ref:e},rB(l,{liquid:!c.isHeightAuto&&!c.forPrint,collapsibleWidth:!1,colGroups:[{width:"shrink",cols:[{width:"shrink"}]},{cols:[{span:r,minWidth:o}]},],sections:f}))})},t.prototype.getAllDayMaxEventProps=function(){var e=this.context.options,t=e.dayMaxEvents,n=e.dayMaxEventRows;return(!0===t||!0===n)&&(t=void 0,n=5),{dayMaxEvents:t,dayMaxEventRows:n}},t}(r7);function sa(e){return e.text}var ss=function(){function e(e,t,n){this.positions=e,this.dateProfile=t,this.slotDuration=n}return e.prototype.safeComputeTop=function(e){var t=this.dateProfile;if(nk(t.currentRange,e)){var n=tt(e),r=e.valueOf()-n.valueOf();if(r>=tx(t.slotMinTime)&&r<tx(t.slotMaxTime))return this.computeTimeTop(tR(r))}return null},e.prototype.computeDateTop=function(e,t){return t||(t=tt(e)),this.computeTimeTop(tR(e.valueOf()-t.valueOf()))},e.prototype.computeTimeTop=function(e){var t,n,r=this.positions,o=this.dateProfile,i=r.els.length,a=(e.milliseconds-tx(o.slotMinTime))/tx(this.slotDuration);return a=Math.max(0,a),t=Math.min(t=Math.floor(a=Math.min(i,a)),i-1),n=a-t,r.tops[t]+r.getHeight(t)*n},e}(),sl=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=t.options,r=e.slatElRefs;return rB("tbody",null,e.slatMetas.map(function(o,i){var a={time:o.time,date:t.dateEnv.toDate(o.date),view:t.viewApi},l=["fc-timegrid-slot","fc-timegrid-slot-lane",o.isLabeled?"":"fc-timegrid-slot-minor",];return rB("tr",{key:o.key,ref:r.createRef(o.key)},e.axis&&rB(st,s({},o)),rB(ot,{hookProps:a,classNames:n.slotLaneClassNames,content:n.slotLaneContent,didMount:n.slotLaneDidMount,willUnmount:n.slotLaneWillUnmount},function(e,t,n,r){return rB("td",{ref:e,className:l.concat(t).join(" "),"data-time":o.isoTimeStr},r)}))}))},t}(r3),su=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.rootElRef=rq(),t.slatElRefs=new iO,t}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context;return rB("div",{ref:this.rootElRef,className:"fc-timegrid-slots"},rB("table",{"aria-hidden":!0,className:t.theme.getClass("table"),style:{minWidth:e.tableMinWidth,width:e.clientWidth,height:e.minHeight}},e.tableColGroupNode,rB(sl,{slatElRefs:this.slatElRefs,axis:e.axis,slatMetas:e.slatMetas})))},t.prototype.componentDidMount=function(){this.updateSizing()},t.prototype.componentDidUpdate=function(){this.updateSizing()},t.prototype.componentWillUnmount=function(){this.props.onCoords&&this.props.onCoords(null)},t.prototype.updateSizing=function(){var e,t,n=this.context,r=this.props;if(r.onCoords&&null!==r.clientWidth){this.rootElRef.current.offsetHeight&&r.onCoords(new ss(new rL(this.rootElRef.current,(e=this.slatElRefs.currentMap,t=r.slatMetas,t.map(function(t){return e[t.key]})),!1,!0),this.props.dateProfile,n.options.slotDuration))}},t}(r3);function sc(e,t){var n,r=[];for(n=0;n<t;n+=1)r.push([]);if(e)for(n=0;n<e.length;n+=1)r[e[n].col].push(e[n]);return r}function sd(e,t){var n=[];if(e){for(var r=0;r<t;r+=1)n[r]={affectedInstances:e.affectedInstances,isEvent:e.isEvent,segs:[]};for(var o=0,i=e.segs;o<i.length;o++){var a=i[o];n[a.col].segs.push(a)}}else for(var r=0;r<t;r+=1)n[r]=null;return n}var sp=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.rootElRef=rq(),t}return a(t,e),t.prototype.render=function(){var e=this,t=this.props;return rB(ar,{allDayDate:null,moreCnt:t.hiddenSegs.length,allSegs:t.hiddenSegs,hiddenSegs:t.hiddenSegs,alignmentElRef:this.rootElRef,defaultContent:sf,extraDateSpan:t.extraDateSpan,dateProfile:t.dateProfile,todayRange:t.todayRange,popoverContent:function(){return sC(t.hiddenSegs,t)}},function(n,r,o,i,a,s,l,u){return rB("a",{ref:function(t){r9(n,t),r9(e.rootElRef,t)},className:["fc-timegrid-more-link"].concat(r).join(" "),style:{top:t.top,bottom:t.bottom},onClick:a,title:s,"aria-expanded":l,"aria-controls":u},rB("div",{ref:o,className:"fc-timegrid-more-link-inner fc-sticky"},i))})},t}(r3);function sf(e){return e.shortText}function sh(e,t){if(!e)return[[],0];for(var n=e.level,r=e.lateralStart,o=e.lateralEnd,i=r,a=[];i<o;)a.push(t(n,i)),i+=1;return a.sort(sv),[a.map(sg),a[0][1]]}function sv(e,t){return t[1]-e[1]}function sg(e){return e[0]}function sm(e,t){var n={};return function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];var i=e.apply(void 0,r);return i in n?n[i]:n[i]=t.apply(void 0,r)}}function sy(e,t,n,r){void 0===n&&(n=null),void 0===r&&(r=0);var o=[];if(n)for(var i=0;i<e.length;i+=1){var a=e[i],s=n.computeDateTop(a.start,t),l=Math.max(s+(r||0),n.computeDateTop(a.end,t));o.push({start:Math.round(s),end:Math.round(l)})}return o}var sE=t1({hour:"numeric",minute:"2-digit",meridiem:!1}),sS=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=["fc-timegrid-event","fc-v-event",];return this.props.isShort&&e.push("fc-timegrid-event-short"),rB(i1,s({},this.props,{defaultTimeFormat:sE,extraClassNames:e}))},t}(r3),sD=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.props;return rB(i2,{date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,extraHookProps:e.extraHookProps},function(e,t){return t&&rB("div",{className:"fc-timegrid-col-misc",ref:e},t)})},t}(r3),sb=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.sortEventSegs=tA(nH),t}return a(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.context,r=n.options.selectMirror,o=t.eventDrag&&t.eventDrag.segs||t.eventResize&&t.eventResize.segs||r&&t.dateSelectionSegs||[],i=t.eventDrag&&t.eventDrag.affectedInstances||t.eventResize&&t.eventResize.affectedInstances||{},a=this.sortEventSegs(t.fgEventSegs,n.options.eventOrder);return rB(i9,{elRef:t.elRef,date:t.date,dateProfile:t.dateProfile,todayRange:t.todayRange,extraHookProps:t.extraHookProps},function(n,l,u){return rB("td",s({ref:n,role:"gridcell",className:["fc-timegrid-col"].concat(l,t.extraClassNames||[]).join(" ")},u,t.extraDataAttrs),rB("div",{className:"fc-timegrid-col-frame"},rB("div",{className:"fc-timegrid-col-bg"},e.renderFillSegs(t.businessHourSegs,"non-business"),e.renderFillSegs(t.bgEventSegs,"bg-event"),e.renderFillSegs(t.dateSelectionSegs,"highlight")),rB("div",{className:"fc-timegrid-col-events"},e.renderFgSegs(a,i,!1,!1,!1)),rB("div",{className:"fc-timegrid-col-events"},e.renderFgSegs(o,{},Boolean(t.eventDrag),Boolean(t.eventResize),Boolean(r))),rB("div",{className:"fc-timegrid-now-indicator-container"},e.renderNowIndicator(t.nowIndicatorSegs)),rB(sD,{date:t.date,dateProfile:t.dateProfile,todayRange:t.todayRange,extraHookProps:t.extraHookProps})))})},t.prototype.renderFgSegs=function(e,t,n,r,o){var i=this.props;return i.forPrint?sC(e,i):this.renderPositionedFgSegs(e,t,n,r,o)},t.prototype.renderPositionedFgSegs=function(e,t,n,r,o){var i=this,a=this.context.options,l=a.eventMaxStack,u=a.eventShortHeight,c=a.eventOrderStrict,d=a.eventMinHeight,p=this.props,f=p.date,h=p.slatCoords,v=p.eventSelection,g=p.todayRange,m=p.nowDate,y=n||r||o,E=sy(e,f,h,d),S=function e(t,n,r,o){for(var i=[],a=[],l=0;l<t.length;l+=1){var u=n[l];u?i.push({index:l,thickness:1,span:u}):a.push(t[l])}for(var c,d,p,f,h,v,g,m,y,E,S,D,b=1,C=(v=i,g=r,m=o,y=new oX,null!=g&&(y.strictOrder=g),null!=m&&(y.maxStackCnt=m),E=y.addSegs(v),S=o4(E),{segRects:function e(t){var n=[],r=sm(function(e,t,n){return o1(e)},function(e,t,r){var i=s(s({},e),{levelCoord:t,stackDepth:r,stackForward:0});return n.push(i),i.stackForward=o(e.nextLevelNodes,t+e.thickness,r+1)+1});function o(e,t,n){for(var o=0,i=0,a=e;i<a.length;i++)o=Math.max(r(a[i],t,n),o);return o}return o(t,0,0),n}(D=(f=D=(c=y,d=c.entriesByLevel,p=sm(function(e,t){return e+":"+t},function(e,t){var n=function e(t,n,r){for(var o=t.levelCoords,i=t.entriesByLevel,a=i[n][r],s=o[n]+a.thickness,l=o.length,u=n;u<l&&o[u]<s;u+=1);for(;u<l;u+=1){for(var c=i[u],d=void 0,p=o5(c,a.span.start,o0),f=p[0]+p[1],h=f;(d=c[h])&&d.span.start<a.span.end;)h+=1;if(f<h)return{level:u,lateralStart:f,lateralEnd:h}}return null}(c,e,t),r=sh(n,p),o=d[e][t];return[s(s({},o),{nextLevelNodes:r[0]}),o.thickness+r[1]]}),sh(d.length?{level:0,lateralStart:0,lateralEnd:d[0].length}:null,p)[0]),h=sm(function(e,t,n){return o1(e)},function(e,t,n){var r,o=e.nextLevelNodes,i=e.thickness,a=i+n,l=[];if(o.length)for(var u=0,c=o;u<c.length;u++){var d=c[u];if(void 0===r){var p=h(d,t,a);r=p[0],l.push(p[1])}else{var p=h(d,r,0);l.push(p[1])}}else r=1;var f=(r-t)*(i/a);return[r-f,s(s({},e),{thickness:f,nextLevelNodes:l})]}),f.map(function(e){return h(e,0,0)[1]}))),hiddenGroups:S}),$=C.segRects,R=C.hiddenGroups,w=[],T=0,k=$;T<k.length;T++){var x=k[T];w.push({seg:t[x.index],rect:x})}for(var M=0,_=a;M<_.length;M++){var I=_[M];w.push({seg:I,rect:null})}return{segPlacements:w,hiddenGroups:R}}(e,E,c,l),D=S.segPlacements,b=S.hiddenGroups;return rB(rj,null,this.renderHiddenGroups(b,e),D.map(function(e){var a=e.seg,l=e.rect,c=a.eventRange.instance.instanceId,d=y||Boolean(!t[c]&&l),p=s$(l&&l.span),f=!y&&l?i.computeSegHStyle(l):{left:0,right:0},h=Boolean(l)&&l.stackForward>0,E=Boolean(l)&&l.span.end-l.span.start<u;return rB("div",{className:"fc-timegrid-event-harness"+(h?" fc-timegrid-event-harness-inset":""),key:c,style:s(s({visibility:d?"":"hidden"},p),f)},rB(sS,s({seg:a,isDragging:n,isResizing:r,isDateSelecting:o,isSelected:c===v,isShort:E},nV(a,g,m))))}))},t.prototype.renderHiddenGroups=function(e,t){var n=this.props,r=n.extraDateSpan,o=n.dateProfile,i=n.todayRange,a=n.nowDate,s=n.eventSelection,l=n.eventDrag,u=n.eventResize;return rB(rj,null,e.map(function(e){var n,c,d=s$(e.span),p=(n=e.entries,c=t,n.map(function(e){return c[e.index]}));return rB(sp,{key:tI(aa(p)),hiddenSegs:p,top:d.top,bottom:d.bottom,extraDateSpan:r,dateProfile:o,todayRange:i,nowDate:a,eventSelection:s,eventDrag:l,eventResize:u})}))},t.prototype.renderFillSegs=function(e,t){var n=this.props,r=this.context,o=sy(e,n.date,n.slatCoords,r.options.eventMinHeight).map(function(r,o){var i=e[o];return rB("div",{key:nF(i.eventRange),className:"fc-timegrid-bg-harness",style:s$(r)},"bg-event"===t?rB(i6,s({seg:i},nV(i,n.todayRange,n.nowDate))):i7(t))});return rB(rj,null,o)},t.prototype.renderNowIndicator=function(e){var t=this.props,n=t.slatCoords,r=t.date;return n?e.map(function(e,t){return rB(iK,{isAxis:!1,date:r,key:t},function(t,o,i,a){return rB("div",{ref:t,className:["fc-timegrid-now-indicator-line"].concat(o).join(" "),style:{top:n.computeDateTop(e.start,r)}},a)})}):null},t.prototype.computeSegHStyle=function(e){var t,n,r=this.context,o=r.isRtl,i=r.options.slotEventOverlap,a=e.levelCoord,s=e.levelCoord+e.thickness;i&&(s=Math.min(1,a+(s-a)*2)),o?(t=1-s,n=a):(t=a,n=1-s);var l={zIndex:e.stackDepth+1,left:100*t+"%",right:100*n+"%"};return i&&!e.stackForward&&(l[o?"marginLeft":"marginRight"]=20),l},t}(r3);function sC(e,t){var n=t.todayRange,r=t.nowDate,o=t.eventSelection,i=t.eventDrag,a=t.eventResize,l=(i?i.affectedInstances:null)||(a?a.affectedInstances:null)||{};return rB(rj,null,e.map(function(e){var t=e.eventRange.instance.instanceId;return rB("div",{key:t,style:{visibility:l[t]?"hidden":""}},rB(sS,s({seg:e,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:t===o,isShort:!1},nV(e,n,r))))}))}function s$(e){return e?{top:e.start,bottom:-e.end}:{top:"",bottom:""}}var sR=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.splitFgEventSegs=tA(sc),t.splitBgEventSegs=tA(sc),t.splitBusinessHourSegs=tA(sc),t.splitNowIndicatorSegs=tA(sc),t.splitDateSelectionSegs=tA(sc),t.splitEventDrag=tA(sd),t.splitEventResize=tA(sd),t.rootElRef=rq(),t.cellElRefs=new iO,t}return a(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.context.options.nowIndicator&&t.slatCoords&&t.slatCoords.safeComputeTop(t.nowDate),r=t.cells.length,o=this.splitFgEventSegs(t.fgEventSegs,r),i=this.splitBgEventSegs(t.bgEventSegs,r),a=this.splitBusinessHourSegs(t.businessHourSegs,r),s=this.splitNowIndicatorSegs(t.nowIndicatorSegs,r),l=this.splitDateSelectionSegs(t.dateSelectionSegs,r),u=this.splitEventDrag(t.eventDrag,r),c=this.splitEventResize(t.eventResize,r);return rB("div",{className:"fc-timegrid-cols",ref:this.rootElRef},rB("table",{role:"presentation",style:{minWidth:t.tableMinWidth,width:t.clientWidth}},t.tableColGroupNode,rB("tbody",{role:"presentation"},rB("tr",{role:"row"},t.axis&&rB("td",{"aria-hidden":!0,className:"fc-timegrid-col fc-timegrid-axis"},rB("div",{className:"fc-timegrid-col-frame"},rB("div",{className:"fc-timegrid-now-indicator-container"},"number"==typeof n&&rB(iK,{isAxis:!0,date:t.nowDate},function(e,t,r,o){return rB("div",{ref:e,className:["fc-timegrid-now-indicator-arrow"].concat(t).join(" "),style:{top:n}},o)})))),t.cells.map(function(n,r){return rB(sb,{key:n.key,elRef:e.cellElRefs.createRef(n.key),dateProfile:t.dateProfile,date:n.date,nowDate:t.nowDate,todayRange:t.todayRange,extraHookProps:n.extraHookProps,extraDataAttrs:n.extraDataAttrs,extraClassNames:n.extraClassNames,extraDateSpan:n.extraDateSpan,fgEventSegs:o[r],bgEventSegs:i[r],businessHourSegs:a[r],nowIndicatorSegs:s[r],dateSelectionSegs:l[r],eventDrag:u[r],eventResize:c[r],slatCoords:t.slatCoords,eventSelection:t.eventSelection,forPrint:t.forPrint})})))))},t.prototype.componentDidMount=function(){this.updateCoords()},t.prototype.componentDidUpdate=function(){this.updateCoords()},t.prototype.updateCoords=function(){var e,t,n=this.props;n.onColCoords&&null!==n.clientWidth&&n.onColCoords(new rL(this.rootElRef.current,(e=this.cellElRefs.currentMap,t=n.cells,t.map(function(t){return e[t.key]})),!0,!1))},t}(r3),s8=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.processSlotOptions=tA(sw),t.state={slatCoords:null},t.handleRootEl=function(e){e?t.context.registerInteractiveComponent(t,{el:e,isHitComboAllowed:t.props.isHitComboAllowed}):t.context.unregisterInteractiveComponent(t)},t.handleScrollRequest=function(e){var n=t.props.onScrollTopRequest,r=t.state.slatCoords;if(n&&r){if(e.time){var o=r.computeTimeTop(e.time);(o=Math.ceil(o))&&(o+=1),n(o)}return!0}return!1},t.handleColCoords=function(e){t.colCoords=e},t.handleSlatCoords=function(e){t.setState({slatCoords:e}),t.props.onSlatCoords&&t.props.onSlatCoords(e)},t}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.state;return rB("div",{className:"fc-timegrid-body",ref:this.handleRootEl,style:{width:e.clientWidth,minWidth:e.tableMinWidth}},rB(su,{axis:e.axis,dateProfile:e.dateProfile,slatMetas:e.slatMetas,clientWidth:e.clientWidth,minHeight:e.expandRows?e.clientHeight:"",tableMinWidth:e.tableMinWidth,tableColGroupNode:e.axis?e.tableColGroupNode:null,onCoords:this.handleSlatCoords}),rB(sR,{cells:e.cells,axis:e.axis,dateProfile:e.dateProfile,businessHourSegs:e.businessHourSegs,bgEventSegs:e.bgEventSegs,fgEventSegs:e.fgEventSegs,dateSelectionSegs:e.dateSelectionSegs,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,todayRange:e.todayRange,nowDate:e.nowDate,nowIndicatorSegs:e.nowIndicatorSegs,clientWidth:e.clientWidth,tableMinWidth:e.tableMinWidth,tableColGroupNode:e.tableColGroupNode,slatCoords:t.slatCoords,onColCoords:this.handleColCoords,forPrint:e.forPrint}))},t.prototype.componentDidMount=function(){this.scrollResponder=this.context.createScrollResponder(this.handleScrollRequest)},t.prototype.componentDidUpdate=function(e){this.scrollResponder.update(e.dateProfile!==this.props.dateProfile)},t.prototype.componentWillUnmount=function(){this.scrollResponder.detach()},t.prototype.queryHit=function(e,t){var n=this.context,r=n.dateEnv,o=n.options,i=this.colCoords,a=this.props.dateProfile,l=this.state.slatCoords,u=this.processSlotOptions(this.props.slotDuration,o.snapDuration),c=u.snapDuration,d=u.snapsPerSlot,p=i.leftToIndex(e),f=l.positions.topToIndex(t);if(null!=p&&null!=f){var h=this.props.cells[p],v=l.positions.tops[f],g=l.positions.getHeight(f),m=this.props.cells[p].date,y=tw(a.slotMinTime,tT(c,f*d+Math.floor((t-v)/g*d))),E=r.add(m,y),S=r.add(E,c);return{dateProfile:a,dateSpan:s({range:{start:E,end:S},allDay:!1},h.extraDateSpan),dayEl:i.els[p],rect:{left:i.lefts[p],right:i.rights[p],top:v,bottom:v+g},layer:0}}return null},t}(r7);function sw(e,t){var n=t||e,r=tM(e,n);return null===r&&(n=e,r=1),{snapDuration:n,snapsPerSlot:r}}var sT=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.sliceRange=function(e,t){for(var n=[],r=0;r<t.length;r+=1){var o=nR(e,t[r]);o&&n.push({start:o.start,end:o.end,isStart:o.start.valueOf()===e.start.valueOf(),isEnd:o.end.valueOf()===e.end.valueOf(),col:r})}return n},t}(iR),sk=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.buildDayRanges=tA(sx),t.slicer=new sT,t.timeColsRef=rq(),t}return a(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.context,r=t.dateProfile,o=t.dayTableModel,i=n.options.nowIndicator,a=this.buildDayRanges(o,r,n.dateEnv);return rB(iE,{unit:i?"minute":"day"},function(l,u){return rB(s8,s({ref:e.timeColsRef},e.slicer.sliceProps(t,r,null,n,a),{forPrint:t.forPrint,axis:t.axis,dateProfile:r,slatMetas:t.slatMetas,slotDuration:t.slotDuration,cells:o.cells[0],tableColGroupNode:t.tableColGroupNode,tableMinWidth:t.tableMinWidth,clientWidth:t.clientWidth,clientHeight:t.clientHeight,expandRows:t.expandRows,nowDate:l,nowIndicatorSegs:i&&e.slicer.sliceNowDate(l,n,a),todayRange:u,onScrollTopRequest:t.onScrollTopRequest,onSlatCoords:t.onSlatCoords}))})},t}(r7);function sx(e,t,n){for(var r=[],o=0,i=e.headerDates;o<i.length;o++){var a=i[o];r.push({start:n.add(a,t.slotMinTime),end:n.add(a,t.slotMaxTime)})}return r}var sM=[{hours:1},{minutes:30},{minutes:15},{seconds:30},{seconds:15},];function s_(e,t,n,r,o){for(var i=new Date(0),a=e,s=tR(0),l=n||function e(t){var n,r,o;for(n=sM.length-1;n>=0;n-=1)if(r=tR(sM[n]),null!==(o=tM(r,t))&&o>1)return r;return t}(r),u=[];tx(a)<tx(t);){var c=o.add(i,a),d=null!==tM(s,l);u.push({date:c,time:a,key:c.toISOString(),isoTimeStr:tN(c),isLabeled:d}),a=tw(a,r),s=tw(s,r)}return u}var sI=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.buildTimeColsModel=tA(sP),t.buildSlatMetas=tA(s_),t}return a(t,e),t.prototype.render=function(){var e=this,t=this.context,n=t.options,r=t.dateEnv,o=t.dateProfileGenerator,i=this.props,a=i.dateProfile,l=this.buildTimeColsModel(a,o),u=this.allDaySplitter.splitProps(i),c=this.buildSlatMetas(a.slotMinTime,a.slotMaxTime,n.slotLabelInterval,n.slotDuration,r),d=n.dayMinWidth,p=!d,f=n.dayHeaders&&rB(iD,{dates:l.headerDates,dateProfile:a,datesRepDistinctDays:!0,renderIntro:p?this.renderHeadAxis:null}),h=!1!==n.allDaySlot&&function(t){return rB(a5,s({},u.allDay,{dateProfile:a,dayTableModel:l,nextDayThreshold:n.nextDayThreshold,tableMinWidth:t.tableMinWidth,colGroupNode:t.tableColGroupNode,renderRowIntro:p?e.renderTableRowAxis:null,showWeekNumbers:!1,expandRows:!1,headerAlignElRef:e.headerElRef,clientWidth:t.clientWidth,clientHeight:t.clientHeight,forPrint:i.forPrint},e.getAllDayMaxEventProps()))},v=function(t){return rB(sk,s({},u.timed,{dayTableModel:l,dateProfile:a,axis:p,slotDuration:n.slotDuration,slatMetas:c,forPrint:i.forPrint,tableColGroupNode:t.tableColGroupNode,tableMinWidth:t.tableMinWidth,clientWidth:t.clientWidth,clientHeight:t.clientHeight,onSlatCoords:e.handleSlatCoords,expandRows:t.expandRows,onScrollTopRequest:e.handleScrollTopRequest}))};return d?this.renderHScrollLayout(f,h,v,l.colCnt,d,c,this.state.slatCoords):this.renderSimpleLayout(f,h,v)},t}(si);function sP(e,t){var n=new iC(e.renderRange,t);return new i$(n,!1)}var sN=r6({initialView:"timeGridWeek",optionRefiners:{allDaySlot:Boolean},views:{timeGrid:{component:sI,usesMinMaxTime:!0,allDaySlot:!0,slotDuration:"00:30:00",slotEventOverlap:!0},timeGridDay:{type:"timeGrid",duration:{days:1}},timeGridWeek:{type:"timeGrid",duration:{weeks:1}}}}),sH=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={textId:e_()},t}return a(t,e),t.prototype.render=function(){var e=this.context,t=e.theme,n=e.dateEnv,r=e.options,o=e.viewApi,i=this.props,a=i.cellId,l=i.dayDate,u=i.todayRange,c=this.state.textId,d=r$(l,u),p=r.listDayFormat?n.format(l,r.listDayFormat):"",f=r.listDaySideFormat?n.format(l,r.listDaySideFormat):"",h=s({date:n.toDate(l),view:o,textId:c,text:p,sideText:f,navLinkAttrs:rT(this.context,l),sideNavLinkAttrs:rT(this.context,l,"day",!1)},d),v=["fc-list-day"].concat(rR(d,t));return rB(ot,{hookProps:h,classNames:r.dayHeaderClassNames,content:r.dayHeaderContent,defaultContent:sO,didMount:r.dayHeaderDidMount,willUnmount:r.dayHeaderWillUnmount},function(e,n,r,o){return rB("tr",{ref:e,className:v.concat(n).join(" "),"data-date":tP(l)},rB("th",{scope:"colgroup",colSpan:3,id:a,"aria-labelledby":c},rB("div",{className:"fc-list-day-cushion "+t.getClass("tableCellShaded"),ref:r},o)))})},t}(r3);function sO(e){return rB(rj,null,e.text&&rB("a",s({id:e.textId,className:"fc-list-day-text"},e.navLinkAttrs),e.text),e.sideText&&rB("a",s({"aria-hidden":!0,className:"fc-list-day-side-text"},e.sideNavLinkAttrs),e.sideText))}var sA=t1({hour:"numeric",minute:"2-digit",meridiem:"short"}),sL=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t.prototype.render=function(){var e=this.props,t=this.context,n=e.seg,r=e.timeHeaderId,o=e.eventHeaderId,i=e.dateHeaderId,a=t.options.eventTimeFormat||sA;return rB(i0,{seg:n,timeText:"",disableDragging:!0,disableResizing:!0,defaultContent:function(){var e,r,o;return e=n,r=t,o=nB(e,r),rB("a",s({},o),e.eventRange.def.title)},isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday,isSelected:e.isSelected,isDragging:e.isDragging,isResizing:e.isResizing,isDateSelecting:e.isDateSelecting},function(e,s,l,u,c){return rB("tr",{className:["fc-list-event",c.event.url?"fc-event-forced-url":""].concat(s).join(" "),ref:e},function e(t,n,r,o,i){var a=r.options;if(!1!==a.displayEventTime){var s=t.eventRange.def,l=t.eventRange.instance,u=!1,c=void 0;return(s.allDay?u=!0:nD(t.eventRange.range)?t.isStart?c=nW(t,n,r,null,null,l.range.start,t.end):t.isEnd?c=nW(t,n,r,null,null,t.start,l.range.end):u=!0:c=nW(t,n,r),u)?rB(ot,{hookProps:{text:r.options.allDayText,view:r.viewApi},classNames:a.allDayClassNames,content:a.allDayContent,defaultContent:sU,didMount:a.allDayDidMount,willUnmount:a.allDayWillUnmount},function(e,t,n,r){return rB("td",{ref:e,headers:o+" "+i,className:["fc-list-event-time"].concat(t).join(" ")},r)}):rB("td",{className:"fc-list-event-time"},c)}return null}(n,a,t,r,i),rB("td",{"aria-hidden":!0,className:"fc-list-event-graphic"},rB("span",{className:"fc-list-event-dot",style:{borderColor:c.borderColor||c.backgroundColor}})),rB("td",{ref:l,headers:o+" "+i,className:"fc-list-event-title"},u))})},t}(r3);function sU(e){return e.text}var sW=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.computeDateVars=tA(sz),t.eventStoreToSegs=tA(t._eventStoreToSegs),t.state={timeHeaderId:e_(),eventHeaderId:e_(),dateHeaderIdRoot:e_()},t.setRootEl=function(e){e?t.context.registerInteractiveComponent(t,{el:e}):t.context.unregisterInteractiveComponent(t)},t}return a(t,e),t.prototype.render=function(){var e=this,t=this.props,n=this.context,r=["fc-list",n.theme.getClass("table"),!1!==n.options.stickyHeaderDates?"fc-list-sticky":"",],o=this.computeDateVars(t.dateProfile),i=o.dayDates,a=o.dayRanges,s=this.eventStoreToSegs(t.eventStore,t.eventUiBases,a);return rB(ou,{viewSpec:n.viewSpec,elRef:this.setRootEl},function(n,o){return rB("div",{ref:n,className:r.concat(o).join(" ")},rB(iH,{liquid:!t.isHeightAuto,overflowX:t.isHeightAuto?"visible":"hidden",overflowY:t.isHeightAuto?"visible":"auto"},s.length>0?e.renderSegList(s,i):e.renderEmptyMessage()))})},t.prototype.renderEmptyMessage=function(){var e=this.context,t=e.options,n=e.viewApi;return rB(ot,{hookProps:{text:t.noEventsText,view:n},classNames:t.noEventsClassNames,content:t.noEventsContent,defaultContent:sV,didMount:t.noEventsDidMount,willUnmount:t.noEventsWillUnmount},function(e,t,n,r){return rB("div",{className:["fc-list-empty"].concat(t).join(" "),ref:e},rB("div",{className:"fc-list-empty-cushion",ref:n},r))})},t.prototype.renderSegList=function(e,t){var n=this.context,r=n.theme,o=n.options,i=this.state,a=i.timeHeaderId,l=i.eventHeaderId,u=i.dateHeaderIdRoot,c=function e(t){var n,r,o=[];for(n=0;n<t.length;n+=1)(o[(r=t[n]).dayIndex]||(o[r.dayIndex]=[])).push(r);return o}(e);return rB(iE,{unit:"day"},function(e,n){for(var i=[],d=0;d<c.length;d+=1){var p=c[d];if(p){var f=tP(t[d]),h=u+"-"+f;i.push(rB(sH,{key:f,cellId:h,dayDate:t[d],todayRange:n})),p=nH(p,o.eventOrder);for(var v=0,g=p;v<g.length;v++){var m=g[v];i.push(rB(sL,s({key:f+":"+m.eventRange.instance.instanceId,seg:m,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,timeHeaderId:a,eventHeaderId:l,dateHeaderId:h},nV(m,n,e))))}}}return rB("table",{className:"fc-list-table "+r.getClass("table")},rB("thead",null,rB("tr",null,rB("th",{scope:"col",id:a},o.timeHint),rB("th",{scope:"col","aria-hidden":!0}),rB("th",{scope:"col",id:l},o.eventHint))),rB("tbody",null,i))})},t.prototype._eventStoreToSegs=function(e,t,n){return this.eventRangesToSegs(nx(e,t,this.props.dateProfile.activeRange,this.context.options.nextDayThreshold).fg,n)},t.prototype.eventRangesToSegs=function(e,t){for(var n=[],r=0,o=e;r<o.length;r++){var i=o[r];n.push.apply(n,this.eventRangeToSegs(i,t))}return n},t.prototype.eventRangeToSegs=function(e,t){var n,r,o,i=this.context.dateEnv,a=this.context.options.nextDayThreshold,s=e.range,l=e.def.allDay,u=[];for(n=0;n<t.length;n+=1)if((r=nR(s,t[n]))&&(u.push(o={component:this,eventRange:e,start:r.start,end:r.end,isStart:e.isStart&&r.start.valueOf()===s.start.valueOf(),isEnd:e.isEnd&&r.end.valueOf()===s.end.valueOf(),dayIndex:n}),!o.isEnd&&!l&&n+1<t.length&&s.end<i.add(t[n+1].start,a))){o.end=s.end,o.isEnd=!0;break}return u},t}(r7);function sV(e){return e.text}function sz(e){for(var t=tt(e.renderRange.start),n=e.renderRange.end,r=[],o=[];t<n;)r.push(t),o.push({start:t,end:e5(t,1)}),t=e5(t,1);return{dayDates:r,dayRanges:o}}function sF(e){return!1===e?null:t1(e)}var sB=r6({optionRefiners:{listDayFormat:sF,listDaySideFormat:sF,noEventsClassNames:tJ,noEventsContent:tJ,noEventsDidMount:tJ,noEventsWillUnmount:tJ},views:{list:{component:sW,buttonTextKey:"list",listDayFormat:{month:"long",day:"numeric",year:"numeric"}},listDay:{type:"list",duration:{days:1},listDayFormat:{weekday:"long"}},listWeek:{type:"list",duration:{weeks:1},listDayFormat:{weekday:"long"},listDaySideFormat:{month:"long",day:"numeric",year:"numeric"}},listMonth:{type:"list",duration:{month:1},listDaySideFormat:{weekday:"long"}},listYear:{type:"list",duration:{year:1},listDaySideFormat:{weekday:"long"}}}}),sG=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t}(rz);sG.prototype.classes={root:"fc-theme-bootstrap",table:"table-bordered",tableCellShaded:"table-active",buttonGroup:"btn-group",button:"btn btn-primary",buttonActive:"active",popover:"popover",popoverHeader:"popover-header",popoverContent:"popover-body"},sG.prototype.baseIconClass="fa",sG.prototype.iconClasses={close:"fa-times",prev:"fa-chevron-left",next:"fa-chevron-right",prevYear:"fa-angle-double-left",nextYear:"fa-angle-double-right"},sG.prototype.rtlIconClasses={prev:"fa-chevron-right",next:"fa-chevron-left",prevYear:"fa-angle-double-right",nextYear:"fa-angle-double-left"},sG.prototype.iconOverrideOption="bootstrapFontAwesome",sG.prototype.iconOverrideCustomButtonOption="bootstrapFontAwesome",sG.prototype.iconOverridePrefix="fa-";var sq=r6({themeClasses:{bootstrap:sG}}),sj=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return a(t,e),t}(rz);sj.prototype.classes={root:"fc-theme-bootstrap5",tableCellShaded:"fc-theme-bootstrap5-shaded",buttonGroup:"btn-group",button:"btn btn-primary",buttonActive:"active",popover:"popover",popoverHeader:"popover-header",popoverContent:"popover-body"},sj.prototype.baseIconClass="bi",sj.prototype.iconClasses={close:"bi-x-lg",prev:"bi-chevron-left",next:"bi-chevron-right",prevYear:"bi-chevron-double-left",nextYear:"bi-chevron-double-right"},sj.prototype.rtlIconClasses={prev:"bi-chevron-right",next:"bi-chevron-left",prevYear:"bi-chevron-double-right",nextYear:"bi-chevron-double-left"},sj.prototype.iconOverrideOption="buttonIcons",sj.prototype.iconOverrideCustomButtonOption="icon",sj.prototype.iconOverridePrefix="bi-";var sY=r6({themeClasses:{bootstrap5:sj}}),sZ=r6({eventSourceDefs:[{parseMeta:function(e){var t,n,r=e.googleCalendarId;return(!r&&e.url&&(r=(t=e.url,/^[^/]+@([^/.]+\.)*(google|googlemail|gmail)\.com$/.test(t)?t:(n=/^https:\/\/www.googleapis.com\/calendar\/v3\/calendars\/([^/]*)/.exec(t))||(n=/^https?:\/\/www.google.com\/calendar\/feeds\/([^/]*)/.exec(t))?decodeURIComponent(n[1]):null)),r)?{googleCalendarId:r,googleCalendarApiKey:e.googleCalendarApiKey,googleCalendarApiBase:e.googleCalendarApiBase,extraParams:e.extraParams}:null},fetch:function(e,t,n){var r=e.context,o=r.dateEnv,i=r.options,a=e.eventSource.meta,l=a.googleCalendarApiKey||i.googleCalendarApiKey;if(l){var u,c,d,p,f,h,v,g,m,y=(u=a,c=u.googleCalendarApiBase,c||(c="https://www.googleapis.com/calendar/v3/calendars"),c+"/"+encodeURIComponent(u.googleCalendarId)+"/events"),E=a.extraParams,S="function"==typeof E?E():E,D=(d=e.range,p=l,f=S,h=o,h.canComputeOffset?(g=h.formatIso(d.start),m=h.formatIso(d.end)):(g=e5(d.start,-1).toISOString(),m=e5(d.end,1).toISOString()),v=s(s({},f||{}),{key:p,timeMin:g,timeMax:m,singleEvents:!0,maxResults:9999}),"local"!==h.timeZone&&(v.timeZone=h.timeZone),v);ok("GET",y,D,function(e,r){var o,i;e.error?n({message:"Google Calendar API: "+e.error.message,errors:e.error.errors,xhr:r}):t({rawEvents:(o=e.items,i=D.timeZone,o.map(function(e){var t,n,r,o,a;return t=e,n=i,r=t.htmlLink||null,r&&n&&(r=(o=r,a="ctz="+n,o.replace(/(\?.*?)?(#|$)/,function(e,t,n){return(t?t+"&":"?")+a+n}))),{id:t.id,title:t.summary,start:t.start.dateTime||t.start.date,end:t.end.dateTime||t.end.date,url:r,location:t.location,description:t.description,attachments:t.attachments||[],extendedProps:(t.extendedProperties||{}).shared||{}}})),xhr:r})},function(e,t){n({message:e,xhr:t})})}else n({message:"Specify a googleCalendarApiKey. See http://fullcalendar.io/docs/google_calendar/"})}}],optionRefiners:{googleCalendarApiKey:String},eventSourceRefiners:{googleCalendarApiKey:String,googleCalendarId:String,googleCalendarApiBase:String,extraParams:tJ}});return oN.push(aH,aQ,sN,sB,sq,sY,sZ),e.BASE_OPTION_DEFAULTS=tK,e.BASE_OPTION_REFINERS=t4,e.BaseComponent=r3,e.BgEvent=i6,e.BootstrapTheme=sG,e.Calendar=au,e.CalendarApi=n6,e.CalendarContent=il,e.CalendarDataManager=oL,e.CalendarDataProvider=oZ,e.CalendarRoot=id,e.Component=rF,e.ContentHook=or,e.CustomContentRenderContext=on,e.DateComponent=r7,e.DateEnv=ri,e.DateProfileGenerator=oh,e.DayCellContent=i2,e.DayCellRoot=i9,e.DayGridView=a9,e.DayHeader=iD,e.DaySeriesModel=iC,e.DayTable=a5,e.DayTableModel=i$,e.DayTableSlicer=a2,e.DayTimeCols=sk,e.DayTimeColsSlicer=sT,e.DayTimeColsView=sI,e.DelayedRunner=oH,e.Draggable=aI,e.ElementDragging=oQ,e.ElementScrollController=rW,e.Emitter=rA,e.EventApi=nQ,e.EventRoot=i0,e.EventSourceApi=eD,e.FeaturefulElementDragging=aD,e.Fragment=rj,e.Interaction=o9,e.ListView=sW,e.MoreLinkRoot=ar,e.MountHook=oi,e.NamedTimeZoneImpl=function e(t){this.timeZoneName=t},e.NowIndicatorRoot=iK,e.NowTimer=iE,e.PointerDragging=af,e.PositionCache=rL,e.RefMap=iO,e.RenderHook=ot,e.ScrollController=rU,e.ScrollResponder=r0,e.Scroller=iH,e.SegHierarchy=oX,e.SimpleScrollGrid=iX,e.Slicer=iR,e.Splitter=rb,e.StandardEvent=i1,e.Table=aK,e.TableDateCell=ig,e.TableDowCell=iy,e.TableView=aO,e.Theme=rz,e.ThirdPartyDraggable=aN,e.TimeCols=s8,e.TimeColsSlatsCoords=ss,e.TimeColsView=si,e.ViewApi=n3,e.ViewContextType=r1,e.ViewRoot=ou,e.WeekNumberRoot=iJ,e.WindowScrollController=rV,e.addDays=e5,e.addDurations=tw,e.addMs=e9,e.addWeeks=e2,e.allowContextMenu=eG,e.allowSelection=eF,e.applyMutationToEventStore=n1,e.applyStyle=ew,e.applyStyleProp=eT,e.asCleanDays=function e(t){return t.years||t.months||t.milliseconds?0:t.days},e.asRoughMinutes=function e(t){return tx(t)/6e4},e.asRoughMs=tx,e.asRoughSeconds=function e(t){return tx(t)/1e3},e.binarySearch=o5,e.buildClassNameNormalizer=oa,e.buildDayRanges=sx,e.buildDayTableModel=a7,e.buildEntryKey=o1,e.buildEventApis=re,e.buildEventRangeKey=nF,e.buildHashFromArray=function e(t,n){for(var r={},o=0;o<t.length;o+=1){var i=n(t[o],o);r[i[0]]=i[1]}return r},e.buildIsoString=tI,e.buildNavLinkAttrs=rT,e.buildSegCompareObj=nO,e.buildSegTimeText=nW,e.buildSlatMetas=s_,e.buildTimeColsModel=sP,e.collectFromHash=tS,e.combineEventUis=nc,e.compareByFieldSpec=eY,e.compareByFieldSpecs=ej,e.compareNumbers=e1,e.compareObjs=ty,e.computeEarliestSegStart=aa,e.computeEdges=rI,e.computeFallbackHeaderFormat=ip,e.computeHeightAndMargins=function e(t){var n,r;return t.getBoundingClientRect().height+(n=t,r=window.getComputedStyle(n),parseInt(r.marginTop,10)+parseInt(r.marginBottom,10))},e.computeInnerRect=rP,e.computeRect=rN,e.computeSegDraggable=nA,e.computeSegEndResizable=nU,e.computeSegStartResizable=nL,e.computeShrinkWidth=iA,e.computeSmallestCellWidth=eK,e.computeVisibleDayRange=nS,e.config=oJ,e.constrainPoint=rm,e.createAriaClickAttrs=eO,e.createContext=rY,e.createDuration=tR,e.createElement=rB,e.createEmptyEventStore=nr,e.createEventInstance=tu,e.createEventUi=nu,e.createFormatter=t1,e.createPlugin=r6,e.createPortal=rZ,e.createRef=rq,e.diffDates=nb,e.diffDayAndTime=eQ,e.diffDays=e6,e.diffPoints=rE,e.diffWeeks=e7,e.diffWholeDays=te,e.diffWholeWeeks=eJ,e.disableCursor=eW,e.elementClosest=eC,e.elementMatches=e$,e.enableCursor=eV,e.eventTupleToStore=nt,e.filterEventStoreDefs=ni,e.filterHash=tp,e.findDirectChildren=function e(t,n){for(var r=t instanceof HTMLElement?[t]:t,o=[],i=0;i<r.length;i+=1)for(var a=r[i].children,s=0;s<a.length;s+=1){var l=a[s];(!n||e$(l,n))&&o.push(l)}return o},e.findElements=eR,e.flexibleCompare=eZ,e.flushSync=eE,e.formatDate=function e(t,n){void 0===n&&(n={});var r=rp(n),o=t1(n),i=r.createMarkerMeta(t);return i?r.format(i.marker,o,{forcedTzo:i.forcedTzo}):""},e.formatDayString=tP,e.formatIsoTimeString=tN,e.formatRange=function e(t,n,r){var o=rp("object"==typeof r&&r?r:{}),i=t1(r),a=o.createMarkerMeta(t),s=o.createMarkerMeta(n);return a&&s?o.formatRange(a.marker,s.marker,i,{forcedStartTzo:a.forcedTzo,forcedEndTzo:s.forcedTzo,isEndExclusive:r.isEndExclusive,defaultSeparator:tK.defaultRangeSeparator}):""},e.getAllowYScrolling=iU,e.getCanVGrowWithinCell=rS,e.getClippingParents=rH,e.getDateMeta=r$,e.getDayClassNames=rR,e.getDefaultEventEnd=n0,e.getElRoot=ex,e.getElSeg=nI,e.getEntrySpanEnd=o0,e.getEventClassNames=nz,e.getEventTargetViaRoot=ek,e.getIsRtlScrollbarOnLeft=rx,e.getRectCenter=ry,e.getRelevantEvents=nn,e.getScrollGridClassNames=iG,e.getScrollbarWidths=rM,e.getSectionClassNames=iq,e.getSectionHasLiquidHeight=iL,e.getSegAnchorAttrs=nB,e.getSegMeta=nV,e.getSlotClassNames=function e(t,n){var r=["fc-slot","fc-slot-"+e3[t.dow],];return t.isDisabled?r.push("fc-slot-disabled"):(t.isToday&&(r.push("fc-slot-today"),r.push(n.getClass("today"))),t.isPast&&r.push("fc-slot-past"),t.isFuture&&r.push("fc-slot-future")),r},e.getStickyFooterScrollbar=iZ,e.getStickyHeaderDates=iY,e.getUnequalProps=tm,e.getUniqueDomId=e_,e.globalLocales=ra,e.globalPlugins=oN,e.greatestDurationDenominator=t_,e.groupIntersectingEntries=o4,e.guid=eU,e.hasBgRendering=nM,e.hasShrinkWidth=iB,e.identity=tJ,e.interactionSettingsStore=o6,e.interactionSettingsToStore=o7,e.intersectRanges=nR,e.intersectRects=rg,e.intersectSpans=o3,e.isArraysEqual=tO,e.isColPropsEqual=iV,e.isDateSelectionValid=iT,e.isDateSpansEqual=nq,e.isInt=e4,e.isInteractionValid=iw,e.isMultiDayRange=nD,e.isPropsEqual=tg,e.isPropsValid=ix,e.isValidDate=ts,e.joinSpans=oK,e.listenBySelector=eP,e.mapHash=tf,e.memoize=tA,e.memoizeArraylike=function e(t,n,r){var o=this,i=[],a=[];return function(e){for(var s=i.length,l=e.length,u=0;u<s;u+=1)if(e[u]){if(!tO(i[u],e[u])){r&&r(a[u]);var c=t.apply(o,e[u]);n&&n(c,a[u])||(a[u]=c)}}else r&&r(a[u]);for(;u<l;u+=1)a[u]=t.apply(o,e[u]);return i=e,a.splice(l),a}},e.memoizeHashlike=function e(t,n,r){var o=this,i={},a={};return function(e){var s={};for(var l in e)if(a[l]){if(tO(i[l],e[l]))s[l]=a[l];else{r&&r(a[l]);var u=t.apply(o,e[l]);s[l]=n&&n(u,a[l])?a[l]:u}}else s[l]=t.apply(o,e[l]);return i=e,a=s,s}},e.memoizeObjArg=tL,e.mergeEventStores=no,e.multiplyDuration=tT,e.padStart=eX,e.parseBusinessHours=rh,e.parseClassNames=na,e.parseDragMeta=it,e.parseEventDef=ny,e.parseFieldSpecs=eq,e.parseMarker=ro,e.pointInsideRect=rv,e.preventContextMenu=eB,e.preventDefault=eI,e.preventSelection=ez,e.rangeContainsMarker=nk,e.rangeContainsRange=nT,e.rangesEqual=n8,e.rangesIntersect=nw,e.refineEventDef=ng,e.refineProps=tQ,e.removeElement=eb,e.removeExact=function e(t,n){for(var r=0,o=0;o<t.length;)t[o]===n?(t.splice(o,1),r+=1):o+=1;return r},e.render=rG,e.renderChunkContent=iW,e.renderFill=i7,e.renderMicroColGroup=iz,e.renderScrollShim=ij,e.requestJson=ok,e.sanitizeShrinkWidth=iF,e.setElSeg=n_,e.setRef=r9,e.sliceEventStore=nx,e.sliceEvents=function e(t,n){return nx(t.eventStore,t.eventUiBases,t.dateProfile.activeRange,n?t.nextDayThreshold:null).fg},e.sortEventSegs=nH,e.startOfDay=tt,e.translateRect=function e(t,n,r){return{left:t.left+n,right:t.right+n,top:t.top+r,bottom:t.bottom+r}},e.triggerDateSelect=nZ,e.unmountComponentAtNode=rX,e.unpromisify=rO,e.version="5.11.3",e.whenTransitionDone=eH,e.wholeDivideDurations=tM,Object.defineProperty(e,"__esModule",{value:!0}),e}({});