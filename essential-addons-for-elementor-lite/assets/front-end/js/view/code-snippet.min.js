!function(e){var t={};function n(o){if(t[o])return t[o].exports;var i=t[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(o,i,function(t){return e[t]}.bind(null,i));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=6)}({6:function(e,t){var n=function(e,t){window.EaelCodeSnippet=window.EaelCodeSnippet||{};var n=2e3,o={html:"xml",jsx:"javascript",vue:"javascript",ts:"typescript",py:"python",rd:"ruby",yml:"yaml",cpp:"cpp",cs:"csharp",rs:"rust",kt:"kotlin",md:"markdown",sh:"bash",ps1:"powershell",dockerfile:"dockerfile"};function i(e){return o[e]||e}var r=!1,l=!1;function a(e){if(r)e&&e(!0);else if(l)var t=setInterval((function(){!r&&l||(clearInterval(t),e&&e(r))}),100);else{if(l=!0,window.hljs)return r=!0,l=!1,void(e&&e(!0));var n=0,o=setInterval((function(){n++,window.hljs?(r=!0,l=!1,clearInterval(o),e&&e(!0)):n>=10&&(l=!1,clearInterval(o),window.console&&window.console.warn&&console.warn("Essential Addons: Syntax highlighting unavailable"),e&&e(!1))}),100)}}function s(){r&&window.hljs&&document.querySelectorAll(".eael-code-snippet-code:not(.hljs)").forEach((function(e){try{window.hljs.highlightElement(e)}catch(e){window.console&&window.console.warn&&console.warn("Essential Addons: Syntax highlighting error")}}))}function c(e,t){if(!r||!window.hljs)return!1;try{var n=i(t);return e.className=e.className.replace(/\bhljs\b/g,"").replace(/\blanguage-\w+/g,"").trim(),n&&(e.className+=" language-"+n),e.removeAttribute("data-highlighted"),window.hljs.highlightElement(e),!0}catch(e){return window.console&&window.console.warn&&console.warn("Essential Addons: Syntax highlighting error"),!1}}function d(e,t){var n=document.createElement("textarea");n.value=e,n.style.position="fixed",n.style.left="-999999px",n.style.top="-999999px",document.body.appendChild(n);try{n.focus(),n.select();var o=document.execCommand("copy");t&&t(o)}catch(e){window.console&&window.console.warn&&console.warn("Essential Addons: Copy operation failed"),t&&t(!1)}finally{document.body.removeChild(n)}}function u(e,t){var n=e.getBoundingClientRect(),o=t.getBoundingClientRect(),i=n.left+n.width/2-o.width/2,r=n.top-o.height-8;t.style.left=Math.max(8,i)+"px",t.style.top=Math.max(8,r)+"px"}function p(e){var t,o,i,r=e.querySelector(".eael-code-snippet-copy-button"),l=e.querySelector(".eael-code-snippet-code code");(l||(l=e.querySelector(".eael-code-snippet-code")),l||(l=e.querySelector("code")),l||(l=e.querySelector("pre")),r)&&(l?(o=(t=r).closest(".eael-code-snippet-copy-container"),(i=o?o.querySelector(".eael-code-snippet-tooltip"):null)&&(t.addEventListener("mouseenter",(function(){i.classList.add("show"),u(t,i)})),t.addEventListener("mouseleave",(function(){i.classList.remove("show")})),t.addEventListener("blur",(function(){i.classList.remove("show")})),window.addEventListener("scroll",(function(){i.classList.contains("show")&&u(t,i)})),window.addEventListener("resize",(function(){i.classList.contains("show")&&u(t,i)}))),r.removeEventListener("click",r._eaelClickHandler),r._eaelClickHandler=function(t){t.preventDefault();var o,i,a=l.textContent||l.innerText||"";a.trim()?(o=a,i=function(t){if(t){!function(e){var t=e.closest(".eael-code-snippet-copy-container"),o=t?t.querySelector(".eael-code-snippet-tooltip"):null,i=e.querySelector("svg");if(i){var r=i.outerHTML;i.style.transition="all 0.3s ease",i.style.transform="scale(0.8)",setTimeout((function(){i.innerHTML='<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/>',i.setAttribute("viewBox","0 0 24 24"),i.style.transform="scale(1)"}),150),setTimeout((function(){i.style.transform="scale(0.8)",setTimeout((function(){i.outerHTML=r;var t=e.querySelector("svg");t&&(t.style.transition="all 0.3s ease",t.style.transform="scale(1)")}),150)}),1e3)}if(o){var l=o.textContent;o.textContent="Copied!",o.classList.contains("show")&&u(e,o),setTimeout((function(){o.textContent=l,o.classList.contains("show")&&u(e,o)}),n)}}(r);var o=new CustomEvent("eael-code-copied",{detail:{snippet:e,code:a,language:e.dataset.language}});document.dispatchEvent(o)}else window.console&&window.console.error&&console.error("Essential Addons: Copy operation failed"),function(e){var t=e.closest(".eael-code-snippet-copy-container"),n=t?t.querySelector(".eael-code-snippet-tooltip"):null;if(n){var o=n.textContent;n.textContent="Copy Failed",n.classList.contains("show")&&u(e,n),setTimeout((function(){n.textContent=o,n.classList.contains("show")&&u(e,n)}),2e3)}}(r)},navigator.clipboard&&window.isSecureContext?navigator.clipboard.writeText(o).then((function(){i&&i(!0)})).catch((function(){window.console&&window.console.warn&&console.warn("Essential Addons: Copy operation failed"),d(o,i)})):d(o,i)):window.console&&window.console.warn&&console.warn("Essential Addons: No content to copy")},r.addEventListener("click",r._eaelClickHandler)):window.console&&window.console.warn&&console.warn("Essential Addons: Code element not found for copy button"))}function f(){var e=document.querySelectorAll(".eael-code-snippet-wrapper");0!==e.length&&(a((function(e){e&&s()})),e.forEach((function(e){e.querySelector(".eael-code-snippet-copy-button")&&p(e)})))}function w(){f()}(window.EaelCodeSnippet={init:f,reinit:w,initCopyButton:p,applySyntaxHighlighting:s,applySyntaxHighlightingToBlock:c,updateSnippetLanguage:function(e,t){if(!e)return!1;var n=e.querySelector(".eael-code-snippet-code");return!!n&&(e.dataset.language=t,c(n,t))},getHighlightLanguage:i,loadHighlightJs:a},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",f):f(),window.MutationObserver)&&new MutationObserver((function(e){var t=!1;e.forEach((function(e){"childList"===e.type&&e.addedNodes.forEach((function(e){1===e.nodeType&&(e.classList&&e.classList.contains("eael-code-snippet-wrapper")||e.querySelector&&e.querySelector(".eael-code-snippet-wrapper"))&&(t=!0)}))})),t&&setTimeout(w,100)})).observe(document.body,{childList:!0,subtree:!0})};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/eael-code-snippet.default",n)}))}});