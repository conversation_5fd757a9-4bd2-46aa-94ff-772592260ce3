!function(e){var t={};function a(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,a),o.l=!0,o.exports}a.m=e,a.c=t,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)a.d(n,o,function(t){return e[t]}.bind(null,o));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=11)}({11:function(e,t){function a(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=function(e,t){if(e){if("string"==typeof e)return n(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?n(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var d,l=!0,i=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return l=e.done,e},e:function(e){i=!0,d=e},f:function(){try{l||null==a.return||a.return()}finally{if(i)throw d}}}}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=Array(t);a<t;a++)n[a]=e[a];return n}var o=function(e,t){var n=FullCalendar.Calendar,o=t(".eael-event-calendar-cls",e),r=t(".eael-event-calendar-wrapper",e),d=t(".eaelec-modal-close",e).eq(0),l=t("#eaelecModal",e),i=o.data("events"),m=o.data("first_day"),s=(o.data("cal_id"),o.data("locale")),c=o.data("translate"),f=o.data("defaultview"),u=o.data("defaultdate"),v=void 0!==o.data("multidays_event_day_count")?o.data("multidays_event_day_count"):0,y=o.data("event_limit"),Y=o.data("popup_date_format"),p=o.data("monthcolumnheaderformat"),D=o.data("weekcolumnheaderformat"),h="yes"==o.data("time_format");if(r.hasClass("layout-calendar")){var M=function(){var a=t(".eaelec-modal-footer"),n=a.find("a").attr("class"),o=t(".eael-event-calendar-cls",e).attr("data-detailsButtonText");a.html('<a class="'+n+'">'+DOMPurify.sanitize(o)+"</a>")},b=new n(e[0].querySelector(".eael-event-calendar-cls"),{views:{month:{dayHeaderContent:function(e){if("dayGridMonth"===e.view.type&&p)return moment(e.date).format(p)}},week:{dayHeaderContent:function(e){if(console.log("weekColumnHeaderFormat",D),D)return moment(e.date).format(D)}}},editable:!1,selectable:!1,firstDay:m,eventTimeFormat:{hour:"2-digit",minute:"2-digit",hour12:!h},nextDayThreshold:"00:00:00",headerToolbar:{start:"prev,next today",center:"title",end:"timeGridDay,timeGridWeek,dayGridMonth,listMonth"},events:i,locale:s,dayMaxEventRows:void 0!==y&&y>0?parseInt(y):3,initialView:f,initialDate:u,eventClassNames:function(e){},eventContent:function(e){},eventDidMount:function(a){var n=t(a.el),o=a.event;if(moment.locale(s),v&&o.endStr>o.startStr){var r,d=void 0!==o.startStr?new Date(o.startStr):"",i=((void 0!==o.endStr?new Date(o.endStr):"")-d)/864e5,m=null===(r=t(n).prevAll("tr.fc-list-day:first"))||void 0===r?void 0:r.data("date");m=void 0!==m?new Date(m):"";var f=d&&m?Math.ceil((m-d)/864e5)+1:"",u="".concat(o.title," (Day ").concat(f,"/").concat(i," )");n.find(".fc-list-event-title a").text(u)}n.hasClass("fc-event-past")&&n.find(".fc-event-title").addClass("eael-event-completed"),c.today=a.event._context.dateEnv.locale.options.buttonText.today,n.attr("style","color:"+o.textColor+";background:"+o.backgroundColor+";"),n.find(".fc-list-event-dot").attr("style","border-color:"+o.backgroundColor+";"),n.find(".fc-daygrid-event-dot").remove(),"yes"===o._def.extendedProps.is_redirect?(n.attr("href",o.url),"on"===o._def.extendedProps.external&&n.attr("target","_blank"),"on"===o._def.extendedProps.nofollow&&n.attr("rel","nofollow"),""!==o._def.extendedProps.custom_attributes&&t.each(o._def.extendedProps.custom_attributes,(function(e,t){n.attr(t.key,t.value)})),n.hasClass("fc-list-item")&&(n.removeAttr("href target rel"),n.removeClass("fc-has-url"),n.css("cursor","default"))):(n.attr("href","javascript:void(0);"),n.click((function(a){a.preventDefault(),a.stopPropagation();var n=o.start,r=h?"H:mm":"h:mm A",d=o.end,i=t("span.eaelec-event-date-start"),m=t("span.eaelec-event-date-end"),f=t(".eaelec-modal-footer a");o.allDay&&(d=moment(d).subtract(1,"days")._d,r=" ");moment.locale(s);var u=moment(n).format("YYYY"),v=moment(d).format("YYYY"),y=v>u,p="",D="";i.html(" "),m.html(" "),l.addClass("eael-ec-popup-ready").removeClass("eael-ec-modal-removing"),o.allDay&&moment(n).format("MM-DD-YYYY")===moment(d).format("MM-DD-YYYY")?(p=moment(n).format("MMM Do"),!0===moment(n).isSame(Date.now(),"day")?p=c.today:moment(n).format("MM-DD-YYYY")===moment(new Date).add(1,"days").format("MM-DD-YYYY")&&(p=c.tomorrow)):(!0===moment(o.start).isSame(Date.now(),"day")&&(p=c.today+" "+moment(o.start).format(r)),moment(n).format("MM-DD-YYYY")===moment(new Date).add(1,"days").format("MM-DD-YYYY")&&(p=c.tomorrow+" "+moment(o.start).format(r)),(moment(n).format("MM-DD-YYYY")<moment(new Date).format("MM-DD-YYYY")||moment(n).format("MM-DD-YYYY")>moment(new Date).add(1,"days").format("MM-DD-YYYY"))&&(p=moment(o.start).format(Y+" "+r)),p=y?u+" "+p:p,!0===moment(d).isSame(Date.now(),"day")&&(D=!0!==moment(n).isSame(Date.now(),"day")?c.today+" "+moment(d).format(r):moment(d).format(r)),moment(n).format("MM-DD-YYYY")!==moment(new Date).add(1,"days").format("MM-DD-YYYY")&&moment(d).format("MM-DD-YYYY")===moment(new Date).add(1,"days").format("MM-DD-YYYY")&&(D=c.tomorrow+" "+moment(d).format(r)),moment(n).format("MM-DD-YYYY")===moment(new Date).add(1,"days").format("MM-DD-YYYY")&&moment(d).format("MM-DD-YYYY")===moment(new Date).add(1,"days").format("MM-DD-YYYY")&&(D=moment(d).format(r)),moment(d).diff(moment(n),"days")>0&&m.text().trim().length<1&&(D=moment(d).format(Y+" "+r)),moment(n).format("MM-DD-YYYY")===moment(d).format("MM-DD-YYYY")&&(D=moment(d).format(r)),D=y?v+" "+D:D),void 0!==o.extendedProps.hideEndDate&&"yes"===o.extendedProps.hideEndDate?m.html(" "):m.html(""!=D?"- "+D:""),i.html('<i class="eicon-calendar"></i> '+p),t(".eaelec-modal-header h2").html(DOMPurify.sanitize(o.title)),t(".eaelec-modal-body").html(DOMPurify.sanitize(o.extendedProps.description)),o.extendedProps.description.length<1?t(".eaelec-modal-body").css("height","auto"):t(".eaelec-modal-body").css("height","300px"),"yes"!==t(".eael-event-calendar-cls",e).data("hidedetailslink")?f.attr("href",o.url).css("display","block"):f.css("display","none"),"on"===o.extendedProps.external&&f.attr("target","_blank"),"on"===o.extendedProps.nofollow&&f.attr("rel","nofollow"),""!=o.extendedProps.custom_attributes&&t.each(o.extendedProps.custom_attributes,(function(e,t){f.attr(t.key,t.value)})),t(".eaelec-modal-header").css("border-left","5px solid "+o.borderColor),t(".eaelec-modal-header").css("border-left","5px solid "+o.borderColor)})))},eventWillUnmount:function(e){}});d.on("click",(function(e){e.stopPropagation(),l.addClass("eael-ec-modal-removing").removeClass("eael-ec-popup-ready"),M()})),t(document).on("click",(function(e){e.target.closest(".eaelec-modal-content")||l.hasClass("eael-ec-popup-ready")&&(l.addClass("eael-ec-modal-removing").removeClass("eael-ec-popup-ready"),M())})),b.render(),setTimeout((function(){b.setOption("locale",s)}),100),new IntersectionObserver((function(e){var t,n=a(e);try{for(n.s();!(t=n.n()).done;){t.value;window.dispatchEvent(new Event("resize")),setTimeout((function(){window.dispatchEvent(new Event("resize"))}),200)}}catch(e){n.e(e)}finally{n.f()}})).observe(o[0]),eael.hooks.addAction("eventCalendar.reinit","ea",(function(){b.today()}))}else{var w=t(".eael-event-calendar-table",r),g=w.hasClass("ea-ec-table-paginated"),x=g?w.data("items-per-page"):10,_=t(".eael-ec-event-date",w).index();t(".eael-event-calendar-table",r).fancyTable({sortColumn:_>=0?_:0,pagination:g,perPage:x,globalSearch:!0,searchInput:t(".ea-ec-search-wrap",r),paginationElement:t(".eael-event-calendar-pagination",r)})}};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("eaelEventCalendar"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-event-calendar.default",o)}))}});