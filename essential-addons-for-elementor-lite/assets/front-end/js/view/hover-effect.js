/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./src/js/view/hover-effect.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./src/js/view/hover-effect.js":
/*!*************************************!*\
  !*** ./src/js/view/hover-effect.js ***!
  \*************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("var HoverEffectHandler = function HoverEffectHandler($scope, $) {\n  var _$Opacity, _$eaelOffsetTop, _$eaelOffsetLeft, _$eaelOffsetHoverTop, _$eaelOffsetHoverLeft, _$eaelDuration, _$eaelDelay, _$eaelEasing, _$eaelHoverDuration, _$eaelHoverDelay, _$eaelHoverEasing, _$eaelBlurEffect, _$eaelContrastEffect, _$eaelGrayscaleEffect, _$eaelInvertEffect, _$eaelSaturateEffect, _$eaelSepiaEffect, _$eaelRotateEffect, _$eaelRotateEffect2, _$eaelRotateEffect3, _$eaelScaleEffect, _$eaelScaleEffect2, _$eaelSkewEffect, _$eaelSkewEffect2, _$opacityHover, _$eaelRotateHoverEffe, _$eaelRotateHoverEffe2, _$eaelRotateHoverEffe3, _$eaelScaleHoverEffec, _$eaelScaleHoverEffec2, _$eaelSkewHoverEffect, _$eaelSkewHoverEffect2, _$eaelBurHoverEffect, _$eaelContrastHoverEf, _$eaelGrayscalHoverEf, _$eaelInvertHoverEffe, _$eaelSaturateHoverEf, _$eaelSepiaHoverEffec;\n  var $eaelRotateEffect = $scope.data('eael_rotate_effect'),\n    $eaelScaleEffect = $scope.data('eael_scale_effect'),\n    $eaelSkewEffect = $scope.data('eael_skew_effect'),\n    $Opacity = $scope.data('eael_opacity'),\n    $eaelBlurEffect = $scope.data('eael_blur_effect'),\n    $eaelContrastEffect = $scope.data('eael_contrast_effect'),\n    $eaelGrayscaleEffect = $scope.data('eael_grayscale_effect'),\n    $eaelInvertEffect = $scope.data('eael_invert_effect'),\n    $eaelSaturateEffect = $scope.data('eael_saturate_effect'),\n    $eaelSepiaEffect = $scope.data('eael_sepia_effect'),\n    $scopeId = $scope.data('id'),\n    $eaelBurHoverEffect = $scope.data('eael_blur_hover_effect'),\n    $eaelContrastHoverEffect = $scope.data('eael_contrast_hover_effect'),\n    $eaelGrayscalHoverEffect = $scope.data('eael_grayscal_hover_effect'),\n    $eaelInvertHoverEffect = $scope.data('eael_invert_hover_effect'),\n    $eaelSaturateHoverEffect = $scope.data('eael_saturate_hover_effect'),\n    $eaelSepiaHoverEffect = $scope.data('eael_sepia_hover_effect'),\n    $eaelRotateHoverEffect = $scope.data('eael_rotate_hover_effect'),\n    $eaelScaleHoverEffect = $scope.data('eael_scale_hover_effect'),\n    $eaelSkewHoverEffect = $scope.data('eael_skew_hover_effect'),\n    $opacityHover = $scope.data('eael_opacity_hover'),\n    $eaelDuration = $scope.data('eael_duration'),\n    $eaelDelay = $scope.data('eael_delay'),\n    $eaelEasing = $scope.data('eael_easing'),\n    $eaelHoverDuration = $scope.data('eael_hover_duration'),\n    $eaelHoverDelay = $scope.data('eael_hover_delay'),\n    $eaelHoverEasing = $scope.data('eael_hover_easing'),\n    $eaelOffsetTop = $scope.data('eael_offset_top'),\n    $eaelOffsetLeft = $scope.data('eael_offset_left'),\n    $eaelOffsetHoverTop = $scope.data('eael_offset_hover_top'),\n    $eaelOffsetHoverLeft = $scope.data('eael_offset_hover_left'),\n    $eaelTilt = $scope.data('eaeltilt'),\n    $enabledElementList = [];\n\n  /**\n   * For editor page\n   */\n  if (window.isEditMode) {\n    if (window.isRunFirstTime === undefined && window.isEditMode || 1) {\n      var getHoverEffectSettingsVal = function getHoverEffectSettingsVal($el) {\n        $.each($el, function (i, el) {\n          // console.log(el.attributes.settings.attributes);\n          var $getSettings = el.attributes.settings.attributes;\n          if (el.attributes.elType === 'widget') {\n            if ($getSettings['eael_hover_effect_switch'] === 'yes') {\n              $enabledElementList.push(el.attributes.id);\n              if ($getSettings['eael_hover_effect_enable_live_changes'] === 'yes') {\n                eaelEditModeSettings[el.attributes.id] = el.attributes.settings.attributes;\n              }\n            }\n          }\n          if (el.attributes.elType === 'container') {\n            getHoverEffectSettingsVal(el.attributes.elements.models);\n          }\n          if (el.attributes.elType === 'section') {\n            getHoverEffectSettingsVal(el.attributes.elements.models);\n          }\n          if (el.attributes.elType === 'column') {\n            getHoverEffectSettingsVal(el.attributes.elements.models);\n          }\n        });\n      };\n      window.isRunFirstTime = true;\n      var eaelEditModeSettings = [];\n      getHoverEffectSettingsVal(window.elementor.elements.models);\n    }\n    for (var key in eaelEditModeSettings) {\n      if ($scopeId === key) {\n        var _eaelEditModeSettings, _eaelEditModeSettings2, _eaelEditModeSettings5, _eaelEditModeSettings8, _eaelEditModeSettings27, _eaelEditModeSettings46, _eaelEditModeSettings55, _eaelEditModeSettings64, _eaelEditModeSettings82, _eaelEditModeSettings100, _eaelEditModeSettings101, _eaelEditModeSettings102, _eaelEditModeSettings103, _eaelEditModeSettings104, _eaelEditModeSettings105, _eaelEditModeSettings106, _eaelEditModeSettings107, _eaelEditModeSettings108, _eaelEditModeSettings109;\n        //Tilt\n        if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings = eaelEditModeSettings[key]) === null || _eaelEditModeSettings === void 0 ? void 0 : _eaelEditModeSettings['eael_hover_effect_hover_tilt']) === 'yes') {\n          $eaelTilt = 'eael_tilt';\n        }\n\n        //Opacity\n        if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings2 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings2 === void 0 ? void 0 : _eaelEditModeSettings2['eael_hover_effect_opacity_popover']) === 'yes') {\n          var _eaelEditModeSettings3, _eaelEditModeSettings4;\n          $Opacity = {\n            'opacity': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings3 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings3 === void 0 ? void 0 : (_eaelEditModeSettings4 = _eaelEditModeSettings3['eael_hover_effect_opacity']) === null || _eaelEditModeSettings4 === void 0 ? void 0 : _eaelEditModeSettings4['size']\n          };\n        }\n        if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings5 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings5 === void 0 ? void 0 : _eaelEditModeSettings5['eael_hover_effect_opacity_popover_hover']) === 'yes') {\n          var _eaelEditModeSettings6, _eaelEditModeSettings7;\n          $opacityHover = {\n            'opacity': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings6 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings6 === void 0 ? void 0 : (_eaelEditModeSettings7 = _eaelEditModeSettings6['eael_hover_effect_opacity_hover']) === null || _eaelEditModeSettings7 === void 0 ? void 0 : _eaelEditModeSettings7['size']\n          };\n        }\n        //Filter\n        if ('yes' === (eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings8 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings8 === void 0 ? void 0 : _eaelEditModeSettings8['eael_hover_effect_filter_popover'])) {\n          var _eaelEditModeSettings9, _eaelEditModeSettings12, _eaelEditModeSettings15, _eaelEditModeSettings18, _eaelEditModeSettings21, _eaelEditModeSettings24;\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings9 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings9 === void 0 ? void 0 : _eaelEditModeSettings9['eael_hover_effect_blur_is_on']) === 'yes') {\n            var _eaelEditModeSettings10, _eaelEditModeSettings11;\n            $eaelBlurEffect = {\n              'blur': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings10 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings10 === void 0 ? void 0 : (_eaelEditModeSettings11 = _eaelEditModeSettings10['eael_hover_effect_blur']) === null || _eaelEditModeSettings11 === void 0 ? void 0 : _eaelEditModeSettings11['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings12 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings12 === void 0 ? void 0 : _eaelEditModeSettings12['eael_hover_effect_contrast_is_on']) === 'yes') {\n            var _eaelEditModeSettings13, _eaelEditModeSettings14;\n            $eaelContrastEffect = {\n              'contrast': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings13 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings13 === void 0 ? void 0 : (_eaelEditModeSettings14 = _eaelEditModeSettings13['eael_hover_effect_contrast']) === null || _eaelEditModeSettings14 === void 0 ? void 0 : _eaelEditModeSettings14['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings15 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings15 === void 0 ? void 0 : _eaelEditModeSettings15['eael_hover_effect_grayscale_is_on']) === 'yes') {\n            var _eaelEditModeSettings16, _eaelEditModeSettings17;\n            $eaelGrayscaleEffect = {\n              'grayscale': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings16 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings16 === void 0 ? void 0 : (_eaelEditModeSettings17 = _eaelEditModeSettings16['eael_hover_effect_grayscal']) === null || _eaelEditModeSettings17 === void 0 ? void 0 : _eaelEditModeSettings17['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings18 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings18 === void 0 ? void 0 : _eaelEditModeSettings18['eael_hover_effect_invert_is_on']) === 'yes') {\n            var _eaelEditModeSettings19, _eaelEditModeSettings20;\n            $eaelInvertEffect = {\n              'invert': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings19 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings19 === void 0 ? void 0 : (_eaelEditModeSettings20 = _eaelEditModeSettings19['eael_hover_effect_invert']) === null || _eaelEditModeSettings20 === void 0 ? void 0 : _eaelEditModeSettings20['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings21 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings21 === void 0 ? void 0 : _eaelEditModeSettings21['eael_hover_effect_saturate_is_on']) === 'yes') {\n            var _eaelEditModeSettings22, _eaelEditModeSettings23;\n            $eaelSaturateEffect = {\n              'saturate': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings22 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings22 === void 0 ? void 0 : (_eaelEditModeSettings23 = _eaelEditModeSettings22['eael_hover_effect_saturate']) === null || _eaelEditModeSettings23 === void 0 ? void 0 : _eaelEditModeSettings23['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings24 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings24 === void 0 ? void 0 : _eaelEditModeSettings24['eael_hover_effect_sepia_is_on']) === 'yes') {\n            var _eaelEditModeSettings25, _eaelEditModeSettings26;\n            $eaelSepiaEffect = {\n              'sepia': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings25 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings25 === void 0 ? void 0 : (_eaelEditModeSettings26 = _eaelEditModeSettings25['eael_hover_effect_sepia']) === null || _eaelEditModeSettings26 === void 0 ? void 0 : _eaelEditModeSettings26['size']\n            };\n          }\n        }\n\n        //Filter Hover\n        if ('yes' === (eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings27 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings27 === void 0 ? void 0 : _eaelEditModeSettings27['eael_hover_effect_filter_hover_popover'])) {\n          var _eaelEditModeSettings28, _eaelEditModeSettings31, _eaelEditModeSettings34, _eaelEditModeSettings37, _eaelEditModeSettings40, _eaelEditModeSettings43;\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings28 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings28 === void 0 ? void 0 : _eaelEditModeSettings28['eael_hover_effect_blur_hover_is_on']) === 'yes') {\n            var _eaelEditModeSettings29, _eaelEditModeSettings30;\n            $eaelBurHoverEffect = {\n              'blur': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings29 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings29 === void 0 ? void 0 : (_eaelEditModeSettings30 = _eaelEditModeSettings29['eael_hover_effect_blur_hover']) === null || _eaelEditModeSettings30 === void 0 ? void 0 : _eaelEditModeSettings30['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings31 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings31 === void 0 ? void 0 : _eaelEditModeSettings31['eael_hover_effect_contrast_hover_is_on']) === 'yes') {\n            var _eaelEditModeSettings32, _eaelEditModeSettings33;\n            $eaelContrastHoverEffect = {\n              'contrast': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings32 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings32 === void 0 ? void 0 : (_eaelEditModeSettings33 = _eaelEditModeSettings32['eael_hover_effect_contrast_hover']) === null || _eaelEditModeSettings33 === void 0 ? void 0 : _eaelEditModeSettings33['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings34 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings34 === void 0 ? void 0 : _eaelEditModeSettings34['eael_hover_effect_grayscale_hover_is_on']) === 'yes') {\n            var _eaelEditModeSettings35, _eaelEditModeSettings36;\n            $eaelGrayscalHoverEffect = {\n              'grayscale': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings35 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings35 === void 0 ? void 0 : (_eaelEditModeSettings36 = _eaelEditModeSettings35['eael_hover_effect_grayscal_hover']) === null || _eaelEditModeSettings36 === void 0 ? void 0 : _eaelEditModeSettings36['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings37 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings37 === void 0 ? void 0 : _eaelEditModeSettings37['eael_hover_effect_invert_hover_is_on']) === 'yes') {\n            var _eaelEditModeSettings38, _eaelEditModeSettings39;\n            $eaelInvertHoverEffect = {\n              'invert': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings38 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings38 === void 0 ? void 0 : (_eaelEditModeSettings39 = _eaelEditModeSettings38['eael_hover_effect_invert_hover']) === null || _eaelEditModeSettings39 === void 0 ? void 0 : _eaelEditModeSettings39['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings40 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings40 === void 0 ? void 0 : _eaelEditModeSettings40['eael_hover_effect_saturate_hover_is_on']) === 'yes') {\n            var _eaelEditModeSettings41, _eaelEditModeSettings42;\n            $eaelSaturateHoverEffect = {\n              'saturate': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings41 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings41 === void 0 ? void 0 : (_eaelEditModeSettings42 = _eaelEditModeSettings41['eael_hover_effect_saturate_hover']) === null || _eaelEditModeSettings42 === void 0 ? void 0 : _eaelEditModeSettings42['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings43 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings43 === void 0 ? void 0 : _eaelEditModeSettings43['eael_hover_effect_sepia_is_on']) === 'yes') {\n            var _eaelEditModeSettings44, _eaelEditModeSettings45;\n            $eaelSepiaHoverEffect = {\n              'sepia': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings44 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings44 === void 0 ? void 0 : (_eaelEditModeSettings45 = _eaelEditModeSettings44['eael_hover_effect_sepia_hover']) === null || _eaelEditModeSettings45 === void 0 ? void 0 : _eaelEditModeSettings45['size']\n            };\n          }\n        }\n\n        //Offset\n        if ('yes' === (eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings46 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings46 === void 0 ? void 0 : _eaelEditModeSettings46['eael_hover_effect_offset_popover'])) {\n          var _eaelEditModeSettings47, _eaelEditModeSettings48, _eaelEditModeSettings49, _eaelEditModeSettings50, _eaelEditModeSettings51, _eaelEditModeSettings52, _eaelEditModeSettings53, _eaelEditModeSettings54;\n          $eaelOffsetTop = {\n            'size': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings47 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings47 === void 0 ? void 0 : (_eaelEditModeSettings48 = _eaelEditModeSettings47['eael_hover_effect_offset_top']) === null || _eaelEditModeSettings48 === void 0 ? void 0 : _eaelEditModeSettings48['size'],\n            'unit': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings49 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings49 === void 0 ? void 0 : (_eaelEditModeSettings50 = _eaelEditModeSettings49['eael_hover_effect_offset_top']) === null || _eaelEditModeSettings50 === void 0 ? void 0 : _eaelEditModeSettings50['unit']\n          };\n          $eaelOffsetLeft = {\n            'size': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings51 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings51 === void 0 ? void 0 : (_eaelEditModeSettings52 = _eaelEditModeSettings51['eael_hover_effect_offset_left']) === null || _eaelEditModeSettings52 === void 0 ? void 0 : _eaelEditModeSettings52['size'],\n            'unit': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings53 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings53 === void 0 ? void 0 : (_eaelEditModeSettings54 = _eaelEditModeSettings53['eael_hover_effect_offset_left']) === null || _eaelEditModeSettings54 === void 0 ? void 0 : _eaelEditModeSettings54['unit']\n          };\n        }\n\n        //Offset Hover\n        if ('yes' === (eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings55 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings55 === void 0 ? void 0 : _eaelEditModeSettings55['eael_hover_effect_offset_hover_popover'])) {\n          var _eaelEditModeSettings56, _eaelEditModeSettings57, _eaelEditModeSettings58, _eaelEditModeSettings59, _eaelEditModeSettings60, _eaelEditModeSettings61, _eaelEditModeSettings62, _eaelEditModeSettings63;\n          $eaelOffsetHoverTop = {\n            'size': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings56 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings56 === void 0 ? void 0 : (_eaelEditModeSettings57 = _eaelEditModeSettings56['eael_hover_effect_offset_hover_top']) === null || _eaelEditModeSettings57 === void 0 ? void 0 : _eaelEditModeSettings57['size'],\n            'unit': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings58 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings58 === void 0 ? void 0 : (_eaelEditModeSettings59 = _eaelEditModeSettings58['eael_hover_effect_offset_hover_top']) === null || _eaelEditModeSettings59 === void 0 ? void 0 : _eaelEditModeSettings59['unit']\n          };\n          $eaelOffsetHoverLeft = {\n            'size': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings60 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings60 === void 0 ? void 0 : (_eaelEditModeSettings61 = _eaelEditModeSettings60['eael_hover_effect_offset_hover_left']) === null || _eaelEditModeSettings61 === void 0 ? void 0 : _eaelEditModeSettings61['size'],\n            'unit': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings62 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings62 === void 0 ? void 0 : (_eaelEditModeSettings63 = _eaelEditModeSettings62['eael_hover_effect_offset_hover_left']) === null || _eaelEditModeSettings63 === void 0 ? void 0 : _eaelEditModeSettings63['unit']\n          };\n        }\n\n        //Tranform\n        if ('yes' === (eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings64 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings64 === void 0 ? void 0 : _eaelEditModeSettings64['eael_hover_effect_transform_popover'])) {\n          var _eaelEditModeSettings65, _eaelEditModeSettings72, _eaelEditModeSettings77;\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings65 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings65 === void 0 ? void 0 : _eaelEditModeSettings65['eael_hover_effect_rotate_is_on']) === 'yes') {\n            var _eaelEditModeSettings66, _eaelEditModeSettings67, _eaelEditModeSettings68, _eaelEditModeSettings69, _eaelEditModeSettings70, _eaelEditModeSettings71;\n            $eaelRotateEffect = {\n              'rotate_x': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings66 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings66 === void 0 ? void 0 : (_eaelEditModeSettings67 = _eaelEditModeSettings66['eael_hover_effect_transform_rotatex']) === null || _eaelEditModeSettings67 === void 0 ? void 0 : _eaelEditModeSettings67['size'],\n              'rotate_y': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings68 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings68 === void 0 ? void 0 : (_eaelEditModeSettings69 = _eaelEditModeSettings68['eael_hover_effect_transform_rotatey']) === null || _eaelEditModeSettings69 === void 0 ? void 0 : _eaelEditModeSettings69['size'],\n              'rotate_z': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings70 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings70 === void 0 ? void 0 : (_eaelEditModeSettings71 = _eaelEditModeSettings70['eael_hover_effect_transform_rotatez']) === null || _eaelEditModeSettings71 === void 0 ? void 0 : _eaelEditModeSettings71['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings72 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings72 === void 0 ? void 0 : _eaelEditModeSettings72['eael_hover_effect_scale_is_on']) === 'yes') {\n            var _eaelEditModeSettings73, _eaelEditModeSettings74, _eaelEditModeSettings75, _eaelEditModeSettings76;\n            $eaelScaleEffect = {\n              'scale_x': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings73 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings73 === void 0 ? void 0 : (_eaelEditModeSettings74 = _eaelEditModeSettings73['eael_hover_effect_transform_scalex']) === null || _eaelEditModeSettings74 === void 0 ? void 0 : _eaelEditModeSettings74['size'],\n              'scale_y': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings75 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings75 === void 0 ? void 0 : (_eaelEditModeSettings76 = _eaelEditModeSettings75['eael_hover_effect_transform_scaley']) === null || _eaelEditModeSettings76 === void 0 ? void 0 : _eaelEditModeSettings76['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings77 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings77 === void 0 ? void 0 : _eaelEditModeSettings77['eael_hover_effect_skew_is_on']) === 'yes') {\n            var _eaelEditModeSettings78, _eaelEditModeSettings79, _eaelEditModeSettings80, _eaelEditModeSettings81;\n            $eaelSkewEffect = {\n              'skew_x': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings78 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings78 === void 0 ? void 0 : (_eaelEditModeSettings79 = _eaelEditModeSettings78['eael_hover_effect_transform_skewx']) === null || _eaelEditModeSettings79 === void 0 ? void 0 : _eaelEditModeSettings79['size'],\n              'skew_y': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings80 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings80 === void 0 ? void 0 : (_eaelEditModeSettings81 = _eaelEditModeSettings80['eael_hover_effect_transform_skewy']) === null || _eaelEditModeSettings81 === void 0 ? void 0 : _eaelEditModeSettings81['size']\n            };\n          }\n        }\n\n        //Tranform Hover\n        if ('yes' === (eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings82 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings82 === void 0 ? void 0 : _eaelEditModeSettings82['eael_hover_effect_transform_hover_popover'])) {\n          var _eaelEditModeSettings83, _eaelEditModeSettings90, _eaelEditModeSettings95;\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings83 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings83 === void 0 ? void 0 : _eaelEditModeSettings83['eael_hover_effect_rotate_hover_is_on']) === 'yes') {\n            var _eaelEditModeSettings84, _eaelEditModeSettings85, _eaelEditModeSettings86, _eaelEditModeSettings87, _eaelEditModeSettings88, _eaelEditModeSettings89;\n            $eaelRotateHoverEffect = {\n              'rotate_x': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings84 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings84 === void 0 ? void 0 : (_eaelEditModeSettings85 = _eaelEditModeSettings84['eael_hover_effect_transform_hover_rotatex']) === null || _eaelEditModeSettings85 === void 0 ? void 0 : _eaelEditModeSettings85['size'],\n              'rotate_y': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings86 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings86 === void 0 ? void 0 : (_eaelEditModeSettings87 = _eaelEditModeSettings86['eael_hover_effect_transform_hover_rotatey']) === null || _eaelEditModeSettings87 === void 0 ? void 0 : _eaelEditModeSettings87['size'],\n              'rotate_z': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings88 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings88 === void 0 ? void 0 : (_eaelEditModeSettings89 = _eaelEditModeSettings88['eael_hover_effect_transform_hover_rotatez']) === null || _eaelEditModeSettings89 === void 0 ? void 0 : _eaelEditModeSettings89['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings90 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings90 === void 0 ? void 0 : _eaelEditModeSettings90['eael_hover_effect_scale_hover_is_on']) === 'yes') {\n            var _eaelEditModeSettings91, _eaelEditModeSettings92, _eaelEditModeSettings93, _eaelEditModeSettings94;\n            $eaelScaleHoverEffect = {\n              'scale_x': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings91 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings91 === void 0 ? void 0 : (_eaelEditModeSettings92 = _eaelEditModeSettings91['eael_hover_effect_transform_hover_scalex']) === null || _eaelEditModeSettings92 === void 0 ? void 0 : _eaelEditModeSettings92['size'],\n              'scale_y': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings93 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings93 === void 0 ? void 0 : (_eaelEditModeSettings94 = _eaelEditModeSettings93['eael_hover_effect_transform_hover_scaley']) === null || _eaelEditModeSettings94 === void 0 ? void 0 : _eaelEditModeSettings94['size']\n            };\n          }\n          if ((eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings95 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings95 === void 0 ? void 0 : _eaelEditModeSettings95['eael_hover_effect_skew_hover_is_on']) === 'yes') {\n            var _eaelEditModeSettings96, _eaelEditModeSettings97, _eaelEditModeSettings98, _eaelEditModeSettings99;\n            $eaelSkewHoverEffect = {\n              'skew_x': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings96 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings96 === void 0 ? void 0 : (_eaelEditModeSettings97 = _eaelEditModeSettings96['eael_hover_effect_transform_hover_skewx']) === null || _eaelEditModeSettings97 === void 0 ? void 0 : _eaelEditModeSettings97['size'],\n              'skew_y': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings98 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings98 === void 0 ? void 0 : (_eaelEditModeSettings99 = _eaelEditModeSettings98['eael_hover_effect_transform_hover_skewy']) === null || _eaelEditModeSettings99 === void 0 ? void 0 : _eaelEditModeSettings99['size']\n            };\n          }\n        }\n\n        //Transition\n        $eaelDuration = {\n          'transitionDuration': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings100 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings100 === void 0 ? void 0 : (_eaelEditModeSettings101 = _eaelEditModeSettings100['eael_hover_effect_general_settings_duration']) === null || _eaelEditModeSettings101 === void 0 ? void 0 : _eaelEditModeSettings101['size']\n        };\n        $eaelDelay = {\n          'transitionDelay': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings102 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings102 === void 0 ? void 0 : (_eaelEditModeSettings103 = _eaelEditModeSettings102['eael_hover_effect_general_settings_delay']) === null || _eaelEditModeSettings103 === void 0 ? void 0 : _eaelEditModeSettings103['size']\n        };\n        $eaelEasing = {\n          'transitionEasing': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings104 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings104 === void 0 ? void 0 : _eaelEditModeSettings104['eael_hover_effect_general_settings_easing']\n        };\n\n        //Transition Hover\n        $eaelHoverDuration = {\n          'transitionDuration': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings105 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings105 === void 0 ? void 0 : (_eaelEditModeSettings106 = _eaelEditModeSettings105['eael_hover_effect_general_settings_duration']) === null || _eaelEditModeSettings106 === void 0 ? void 0 : _eaelEditModeSettings106['size']\n        };\n        $eaelHoverDelay = {\n          'transitionDelay': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings107 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings107 === void 0 ? void 0 : (_eaelEditModeSettings108 = _eaelEditModeSettings107['eael_hover_effect_general_settings_delay']) === null || _eaelEditModeSettings108 === void 0 ? void 0 : _eaelEditModeSettings108['size']\n        };\n        $eaelHoverEasing = {\n          'transitionEasing': eaelEditModeSettings === null || eaelEditModeSettings === void 0 ? void 0 : (_eaelEditModeSettings109 = eaelEditModeSettings[key]) === null || _eaelEditModeSettings109 === void 0 ? void 0 : _eaelEditModeSettings109['eael_hover_effect_general_settings_easing']\n        };\n      }\n    }\n  }\n  var hoverSelector = window.isEditMode ? \"body [data-id=\\\"\".concat($scopeId, \"\\\"]\") : \"body .eael_hover_effect[data-id=\\\"\".concat($scopeId, \"\\\"]\"),\n    $hoverSelector = $(hoverSelector);\n\n  //Opacity\n  var $opacityVal = $Opacity ? (_$Opacity = $Opacity) === null || _$Opacity === void 0 ? void 0 : _$Opacity.opacity : '1';\n\n  //Offset\n  var $offsetX = (_$eaelOffsetTop = $eaelOffsetTop) !== null && _$eaelOffsetTop !== void 0 && _$eaelOffsetTop.size ? \"translateX(\".concat($eaelOffsetTop.size).concat($eaelOffsetTop.unit, \")\") : 'translateX(0)';\n  var $offsetY = (_$eaelOffsetLeft = $eaelOffsetLeft) !== null && _$eaelOffsetLeft !== void 0 && _$eaelOffsetLeft.size ? \"translateY(\".concat($eaelOffsetLeft.size).concat($eaelOffsetLeft.unit, \")\") : 'translateY(0)';\n\n  //Offset Hover\n  var $offsetHoverX = (_$eaelOffsetHoverTop = $eaelOffsetHoverTop) !== null && _$eaelOffsetHoverTop !== void 0 && _$eaelOffsetHoverTop.size ? \"translateX(\".concat($eaelOffsetHoverTop.size).concat($eaelOffsetHoverTop.unit, \")\") : 'translateX(0)';\n  var $offsetHoverY = (_$eaelOffsetHoverLeft = $eaelOffsetHoverLeft) !== null && _$eaelOffsetHoverLeft !== void 0 && _$eaelOffsetHoverLeft.size ? \"translateY(\".concat($eaelOffsetHoverLeft.size).concat($eaelOffsetHoverLeft.unit, \")\") : 'translateY(0)';\n\n  //Transitions\n  var $eaelDurationVal = $eaelDuration ? (_$eaelDuration = $eaelDuration) === null || _$eaelDuration === void 0 ? void 0 : _$eaelDuration.transitionDuration : '0';\n  var $eaelDelayVal = $eaelDelay ? (_$eaelDelay = $eaelDelay) === null || _$eaelDelay === void 0 ? void 0 : _$eaelDelay.transitionDelay : '0';\n  var $eaelEasingVal = $eaelEasing ? (_$eaelEasing = $eaelEasing) === null || _$eaelEasing === void 0 ? void 0 : _$eaelEasing.transitionEasing : '0';\n\n  //Transitions Hover\n  var $eaelDurationHoverVal = $eaelHoverDuration ? (_$eaelHoverDuration = $eaelHoverDuration) === null || _$eaelHoverDuration === void 0 ? void 0 : _$eaelHoverDuration.transitionDuration : '0';\n  var $eaelDelayHoverVal = $eaelHoverDelay ? (_$eaelHoverDelay = $eaelHoverDelay) === null || _$eaelHoverDelay === void 0 ? void 0 : _$eaelHoverDelay.transitionDelay : '0';\n  var $eaelEasingHoverVal = $eaelHoverEasing ? (_$eaelHoverEasing = $eaelHoverEasing) === null || _$eaelHoverEasing === void 0 ? void 0 : _$eaelHoverEasing.transitionEasing : '0';\n\n  //Filter\n  var $blur = (_$eaelBlurEffect = $eaelBlurEffect) !== null && _$eaelBlurEffect !== void 0 && _$eaelBlurEffect.blur ? \"blur(\".concat($eaelBlurEffect.blur, \"px)\") : 'blur(0px)';\n  var $contrast = (_$eaelContrastEffect = $eaelContrastEffect) !== null && _$eaelContrastEffect !== void 0 && _$eaelContrastEffect.contrast ? \"contrast(\".concat($eaelContrastEffect.contrast, \"%)\") : 'contrast(100%)';\n  var $grayscale = (_$eaelGrayscaleEffect = $eaelGrayscaleEffect) !== null && _$eaelGrayscaleEffect !== void 0 && _$eaelGrayscaleEffect.grayscale ? \"grayscale(\".concat($eaelGrayscaleEffect.grayscale, \"%)\") : 'grayscale(0%)';\n  var $invert = (_$eaelInvertEffect = $eaelInvertEffect) !== null && _$eaelInvertEffect !== void 0 && _$eaelInvertEffect.invert ? \"invert(\".concat($eaelInvertEffect.invert, \"%)\") : 'invert(0%)';\n  var $saturate = (_$eaelSaturateEffect = $eaelSaturateEffect) !== null && _$eaelSaturateEffect !== void 0 && _$eaelSaturateEffect.saturate ? \"saturate(\".concat($eaelSaturateEffect.saturate, \"%)\") : 'saturate(100%)';\n  var $sepia = (_$eaelSepiaEffect = $eaelSepiaEffect) !== null && _$eaelSepiaEffect !== void 0 && _$eaelSepiaEffect.sepia ? \"sepia(\".concat($eaelSepiaEffect.sepia, \"%)\") : 'sepia(0%)';\n\n  //Rotate\n  var $rotateX = (_$eaelRotateEffect = $eaelRotateEffect) !== null && _$eaelRotateEffect !== void 0 && _$eaelRotateEffect.rotate_x ? \"rotateX(\".concat($eaelRotateEffect.rotate_x, \"deg)\") : 'rotateX(0)';\n  var $rotateY = (_$eaelRotateEffect2 = $eaelRotateEffect) !== null && _$eaelRotateEffect2 !== void 0 && _$eaelRotateEffect2.rotate_y ? \"rotateY(\".concat($eaelRotateEffect.rotate_y, \"deg)\") : 'rotateY(0)';\n  var $rotateZ = (_$eaelRotateEffect3 = $eaelRotateEffect) !== null && _$eaelRotateEffect3 !== void 0 && _$eaelRotateEffect3.rotate_z ? \"rotateZ(\".concat($eaelRotateEffect.rotate_z, \"deg)\") : 'rotateZ(0)';\n\n  //Scale\n  var $scaleX = (_$eaelScaleEffect = $eaelScaleEffect) !== null && _$eaelScaleEffect !== void 0 && _$eaelScaleEffect.scale_x ? \"scaleX(\".concat($eaelScaleEffect.scale_x, \")\") : 'scaleX(1)';\n  var $scaleY = (_$eaelScaleEffect2 = $eaelScaleEffect) !== null && _$eaelScaleEffect2 !== void 0 && _$eaelScaleEffect2.scale_y ? \"scaleY(\".concat($eaelScaleEffect.scale_y, \")\") : 'scaleY(1)';\n\n  //Skew\n  var $skewX = (_$eaelSkewEffect = $eaelSkewEffect) !== null && _$eaelSkewEffect !== void 0 && _$eaelSkewEffect.skew_x ? \"skewX(\".concat($eaelSkewEffect.skew_x, \"deg)\") : 'skewX(0deg)';\n  var $skewY = (_$eaelSkewEffect2 = $eaelSkewEffect) !== null && _$eaelSkewEffect2 !== void 0 && _$eaelSkewEffect2.skew_y ? \"skewY(\".concat($eaelSkewEffect.skew_y, \"deg)\") : 'skewY(0deg)';\n\n  //Hover\n  var $opacityHoverVal = $opacityHover ? (_$opacityHover = $opacityHover) === null || _$opacityHover === void 0 ? void 0 : _$opacityHover.opacity : '1';\n  //Rotate\n  var $rotateXHover = (_$eaelRotateHoverEffe = $eaelRotateHoverEffect) !== null && _$eaelRotateHoverEffe !== void 0 && _$eaelRotateHoverEffe.rotate_x ? \"rotateX(\".concat($eaelRotateHoverEffect.rotate_x, \"deg)\") : 'rotateX(0)';\n  var $rotateYHover = (_$eaelRotateHoverEffe2 = $eaelRotateHoverEffect) !== null && _$eaelRotateHoverEffe2 !== void 0 && _$eaelRotateHoverEffe2.rotate_y ? \"rotateY(\".concat($eaelRotateHoverEffect.rotate_y, \"deg)\") : 'rotateY(0)';\n  var $rotateZHover = (_$eaelRotateHoverEffe3 = $eaelRotateHoverEffect) !== null && _$eaelRotateHoverEffe3 !== void 0 && _$eaelRotateHoverEffe3.rotate_z ? \"rotateZ(\".concat($eaelRotateHoverEffect.rotate_z, \"deg)\") : 'rotateZ(0)';\n\n  //Scale\n  var $scaleXHover = (_$eaelScaleHoverEffec = $eaelScaleHoverEffect) !== null && _$eaelScaleHoverEffec !== void 0 && _$eaelScaleHoverEffec.scale_x ? \"scaleX(\".concat($eaelScaleHoverEffect.scale_x, \")\") : 'scaleX(1)';\n  var $scaleYHover = (_$eaelScaleHoverEffec2 = $eaelScaleHoverEffect) !== null && _$eaelScaleHoverEffec2 !== void 0 && _$eaelScaleHoverEffec2.scale_y ? \"scaleY(\".concat($eaelScaleHoverEffect.scale_y, \")\") : 'scaleY(1)';\n\n  //Skew\n  var $skewXHover = (_$eaelSkewHoverEffect = $eaelSkewHoverEffect) !== null && _$eaelSkewHoverEffect !== void 0 && _$eaelSkewHoverEffect.skew_x ? \"skewX(\".concat($eaelSkewHoverEffect.skew_x, \"deg)\") : 'skewX(0)';\n  var $skewYHover = (_$eaelSkewHoverEffect2 = $eaelSkewHoverEffect) !== null && _$eaelSkewHoverEffect2 !== void 0 && _$eaelSkewHoverEffect2.skew_y ? \"skewY(\".concat($eaelSkewHoverEffect.skew_y, \"deg)\") : 'skewY(0)';\n\n  //Filter Hover\n  var $blurHover = (_$eaelBurHoverEffect = $eaelBurHoverEffect) !== null && _$eaelBurHoverEffect !== void 0 && _$eaelBurHoverEffect.blur ? \"blur(\".concat($eaelBurHoverEffect.blur, \"px)\") : 'blur(0px)';\n  var $contrastHover = (_$eaelContrastHoverEf = $eaelContrastHoverEffect) !== null && _$eaelContrastHoverEf !== void 0 && _$eaelContrastHoverEf.contrast ? \"contrast(\".concat($eaelContrastHoverEffect.contrast, \"%)\") : 'contrast(100%)';\n  var $grayscaleHover = (_$eaelGrayscalHoverEf = $eaelGrayscalHoverEffect) !== null && _$eaelGrayscalHoverEf !== void 0 && _$eaelGrayscalHoverEf.grayscale ? \"grayscale(\".concat($eaelGrayscalHoverEffect.grayscale, \"%)\") : 'grayscale(0%)';\n  var $invertHover = (_$eaelInvertHoverEffe = $eaelInvertHoverEffect) !== null && _$eaelInvertHoverEffe !== void 0 && _$eaelInvertHoverEffe.invert ? \"invert(\".concat($eaelInvertHoverEffect.invert, \"%)\") : 'invert(0%)';\n  var $saturateHover = (_$eaelSaturateHoverEf = $eaelSaturateHoverEffect) !== null && _$eaelSaturateHoverEf !== void 0 && _$eaelSaturateHoverEf.saturate ? \"saturate(\".concat($eaelSaturateHoverEffect.saturate, \"%)\") : 'saturate(100%)';\n  var $sepiaHover = (_$eaelSepiaHoverEffec = $eaelSepiaHoverEffect) !== null && _$eaelSepiaHoverEffec !== void 0 && _$eaelSepiaHoverEffec.sepia ? \"sepia(\".concat($eaelSepiaHoverEffect.sepia, \"%)\") : 'sepia(0%)';\n\n  //Normal\n  var normalStyles = {\n    \"transform\": \"\".concat($rotateX, \" \").concat($rotateY, \" \").concat($rotateZ, \" \").concat($scaleX, \" \").concat($scaleY, \" \").concat($skewX, \" \").concat($skewY, \" \").concat($offsetX, \" \").concat($offsetY),\n    \"opacity\": $opacityVal,\n    \"filter\": \"\".concat($blur, \" \").concat($contrast, \" \").concat($grayscale, \" \").concat($invert, \" \").concat($saturate, \" \").concat($sepia),\n    \"transition-property\": 'all',\n    \"transition-duration\": \"\".concat($eaelDurationVal, \"ms\"),\n    \"transition-delay\": \"\".concat($eaelDelayVal, \"ms\"),\n    \"transition-timing-function\": $eaelEasingVal,\n    \"z-index\": 1\n  };\n\n  //Hover\n  var hoverStyles = {\n    'opacity': $opacityHoverVal,\n    'filter': \"\".concat($blurHover, \" \").concat($contrastHover, \" \").concat($grayscaleHover, \" \").concat($invertHover, \" \").concat($saturateHover, \" \").concat($sepiaHover),\n    \"transform\": \"\".concat($rotateXHover, \" \").concat($rotateYHover, \" \").concat($rotateZHover, \" \").concat($scaleXHover, \" \").concat($scaleYHover, \" \").concat($skewXHover, \" \").concat($skewYHover, \" \").concat($offsetHoverX, \" \").concat($offsetHoverY),\n    \"transition-property\": 'all',\n    \"transition-duration\": \"\".concat($eaelDurationHoverVal, \"ms\"),\n    \"transition-delay\": \"\".concat($eaelDelayHoverVal, \"ms\"),\n    \"transition-timing-function\": $eaelEasingHoverVal,\n    \"z-index\": 2\n  };\n  if (window.isEditMode && $enabledElementList.includes($scopeId) || !window.isEditMode && $hoverSelector.length) {\n    $hoverSelector.hover(function () {\n      $(this).css(hoverStyles);\n    }, function () {\n      $(this).css(normalStyles);\n    });\n    $hoverSelector.css(normalStyles);\n  }\n\n  //Tilt Effect\n  if ($eaelTilt === 'eael_tilt') {\n    $(\".elementor-element-\".concat($scopeId)).mousemove(function (e) {\n      var cox = (e.pageX - $(this).offset().left - $(this).width() / 2) / 20;\n      var coy = ($(this).height() / 2 - (e.pageY - $(this).offset().top)) / 20;\n      if ($(this).hasClass('eael_hover_effect')) {\n        $(this).css('transform', 'perspective(500px) rotateY(' + cox + 'deg) rotateX(' + coy + 'deg)');\n      }\n    });\n    $(\".elementor-element-\".concat($scopeId)).mouseleave(function (e) {\n      if ($(this).hasClass('eael_hover_effect')) {\n        $(this).css('transform', 'rotateY(0) rotateX(0)');\n      }\n    });\n  }\n};\njQuery(window).on(\"elementor/frontend/init\", function () {\n  if (eael.elementStatusCheck('eaelHoverEffect')) {\n    return false;\n  }\n  elementorFrontend.hooks.addAction(\"frontend/element_ready/widget\", HoverEffectHandler);\n});\n\n//# sourceURL=webpack:///./src/js/view/hover-effect.js?");

/***/ })

/******/ });