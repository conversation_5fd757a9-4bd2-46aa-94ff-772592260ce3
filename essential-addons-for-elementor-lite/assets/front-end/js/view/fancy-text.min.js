!function(e){var t={};function n(a){if(t[a])return t[a].exports;var o=t[a]={i:a,l:!1,exports:{}};return e[a].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,a){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(a,o,function(t){return e[t]}.bind(null,o));return a},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=13)}({13:function(e,t){var n=function(e,t){var n=e.find(".eael-fancy-text-container").eq(0),a={id:n.data("fancy-text-id"),text:DOMPurify.sanitize(n.data("fancy-text")||"").split("|"),transitionType:n.data("fancy-text-transition-type"),speed:n.data("fancy-text-speed"),delay:n.data("fancy-text-delay"),showCursor:"yes"===n.data("fancy-text-cursor"),loop:"yes"===n.data("fancy-text-loop"),action:n.data("fancy-text-action")};function o(){return new Typed("#eael-fancy-text-"+a.id,{strings:a.text,typeSpeed:a.speed,backSpeed:0,startDelay:300,backDelay:a.delay,showCursor:a.showCursor,loop:a.loop})}function r(){t("#eael-fancy-text-"+a.id).Morphext({animation:a.transitionType,separator:", ",speed:a.delay,complete:function(){a.loop||t(this)[0].index+1!==t(this)[0].phrases.length||t(this)[0].stop()}})}function i(){t(".eael-fancy-text-strings",e).css("display","inline-block")}"typing"===a.transitionType?"page_load"===a.action?o():t(window).on("scroll",(function(){n.isInViewport(1)&&!n.hasClass("eael-animated")&&(o(),n.addClass("eael-animated"))})):"page_load"===a.action?r():t(window).on("scroll",(function(){n.isInViewport(1)&&!n.hasClass("eael-animated")&&(r(),n.addClass("eael-animated"))})),t(document).ready((function(){setTimeout(i,500)})),isEditMode&&setTimeout(i,800)};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("eaelFancyTextLoad"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-fancy-text.default",n)}))}});