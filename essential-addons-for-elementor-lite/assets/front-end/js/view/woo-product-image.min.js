!function(e){var t={};function r(i){if(t[i])return t[i].exports;var n=t[i]={i:i,l:!1,exports:{}};return e[i].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.m=e,r.c=t,r.d=function(e,t,i){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)r.d(i,n,function(t){return e[t]}.bind(null,n));return i},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=38)}({38:function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,i)}return r}function n(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(e,t,i){return(t=function(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var a=function(e,t){var r=t(".eael-single-product-images"),i=t(".product_image_slider__thumbs",e),o=i.data("pi_thumb"),a=t(".swiper-wrapper .swiper-slide:first-child .image_slider__image > img",r),s=t(".swiper-wrapper .swiper-slide:first-child .product_image_slider__thumbs__image > img",r),c=l(a),u=l(s);function l(e){return{src:e.attr("src"),srcset:e.attr("srcset"),sizes:e.attr("sizes")}}function d(r){t(".swiper-container",e).each((function(e,t){t.swiper.autoplay[r](),t.swiper.slideTo(0)}))}function m(e,t){e.fadeOut(100,(function(){e.attr("src",t.src).attr("srcset",t.srcset).attr("sizes",t.sizes).removeAttr("data-src").removeAttr("data-large_image"),e.fadeIn(100)}))}t(".variations_form").on("show_variation",(function(e,t){var r;null!=t&&null!==(r=t.image)&&void 0!==r&&r.src&&(i=t.image,n=i,a.attr("src",n.src).attr("srcset",n.srcset).attr("sizes",n.sizes).attr("data-src",n.src).attr("data-large_image",n.full_src),function(e,t){e.attr("src",t.gallery_thumbnail_src).attr("srcset",t.gallery_thumbnail_src).attr("sizes",t.gallery_thumbnail_src_h)}(s,i),d("stop"));var i,n})),t(".variations_form").on("hide_variation reset_image",(function(){m(a,c),m(s,u),void 0!==o.autoplay&&d("start")}));var f=function(e,t){return"undefined"==typeof Swiper||"function"==typeof Swiper?new(0,elementorFrontend.utils.swiper)(e,t).then((function(e){return e})):p(e,t)},p=function(e,t){return new Promise((function(r,i){r(new Swiper(e,t))}))},_=e.data("id"),g="#slider-container-".concat(_," .product_image_slider__thumbs .swiper-container"),b="#slider-container-".concat(_," .product_image_slider__container .swiper-container"),h=i.data("for_mobile"),y=t(".product_image_slider__container",e).data("pi_image");t(window).on("load",(function(){if(window.matchMedia("(max-width: 767px)").matches){var r=t(".image_slider__image",e).height(),i=o.slidesPerView*h,n=Math.min(i,r);t(".eael-pi-thumb-left .product_image_slider .product_image_slider__thumbs, .eael-pi-thumb-right .product_image_slider .product_image_slider__thumbs",e).css("height",n),e.find(".eael-pi-thumb-bottom .product_image_slider .product_image_slider__thumbs").css("width",n)}else{var a=t(".image_slider__image",e).height(),s=100*o.slidesPerView,c=Math.min(s,a);t(".eael-pi-thumb-left .product_image_slider .product_image_slider__thumbs, .eael-pi-thumb-right .product_image_slider .product_image_slider__thumbs",e).css("height",c)}})),f(t(g),o).then((function(e){var r=n(n({},y),"yes"===o.thumbnail&&{thumbs:{swiper:e}});f(t(b),r).then((function(e){})).catch((function(e){console.log("Error initializing main Swiper:",e)}))})).catch((function(e){console.log("Error initializing Swiper thumbs:",e)})),t(".product_image_slider__trigger a",e).on("click",(function(r){r.preventDefault();var i=[];e.find(".swiper-slide .image_slider__image img").each((function(e){i.push({src:t(this).attr("src")})})),t.magnificPopup.open({items:i,mainClass:"eael-pi",gallery:{enabled:!0},type:"image"})}))};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/eael-woo-product-images.default",a)}))}});