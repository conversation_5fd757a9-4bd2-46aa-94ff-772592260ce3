!function(e){var t={};function o(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,o),r.l=!0,r.exports}o.m=e,o.c=t,o.d=function(e,t,i){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(o.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)o.d(i,r,function(t){return e[t]}.bind(null,r));return i},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=29)}({29:function(e,t){var o="",i=0,r=0,a=0,s="off",n=0,l=[];function u(e){var t=jQuery(e).parent().height(),o=n*t/100;return jQuery(e).parent().offset().top+o}function y(e,t,o){"top-left"==e&&(jQuery(".eael-sticky-video-player2.out").css("top","40px"),jQuery(".eael-sticky-video-player2.out").css("bottom","auto"),jQuery(".eael-sticky-video-player2.out").css("left","40px"),jQuery(".eael-sticky-video-player2.out").css("right","auto")),"top-right"==e&&(jQuery(".eael-sticky-video-player2.out").css("top","40px"),jQuery(".eael-sticky-video-player2.out").css("bottom","auto"),jQuery(".eael-sticky-video-player2.out").css("right","40px"),jQuery(".eael-sticky-video-player2.out").css("left","auto")),"bottom-right"==e&&(jQuery(".eael-sticky-video-player2.out").css("bottom","40px"),jQuery(".eael-sticky-video-player2.out").css("top","auto"),jQuery(".eael-sticky-video-player2.out").css("right","40px"),jQuery(".eael-sticky-video-player2.out").css("left","auto")),"bottom-left"==e&&(jQuery(".eael-sticky-video-player2.out").css("bottom","40px"),jQuery(".eael-sticky-video-player2.out").css("top","auto"),jQuery(".eael-sticky-video-player2.out").css("left","40px"),jQuery(".eael-sticky-video-player2.out").css("right","auto")),jQuery(".eael-sticky-video-player2.out").css("width",o+"px"),jQuery(".eael-sticky-video-player2.out").css("height",t+"px")}function c(e,t){e.on("play",(function(n){a=u(t);var y=t.hasClass("out");jQuery(".eael-sticky-video-player2").removeAttr("id").removeClass("out"),t.attr("id","videobox"),y&&t.addClass("out"),l.length&&l.forEach((function(t,o){e!==t&&t.pause()})),s="on",o=t.data("position"),r=t.data("sheight"),i=t.data("swidth")}))}jQuery(window).on("elementor/frontend/init",(function(){isEditMode&&elementor.hooks.addAction("panel/open_editor/widget/eael-sticky-video",(function(e,t,o){var i;t.attributes.settings.on("change:eaelsv_sticky_width",(function(){clearTimeout(i),i=setTimeout((function(){var o=Math.ceil(t.getSetting("eaelsv_sticky_width")/1.78);t.attributes.settings.attributes.eaelsv_sticky_height=o,e.el.querySelector('[data-setting="eaelsv_sticky_height"]').value=o}),250)})),t.attributes.settings.on("change:eaelsv_sticky_height",(function(){clearTimeout(i),i=setTimeout((function(){var o=Math.ceil(1.78*t.getSetting("eaelsv_sticky_height"));t.attributes.settings.attributes.eaelsv_sticky_width=o,e.el.querySelector('[data-setting="eaelsv_sticky_width"]').value=o}),250)}))})),elementorFrontend.hooks.addAction("frontend/element_ready/eael-sticky-video.default",(function(e,t){t(".eaelsv-sticky-player-close",e).hide();var d,p,v,f=e.find(".eael-sticky-video-player2");d=f.data("sticky"),p=f.data("autoplay"),o=f.data("position"),r=f.data("sheight"),i=f.data("swidth"),v=f.data("overlay"),n=f.data("scroll_height"),y(o,r,i);var h=new Plyr("#eaelsv-player-"+e.data("id"));if(l.push(h),"no"===v&&"yes"===d&&(a=u(f),f.attr("id","videobox"),s="on",c(h,f)),"yes"===v&&"yes"===p){var g=f.prev();s="off",t(".eael-sticky-video-wrapper > i").hide(),g.css("display","none"),h.play(),"yes"===d&&(a=u(f),f.attr("id","videobox"),s="on",c(h,f))}else if("yes"===v){g=f.prev();s="off",t(g).on("click",(function(){t(".eael-sticky-video-wrapper > i").hide(),t(this).css("display","none"),h.play(),"yes"===d&&(a=u(f),f.attr("id","videobox"),s="on",c(h,f))}))}h.on("pause",(function(e){s="off"})),h.on("play",(function(e){f.closest(".eael-sticky-video-player2").find(".plyr__poster").hide(),s="on"})),t(".eaelsv-sticky-player-close").on("click",(function(){f.removeClass("out").addClass("in"),t(".eael-sticky-video-player2").removeAttr("style"),s="off"})),f.parent().css("height",f.height()+"px"),t(window).resize((function(){f.parent().css("height",f.height()+"px")}))}))})),jQuery(window).scroll((function(){var e=jQuery(window).scrollTop();if(jQuery(document).height()-e>jQuery(window).height()+400){if("on"==s){var t=jQuery(".eael-sticky-video-wrapper"),n=function(e){if(!e)return 0;for(var t=e.split(";"),o=0;o<t.length;o++){var i=t[o].trim();if(-1!==i.indexOf("height"))return parseFloat(i.match(/\d+(\.\d+)?/)[0])}}(t.attr("style"));t.attr("style","height:"+n+"px !important;")}e>=a?"on"==s&&(jQuery("#videobox").find(".eaelsv-sticky-player-close").css("display","block"),jQuery("#videobox").removeClass("in").addClass("out"),y(o,r,i)):(jQuery(".eaelsv-sticky-player-close").hide(),jQuery("#videobox").removeClass("out").addClass("in"),jQuery(".eael-sticky-video-player2").removeAttr("style"))}})),jQuery(window).on("load",(function(){jQuery(window).trigger("scroll")}))}});