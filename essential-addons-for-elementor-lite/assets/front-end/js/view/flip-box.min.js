!function(e){var t={};function n(o){if(t[o])return t[o].exports;var i=t[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(o,i,function(t){return e[t]}.bind(null,i));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=15)}({15:function(e,t){var n=function(e,t){var n=e.find(".eael-elements-flip-box-container");function o(){var e=n.find(".eael-elements-flip-box-front-container").outerHeight(),t=n.find(".eael-elements-flip-box-rear-container").outerHeight();n.hasClass("--active")?n.find(".eael-elements-flip-box-flip-card").height(t):n.find(".eael-elements-flip-box-flip-card").height(e)}if(t(".eael-flip-box-click",e).off("click").on("click",(function(){t(this).toggleClass("--active")})),t(".eael-flip-box-hover",e).on("mouseenter mouseleave",(function(){t(this).toggleClass("--active")})),n.hasClass("eael-flipbox-auto-height"))if(n.hasClass("eael-flipbox-max")){var i=setInterval((function(){var e=n.find(".eael-elements-flip-box-front-container").outerHeight(),t=n.find(".eael-elements-flip-box-rear-container").outerHeight(),o=Math.max(e,t);n.find(".eael-elements-flip-box-flip-card").height(o)}),200);setTimeout((function(){clearInterval(i)}),5e3)}else n.hasClass("eael-flipbox-dynamic")&&(t(".eael-flip-box-click",e).on("click",l(o,100)),t(".eael-flip-box-hover",e).on("mouseenter mouseleave",l(o,100)));function l(e,t){var n;return function(){clearTimeout(n),n=setTimeout((function(){clearTimeout(n),e()}),t)}}};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("eaelFlipBox"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-flip-box.default",n)}))}});