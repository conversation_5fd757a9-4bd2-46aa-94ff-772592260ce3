!function(e){var r={};function n(t){if(r[t])return r[t].exports;var o=r[t]={i:t,l:!1,exports:{}};return e[t].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=r,n.d=function(e,r,t){n.o(e,r)||Object.defineProperty(e,r,{enumerable:!0,get:t})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,r){if(1&r&&(e=n(e)),8&r)return e;if(4&r&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(n.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&r&&"string"!=typeof e)for(var o in e)n.d(t,o,function(r){return e[r]}.bind(null,o));return t},n.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(r,"a",r),r},n.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},n.p="",n(n.s=45)}({45:function(e,r){eael.hooks.addAction("editMode.init","ea",(function(){elementor.settings.page.addChangeCallback("eael_ext_reading_progress",(function(e){jQuery(".eael-reading-progress-wrap").addClass("eael-reading-progress-wrap-disabled"),elementor.saver.update.apply().then((function(){elementor.reloadPreview()}))})),elementor.settings.page.addChangeCallback("eael_ext_reading_progress_position",(function(e){elementor.settings.page.setSettings("eael_ext_reading_progress_position",e),jQuery(".eael-reading-progress").removeClass("eael-reading-progress-top eael-reading-progress-bottom").addClass("eael-reading-progress-"+e)})),elementor.settings.page.addChangeCallback("eael_ext_reading_progress_fill_color",(function(e){var r=jQuery(".eael-reading-progress-wrap").attr("id");jQuery("#".concat(r,"  .eael-reading-progress .eael-reading-progress-fill")).css("background-color",e)}))}))}});