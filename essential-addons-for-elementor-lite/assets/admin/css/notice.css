/* review notice */

#wpnotice-essential-addons-for-elementor-black_friday_notice .wpnotice-content-wrapper {
    padding: 0 !important;
}
#wpnotice-essential-addons-for-elementor-black_friday_notice .wpnotice-content-wrapper p {
    margin: 0;
}

.wpnotice-content-wrapper .button {
    text-transform: capitalize;
}

.wpdeveloper-review-notice {
    padding: 10px;
    background-color: #fff;
    border-radius: 3px;
    margin: 15px;
    border-left: 4px solid transparent;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.wpdeveloper-review-notice:after {
    content: "";
    display: table;
    clear: both;
}
.wpdeveloper-notice-thumbnail {
    width: 90px;
    float: left;
    padding: 5px;
    text-align: center;
    border-right: 4px solid transparent;
}
.wpdeveloper-notice-thumbnail img {
    width: 72px;
    opacity: 0.85;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}
.wpdeveloper-notice-thumbnail img:hover {
    opacity: 1;
}
.wpdeveloper-update-notice .wpdeveloper-notice-thumbnail img,
.wpdeveloper-update_400k-notice .wpdeveloper-notice-thumbnail img {
    width: 32px;
}
.wpdeveloper-update-notice .wpdeveloper-notice-thumbnail,
.wpdeveloper-update_400k-notice .wpdeveloper-notice-thumbnail {
    width: auto;
    padding: 7px;
}
.wpdeveloper-update-notice .wpdeveloper-notice-message,
.wpdeveloper-update_400k-notice .wpdeveloper-notice-message {
    padding: 5px 0;
}
.wpdeveloper-update-notice,
.wpdeveloper-update_400k-notice {
    border-color: #6648fe;
    padding: 0
}
a.ea-notice-cta {
    background-color: #4d18ff;
    background: linear-gradient(-30deg, #4d18ff, #9a7cff);
    margin-top: 30px;
    color: #fff;
    padding: 8px 20px;
    outline: none;
    text-decoration: none;
    border-radius: 3px;
    margin-left: 10px;
    transition: all 0.3s ease;
}
a.ea-notice-cta:hover {
    opacity: 0.85;
}
span.coupon-code {
    background: #ebebeb;
    padding: 5px 10px;
    letter-spacing: 0.035em;
}
.eael-review-text {
    overflow: hidden;
}
.eael-review-text h3 {
    font-size: 24px;
    margin: 0 0 5px;
    font-weight: 400;
    line-height: 1.3;
}
.eael-review-text p {
    font-size: 13px;
    margin: 0 0 5px;
}
.wpdeveloper-notice-link {
    margin: 8px 0 0 0;
    padding: 0;
}
.wpdeveloper-notice-link li {
    display: inline-block;
    margin-right: 15px;
}
.wpdeveloper-notice-link li a {
    display: inline-block;
    color: #10738b;
    text-decoration: none;
    padding-left: 26px;
    position: relative;
}
.wpdeveloper-notice-link li a span {
    position: absolute;
    left: 0;
    top: -2px;
}
.wpdeveloper-notice-message {
    padding: 10px 0;
}
.wpdeveloper-upsale-notice .wpdeveloper-notice-message {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 10px 0;
}
.wpdeveloper-upsale-notice .wpdeveloper-notice-message + .notice-dismiss {
    top: 10px;
}
.wpdeveloper-upsale-notice #plugin-install-core {
    margin-left: 10px;
}
.notice.notice-has-thumbnail {
    padding-left: 0;
}
.wpdeveloper-upsale-notice {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.wpdeveloper-upsale-notice .wpdeveloper-notice-thumbnail {
    padding: 10px;
    width: 40px;
}
.wpdeveloper-upsale-notice .wpdeveloper-notice-thumbnail img {
    width: 32px;
}
.toplevel_page_eael-settings .wp-menu-image img {
    max-width: 20px;
    padding-top: 8px !important;
}
.wpdeveloper-upsale-notice .wpdeveloper-notice-message .button {
    margin-left: 15px;
}

.eael-menu-notice {
    background: red;
    position: absolute;
    bottom: 24px;
    right: 0px;
    display: inline-block;
    vertical-align: top;
    box-sizing: border-box;
    margin: 1px 0 -1px 2px;
    padding: 0 5px;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    background-color: #D63638;
    color: #fff;
    font-size: 11px;
    line-height: 1.6;
    text-align: center;
    z-index: 26;
    display:none;
}

.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice,
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice * {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice {
    padding: 0;
    border: 0;
    border-left: 4px solid #1E1E1E;
}
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice .wpnotice-content-wrapper {
    display: flex;
    width: 100%;
    padding: 12px 0;
    align-items: center;
}
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice .wpnotice-content-wrapper .eael-black-friday-optin-logo {
    width: 225px;
    padding: 0;
    text-align: center;
}
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice .wpnotice-content-wrapper .eael-black-friday-optin-logo img {
    width: auto;
    display: block;
    margin: auto;
    max-width: 175px;
}
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice .wpnotice-content-wrapper .eael-black-friday-optin {
    padding-left: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: calc(100% - 225px);
    align-items: flex-start;
}
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice .wpnotice-content-wrapper .eael-black-friday-optin p {
    color: #3C434A;
    font-size: 15px;
    margin-top: 0;
}
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice .wpnotice-content-wrapper .eael-black-friday-optin a.button-primary {
    color: #ffffff;
    background: #5626E7;
    font-size: 15px;
    font-weight: 600;
    border: 0;
    border-radius: 4px;
    height: 38px;
    line-height: 38px;
    padding: 0 15px;
}

.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice .wpnotice-content-wrapper .eael-black-friday-optin a + a {
    color: #5626E7;
    margin-left: 20px;
}
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice .wpnotice-content-wrapper .eael-black-friday-optin svg {
    vertical-align: sub;
    margin-right: 3px;
}
.notice-essential-addons-for-elementor-lite-ea8th_birthday_notice .notice-dismiss:before {
    content: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYuOTk5OTcgNS41ODU5MUwxMS45NDk1IDAuNjM2MzY4TDEzLjM2MzYgMi4wNTA1MUw4LjQxNDA5IDcuMDAwMDRMMTMuMzYzNiAxMS45NDk1TDExLjk0OTUgMTMuMzYzNkw2Ljk5OTk3IDguNDE0MThMMi4wNTA1IDEzLjM2MzZMMC42MzYzNTMgMTEuOTQ5NUw1LjU4NTg0IDcuMDAwMDRMMC42MzYzNTMgMi4wNTA1MUwyLjA1MDUgMC42MzYzNjhMNi45OTk5NyA1LjU4NTkxWiIgZmlsbD0iIzNDNDM0QSIvPgo8L3N2Zz4=")
}

#wpnotice-essential-addons-for-elementor-review {
    display: grid !important;
    grid-template-columns: 80px 1fr !important;
    padding: 10px;
}

#wpnotice-essential-addons-for-elementor-review a {
    text-decoration: none;
}

@media screen and (max-width: 767px) {
    .notice-essential-addons-for-elementor-lite-ea8th_birthday_notice {
        display: none !important;
    }
}

.notice-essential-addons-for-elementor-lite-review ul.essential-addons-for-elementor-lite-notice-links li a span {
    text-decoration: none;
}