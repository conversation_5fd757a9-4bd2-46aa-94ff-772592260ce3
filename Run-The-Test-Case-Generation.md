Dev Branch For Release {{dev}}

Please use the GitHub CLI (`gh`) to fetch and review the diff for the branch for the next Release WPDevelopers/essential-addons-for-elementor-lite and WPDevelopers/essential-addons-elementor repositories against the master branch. After analyzing the changes, create a Test Scenarios/Cases description inside `qa-checklist/release-branchname.md` with two main sections:

**1. Description Section:**

-   **What**: Provide a clear, concise summary of the specific changes made (e.g., "Added New Feature x For Source Y", "Fixed x bug in y features")
-   **Why**: Explain the business reason or problem this PR solves (e.g., "To support new sources api updates", "To resolve embedding issues reported by users in elementor")
-   **Impact**: Identify which features, pages, or user workflows are affected by these changes

**2. QA Testing Checklist:**
Create a focused, actionable testing checklist using markdown checkbox syntax (`- [ ]`) that only includes areas realistically impacted by the code changes. For each impacted area, provide:

-   [ ] **[Feature/Component Name]** - Brief description of what was modified
    -   **Routes to test**: List specific URLs/routes that need verification
    -   **Consideration**: Consider test cases based on different user roles and capabilities.
    -   **Testing steps**:
        1. Exact navigation path (e.g., "Go to /dashboard/billing")
        2. Specific actions to perform (e.g., "Click 'Upgrade Plan' button")
        3. Expected result and what to verify
        4. [Additional steps as needed]
    -   **Edge cases**: Any error scenarios or boundary conditions to test (if applicable)

**Guidelines for creating the checklist:**

-   Only include features/components that are directly modified or could be realistically affected
-   For React/Vue/Blade/jQuery file changes: trace component usage to identify parent components and routes
-   Focus on critical functionality and user-facing changes
-   Avoid generic testing unrelated to the actual modifications
-   Prioritize based on change complexity and user impact
-   Combine related testing into logical groups rather than separate items
