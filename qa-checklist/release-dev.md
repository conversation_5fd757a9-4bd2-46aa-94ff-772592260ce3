# Dev Branch For Release `dev`

## 1. Description Section

### What
- **Enhanced Login/Register Form**: Added reCAPTCHA and Cloudflare Turnstile support for lost password functionality
- **New Figma to Elementor Converter Widget**: Added a new beta widget for importing Figma designs into Elementor
- **Call to Action Widget**: Improved styling and functionality
- **WooCommerce Product Carousel**: Updated CSS selectors to match WooCommerce structure
- **Dashboard Improvements**: Updated UI elements and added Cloudflare integration

### Why
- To enhance security for password reset functionality
- To provide designers with a direct workflow from Figma to Elementor
- To improve user experience and visual appeal of existing widgets
- To maintain compatibility with WooCommerce updates
- To improve admin dashboard usability

### Impact
- Login/Register Form widget
- Figma to Elementor Converter widget (new)
- Call to Action widget
- WooCommerce Product Carousel widget
- Admin Dashboard

## 2. QA Testing Checklist

### Login/Register Form Enhancements

- [ ] **Lost Password reCAPTCHA Integration**
    - **Routes to test**: 
        - Frontend pages with Login/Register Form widget
    - **Consideration**: 
        - Test with different user roles (admin, editor, subscriber)
        - Test with different reCAPTCHA versions (v2, v3)
    - **Testing steps**:
        1. Go to a page with the Login/Register Form widget
        2. Click on "Lost your password?" link
        3. Verify reCAPTCHA appears on the lost password form
        4. Submit the form with valid email and solved reCAPTCHA
        5. Verify password reset email is sent
        6. Try submitting without solving reCAPTCHA and verify error message
    - **Edge cases**:
        - Test with invalid email addresses
        - Test with reCAPTCHA keys misconfigured

- [ ] **Cloudflare Turnstile for Lost Password**
    - **Routes to test**: 
        - Frontend pages with Login/Register Form widget
    - **Consideration**: 
        - Test with different user roles
    - **Testing steps**:
        1. Go to Elementor editor and add Login/Register Form widget
        2. Enable Cloudflare Turnstile in widget settings
        3. Configure with valid site key and secret key
        4. Preview the page and click "Lost your password?" link
        5. Verify Cloudflare Turnstile appears on the form
        6. Submit with valid email and verify password reset email is sent
    - **Edge cases**:
        - Test with invalid Cloudflare Turnstile keys
        - Test with network connectivity issues

### Figma to Elementor Converter (New Widget)

- [ ] **Basic Functionality**
    - **Routes to test**: 
        - Elementor editor with the widget added to a page
    - **Consideration**: 
        - Test with different Figma design complexities
    - **Testing steps**:
        1. Add the Figma to Elementor widget to a page in Elementor editor
        2. Verify the widget shows the "BETA" label
        3. Enter a valid Figma file URL
        4. Click "Import From Figma" button
        5. Verify loading indicator appears during import
        6. Verify Figma design elements are properly imported
    - **Edge cases**:
        - Test with invalid Figma URLs
        - Test with very complex Figma designs
        - Test with Figma designs containing unsupported elements

- [ ] **UI and Styling**
    - **Routes to test**: 
        - Elementor editor widget panel
    - **Testing steps**:
        1. Verify the widget icon appears correctly in the Elementor panel
        2. Verify the BETA SVG icon appears correctly next to the widget title
        3. Check all control labels for typos (especially "Import From")
        4. Verify styling of imported elements matches Figma design

### Call to Action Widget

- [ ] **Styling Updates**
    - **Routes to test**: 
        - Frontend pages with Call to Action widget
    - **Testing steps**:
        1. Add Call to Action widget to a page
        2. Test different style presets
        3. Verify responsive behavior on mobile, tablet, and desktop
        4. Check hover effects and transitions
    - **Edge cases**:
        - Test with very long content
        - Test with different image sizes

### WooCommerce Product Carousel

- [ ] **CSS Selector Updates**
    - **Routes to test**: 
        - WooCommerce product pages with Product Carousel
    - **Consideration**: 
        - Test with latest WooCommerce version
    - **Testing steps**:
        1. Add WooCommerce Product Carousel to a page
        2. Verify products display correctly
        3. Check carousel navigation and pagination
        4. Verify product information (price, title, rating) displays correctly
    - **Edge cases**:
        - Test with products on sale
        - Test with products out of stock

### Admin Dashboard

- [ ] **UI Updates**
    - **Routes to test**: 
        - Essential Addons admin dashboard
    - **Testing steps**:
        1. Navigate to Essential Addons dashboard in WordPress admin
        2. Verify Cloudflare integration UI elements
        3. Check element categorization and filtering
        4. Verify icon fonts load correctly
    - **Edge cases**:
        - Test on different screen sizes
        - Test with many plugins activated
